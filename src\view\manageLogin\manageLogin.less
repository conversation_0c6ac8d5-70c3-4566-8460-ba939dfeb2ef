.vertical-center-modal{
  display: flex;
  align-items: center;
  justify-content: center;

  .ivu-modal{
      top: 0;
  }
}
.login{
  width: 100%;
  height: 100%;
  background-image: url('../../assets/images/login-bg.png');
  background-size: cover;
  background-position: bottom;
  position: relative;
  .ivu-modal-footer {
      display: none;
  }
  &-text {
      text-align: center;
      font-size: 16px;
      font-weight: 600;
  }
  &-btn {
      position: fixed;
      right: 30px;
      top: 20px;
  }
  &-search{
      padding-top: 185px;
      text-align: center;
      .search-title {
          font-size: 32px;
          font-weight: 500;
          color: #fff;
          letter-spacing: 4px;
          margin-bottom: 5px;
          // text-shadow: 5px 8px 12px rgba(10,10,10,0.5);
      }
      .search-subtitle {
          font-size: 14px;
          letter-spacing: 6px;
          font-weight: 500;
          color: #fff;
          // text-shadow: 2px 4px 6px rgba(10,10,10,0.6);
      }
      .search-con {
          width: 630px;
          margin: 60px auto;
          input {
              font-size: 18px;
              height: 60px;
              line-height: 60px;
          }
          .ivu-input {
              border-radius: 30px;
          }
          .search-type {
              width: 120px;
              font-size: 20px;
              padding: 10px;
              color: #57a3f3;
          }
          .ivu-input-group {
              font-size: 16px;
          }
          .ivu-input-default {
              padding-left: 30px;
          }
          .ivu-input-search {
              position: absolute;
              height: 50px;
              width: 96px;
              line-height: 50px;
              right: 7px;
              margin-top: 5px;
              border-radius: 25px !important;
              border: none !important;
              &::before {
                  content: '';
                  display: none;
              }
          }
          .ivu-select-selected-value {
              font-size: 16px;
          }
      }
  }
  &-con{
      width: 300px;
      &-header{
          font-size: 16px;
          font-weight: 300;
          text-align: center;
          padding: 30px 0;
      }
      .form-con{
          padding: 10px 0 0;
      }
      .login-tip{
          font-size: 10px;
          text-align: center;
          color: #c3c3c3;
      }
  }
}
.header {
  width: 100%;
  height: 90px;
  line-height: 90px;
  background: #fff;
  .content, .footer .content {
      width: 100%;
      max-width: 1200px;
      margin: 0 auto;
      .logo {
          display: inline-block;
          margin: 20px 0;
          height: 50px;
      }
      .logo-font {
          position: relative;
          top: -10px;
          left: 5px;
          border: 0;
          font-size: 24px;
          color: #fff;
          padding-left: 12px;
      }
      .fn-left, .fn-right {
          display: inline;
      }
      .free-tell {
          color: #0047bd;
          .f-name {
              height: 20px;
              line-height: 20px;
              font-size: 16px;
              font-weight: 600;
          }
          .f-tell {
              font-size: 32px;
          }
      }
      .fn-right {
          float: right;
      }
  }
  .swNav {
      margin-top: -20px;
      height: 50px;
      background: #0047bd;
  }
  .top-nav>li {
      float: left;
  }
  .cargo-nav {
      height: 40px;
  }
  .cargo-nav li {
      float: left;
      height: 40px;
      line-height: 40px;
  }
  .top-nav>li>a {
      float: left;
      font-size: 18px;
      width: 120px;
      height: 50px;
      line-height: 50px;
      margin: 0;
      padding: 0;
      text-align: center;
      color: #fff;
  }
  .top-nav>li>a.current, .top-nav>li>a:hover {
      color: #fff;
      background: url('../../assets/images/nav_line.png') no-repeat bottom center;
      background-color: #13387a;
  }
}
