<template>
    <div class="month-range-class">
        <Poptip v-model="visible" :placement="placeMent" @on-popper-show="popperShowEvent">
            <Input
                v-model="monthStart"
                suffix="ios-calendar-outline"
                readonly
                placeholder="选择月份"
                clearable
                @on-clear="clearChange"
                class="month-select-input"
            />
            <div class="monthPanpel" slot="content">
                <div class="monthPanpel-left">
                  <div style="width:100%;display:flex;align-items:center;">
                      <span>
                          <Icon
                              type="ios-arrow-dropleft"
                              size="20"
                              @click="handleYearBefore('left')"
                              style="cursor:pointer"
                          />
                      </span>
                      <div
                          style="text-align:center;width: 100%;font-size: 16px;"
                      >{{`${yearStart}年`}}</div>
                      <span v-if="!rightYearShow">
                          <Icon
                              type="ios-arrow-dropright"
                              size="20"
                              @click="handleYearBefore('right')"
                              style="cursor:pointer"
                          />
                      </span>
                  </div>

                  <div
                      v-for="(item,index) in monthList"
                      class="monthPanpel-left-cell"
                      :class="isDisabled(index, 'start') ? 'month-disabled' : 'monthPanpel-can-click'"
                      :key="item.value"
                      @click="handleMonthClick('left',index)"
                      :style="{background:currentBgNum1.indexOf(index)>=0?'#f3f3f3':'',
                                color:currentBgNum1.indexOf(index)>=0?'#000':''}">
                      <span>{{item.label}}</span>
                  </div>
                </div>
                <div class="monthPanpel-right" v-if="rightYearShow">
                  <div style="width:100%;display:flex;align-items:center;">
                    <div
                        style="text-align:center;width: 100%;font-size: 16px;"
                    >{{`${yearEnd}年`}}</div>
                    <span>
                        <Icon
                            type="ios-arrow-dropright"
                            size="20"
                            @click="handleYearBefore('right')"
                            style="cursor:pointer"
                        />
                    </span>
                </div>
                <div
                    v-for="(item,index) in monthList"
                    class="monthPanpel-right-cell"
                    :class="isDisabled(index, 'end') ? 'month-disabled' : 'monthPanpel-can-click'"
                    :key="item.value"
                    @click="handleMonthClick('right',index)"
                    :style="{background:currentBgNum2.indexOf(index)>=0?'#f3f3f3':'',
                            color:currentBgNum2.indexOf(index)>=0?'#000':''}">
                    <span>{{item.label}}</span>
                </div>
              </div>
            </div>
        </Poptip>
    </div>
</template>
<script>
export default {
  name: 'MonthSelect',
  props: {
    value: String,
    placeMent: {
      type: String,
      default: 'bottom-end'
    },
    limitDate: {
      type: Array,
      default: function () {
        return []
      }
    }
  },
  data () {
    return {
      rightYearShow: true,
      visible: false,
      currentBgNum1: [],
      currentBgNum2: [],
      clickNum: 0,
      yearStart: new Date().getFullYear(),
      yearEnd: new Date().getFullYear() + 1,
      limitStart: '', // 限制起始时间
      limitEnd: '', // 限制结束时间
      monthStart: '',
      startT: [],
      monthList: [
        {
          label: '1月',
          value: '1'
        },
        {
          label: '2月',
          value: '2'
        },
        {
          label: '3月',
          value: '3'
        },
        {
          label: '4月',
          value: '4'
        },
        {
          label: '5月',
          value: '5'
        },
        {
          label: '6月',
          value: '6'
        },
        {
          label: '7月',
          value: '7'
        },
        {
          label: '8月',
          value: '8'
        },
        {
          label: '9月',
          value: '9'
        },
        {
          label: '10月',
          value: '10'
        },
        {
          label: '11月',
          value: '11'
        },
        {
          label: '12月',
          value: '12'
        }
      ]
    }
  },
  methods: {
    clearChange () {
      this.startT = []
      this.$emit('on-change', this.startT)
    },
    isDisabled (idx, str) {
      if (this.limitDate.length > 0) {
        // console.log(this.yearStart, this.yearEnd, this.limitDate)
        let _curStartDate = this.yearStart + '' + (idx > 9 ? (idx + 1) : '0' + (idx + 1))
        let _curEndDate = this.yearEnd + '' + (idx > 9 ? (idx + 1) : '0' + (idx + 1))
        let limitStartDate = this.limitDate[0].split('-')[0] + this.limitDate[0].split('-')[1]
        let limitEndDate = this.limitDate[1].split('-')[0] + this.limitDate[1].split('-')[1]
        if (str === 'start') {
          if (parseInt(_curStartDate) >= parseInt(limitStartDate) && parseInt(_curStartDate) <= parseInt(limitEndDate)) {
            return false
          } else {
            return true
          }
        }
        if (str === 'end') {
          if (parseInt(_curEndDate) >= parseInt(limitStartDate) && parseInt(_curEndDate) <= parseInt(limitEndDate)) {
            return false
          } else {
            return true
          }
        }
      }
      return false
    },
    popperShowEvent () {
      this.currentBgNum1 = []
      this.currentBgNum2 = []
      this.startT = []
    },
    handleMonthClick (type, index) {
      // const curDate = new Date();
      // const curYear = curDate.getFullYear();
      // const curMonth = curDate.getMonth() + 1;
      if (type === 'left' && this.startT.length < 2 && !this.isDisabled(index, 'start')) {
        this.currentBgNum1.push(index)
        const mon = (`${index + 1}`).padStart(2, 0)
        const startT = `${this.yearStart}-${mon}`
        // if (new Date(this.startT).getTime() > new Date().getTime()) return;
        this.startT.push(startT)
      } else if ((type === 'right' && this.startT.length < 2) && !this.isDisabled(index, 'end')) {
        this.currentBgNum2.push(index)
        const mon = (`${index + 1}`).padStart(2, 0)
        const startT = `${this.yearEnd}-${mon}`
        // if (new Date(this.startT).getTime() > new Date().getTime()) return;
        this.startT.push(startT)
      }
      if (this.startT.length >= 2) {
        // eslint-disable-next-line no-unused-expressions
        new Date(this.startT[0]).getTime() > new Date(this.startT[1]).getTime()
          ? this.startT.reverse()
          : ''
        this.monthStart = this.startT.join('~')
        this.$emit('on-change', this.startT)
        this.visible = false
      }
    },
    handleYearBefore (type) {
      if (type === 'left') {
        this.yearStart -= 1
        this.yearEnd -= 1
      } else if (type === 'right') {
        this.yearEnd += 1
        this.yearStart += 1
      }
      if (this.startT.length === 1) {
        const acrossM = this.startT[0].split('-')[1]
        const acrossY = this.startT[0].split('-')[0]
        if (acrossY === this.yearStart) {
          this.currentBgNum1 = [Number(acrossM)]
        } else {
          this.currentBgNum1 = []
        }
        if (acrossY === this.yearEnd) {
          this.currentBgNum2 = [Number(acrossM)]
        } else {
          this.currentBgNum2 = []
        }
      }
    }
  },
  created () {
    this.monthStart = this.value
  },
  watch: {
    value (nV, oV) {
      this.monthStart = nV
    }
  }
}
</script>

<style lang="less" scoped>
.month-select-input {
  width: 150px;
  margin-bottom:0px;
}
.month-range-class {
  display: inline-block;
  line-height: normal;
  margin-left: 12px;
  // align-items: center;
  // margin-bottom: 12px;
}
.monthPanpel {
  display: flex;
  .monthPanpel-left,
  .monthPanpel-right {
    width: 210px;
    display: flex;
    flex: 1;
    padding: 12px;
    flex-wrap: wrap;
    .monthPanpel-left-cell,
    .monthPanpel-right-cell {
      width: 60px;
      text-align: center;
      height: 30px;
      line-height: 30px;
      border-radius: 10px;
    }
    .monthPanpel-can-click {
      cursor: pointer;
      &:hover {
        background: #f3f3f3;
        color: #000;
      }
    }
  }
  .monthPanpel-right {
    border-left: 2px solid #fff;
  }
}
</style>
<style>
  .month-select-input .ivu-input {
    border: 1px solid #dcdee2;
    color: #515a6e;
    height: 32px;
  }
  .month-select-input .ivu-input:focus {
    border-color: #57a3f3;
    outline: 0;
  }
  .month-select-input .ivu-input-icon {
    line-height: 32px;
  }
  .ivu-poptip-body {
    background: #fff;
    color: #000;
    border-radius: 5px;
  }
  .ivu-poptip-popper[x-placement^=bottom] .ivu-poptip-arrow:after {
    border-bottom-color: #fff;
  }
  .month-disabled {
    background: rgb(243, 243, 243);
    color: rgb(0, 0, 0, 0.3);
  }
</style>
