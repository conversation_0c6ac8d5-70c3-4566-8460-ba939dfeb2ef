<template>
  <div>
    <!-- 万兴船舶模板 -->
    <Table border :columns="wanxingColumns" :data="templateData" :max-height="`${winHeight}`" class="alignTable"></Table>
  </div>
</template>
<script>
export default {
  props: {
    templateData: Array
  },
  data () {
    return {
      winHeight: 500,
      wanxingColumns: [
        {
          title: '序号',
          type: 'index',
          align: 'center',
          width: 65
        },
        {
          title: '船名/航次',
          align: 'center',
          width: 140,
          render: (h, params) => {
            return h('div', [
              h('div', {}, params.row.ship_name + ' ' + params.row.voyage_no)
            ])
          }
        },
        {
          title: '货品',
          key: 'goods_name',
          align: 'center',
          width: 100
        },
        {
          title: '货量',
          key: 'amounts',
          align: 'center',
          width: 90
        },
        {
          title: '装货港',
          key: 'port_name',
          align: 'center',
          width: 90
        },
        {
          title: '预抵装港时间',
          key: 'export_load_data',
          align: 'center'
        },
        {
          title: '预抵卸港时间',
          key: 'export_unload_data',
          align: 'center'
        },
        {
          title: '备注',
          key: 'remark',
          align: 'center'
        }
      ]
    }
  },
  mounted () {
    // 挂载浏览器高度获取方法
    const that = this
    that.winHeight = window.innerHeight - 280
    window.onresize = () => {
      return (() => {
        that.winHeight = window.innerHeight - 280
      })()
    }
  }
}
</script>
