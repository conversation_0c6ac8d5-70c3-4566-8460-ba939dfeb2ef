import axios from 'axios'
import { getToken, getShipCompanyId } from '@/libs/util'
import router from '../router'
import Cookies from 'js-cookie'

class HttpRequest {
  constructor (baseUrl = baseURL) {
    this.timeout = 5000
    this.baseUrl = baseUrl
    this.queue = {}
    this.pending = [] // 声明一个数组用于存储每个ajax请求的取消函数和ajax标识
    // this.CancelToken = axios.CancelToken
    this.removePending = e => {
      for (let p in this.pending) {
        if (this.pending[p].u === e.url + '&' + e.method) { // 当当前请求在数组中存在时执行函数体
          this.pending[p].f() // 执行取消操作
          this.pending.splice(p, 1) // 把这条记录从数组中移除
        }
      }
    }
  }
  getInsideConfig () {
    const config = {
      baseURL: this.baseUrl,
      headers: {}
    }
    return config
  }
  destroy (url) {
    delete this.queue[url]
    if (!Object.keys(this.queue).length) {
      // Spin.hide()
    }
  }
  interceptors (instance, url) {
    // 请求拦截
    instance.interceptors.request.use(config => {
      // 添加全局的loading...
      if (!Object.keys(this.queue).length) {
        // Spin.show() // 不建议开启，因为界面不友好
      }
      this.queue[url] = true
      // 统一拦截添加token
      config.params = {}
      config.params.token = getToken()
      let CancelToken = axios.CancelToken
      if (getShipCompanyId() && getShipCompanyId() !== '') {
        config.params.ship_company_id = getShipCompanyId()
      }
      if (config.cancelToken) {
        this.removePending(config) // 在一个ajax发送前执行一下取消操作
        config.cancelToken = new CancelToken(c => {
          // 这里的ajax标识我是用请求地址&请求方式拼接的字符串，当然你可以选择其他的一些方式
          this.pending.push({ u: config.url + '&' + config.method, f: c })
        })
      }
      if (url.indexOf('http') >= 0) {
        config.params = {}
        config.url = url
      }
      return config
    }, error => {
      return Promise.reject(error)
    })
    // 响应拦截
    instance.interceptors.response.use(res => {
      this.destroy(url)
      const { data, status } = res
      this.removePending(res.config) // 在一个ajax响应后再执行一下取消操作，把已经完成的请求从pending中移除
      // 接口token过期处理
      if (res.data.Code === -202) {
        Cookies.set('token', '')
        let toRouteName = (Cookies.get('access') && (Cookies.get('access').includes('super_admin') || Cookies.get('access').includes('super_set'))) ? 'manageLogin' : 'login'
        router.push({ name: toRouteName, params: { failure: true } })
      }
      return { data, status }
    }, error => {
      this.destroy(url)
      if (!error.message) {
        return Promise.reject(error)
        // console.log('请求被中断！')
      }
      return Promise.reject(error)
    })
  }
  request (options) {
    const instance = axios.create()
    options = Object.assign(this.getInsideConfig(), options)
    this.interceptors(instance, options.url)
    return instance(options)
  }
}
export default HttpRequest
