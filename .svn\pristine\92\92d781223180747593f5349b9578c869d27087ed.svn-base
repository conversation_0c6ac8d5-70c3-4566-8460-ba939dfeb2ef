<template>
  <div>
    <!-- 查询 -->
    <Card v-if="showType !== 'searchDetail'">
      <search @searchResults='searchResults' @selectOnChanged='selectOnChanged' :setSearch='setSearchData' @keydown.enter.native='searchResults' @resetResults='resetResults'></search>
    </Card>
    <Card style="margin-top:10px;padding-top:6px;">
      <p slot='title' class="bold-font" v-if="showType !== 'searchDetail'">靠泊计划</p>
      <div class="extra" slot="extra" v-if="showType !== 'searchDetail'">
        <formAction :setFormAction='setFormAction' @handleCreate='handleCreate' @handleUpdateTable="handleUpdateTable"></formAction>
      </div>
      <div class="datalisttable">
        <Row class="datalisthead">
          <Col span="3" v-if="showType !== 'searchDetail'" style="text-align: left;">船名</Col>
          <Col :span="operateShow ? 4 : 2" v-if="showType !== 'searchDetail'"  style="text-align: left;">航次</Col>
          <Col span="5" style="text-align: left;">靠泊计划</Col>
          <Col :span="showType === 'searchDetail' ? 6 : 4" style="text-align: left;">预计靠泊时间</Col>
          <Col span="4" style="text-align: left;">最新发送时间</Col>
          <Col :span="showType === 'searchDetail' ? 6 : 4" style="text-align: left;">最新发送状态</Col>
          <Col :span="showType === 'searchDetail' ? 3 : 2" style="text-align: left; padding-left: 25px;" v-if="!operateShow">操作</Col>
        </Row>
        <Row v-if="berthingPlanList.length === 0" class="datalistempty">暂无数据</Row>
        <Row v-for="(item, index) in berthingPlanList" :key="index" class="datalistdiv">
          <!-- <div class="ired" v-if="item.result === '1'"><img src="../../assets/images/voy.png" alt=""></div> -->
          <Col span="3" style="color: #333;font-size: 16px;" v-if="showType !== 'searchDetail'">{{ item.ship_name !== '' ? item.ship_name : '&nbsp;' }}</Col>
          <Col :span="operateShow ? 4 : 2" style="color: #333;font-size: 16px;" v-if="showType !== 'searchDetail'">{{ item.voyage_no !== '' ? item.voyage_no : '&nbsp;' }}</Col>
          <Col span="5">{{ item.port_name !== '' ? item.port_name : '&nbsp;' }}{{ item.wharf_name }}{{ item.berth_name }}</Col>
          <Col :span="showType === 'searchDetail' ? 6 : 4">{{ item.plan_date !== '' ? item.plan_date : '&nbsp;' }} </Col>
          <Col span="4">{{ item.send_date !== '' ? item.send_date : '&nbsp;' }} </Col>
          <Col :span="showType === 'searchDetail' ? 6 : 4" style="color: #333;font-size: 16px;">{{ item.result_name !== '' ? item.result_name : '&nbsp;' }}</Col>
          <Col :span="showType === 'searchDetail' ? 3 : 2" v-if="!operateShow">
            <Button type="text" @click="sendVoyageHandle(item.id)" v-if="item.send_num === '0' && item.voyage_status !== '3'">发送</Button>
            <Button type="text" @click="sendAgainVoyage(item.id)" v-else-if="item.send_num !== '0' && item.voyage_status !== '3'">再次发送</Button>
            <Button type="text" @click="handleUpdate(item)" v-if="item.send_num === '0' && item.voyage_status !== '3'">修改</Button>
            <Button type="text" @click="remove(item)" v-if="item.send_num === '0' && item.voyage_status !== '3'">删除</Button>
            <Button type="text" @click="handlePushHistory(item)" v-if="item.send_num !== '0'">推送历史</Button>
          </Col>
        </Row>
      </div>
      <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
          :total="total" prev-text="< 上一页" next-text="下一页 >"
          @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
      <Spin fix v-if="spinShow">
        <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
        <div>Loading</div>
      </Spin>
    </Card>
    <berthingPlanEdit ref="berthingPlanEditModal" @addSuccess="getList"></berthingPlanEdit>
    <!-- 推送历史弹窗 -->
    <pushHistoryDrawer ref="pushHistoryDrawer"></pushHistoryDrawer>
  </div>
</template>

<script>
import search from '_c/search' // 查询组件
import formAction from '_c/formAction' // 表单操作组件
import API from '@/api/berthingPlan/berthingPlan'
import berthingPlanEdit from './berthingPlanEdit'
import pushHistoryDrawer from './pushHistoryDrawer'

export default {
  props: {
    showType: String, // 仅供详情页调用使用
    berthObj: Object // 详情页调用传回的入参信息
  },
  components: {
    search,
    formAction,
    berthingPlanEdit,
    pushHistoryDrawer
  },
  data () {
    return {
      operateShow: false, // 是否可操作权限
      spinShow: false, // loading
      setSearchData: {// 查询设置，对象key值为回调参数
        ship_name: {
          type: 'select',
          label: '船名',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 150,
          value: ''
        },
        voyage_no: {
          type: 'text',
          label: '航次',
          placeholder: 'V.',
          width: 150,
          value: ''
        },
        start_plan_date: {
          type: 'date',
          label: '时间',
          selected: '',
          width: 130,
          value: '',
          isdisable: false
        },
        end_plan_date: {
          type: 'date_end',
          label: '-',
          selected: '',
          width: 130,
          value: '',
          isdisabled: false
        }
      },
      setFormAction: {
        operation: this.operateShow ? ['create', 'updateTable'] : ['updateTable']
      },
      total: null, // 列表数据条数
      listQuery: {// 列表请求参数
        pageSize: 6,
        pageIndex: 1,
        ship_name: '',
        voyage_no: '',
        port_id: '',
        start_plan_date: '',
        end_plan_date: '',
        company_id: ''
      },
      listCurrent: 1, // 当前页码
      subMenuDisabled: [], // 按钮权限
      berthingPlanList: [] // 储存列表数据
    }
  },
  created () {
    if (localStorage.menuSort && JSON.parse(localStorage.menuSort).length !== 0) {
      JSON.parse(localStorage.menuSort).map(item => {
        this.subMenuDisabled.push(item.menu_sort)
      })
      this.subMenuDisabled = this.subMenuDisabled.join(',')
    }
    // 获取船名
    if (window.localStorage.shipNameList) {
      let shipList = JSON.parse(window.localStorage.shipNameList)
      shipList.map(item => {
        if ((item.business_model === '1' || item.business_model === '2') && !item.ship_name.includes('善')) { // 只要自营和船期船舶且不需要万邦船舶  2024/09/09调整
          this.setSearchData.ship_name.selectData.push({
            value: item.ship_name,
            label: item.ship_name
          })
        }
      })
    } else {
      this.setSearchData.ship_name.selectData = []
    }
    if (window.localStorage.userDataId) {
      this.operateShow = window.localStorage.userDataId === '9'
    }
    this.getList()
  },
  methods: {
    // 查询
    searchResults (e) {
      if (e.target) delete e.target
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      Object.assign(this.listQuery, e)
      this.listQuery.start_plan_date = this.start_plan_date
      this.listQuery.end_plan_date = this.end_plan_date
      this.getList()
    },
    selectOnChanged (e) {
      if (e.flag === 'date_start') {
        this.start_plan_date = e.key
      } else if (e.flag === 'date_end') {
        this.end_plan_date = e.key
      }
    },
    // 重置
    resetResults () {
      this.listCurrent = 1
      this.listQuery = Object.assign(this.listQuery, {
        pageIndex: 1,
        ship_name: '',
        voyage_no: '',
        port_id: '',
        start_plan_date: '',
        end_plan_date: '',
        company_id: ''
      })
      this.setSearchData.ship_name.selected = ''
      this.setSearchData.voyage_no.value = ''
      this.setSearchData.start_plan_date.selected = ''
      this.setSearchData.end_plan_date.selected = ''
      this.getList()
    },
    // 获取列表
    getList () {
      this.spinShow = true
      if (this.showType === 'searchDetail') {
        this.listQuery.ship_name = this.berthObj.ship_name
        this.listQuery.voyage_no = this.berthObj.voyage_no
      }
      API.berthplanList(this.listQuery).then(response => {
        if (response.data.Code === 10000) {
          this.berthingPlanList = response.data.Result
          this.list = response.data.Result
          this.total = response.data.Total
        } else {
          this.$Message.error(response.data.Message)
        }
        setTimeout(() => {
          this.spinShow = false
        }, 1 * 200)
      }).catch(
        setTimeout(() => {
          this.spinShow = false
        }, 1 * 800)
      )
    },
    // 开启组件
    formModalState (d, row) {
      this.$refs.berthingPlanEditModal.dialogType = d
      if (d === 'create') {
        this.$refs.berthingPlanEditModal.berthingPlanFormTitle = '新增'
      } else if (d === 'detail') {
        row.post_maxnum = parseInt(row.post_maxnum)
        this.$refs.berthingPlanEditModal.modalData = Object.assign({}, row)
        this.$refs.berthingPlanEditModal.berthingPlanFormTitle = '详情'
      } else if (d === 'update') {
        row.post_maxnum = parseInt(row.post_maxnum)
        this.$refs.berthingPlanEditModal.modalData = Object.assign({}, row)
        this.$refs.berthingPlanEditModal.berthingPlanFormTitle = '编辑'
      }
      this.$refs.berthingPlanEditModal.berthingFormModal = true
    },
    // 手动更新列表
    handleUpdateTable () {
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      this.getList()
    },
    // 查看详情
    handleDetail (row) {
      this.formModalState('detail', row)
    },
    // 新增
    handleCreate () {
      this.formModalState('create')
    },
    // 修改
    handleUpdate (row) {
      this.formModalState('update', row)
    },
    // 删除
    remove (row) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确定删除该条记录？</p>',
        loading: true,
        onOk: () => {
          API.berthplanDelete({ id: row.id }).then(res => {
            this.$Modal.remove()
            res.data.Code === 10000 ? this.$Message.success(res.data.Message) : this.$Message.warning(res.data.Message)
            this.getList()
          })
        }
      })
    },
    // 推送历史
    handlePushHistory (d) {
      this.$refs.pushHistoryDrawer.formModal = true
      this.$refs.pushHistoryDrawer.title = '靠泊计划推送历史'
      this.$refs.pushHistoryDrawer.listQuery.key_type = 2
      this.$refs.pushHistoryDrawer.listQuery.key_id = d.id
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    },
    // 发送
    sendVoyageHandle (id) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>是否发送靠泊计划？</p>',
        loading: true,
        onOk: () => {
          API.sendBerthPlan({ id: id }).then(response => {
            if (response.data.Code === 10000) {
              this.$Message.success(response.data.Message)
              this.$Modal.remove()
              this.loading = false
              this.getList()
            } else {
              this.$Message.error(response.data.Message)
              this.$Modal.remove()
              this.loading = false
            }
          })
        }
      })
    },
    // 再次发送
    sendAgainVoyage (id) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>是否再次发送靠泊计划？</p>',
        loading: true,
        onOk: () => {
          API.sendAgainBerthPlan({ id: id }).then(response => {
            if (response.data.Code === 10000) {
              this.$Message.success(response.data.Message)
              this.$Modal.remove()
              this.loading = false
              this.getList()
            } else {
              this.$Message.error(response.data.Message)
              this.$Modal.remove()
              this.loading = false
            }
          })
        }
      })
    }
  }
}
</script>
<style lang="less">
.datalisttable {
  .datalisthead {
    margin-bottom: 5px;
    padding: 0 15px;
    .ivu-col {
      padding: 0 5px;
    }
  }
}
.datalistdiv {
  align-items: center;
  display: flex;
  margin-bottom: 15px;
  border-radius: 4px;
  padding: 20px 15px;
  position: relative;
  border: 1px solid #D9D9D9;
  .ivu-col {
    padding: 0 5px;
    word-break: break-all;
  }
  button {
    color: #007DFF;
    font-size: 14px;
    line-height: 0.8;
    display: block;
  }
  .ired {
    left: 0;
    top: 0;
    position: absolute;
    img {
      width: 42px;
    }
  }
}
.datalistempty {
  border-radius: 4px;
  padding: 30px 15px;
  text-align: center;
  border: 1px solid #D9D9D9;
}
.ivu-card-head {
  border-bottom: 1px solid #e8eaec;
  padding: 14px 16px 0;
  line-height: 1;
}
</style>
