<template>
  <div>
    <Card>
      <search @searchResults='searchResults' :setSearch='setSearchData' @keydown.enter.native='searchResults' @resetResults='resetResults'></search>
    </Card>
    <Card class="cardDiv">
      <p slot='title' class="bold-font">历史航次</p>
      <div class="extra" slot="extra">
        <formAction :setFormAction='setFormAction' @handleUpdateTable="getList"></formAction>
      </div>
      <Table border :loading="loading" :columns="columns" :data="listData"></Table>
      <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
          :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
    </Card>
    <editModal :modalData="modalData" @addSuccess="getList"></editModal>
    <stepAdjustModal :portLineData="portLineData" @addSuccess="getList"></stepAdjustModal>
  </div>
</template>

<script>
import search from '_c/search' // 查询组件
import editModal from './editModal'
import stepAdjustModal from './stepAdjustModal'
import formAction from '_c/formAction' // 表单操作组件
import { queryVoHistoryMPage, addSingleVoyage, addVoyageReportDetail } from '@/api/voyageHistory'
import { queryShipList } from '@/api/dynamicManagement'
import { queryAllCompanyList } from '@/api/customerManagement/customerManagement'

export default {
  name: 'history',
  components: {
    search,
    formAction,
    editModal,
    stepAdjustModal
  },
  data () {
    return {
      setSearchData: {// 查询设置，对象key值为回调参数
        ship_id: {
          type: 'select',
          label: '船名',
          width: 150,
          value: '',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          filterable: true,
          isdisable: true
        },
        voyage_no: {
          type: 'text',
          label: '航次',
          width: 150,
          value: '',
          isdisable: false
        },
        ship_company_id: {
          type: 'select',
          label: '公司',
          selectData: [],
          selected: '',
          filterable: true,
          placeholder: '请选择',
          selectName: '',
          width: 280,
          value: ''
        }
      },
      setFormAction: {
        operation: ['updateTable']
      },
      total: null, // 列表数据条数
      listData: [],
      loading: false, // 表单列表loding状态
      columns: [
        {
          title: '船名',
          key: 'ship_name',
          align: 'center'
        },
        {
          title: '航次',
          key: 'voyage_no',
          align: 'center'
        },
        {
          title: '装港',
          key: 'load_port_names',
          align: 'center'
        },
        {
          title: '卸港',
          key: 'unload_port_names',
          align: 'center'
        },
        {
          title: '最新动态',
          width: 300,
          align: 'center',
          render: (h, params) => {
            let curData = params.row.dynamic_name !== '' ? '，' : ''
            return h('div', {}, params.row.node_name + curData + params.row.dynamic_name)
          }
        },
        {
          title: '操作',
          align: 'center',
          width: this.$store.state.user.access === 'super_admin' ? 410 : 230,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: {
                  icon: 'md-add',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleCreate(params.row)
                  }
                }
              }, '添加节点'),
              h('Button', {
                style: {
                  margin: '0 0 0 8px',
                  display: this.$store.state.user.access === 'super_admin' ? 'inline-block' : 'none'
                },
                props: {
                  icon: 'md-brush',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleUpdate(params.row)
                  }
                }
              }, '步骤数调整'),
              h('Button', {
                style: {
                  margin: '0 0 0 8px'
                },
                props: {
                  icon: 'md-podium',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleCharts(params.row)
                  }
                }
              }, '统计同步'),
              h('Button', {
                style: {
                  margin: '0 0 0 8px',
                  display: this.$store.state.user.access === 'super_admin' ? 'inline-block' : 'none'
                },
                props: {
                  icon: 'ios-boat',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleVoyage(params.row)
                  }
                }
              }, '航程同步')
            ])
          }
        }
      ],
      listQuery: {// 列表请求参数
        ship_id: '',
        voyage_no: '',
        ship_company_id: '',
        pageSize: 10,
        pageIndex: 1
      },
      listCurrent: 1, // 当前页码
      modalData: {},
      portLineData: {}
    }
  },
  created () {
    // 获取船名
    queryShipList().then(res => {
      if (res.data.Code === 10000) {
        res.data.Result.map(item => {
          this.setSearchData.ship_id.selectData.push({
            value: item.ship_id,
            label: item.ship_name
          })
        })
      }
    })
    queryAllCompanyList({ find_all: 1 }).then(res => {
      if (res.data.Code === 10000) {
        res.data.Result.map(item => {
          this.setSearchData.ship_company_id.selectData.push({
            value: item.id,
            label: item.name
          })
        })
      }
    })
    this.getList()
  },
  methods: {
    // 获取列表
    getList () {
      this.loading = true
      this.listQuery.ship_id = this.setSearchData.ship_id.selected
      this.listQuery.voyage_no = this.setSearchData.voyage_no.value
      this.listQuery.ship_company_id = this.setSearchData.ship_company_id.selected
      queryVoHistoryMPage(this.listQuery).then(response => {
        if (response.data.Code === 10000) {
          this.loading = false
          this.listData = response.data.Result
          this.total = response.data.Total
        } else {
          this.loading = false
          this.$Message.error(response.data.Message)
        }
      }).catch(
        setTimeout(() => {
          this.loading = false
        }, 800)
      )
    },
    // 查询
    searchResults (e) {
      if (e.target) delete e.target
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      Object.assign(this.listQuery, e)
      this.getList()
    },
    // 重置
    resetResults () {
      this.listCurrent = 1
      this.listQuery = {
        ship_id: '',
        voyage_no: '',
        ship_company_id: '',
        pageSize: 10,
        pageIndex: 1
      }
      this.setSearchData.ship_id.selected = ''
      this.setSearchData.voyage_no.value = ''
      this.setSearchData.ship_company_id.selected = ''
      this.getList()
    },
    // 添加节点
    handleCreate (row) {
      this.modalData = {
        modal: true,
        data: row
      }
    },
    // 步骤数调整
    handleUpdate (row) {
      this.portLineData = {
        modal: true,
        data: row
      }
    },
    // 统计同步
    handleCharts (row) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>将' + row.ship_name + '船舶' + row.voyage_no + '航次数据进行重新统计。</p>',
        loading: true,
        onOk: () => {
          addSingleVoyage({ voyage_id: row.id }).then(res => {
            this.loading = false
            if (res.data.Code === 10000) {
              this.$Message.success(res.data.Message)
              this.$Modal.remove()
              this.getList()
            } else {
              this.modalData.modal = true
              this.$Message.error(res.data.Message)
            }
          }).catch()
        }
      })
    },
    // 航程同步
    handleVoyage (row) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>将' + row.ship_name + '船舶' + row.voyage_no + '航次进行航程同步。</p>',
        loading: true,
        onOk: () => {
          addVoyageReportDetail({ pre_voyage_id: row.id }).then(res => {
            if (res.data.Code === 10000) {
              this.$Message.success(res.data.Message)
              this.$Modal.remove()
              this.loading = false
              this.getList()
            } else {
              this.loading = false
              this.$Modal.remove()
              this.$Message.error(res.data.Message)
            }
          }).catch()
        }
      })
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    }
  }
}
</script>
<style scoped>
.cardDiv {
  margin-top: 20px;
  border: none;
  background: transparent;
}
</style>
