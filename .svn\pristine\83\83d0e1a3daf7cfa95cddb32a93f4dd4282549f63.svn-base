<template>
  <div class="schedule-container">
    <!-- 月度排期计划 - 可滑动日历 -->
    <div class="schedule-calendar-section">
      <div class="section-title">
        <h3>月度排期计划</h3>
      </div>
      
      <!-- 可滑动的日历头部 -->
      <div class="calendar-container">
        <Spin fix v-if="isSchedule" class="loading_area">
          <LoadingModule 
            ref="scheludeLoading"
            :steps="stepsData" 
            :title="title"
            :subtitle="subtitle"
            :autoStart="true"
            :subTaskDuration="subTaskTime"
            :mainTaskInterval="mainTaskTime"
            @step-completed="onStepCompleted"
            @completed="onCompleted"
          />
        </Spin>
        <Spin v-if="isLoading" class="loading_area">
          <ListLoading 
            :visible="isLoading"
            :currentMessage="loadingMessages"
            :progress="curProgress"
            ref="dataLoader"
          />
        </Spin>
        <div class="calendar-controls">
          <!-- <Button icon="ios-arrow-back" @click="scrollCalendar('left')"></Button> -->
          <div class="calendar-outer-container">
            <!-- 固定的船舶列 -->
            <div class="fixed-ship-column">
              <div class="calendar-ship-header">船舶</div>
              <div 
                class="calendar-ship-column" 
                v-for="(ship,idx) in shipSchedules"
                :key="`fixed-${idx}+1`">
                <div>{{ ship.ship_name }}</div>
                <div class="ship-capacity">{{ ship.expected_deadweight }}吨</div>
              </div>
            </div>
            
            <!-- 可滚动的日历部分 -->
            <div 
              class="calendar-scroll-container"
              ref="calendarContainer"
              @mousedown="startDrag"
              @mousemove="onDrag"
              @mouseup="stopDrag"
              @mouseleave="stopDrag">
              <div class="calendar-header-wrapper">
                <div class="calendar-header" ref="calendarHeader">
                  <div 
                    class="calendar-day-header" 
                    v-for="(day, index) in calendarDays" 
                    :key="`header-${day.fullDate}`"
                    :class="{'different-month': day.month !== currentMonth}">
                    <div class="day-number">{{ day.day }}</div>
                    <div class="month-indicator" v-if="day.isFirstOfMonth || day.day === 15">{{ day.monthShort }}</div>
                  </div>
                </div>
              </div>
              
              <!-- 日历内容 -->
              <div class="calendar-body">
                <div class="calendar-row" v-for="ship in shipSchedules" :key="ship.id">
                  <div class="calendar-days-container">
                    <!-- 先渲染月份背景 -->
                    <div 
                      v-for="(month, index) in getUniqueMonths()" 
                      :key="`month-bg-${index}`"
                      class="month-background"
                      :style="calculateMonthBackgroundStyle(month)">
                    </div>
                    
                    <!-- 添加横向线 -->
                    <div class="calendar-row-border-top"></div>
                    <div class="calendar-row-border-bottom"></div>
                    
                    <!-- 再渲染背景网格线 -->
                    <div 
                      class="calendar-day-column" 
                      v-for="(day, index) in calendarDays" 
                      :key="`grid-${day.fullDate}`"
                      :class="{'different-month': day.month !== currentMonth}">
                    </div>
                    <div 
                      v-for="(schedule,idx) in ship.planList"
                      :key="`${idx}-transit`"
                      class="schedule-item"
                      :class="schedule.isRun ? `schedule-loading` : schedule.status === '2' ? 'schedule-unloading' : schedule.status === 3 ? 'schedule-standby' : 'schedule-transit'"
                      :style="(schedule.isRun || schedule.status === 3 || schedule.is_dock_repair === '1') ? calculateScheduleStyle(schedule) : calculateScheduleStyle(schedule)"
                      :title="schedule.is_dock_repair === '1' ? `坞修\n原因：${schedule.remarks}\n日期：${schedule.start_plan_date} ~ ${schedule.estimated_over_day}` : (schedule.isRun || schedule.status === 3) ? `货主：${schedule.shipper_name}\n港口：${schedule.load_port_name} - ${schedule.unload_port_name}\n货品：${schedule.goods_name}\n货量：${schedule.amounts}吨\n日期：${schedule.start_plan_date} ~ ${schedule.estimated_over_day}` : `货主：${schedule.shipper_name}\n港口：${schedule.load_port_name} - ${schedule.unload_port_name}\n货品：${schedule.goods_name}\n货量：${schedule.amounts}吨\n运价：${schedule.freight_rate || '-'}元/吨\n运费：${schedule.shipping_fee || '-'}元\n日期：${schedule.empty_sail_start_day || schedule.start_plan_date} ~ ${schedule.estimated_over_day}`">
                      <div class="schedule-content">
                        <div :title="`受载期\n日期：${schedule.start_plan_date} ~ ${schedule.end_plan_date}`" :style="calculateLoadStartStyle(schedule)"></div>
                        <div v-if="!schedule.isRun || schedule.is_dock_repair !== '1'" :title="`受载期\n日期：${schedule.start_plan_date} ~ ${schedule.end_plan_date}`" :style="calculateLoadStyle(schedule)">
                          <!-- 受载期 -->
                        </div>
                        <div v-if="schedule.is_dock_repair === '1'">
                          <div>坞修</div>
                          <div>{{ schedule.remarks }}</div>
                          <div>{{ schedule.start_plan_date }} ~ {{ schedule.estimated_over_day }}</div>
                        </div>
                        <div v-else>{{ schedule.load_port_name }} - {{ `${schedule.unload_port_name}\n${schedule.goods_name}\n${schedule.amounts}吨` }}</div>
                        <div v-if="!schedule.isRun && schedule.status === '1'" class="schedule_status"><Icon type="md-lock" @click="scheduleUnlock(schedule)"/></div>
                        <div v-if="!schedule.isRun && schedule.status === '2'" class="schedule_status"><Icon type="md-create" @click="scheduleLock(schedule)" /></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- <Button icon="ios-arrow-forward" @click="scrollCalendar('right')"></Button> -->
        </div>
      </div>
    </div>
    <Card class="main-card">
      <!-- 待排期列表 -->
      <div class="section-title">
        <h3>待排期列表</h3>
        <Tag color="blue">{{ total }}</Tag>
        <div class="header-btns">
          <Button class="add_btns" type="primary" icon="md-add" @click="addCargo">添加货源</Button>
          <Button type="default" icon="md-print" @click="generateSchedule">生成排期</Button>
        </div>
      </div>

      <Table border :columns="pendingColumns" :data="pendingData" class="mb-20">
        <template slot-scope="{ row }" slot="action">
          <ButtonGroup size="small">
            <Button type="primary" icon="md-create" @click="editCargo(row)">编辑</Button>
            <Button style="margin-left: 10px;" type="error" icon="md-trash" @click="deleteCargo(row)">删除</Button>
          </ButtonGroup>
        </template>
      </Table>
      <Page show-sizer :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="queryParam.pageSize" :current.sync="queryParam.pageIndex"
          :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
    </Card>
    <!-- 添加货源抽屉组件 -->
    <CargoDrawer 
      v-model="cargoDrawerVisible" 
      :cargo-data="currentCargo" 
      :is-edit="isEditingCargo"
      @submit="handleCargoSubmit"
      @cancel="handleCargoCancel"
    />
    <!-- 引入排期Modal组件 -->
    <ScheduleModal 
      v-model="scheduleModalVisible" 
      @on-success="handleScheduleSuccess"
      @on-cancel="handleScheduleCancel">
    </ScheduleModal>
  </div>
</template>

<script>
import API from '@/api/shipSchedule'
import CargoDrawer from './CargoDrawer.vue'
import ScheduleModal from './ScheduleModal.vue'
import LoadingModule from './components/LoadingModule.vue'
import ListLoading from './components/ListLoading.vue'

export default {
  components: {
    CargoDrawer,
    ScheduleModal,
    LoadingModule,
    ListLoading
  },
  data() {
    return {
      title: '智能排期处理',
      subtitle: '排期进行中...',
      titleIcon: '',
      loadingMessages: '',
      curProgress: 0,
      subTaskTime: 300,
      mainTaskTime: 200,
      loadingMesList: [
        '正在连接数据服务器...',
        '正在获取船舶信息...',
        '正在加载港口数据...',
        '正在分析航线数据...',
        '正在处理航行记录...',
        '正在优化数据结构...',
        '正在应用数据过滤器...',
        '正在验证数据完整性...',
        '正在准备数据展示...',
        '数据获取已完成！'
      ],
      stepsData: [
      {
        title: '数据收集',
        description: '收集船舶信息、港口数据和历史航行记录',
        subTasks: [
          { title: '计划筛查分配', description: '清空和选择时间有交集的所有已自动分配计划' },
          { title: '组装去重计划', description: '剔除异常数据,排除前后计划冲突问题' },
          { title: '整合计划排序', description: '获取所有待分配的计划，按受载期结束时间排序并按货量降序' }
        ]
      },
      {
        title: '计划分析',
        description: '遍历分析待分配的计划',
        subTasks: [
          { title: '匹配最优船舶', description: '获取所有符合要求船舶' },
          { title: '船舶数据比对', description: '依据基础信息匹配货盘要求' },
          { title: '筛查合适船舶', description: '按条件剔除掉不符合的并筛查出适合的船舶' }
        ]
      },
      {
        title: '货盘匹配',
        description: '依据货盘要求,船舶要求匹配已知货盘',
        subTasks: [
          { title: '货盘匹配重组', description: '获得所有可载此货的船舶列表（载重从小到大）' },
          { title: '限制要求匹配', description: '剔除船舶列表中有限制该船 + 货品的客户' },
          { title: '已有计划确认', description: '获取此货盘和已排好计划的船舶时间' },
          { title: '时间交集去重', description: '排除（受载结束~承运结束时间）有交集的船舶' }
        ]
      },
      // {
      //   title: '算法匹配',
      //   description: '货盘前后计划船舶时间计算与冲突解决',
      //   subTasks: [
      //     { title: '前计划匹配', description: '匹配货盘的前一个计划航次，计算得到该货盘航行时间' },
      //     { title: '前计划剔除', description: '剔除船舶列表中没有前一个航次(无法计算出航行时长)的船舶' },
      //     { title: '后计划匹配', description: '剔除船舶列表中后一个航次(无法计算出航行时长)的船舶' },
      //     { title: '匹配载重吨', description: '依据载重吨数排序，取第一个船舶作为该货盘的最适合船舶' }
      //   ]
      // },
      {
        title: '最终排期',
        description: '生成最终船舶排期方案和可视化结果',
        subTasks: [
          { title: '地域限制匹配', description: '省内港口的优先使用限制省内运营的船舶' },
          { title: '成本分析报告', description: '生成最终方案的经济成本分析' },
          { title: '优化结果展示', description: '通过图表展示优化前后的效率提升' }
        ]
      }],
      filterMonth: '2025-04',
      isLoading: false,
      queryParam: {
        pageSize: 5,
        pageIndex: 1
      },
      planDateArr: [], // 计划中的所有开始日期和结束日期
      total: 0,
      startDate: '', // 起始日期
      endDate: '', // 结束日期
      betweenDays: 0, // 所有日期间隔
      scheduleModalVisible: false,
      daysToExtendBefore: 1, // 起始日期前推天数
      pendingColumns: [
        { title: '货主', key: 'shipper_name', align: 'center', render: (h, params) => {
          return h('div', {}, params.row.shipper_name || '-');
         }
        },
        { title: '货品', key: 'goods_name', align: 'center', sortable: true},
        { title: '货量(吨)', key: 'amounts', width: 120, align: 'center', sortable: true, sortMethod: (a, b, type) => {
          if (type === 'asc') {
            return parseInt(a) > parseInt(b) ? -1 : 1
          } else {
            return parseInt(a) > parseInt(b) ? 1 : -1
          }
        }},
        { title: '运价(元/吨)', key: 'freight_rate', align: 'center', render: (h, params) => {
          return h('div', {}, params.row.freight_rate || '-');
         }
        },
        { title: '运费(元)', key: 'shipping_fee', align: 'center', render: (h, params) => {
          return h('div', {}, params.row.shipping_fee || '-');
         }
        },
        { title: '装货港', key: 'load_port_name', align: 'center', render: (h, params) => {
          return h('div', {}, params.row.load_port_name || '-');
         }
        },
        { title: '卸货港', key: 'unload_port_name', align: 'center', render: (h, params) => {
          return h('div', {}, params.row.unload_port_name || '-');
         } },
        { title: '受载开始时间', key: 'start_plan_date', align: 'center' },
        { title: '受载结束时间', key: 'end_plan_date', align: 'center' },
        { title: '状态', key: 'status', align: 'center',
          render: (h, params) => {
            // 状态（0未分配，1人工指定，2系统分配）
            // 人工指定状态为蓝色，系统分配状态为绿色，未分配状态为灰色
            let statusStr = ''
            let statusColor = ''
            if(params.row.status === '0') {
              statusStr = '未分配'
              statusColor = 'gray'
            }
            if(params.row.status === '1') {
              statusStr = '人工指定' 
              statusColor = 'blue'
            }
            if(params.row.status === '2') {
              statusStr = '系统分配' 
              statusColor = 'green'
            }
            return h('Tag', {
              props: {
                color: statusColor
              }
            }, statusStr);
          }
        },
        { title: '操作', slot: 'action', width: 180, align: 'center' }
      ],
      pendingData: [],
      cargoDrawerVisible: false,
      currentCargo: {},
      isEditingCargo: false,
      // 修改日历数据
      currentMonth: 4, // 当前月份数字表示
      calendarDays: [], // 将存储所有要显示的日期
      
      // 修改排期数据结构，使用完整日期字符串
      shipSchedules: [],

      // 鼠标拖动相关状态
      isDragging: false,
      startX: 0,
      scrollLeft: 0,
      
      // 添加固定的日期列宽度
      dayColumnWidth: 40,
      interval: null,
      isSchedule: false // 是否在排期
//       1、清空和选择时间有交集的所有已自动分配计划
// 2、获取所有待分配的计划，按受载期结束时间排序，按amount降序
// 3、从第一个开始，遍历待分配的计划（以下逻辑：获取所有船舶后根据条件剔除掉那些不符合的，降剩余的第一个作为适合的船舶）：
//   （3.1）根据化学品船舶配置详情先匹配这个货盘，获得所有可载此货的船舶列表1（载重从小到大）
//   （3.2）剔除船舶列表1中有限制该船 + 货品的客户，得到船舶列表2
//   （3.3）获取此货盘和已排好计划的船舶时间（受载结束~承运结束时间）有交集的船舶，并排除这批船舶，获得船舶列表3
//   （3.4）遍历船舶列表3，找到该货盘的前一个计划航次，计算得到该货盘航行时间，如果和上一个航次的承运结束时间有交集或历史记录里面无法计算出航行时长，剔除这个船舶；同理找到该货盘的后一个航次，计算后一个航次的航行时间和这个航次的承运结束时间是否有交集或历史记录里面无法计算出航行时长，剔除这个船舶。获得船舶列表4
//   （3.5）船舶列表4根据（3.1）是载重从小到大排列，取第一个船舶作为该货盘的最适合船舶。
//   省内港口的优先使用限制省内运营的船舶
    };
  },
  created() {
    this.getList()
  },
  watch: {
    // filterMonth() {
    //   this.generateCalendarDays();
    // }
  },
  mounted() {
    // 在组件挂载后确保日历头部和内容区域的列宽一致
    this.$nextTick(() => {
      // this.setColumnWidth()
      this.alignCalendarColumns();
      this.alignRowHeights();
      // 监听窗口大小变化，重新对齐列和行高
      window.addEventListener('resize', this.handleResize);
    });
  },
  beforeDestroy() {
    // 组件销毁前移除事件监听
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    getList() {
      if(!this.isSchedule) { // 排期已经在loading,就不需要再重复
        this.isLoading = true
        this.isSchedule = false
      }
      let index = 0
      let interval = setInterval(() => {
        this.loadingMessages = this.loadingMesList[index]
        if (index < 9) {
          index++
        } else {
          clearInterval(interval)
        }
        this.curProgress = index * 10

      }, 500)
      // 获取货源信息
      this.getGoodsList()
      // 获取船舶排期信息
      API.queryVoyageMonthPlanListByShipGroup().then(res => {
        setTimeout(() => {
          this.curProgress = 100
          this.loadingMessages = this.loadingMesList[9]
          setTimeout(() => {
            if(this.isSchedule) {
              this.isSchedule = false
            }
            this.isLoading = false
          }, 100)
        }, 100)
        // this.isLoading = false
        if(res.data.Code === 10000) {
          this.planDateArr = []
          this.shipSchedules = res.data.Result.filter(item => {return item.planList && (item.planList.length > 0 || item.voyageList.length > 0)})
          this.shipSchedules.map(async (item,index) => {
            item.planList.map(it => {
              this.planDateArr.push(it.empty_sail_start_day || it.start_plan_date)
              this.planDateArr.push(it.estimated_over_day || it.end_plan_date)
            })
            if(item.voyageList && item.voyageList.length > 0) {
              item.voyageList.map(it => {
                this.planDateArr.push(it.empty_sail_start_day || it.start_plan_date)
                this.planDateArr.push(it.estimated_over_day || it.end_plan_date)
                this.shipSchedules[index].planList.unshift({
                  voyage_no: it.voyage_no,
                  amounts: it.amount,
                  empty_sail_start_day: it.empty_sail_start_date,
                  end_plan_date: it.end_plan_date,
                  estimated_over_day: it.estimated_over_day, 
                  goods_name: it.goods_name,
                  load_port_name: it.load_port_name,
                  start_plan_date: it.start_plan_date,
                  isRun: it.status === '2'? 1 : undefined, // 运行中
                  status: it.status === '1'? 3 : 1, // 未运行
                  unload_port_name: it.unload_port_name,
                  shipper_name: it.cargo_company_name
                })
              })
            }
          })
          let minDate = this.planDateArr.map(date => new Date(date)).sort((a, b) => a - b)[0]
          let maxDate = this.planDateArr.map(date => new Date(date)).sort((a, b) => b - a)[0]
          this.startDate = minDate.toISOString().split('T')[0]
          this.endDate = maxDate.toISOString().split('T')[0]
          this.generateCalendarDays()
        }
      })
    },
    getUnloadingList() {
      // 获取货源信息
      this.getGoodsList()
      // 获取船舶排期信息
      API.queryVoyageMonthPlanListByShipGroup().then(res => {
        setTimeout(() => {
          this.curProgress = 100
          this.loadingMessages = this.loadingMesList[9]
          setTimeout(() => {
            if(this.isSchedule) {
              this.isSchedule = false
            }
            this.isLoading = false
          })
        })
        // this.isLoading = false
        if(res.data.Code === 10000) {
          this.planDateArr = []
          this.shipSchedules = res.data.Result.filter(item => {return item.planList && (item.planList.length > 0 || item.voyageList.length > 0)})
          this.shipSchedules.map(async (item,index) => {
            item.planList.map(it => {
              this.planDateArr.push(it.empty_sail_start_day || it.start_plan_date)
              this.planDateArr.push(it.estimated_over_day || it.end_plan_date)
            })
            if(item.voyageList && item.voyageList.length > 0) {
              item.voyageList.map(it => {
                this.shipSchedules[index].planList.unshift({
                  voyage_no: it.voyage_no,
                  amounts: it.amount,
                  empty_sail_start_day: it.empty_sail_start_date,
                  end_plan_date: it.end_plan_date,
                  estimated_over_day: it.estimated_over_day, 
                  goods_name: it.goods_name,
                  load_port_name: it.load_port_name,
                  start_plan_date: it.start_plan_date,
                  isRun: it.status === '2'? 1 : undefined, // 运行中
                  status: it.status === '1'? 3 : 1, // 未运行
                  unload_port_name: it.unload_port_name,
                  shipper_name: it.cargo_company_name
                })
              })
            }
          })
          let minDate = this.planDateArr.map(date => new Date(date)).sort((a, b) => a - b)[0]
          let maxDate = this.planDateArr.map(date => new Date(date)).sort((a, b) => b - a)[0]
          this.startDate = minDate.toISOString().split('T')[0]
          this.endDate = maxDate.toISOString().split('T')[0]
          this.generateCalendarDays()
        }
      })
    },
    onStepCompleted() {

    },
    onCompleted() {

    },
    getGoodsList() {
      // 获取货源信息
      API.queryVoyageMonthPlanPage(this.queryParam).then(res => {
        if(res.data.Code === 10000) {
          this.pendingData = res.data.Result
          this.total = res.data.Total
        }
      })
    },
    // 添加货源
    addCargo() {
      this.isEditingCargo = false;
      this.currentCargo = {};
      this.cargoDrawerVisible = true;
    },
    editCargo(cargo) {
      this.isEditingCargo = true;
      this.currentCargo = {...cargo};
      this.cargoDrawerVisible = true;
    },
    deleteCargo(cargo) {
      this.$Modal.confirm({
        title: '确认删除',
        content: `确定要删除 ${cargo.goods_name} 为 ${cargo.amounts}吨 货源吗？`,
        onOk: () => {
          API.delVoyageMonthPlan({voyage_month_plan_id:cargo.voyage_month_plan_id}).then(res => {
            if(res.data.Code === 10000) {
              this.$Message.success(res.data.Message)
              this.getUnloadingList()
            } else {
              this.$Message.error(res.data.Message)
            }}
          )
        }
      })
    },
    // 解锁
    scheduleUnlock(item) {
      this.$Modal.confirm({
        title: '确认解锁',
        content: `确定要解锁 ${item.ship_name} 装载 ${item.goods_name} 为 ${item.amounts}吨 货源吗？`,
        okText: '解锁', 
        onOk: () => {
          Object.assign(item, {
            status: '2'
          })
          let _param = {...item,...{
            ship_id: ''
          }}
          API.updateVoyageMonthPlan(_param).then(res => {
            if(res.data.Code === 10000) {
              this.$Message.success(res.data.Message)
              this.getUnloadingList()
            } else {
              this.$Message.error(res.data.Message)
            }
          }) 
        }
      })
    },
    // 锁定
    scheduleLock(item) {
      this.$Modal.confirm({
        title: '确认锁定',
        content: `确定要锁定 ${item.ship_name} 装载 ${item.goods_name} 为 ${item.amounts}吨 货源吗？`, 
        okText: '锁定',
        onOk: () => {
          Object.assign(item, {
            status: '1'
          })
          let _param = {...item, ...{
            ship_id: item.ship_id_plan
          }}
          API.updateVoyageMonthPlan(_param).then(res => {
            if(res.data.Code === 10000) {
              this.$Message.success(res.data.Message)
              this.getUnloadingList() 
            } else {
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
    },
    // 生成排期 回调
    handleScheduleSuccess({ startDate, endDate }) {
      this.startDate = startDate
      this.endDate = endDate
      let _param = {
        end_plan_date_st: startDate,
        end_plan_date_ed: endDate
      }
      this.isSchedule = true
      this.subTaskTime = 500
      this.mainTaskTime = 350
      API.batchAssignVoyageMonthPlanShips(_param).then(res => {
        if(res.data.Code === 10000) {
          // 如果已经排期完，loading应该快速跳到最后
          this.subTaskTime = 250
          this.mainTaskTime = 100
          this.getList() 
        } else {
          this.isSchedule = false
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 排期回调取消
    handleScheduleCancel() {

    },
    // 生成排期
    generateSchedule() {
      this.scheduleModalVisible = true
    },
    handleCargoSubmit() {
      this.getGoodsList()
      if(this.isEditingCargo) this.getUnloadingList()
    },
    handleCargoCancel() {
      this.$Message.info('已取消操作');
    },
    generateCalendarDays() {
      // const { start, end } = this.dateRange;
      // 计算实际的开始日期（往前推daysToExtendBefore天）
      const actualStartDate = new Date(this.startDate);
      actualStartDate.setDate(actualStartDate.getDate() - this.daysToExtendBefore);
      
      const endDate = new Date(this.endDate);
      
      // 清空现有日历数组
      this.calendarDays = [];
      
      // 设置当前月份为原始开始日期的月份（不是前推后的日期）
      this.currentMonth = new Date(this.startDate).getMonth();
      
      // 生成日期范围内的每一天
      const currentDate = new Date(actualStartDate);
      // 算出两个日期间隔天数
      this.betweenDays = this.getDaysBetween(currentDate, endDate)
      while (currentDate <= endDate) {
        const day = currentDate.getDate();
        const month = currentDate.getMonth();
        const year = currentDate.getFullYear();
        const fullDate = this.formatDate(currentDate);
        
        // 判断是否为月份的第一天
        const isFirstOfMonth = day === 1;
        
        // 获取月份简称
        const monthNames = ['一月', '二月', '三月', '四月', '五月', '六月', 
                           '七月', '八月', '九月', '十月', '十一月', '十二月'];
        const monthShort = monthNames[month];
        
        // 添加到日历数组
        this.calendarDays.push({
          day,
          month,
          year,
          fullDate,
          isFirstOfMonth,
          monthShort
        });
        
        // 移动到下一天
        currentDate.setDate(currentDate.getDate() + 1);
        this.setColumnWidth()
        this.alignCalendarColumns();
        this.alignRowHeights();
      }
    },

    // 获取两个日期相差天数
    getDaysBetween(startDate, endDate) {
      const diffTime = endDate.getTime() - startDate.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 *24));
      return diffDays
    },
    
    // 格式化日期为 YYYY-MM-DD
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    
    // 获取月份简写
    getMonthShort(month) {
      const monthNames = ['一月', '二月', '三月', '四月', '五月', '六月', 
                          '七月', '八月', '九月', '十月', '十一月', '十二月'];
      // 处理月份溢出
      if (month <= 0) month = 12;
      if (month > 12) month = 1;
      return monthNames[month - 1];
    },
    
    // 日历滑动控制
    scrollCalendar(direction) {
      const container = this.$refs.calendarContainer;
      const scrollAmount = 200; // 每次滚动的像素数
      
      if (direction === 'left') {
        container.scrollLeft -= scrollAmount;
      } else {
        container.scrollLeft += scrollAmount;
      }
      
      // 滚动后重新对齐行高
      this.alignRowHeights();
    },
    
    // 获取日历中的唯一月份
    getUniqueMonths() {
      const months = [];
      let currentMonth = null;
      
      for (const day of this.calendarDays) {
        const monthKey = `${day.year}-${day.month}`;
        if (monthKey !== currentMonth) {
          currentMonth = monthKey;
          months.push({
            year: day.year,
            month: day.month,
            startIndex: this.calendarDays.findIndex(d => d.fullDate === day.fullDate)
          });
        }
      }
      
      // 计算每个月的结束索引
      for (let i = 0; i < months.length; i++) {
        if (i < months.length - 1) {
          months[i].endIndex = months[i + 1].startIndex - 1;
        } else {
          months[i].endIndex = this.calendarDays.length - 1;
        }
      }
      
      return months;
    },
    
    // 设置日历每格宽度，如果每格小于默认的40则忽略
    setColumnWidth() {
      const el = document.querySelector('.main-content-con');
      const contentWidth = el.offsetWidth;
      const boxWidth = parseFloat(contentWidth) - 33
      let columnsWidth = boxWidth / this.betweenDays
      // if(columnsWidth > this.dayColumnWidth) {
      //   this.dayColumnWidth = Math.floor(columnsWidth)
      // }
    },

    // 确保日历头部和内容区域的列宽一致
    alignCalendarColumns() {
      const headerCells = this.$refs.calendarHeader.querySelectorAll('.calendar-day-header');
      const rows = this.$el.querySelectorAll('.calendar-row');
      
      if (rows.length === 0 || headerCells.length === 0) return;
      
      // 获取第一行的日期列
      const firstRow = rows[0];
      const contentCells = firstRow.querySelectorAll('.calendar-day-column');
      
      // 确保头部和内容区域的列数一致
      if (headerCells.length !== contentCells.length) return;
      
      // 同步每列的宽度
      for (let i = 0; i < headerCells.length; i++) {
        const width = `${this.dayColumnWidth}px`;
        headerCells[i].style.minWidth = width;
        headerCells[i].style.width = width;
        
        // 更新所有行的对应列
        rows.forEach(row => {
          const cells = row.querySelectorAll('.calendar-day-column');
          if (cells[i]) {
            cells[i].style.minWidth = width;
            cells[i].style.width = width;
          }
        });
      }
      
      // 重新计算排期项目的位置和宽度
      this.updateSchedulePositions();
    },
    
    // 更新所有排期项目的位置和宽度
    updateSchedulePositions() {
      this.$nextTick(() => {
        const scheduleItems = this.$el.querySelectorAll('.schedule-item');
        scheduleItems.forEach(item => {
          const shipId = item.getAttribute('data-ship-id');
          const start = item.getAttribute('data-start');
          const type = item.getAttribute('data-type');
          
          if (shipId && start && type) {
            const ship = this.shipSchedules.find(s => s.id === parseInt(shipId));
            if (ship) {
              const schedule = ship.schedules.find(s => s.start === start && s.type === type);
              if (schedule) {
                const style = this.calculateScheduleStyle(schedule);
                Object.keys(style).forEach(key => {
                  item.style[key] = style[key];
                });
              }
            }
          }
        });
      });
    },
    // 修改计算装载开始样式方法，使用固定的列宽
    calculateLoadStartStyle(schedule) {
      if(!schedule.empty_sail_start_day || schedule.empty_sail_start_day === schedule.start_plan_date) return 
      // 找到开始日期在日历中的索引
      const startIndex = this.calendarDays.findIndex(day => day.fullDate === schedule.empty_sail_start_day);
      const endIndex = this.calendarDays.findIndex(day => day.fullDate === schedule.start_plan_date);
      // 计算宽度（基于持续天数）
      const width = (startIndex - endIndex) * this.dayColumnWidth;
      const leftPosition = width; // 加上边框和内边距
      if(width <= 0) return
      return {
        width: `${width}px`,
        marginLeft: `-${leftPosition}px`,
        borderLeft: '2px dashed rgba(46, 142, 244, 0.8)',
        borderTop: '2px dashed rgba(46, 142, 244, 0.8)',
        borderBottom: '2px dashed rgba(46, 142, 244, 0.8)'
      }
    },
    // 修改计算装载样式方法，使用固定的列宽
    calculateLoadStyle(schedule) {
      if(!schedule.start_plan_date) return
      // 找到开始日期在日历中的索引
      let firstIndex, leftPosition,borderLeft;
      const startIndex = this.calendarDays.findIndex(day => day.fullDate === schedule.start_plan_date);
      const emptyIndex = this.calendarDays.findIndex(day => day.fullDate === schedule.empty_sail_start_day);
      if(startIndex - emptyIndex > 0) { // 判断用start_plan_date还是用empty_sail_start_day
        firstIndex = startIndex
        if(emptyIndex > 0) {
          leftPosition = (startIndex - emptyIndex) * this.dayColumnWidth
          borderLeft = '1px dashed rgba(46, 142, 244, 0.5)'
        }
      } else {
        firstIndex = emptyIndex
        leftPosition = '-2'
        borderLeft = 'none'
      }
      // const startIndex = this.calendarDays.findIndex(day => day.fullDate === schedule.start_plan_date);

      // 找到结束日期在日历中的索引
      const endIndex = this.calendarDays.findIndex(day => day.fullDate === schedule.end_plan_date);
      // 计算宽度（基于持续天数）
      const width = (endIndex - firstIndex) * this.dayColumnWidth;
      return {
        width: `${width}px`,
        borderLeft: `${borderLeft}`,
        borderRight:'1px dashed rgba(46, 142, 244, 0.5)',
        // marginLeft: '-2px',
        marginLeft: `${leftPosition}px`,
        marginRight: '3px',
        textAlign: 'center'
      }
    },
    
    // 修改计算排期样式方法，使用固定的列宽
    calculateScheduleStyle(schedule) {
      // 无起始时间及坞修就不返回
      if(!schedule.empty_sail_start_date && !schedule.start_plan_date) return
      // 找到开始日期在日历中的索引
      // const startIndex = this.calendarDays.findIndex(day => day.fullDate === schedule.empty_sail_start_day || day.fullDate === schedule.start_plan_date)
      let startIndex
      if(schedule.empty_sail_start_day) {
        startIndex = this.calendarDays.findIndex(day => day.fullDate === schedule.empty_sail_start_day);
      } else {
        startIndex = this.calendarDays.findIndex(day => day.fullDate === schedule.start_plan_date);
      }
      // 如果开始日期在可见范围之前，则从第一个可见日期开始
      const visibleStartIndex = startIndex === -1 ? 0 : startIndex;
      
      // 找到结束日期在日历中的索引
      // const endIndex = this.calendarDays.findIndex(day => day.fullDate === schedule.end_plan_date);
      const endIndex = this.calendarDays.findIndex(day => day.fullDate === schedule.estimated_over_day);
      // 如果结束日期在可见范围之后，则到最后一个可见日期结束
      const visibleEndIndex = endIndex === -1 ? this.calendarDays.length - 1 : endIndex;
      
      // 计算持续的天数
      const duration = visibleEndIndex - visibleStartIndex;
      // 计算左侧位置（基于开始日期的索引）
      const leftPosition = visibleStartIndex * this.dayColumnWidth + this.dayColumnWidth/2;
      
      // 计算宽度（基于持续天数）
      const width = duration * this.dayColumnWidth; // 减去边框和内边距
      
      // 根据文本长度决定最小高度
      const textLength = schedule.goods_name.length;
      const minHeight = textLength > 30 ? 60 : 40;
      return {
        position: 'absolute',
        left: `${leftPosition}px`,
        width: `${width}px`,
        minHeight: `${minHeight}px`,
        zIndex: 2,
        borderRadius: '4px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '4px',
        boxSizing: 'border-box',
        top: '50%',
        transform: 'translateY(-50%)', // 垂直居中
        'data-ship-id': schedule.ship_id,
        'data-start': schedule.start_plan_date,
        'data-type': schedule.status
      };
    },
    
    // 计算月份背景样式，使用固定的列宽
    calculateMonthBackgroundStyle(month) {
      const startPosition = month.startIndex * this.dayColumnWidth;
      const width = (month.endIndex - month.startIndex + 1) * this.dayColumnWidth;
      
      // 根据月份是否为当前月份选择不同的背景色
      const isCurrentMonth = month.month === this.currentMonth;
      const backgroundColor = isCurrentMonth ? 'rgba(255, 255, 255, 0)' : 'rgba(248, 248, 249, 0.6)';
      
      return {
        position: 'absolute',
        left: `${startPosition}px`,
        width: `${width}px`,
        height: '100%',
        backgroundColor,
        zIndex: 0
      };
    },
    
    // 窗口大小变化处理
    handleResize() {
      this.setColumnWidth()
      this.alignCalendarColumns();
      this.alignRowHeights();
    },
    
    // 对齐行高
    alignRowHeights() {
      this.$nextTick(() => {
        const fixedShipColumns = this.$el.querySelectorAll('.fixed-ship-column .calendar-ship-column');
        const calendarRows = this.$el.querySelectorAll('.calendar-body .calendar-row');
        
        if (fixedShipColumns.length !== calendarRows.length) return;
        
        // 先重置所有高度
        fixedShipColumns.forEach(col => {
          col.style.height = 'auto';
        });
        
        // 然后设置匹配的高度
        for (let i = 0; i < calendarRows.length; i++) {
          const rowHeight = calendarRows[i].offsetHeight;
          fixedShipColumns[i].style.height = `${rowHeight}px`;
        }
        
        // 确保头部高度也匹配
        const shipHeader = this.$el.querySelector('.calendar-ship-header');
        const dayHeader = this.$el.querySelector('.calendar-header-wrapper');
        if (shipHeader && dayHeader) {
          shipHeader.style.height = `${dayHeader.offsetHeight}px`;
        }
      });
    },
    
    // 开始拖动
    startDrag(e) {
      // 阻止默认行为，防止文本选择
      e.preventDefault();
      // 阻止事件冒泡
      e.stopPropagation();
      this.isDragging = true;
      this.startX = e.pageX - this.$refs.calendarContainer.offsetLeft;
      this.scrollLeft = this.$refs.calendarContainer.scrollLeft;
      
      // 改变鼠标样式
      document.body.style.cursor = 'grabbing';
      this.$refs.calendarContainer.style.cursor = 'grabbing';
    },

    // 拖动中
    onDrag(e) {
      // 直接阻止默认行为
      e.preventDefault();
      // 阻止事件冒泡
      e.stopPropagation();
      if (!this.isDragging) return;
      const x = e.pageX - this.$refs.calendarContainer.offsetLeft;
      const walk = (x - this.startX) * 1.5; // 滚动速度系数
      this.$refs.calendarContainer.scrollLeft = this.scrollLeft - walk;
    },

    // 停止拖动
    stopDrag(e) {
      if (!this.isDragging) return;
      // 直接阻止默认行为
      e.preventDefault();
      // 阻止事件冒泡
      e.stopPropagation();
      this.isDragging = false;
      // // 恢复鼠标样式
      document.body.style.cursor = 'default';
      this.$refs.calendarContainer.style.cursor = 'grab';
      // 拖动结束后重新对齐行高
      // this.alignRowHeights();
    },
    // 页面跳转
    handleSizeChange (val) {
      this.queryParam.pageSize = val
      this.getGoodsList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.queryParam.pageIndex = val
      this.getGoodsList()
    }
  }
};
</script>

<style>
.schedule-container {
  background-color: #f8f8f9;
  overflow-x: hidden;
  overflow-y: visible;
}

.header-buttons {
  display: flex;
  gap: 10px;
}

.filter-section {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #fff;
  border-radius: 4px;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  font-weight: bold;
  font-size: 16px;
}

.section-title .ivu-icon {
  margin-left: 5px;
  color: #2d8cf0;
}
.add_btns .ivu-icon {
  color: #fff;
}

.cargo-table-section, .ship-info-section, .schedule-calendar-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #fff;
  border-radius: 4px;
}

.ship-card {
  width: 280px;
  flex-shrink: 0;
}

.ship-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 10px;
  margin-bottom: 10px;
  border-bottom: 1px solid #e8eaec;
  font-weight: bold;
}

.ship-card-header.available {
  color: #19be6b;
}

.ship-card-header.in-transit {
  color: #2d8cf0;
}

.ship-card-content p {
  margin-bottom: 5px;
  display: flex;
}

.ship-card-content p span {
  width: 80px;
  color: #808695;
}

.calendar-container {
  overflow-x: hidden;
  overflow-y: visible;
  margin-bottom: 20px;
}

.calendar-controls {
  display: flex;
  align-items: center;
}

.calendar-outer-container {
  display: flex;
  flex: 1;
  position: relative;
  margin: 0 10px;
  overflow-x: hidden;
  overflow-y: visible;
}

.calendar-scroll-container {
  flex: 1;
  overflow-x: auto;
  overflow-y: visible;
  scrollbar-width: thin;
  scrollbar-color: #c5c8ce transparent;
  cursor: grab;
  user-select: none;
  position: relative;
  max-height: 100%;
}

.calendar-header {
  display: flex;
  width: fit-content;
}

.calendar-day-header {
  width: 40px;
  min-width: 40px;
  padding: 10px 5px;
  text-align: center;
  border-right: 1px solid #dcdee2;
  background-color: #f8f8f9;
  box-sizing: border-box;
}

.calendar-day-header.different-month {
  background-color: #f0f0f2;
}

.calendar-body {
  width: fit-content;
}

.calendar-row {
  display: flex;
  position: relative;
  width: fit-content;
  min-height: 70px; /* 设置最小高度 */
}

.calendar-days-container {
  display: flex;
  position: relative;
  width: fit-content;
  min-height: 100%;
}

/* 横向线样式 */
.calendar-row-border-top {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background-color: #dcdee2;
  z-index: 2;
  pointer-events: none; /* 确保不会影响鼠标事件 */
}

.calendar-row-border-bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background-color: #dcdee2;
  z-index: 2;
  pointer-events: none; /* 确保不会影响鼠标事件 */
}

/* 确保第一行的顶部边框更粗 */
.calendar-body .calendar-row:first-child .calendar-row-border-top {
  height: 2px;
}

/* 确保最后一行的底部边框更粗 */
.calendar-body .calendar-row:last-child .calendar-row-border-bottom {
  height: 2px;
}

.calendar-day-column {
  width: 40px;
  min-width: 40px;
  height: 100%;
  border-right: 1px solid #dcdee2;
  position: relative;
  z-index: 1;
  box-sizing: border-box;
}

/* 确保最后一列的右边框可见 */
.calendar-days-container::after,
.calendar-header::after {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 1px;
  background-color: #dcdee2;
  z-index: 1;
}

/* 确保头部底部边框更粗 */
.calendar-header-wrapper {
  display: flex;
  background-color: #f8f8f9;
  border-bottom: 2px solid #dcdee2;
  position: sticky;
  top: 0;
  z-index: 5;
  width: fit-content;
}

/* 船舶列标题底部边框与头部一致 */
.calendar-ship-header {
  width: 100px;
  min-width: 100px;
  padding: 10px;
  text-align: center;
  font-weight: bold;
  background-color: #f8f8f9;
  box-sizing: border-box;
  border-bottom: 5px solid #dcdee2;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 船舶列内容边框 */
.calendar-ship-column {
  width: 100px;
  height: 80px;
  min-width: 100px;
  padding: 10px 10px 12px 10px;
  text-align: center;
  font-weight: bold;
  background-color: #f8f8f9;
  display: flex;
  flex-direction: column;
  justify-content: center;
  box-sizing: border-box;
  transition: height 0.2s ease;
  border-bottom: 3px solid #dcdee2;
}

/* 固定的船舶列 */
.fixed-ship-column {
  width: 100px;
  min-width: 100px;
  z-index: 6;
  background-color: #f8f8f9;
  border-right: 2px solid #dcdee2; /* 加粗右侧边框，增强分隔效果 */
  display: flex;
  flex-direction: column;
}

/* 确保固定列的最后一个元素有底部边框 */
.fixed-ship-column .calendar-ship-column:last-child {
  border-bottom: 3px solid #dcdee2;
}

/* 月份背景样式 */
.month-background {
  position: absolute;
  top: 0;
  bottom: 0;
  z-index: 0;
}

.ship-capacity {
  font-size: 12px;
  color: #808695;
  font-weight: normal;
}

.schedule-item {
  padding: 4px;
  font-size: 12px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  overflow: visible;
}

.schedule-content {
  white-space: pre-line;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: left;
  justify-content: left;
  text-align: left;
}

.schedule_status {
  position: absolute;
  right: 5px;
}

.schedule-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
  z-index: 10;
  overflow: visible;
}

.schedule-item:hover .schedule-content {
  overflow: visible;
  white-space: pre-line;
  word-break: break-word;
  background-color: inherit;
  position: relative;
  z-index: 11;
}

.schedule-loading {
  background-color: #e1f3d8;
  color: #19be6b;
  border-left: 2px solid #19be6b;
}

.schedule-unloading {
  background-color: #d7ecf4;
  color: #2d8cf0;
  border-left: 2px solid #2d8cf0;
}

.schedule-transit {
  background-color: #f8e3c5;
  color: #ff9900;
  border-left: 2px solid #ff9900;
}

.schedule-standby {
  background-color: #f3f3f3;
  color: #808695;
  border-left: 2px solid #c5c8ce;
}

@media screen and (max-width: 768px) {
  .calendar-day-column {
    min-width: 30px;
  }
  
  .schedule-item {
    font-size: 10px;
  }
}

.calendar-actions {
  display: flex;
  gap: 10px;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 5px;
}
.header-btns button {
  margin-left: 10px;
}
.ship-schedule-container {
  padding: 16px;
  background-color: #f5f7f9;
  min-height: 100vh;
}

.main-card {
  margin-bottom: 20px;
}

.section-title h3 {
  margin-left: 10px;
  margin-right: 10px;
}

.mb-20 {
  margin-bottom: 20px;
}

.ship-cards {
  height: 160px;
  overflow-y: auto;
  margin-bottom: 20px;
}

.ship-info-card {
  margin-bottom: 16px;
  border-left: 3px solid #ccc;
}

.status-free {
  border-left-color: #19be6b;
}

.status-sailing {
  border-left-color: #2d8cf0;
}

.status-loading {
  border-left-color: #ff9900;
}

.ship-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.ship-card-header h4 {
  margin: 0;
}

.ship-card-content p {
  margin: 8px 0;
}

.ship-card-footer {
  margin-top: 10px;
  text-align: right;
}

.schedule-calendar {
  border: 1px solid #dcdee2;
  border-radius: 4px;
  overflow: hidden;
}

.calendar-header {
  display: flex;
  background-color: #f8f8f9;
  border-bottom: 1px solid #dcdee2;
}

.ship-column {
  width: 120px;
  padding: 10px;
  font-weight: bold;
  border-right: 1px solid #dcdee2;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.ship-capacity {
  font-size: 12px;
  color: #808695;
  font-weight: normal;
}

.day-columns {
  display: flex;
  flex: 1;
}

.day-column {
  flex: 1;
  text-align: center;
  padding: 10px 0;
  border-right: 1px solid #e8eaec;
  min-width: 30px;
}

.calendar-body {
  display: flex;
  flex-direction: column;
}

.calendar-row {
  display: flex;
  border-bottom: 1px solid #dcdee2;
  height: 80px;
}

.calendar-row:last-child {
  border-bottom: none;
}

.day-columns {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(31, 1fr);
  position: relative;
}

.schedule-event {
  background-color: #e8eaec;
  padding: 4px;
  font-size: 12px;
  border-radius: 3px;
  margin: 4px;
  overflow: hidden;
  height: calc(100% - 8px);
}

.event-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.waiting {
  background-color: #f8f8f9;
  color: #808695;
  border-left: 2px solid #808695;
}

.loading {
  background-color: #e3f9e9;
  color: #19be6b;
  border-left: 2px solid #19be6b;
}

.unloading {
  background-color: #e3f9e9;
  color: #19be6b;
  border-left: 2px solid #19be6b;
}

.sailing {
  background-color: #e8f4ff;
  color: #2d8cf0;
  border-left: 2px solid #2d8cf0;
}
.demo-spin-icon-load{
  animation: ani-demo-spin 1s linear infinite;
}
@keyframes ani-demo-spin {
  from { transform: rotate(0deg);}
  50%  { transform: rotate(180deg);}
  to   { transform: rotate(360deg);}
}
.loading_area {
  background: rgba(0,0,0,0.8);
}
</style>