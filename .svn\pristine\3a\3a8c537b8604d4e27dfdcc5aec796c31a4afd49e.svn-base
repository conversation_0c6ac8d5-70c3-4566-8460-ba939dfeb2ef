<template>
  <div ref="dom" class="charts chart-bar"></div>
</template>

<script>
import echarts from 'echarts'
import tdTheme from './theme.json'
import { on, off } from '@/libs/tools'
echarts.registerTheme('tdTheme', tdTheme)
export default {
  name: 'ChartBar',
  props: {
    value: Object,
    text: String,
    subtext: String,
    unit: String,
    clickable: {
      type: Boolean,
      default: false
    },
    rotate: {
      type: String,
      default: '0'
    },
    color: {
      type: Array,
      default: () => {
        return ['#5B8FF9', '#5AD8A6', '#5D7092', '#F6BD16', '#E8684A', '#6DC8EC', '#9270CA', '#FF9D4D', '#269A99', '#FF99C3']
      }
    },
    legendTop: String,
    legendPosition: String
  },
  data () {
    return {
      dom: null
    }
  },
  methods: {
    chartInit () {
      let _that = this
      this.$nextTick(() => {
        let xAxisData = this.value.xAxis
        let seriesData = this.value.data
        let option = {
          title: {
            text: this.text,
            subtext: this.subtext,
            x: 'left'
          },
          color: this.color,
          legend: {
            data: this.value.legend ? this.value.legend : [],
            // itemWidth: 15, //设置legend图标的宽度
            // itemHeight: 15, //设置legend图标的高度
            top: this.legendTop,
            right: this.legendPosition // 设置legend的位置
          },
          grid: {
            left: 0,
            right: 30,
            bottom: 0,
            containLabel: true
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: { // 坐标轴指示器，坐标轴触发有效
              type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
            },
            formatter: item => { // '{b}<br/>{c}' + this.unit
              let str = ''
              if (item.length > 1) {
                str = item[0].name + '<br/>'
                let unitStr = (this.unit && this.unit.includes(',')) ? this.unit.split(',') : this.unit // 额外处理多个柱状图多个单位，数组转换，用”，“隔开来区别
                item.forEach((list, idx) => {
                  if (this.unit && this.unit.includes(',')) { // 有”，“隔开的表示是多个单位
                    str += list.marker + list.seriesName + ':<br/>&nbsp;&nbsp;&nbsp;&nbsp;' + list.value + unitStr[idx] +'<br/>'
                  } else { // 表示是同样的单位只要一个
                    if (this.unit) {
                      str += list.marker + list.seriesName + ':<br/>&nbsp;&nbsp;&nbsp;&nbsp;' + list.value + this.unit +'<br/>'
                    } else {
                      str += list.marker + list.seriesName + ':<br/>&nbsp;&nbsp;&nbsp;&nbsp;' + list.value +'<br/>'
                    }
                  }
                })
              } else {
                // str = item[0].marker + item[0].name + ':' + item[0].value
                str = item[0].name + '<br/>' + item[0].marker + item[0].value + this.unit
              }
              return str
            }
          },
          barMaxWidth: 20,
          xAxis: {
            type: 'category',
            data: xAxisData,
            axisLabel: {
              interval: 0,
              rotate: this.rotate
            },
            splitLine: {
              show: false // 取消网格线
            }
          },
          yAxis: [],
          series: []
          // yAxis: {
          //   type: 'value',
          //   name: this.unit
          // },
          // series: [{
          //   data: seriesData,
          //   type: 'bar'
          // }]
        }
        if (seriesData.length > 0 && seriesData[0].constructor === Array) {
          if (this.value.yAxis) {
            seriesData.forEach((item, idx) => {
              option.yAxis.push({
                type: 'value',
                name: this.value.yAxis[idx].name
                // splitLine: { // Y轴网格线
                //   show: false
                // }
              })
              option.series.push({
                data: item,
                type: 'bar',
                yAxisIndex: idx,
                name: this.value.legend && this.value.legend.length > 1 ? this.value.legend[idx] : ''
              })
            })
          } else {
            if (!this.subtext || this.subtext === '') { // 如果有副标题，则不引入unit作为展示，否则两个位置会覆盖，如果错开有点占空间
              option.yAxis.push({
                type: 'value',
                name: this.unit
              })
            } else {
              option.yAxis.push({
                type: 'value',
                name: ''
              })
            }
            seriesData.forEach((item, idx) => {
              option.series.push({
                data: item,
                type: 'bar',
                name: this.value.legend && this.value.legend.length > 1 ? this.value.legend[idx] : ''
              })
            })
          }
        } else {
          option.yAxis.push({
            type: 'value',
            name: this.unit
          })
          option.series = [{
            data: seriesData,
            type: 'bar'
          }]
        }
        this.dom = echarts.init(this.$refs.dom, 'tdTheme')
        this.dom.setOption(option)
        this.dom.off('click')
        this.dom.getZr().on('click', item => {
          let pointInPixel = [item.offsetX, item.offsetY]
          if (this.dom.containPixel('grid', pointInPixel)) {
            let xIndex = this.dom.convertFromPixel({ seriesIndex: 0 }, [item.offsetX, item.offsetY])[0]
            if (this.clickable) {
              _that.$emit('clickBack', xIndex)
            }
          }
        })
        on(window, 'resize', this.resize)
      })
    },
    resize () {
      this.dom.resize()
    }
  },
  mounted () {
    this.chartInit()
  },
  beforeDestroy () {
    off(window, 'resize', this.resize)
  },
  watch: {
    value: {
      handler (n, o) {
        this.chartInit()
      },
      deep: true,
      immediate: true
    }
  }
}
</script>
