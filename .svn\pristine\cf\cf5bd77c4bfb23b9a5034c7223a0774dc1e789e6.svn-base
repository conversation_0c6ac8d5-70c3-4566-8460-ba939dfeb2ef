<template>
  <div>
    <div v-if="list.length !== 0" style="min-height: 180px;">
      <div v-for="(item, idx) in list" :key="idx" class="berth-con">
        <div>
          <span class="berth-con-voyage">{{ item.ship_name }} {{ item.voyage_no }}</span>
          <Button v-if="item.send_num === '0'" class="berth-con-send" type="text" @click="sendVoyageHandle(item.id)">发送</Button>
          <Button v-if="item.send_num !== '0'" class="berth-con-send" type="text" @click="sendAgainVoyage(item.id)">再次发送</Button>
        </div>
        <div class="berth-con-text">预计{{item.plan_date}}靠泊{{ item.port_name }}{{ item.wharf_name }}{{ item.berth_name }}码头</div>
      </div>
    </div>
    <div v-if="list.length === 0" style="min-height: 180px;">
      <Row class="no-list" justify="center" align="middle">
        <img src="../../../../assets/images/no-data.png" />
        <div>暂无列表数据</div>
      </Row>
    </div>
    <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="pageSize" :current.sync="pageCur"
          :total="total" prev-text="< 上一页" next-text="下一页 >"
          @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
  </div>
</template>
<script>
import './list.less'
import API from '@/api/control'
import berthAPI from '@/api/berthingPlan/berthingPlan'
export default {
  data () {
    return {
      pageCur: 1, // 当前页
      pageSize: 3, // 每页个数
      total: 0, // 总页数
      queryParam: {
        pageSize: 3,
        pageIndex: 1
      },
      list: []
    }
  },
  created () {
    this.getList()
  },
  methods: {
    getList () {
      API.queryBusinessBerthPlanConsolePage(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          this.list = res.data.Result
          this.total = res.data.Total
        }
      })
    },
    // 发送
    sendVoyageHandle (id) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>是否发送靠泊计划？</p>',
        loading: true,
        onOk: () => {
          berthAPI.sendBerthPlan({ id: id }).then(response => {
            if (response.data.Code === 10000) {
              this.$Message.success(response.data.Message)
              this.$Modal.remove()
              this.loading = false
              this.getList()
            } else {
              this.$Message.error(response.data.Message)
              this.$Modal.remove()
              this.loading = false
            }
          })
        }
      })
    },
    // 再次发送
    sendAgainVoyage (id) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>是否再次发送靠泊计划？</p>',
        loading: true,
        onOk: () => {
          berthAPI.sendAgainBerthPlan({ id: id }).then(response => {
            if (response.data.Code === 10000) {
              this.$Message.success(response.data.Message)
              this.$Modal.remove()
              this.loading = false
              this.getList()
            } else {
              this.$Message.error(response.data.Message)
              this.$Modal.remove()
              this.loading = false
            }
          })
        }
      })
    },
    // 页面跳转
    handleSizeChange (val) {
      this.queryParam.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.queryParam.pageIndex = val
      this.getList()
    }
  }
}
</script>
