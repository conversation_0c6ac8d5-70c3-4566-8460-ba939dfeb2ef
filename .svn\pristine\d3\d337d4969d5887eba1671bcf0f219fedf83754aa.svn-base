import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 获取月度报表列表
export function monthReportList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/month/report/queryVoyageMonthReportPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 预览
export function previewHistory (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/month/report/previewDynamicTemplateHistory',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}
