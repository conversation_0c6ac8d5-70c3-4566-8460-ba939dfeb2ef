<template>
  <div>
    <Row :gutter="20" v-if="showHomePage">
      <!-- 图表数据展示 -->
      <Col span="24">
        <!-- 货运量 -->
        <Card shadow class="cargoVolumeDiv tool-num" style="position: relative;">
          <div @click="showModelBtn('total')" class="morebtn">more &gt;</div>
          <Row>
            <Col span="6">
              <h4>总货运量</h4>
              <p class="numDate">{{ statisticGoodsList.now_date ? statisticGoodsList.now_date.split('/')[0] : '' }}/01/01 - {{ statisticGoodsList.now_date }}</p>
              <count-to class="num-data" :end="statisticGoodsList.amount_sum" unitText="万吨" unitClass="num-unit" :decimals="decimals3" usegroup/>
            </Col>
            <Col span="6">
              <h4>化学品货运量</h4>
              <p class="numDate">{{ statisticGoodsList.now_date ? statisticGoodsList.now_date.split('/')[0] : '' }}/01/01 - {{ statisticGoodsList.now_date }}</p>
              <count-to class="num-data" :end="statisticGoodsList.chemical_amount_sum" unitText="万吨" unitClass="num-unit" :decimals="decimals3" usegroup/>
            </Col>
            <Col span="6">
              <h4>油品货运量</h4>
              <p class="numDate">{{ statisticGoodsList.now_date ? statisticGoodsList.now_date.split('/')[0] : '' }}/01/01 - {{ statisticGoodsList.now_date }}</p>
              <count-to class="num-data" :end="statisticGoodsList.oils_amount_sum" unitText="万吨" unitClass="num-unit" :decimals="decimals3" usegroup/>
            </Col>
            <Col span="6">
              <h4>总航次</h4>
              <p class="numDate">{{ statisticGoodsList.now_date ? statisticGoodsList.now_date.split('/')[0] : '' }}/01/01 - {{ statisticGoodsList.now_date }}</p>
              <count-to class="num-data" :end="statisticGoodsList.voyage_total" unitText="次" unitClass="num-unit" usegroup/>
            </Col>
          </Row>
          <Spin size="large" fix v-if="totalSpinShow">
            <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
            <div>Loading</div>
          </Spin>
        </Card>
        <!-- 货品信息 -->
        <div class="goods-area" v-if="statisticGoodsList.priceData">
          <div class="goods-area-span" v-for="(item, index) in statisticGoodsList.priceData" :key="index" >
            <span class="goods-name">
              {{ item.cargo_name }}
              <Icon style="margin-left: 12px;" :type="(item.cargo_price - item.pre_cargo_price) > 0 ? 'md-trending-up' : 'md-trending-down'" :color="(item.cargo_price - item.pre_cargo_price) > 0 ? '#F42323' : '#2ACB97'"/>
            </span>
            <br/>
            <span :class="(item.cargo_price - item.pre_cargo_price) > 0 ? 'goods-up' : 'goods-down'">
              <span class="goods-price">{{ item.cargo_price }}  {{ ((item.cargo_price - (item.pre_cargo_price || item.cargo_price)) / item.pre_cargo_price).toFixed(2) }}%</span>
            </span>
          </div>
        </div>
        <!-- 船舶营运总览 -->
        <Card class="cargoVolumeDiv" shadow style="position: relative;">
          <div @click="showModelBtn('operation')" class="morebtn">more &gt;</div>
          <Spin size="large" fix v-if="shipSpinShow">
            <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
            <div>Loading</div>
          </Spin>
          <Row :gutter="20">
            <Col span="16">
              <ChartBar style="height: 300px;" :value="barData" text="船舶营运总览" unit="万吨"></ChartBar>
            </Col>
            <Col span="8">
              <chart-pie style="height: 300px;" :value="pieData" text="运输货品占比"></chart-pie>
            </Col>
          </Row>
          <Row class="chartNumClass">
            <Col span="3">
              <div class="num-title">货运量</div>
              <count-to class="num-chart" :end="chartGoodsNum" unitClass="num-unit" :decimals="decimals2"/>
              <p>万吨</p>
            </Col>
            <Col span="3">
              <div class="num-title">周转量</div>
              <count-to class="num-chart" :end="chartTurnoverNum" unitClass="num-unit" :decimals="decimals2"/>
              <p>万吨公里</p>
            </Col>
            <Col span="3">
              <div class="num-title">航次数</div>
              <count-to class="num-chart" :end="chartVolumeNum" unitClass="num-unit"/>
              <p>次</p>
            </Col>
            <Col span="3">
              <div class="num-title">航程</div>
              <count-to class="num-chart" :end="chartVoyageNum" unitClass="num-unit" :decimals="decimals2"/>
              <p>万海里</p>
            </Col>
            <Col span="3">
              <div class="num-title">待泊时长</div>
              <count-to class="num-chart" :end="chartWaitingTime" unitClass="num-unit"/>
              <p>小时</p>
            </Col>
            <Col span="3">
              <div class="num-title">平均待泊</div>
              <count-to class="num-chart" :end="chartAverageWaiting" unitClass="num-unit" :decimals="decimals2"/>
              <p>小时</p>
            </Col>
            <Col span="3">
              <div class="num-title">总损耗</div>
              <count-to class="num-chart" :end="chartTotalLoss" unitClass="num-unit" :decimals="decimals4"/>
              <p>万吨</p>
            </Col>
            <Col span="3">
              <div class="num-title">平均损耗率</div>
              <count-to class="num-chart" :end="chartAverageLossRate" unitClass="num-unit" :decimals="decimals2"/>
              <p>‰</p>
            </Col>
          </Row>
        </Card>
        <div class="cargoVolumeDiv">
          <Row :gutter="20">
            <Col span="12">
              <Card shadow style="position: relative;">
                <div @click="showModelBtn('voyage')" class="morebtn">more &gt;</div>
                <Spin size="large" fix v-if="voyageSpinShow">
                  <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
                  <div>Loading</div>
                </Spin>
                <chart-line style="height: 300px;" unit="航程(海里)" :value="voyageNumData" text="航程统计"/>
              </Card>
            </Col>
            <Col span="12">
              <Card shadow style="position: relative;">
                <div @click="showModelBtn('goods')" class="morebtn">more &gt;</div>
                <Spin size="large" fix v-if="numSpinShow">
                  <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
                  <div>Loading</div>
                </Spin>
                <ChartBarAndLine style="height: 300px;" :value="voyageGoodsNumData" text="货运量/周转量"/></ChartBarAndLine>
              </Card>
            </Col>
          </Row>
        </div>
        <Card class="cargoVolumeDiv" shadow>
          <Row :gutter="20">
            <Col span="12">
              <Card shadow style="position: relative;">
                <!-- <div @click="showModelBtn('toanchor')" class="morebtn">more &gt;</div> -->
                <Spin size="large" fix v-if="anchorSpinShow">
                  <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
                  <div>Loading</div>
                </Spin>
                <chart-bar style="height: 300px;" unit="小时" :value="anchorTimeData" text="抛锚时长"/>
              </Card>
            </Col>
            <Col span="12">
              <Card shadow style="position: relative;">
                <div @click="showModelBtn('loadrate')" class="morebtn">more &gt;</div>
                <Spin size="large" fix v-if="loadSpinShow">
                  <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
                  <div>Loading</div>
                </Spin>
                <chart-line style="height: 300px;" unit="吨(小时)" :value="voyageRateData" text="装卸速率"/>
              </Card>
            </Col>
          </Row>
        </Card>
      </Col>
    </Row>
    <totalChart v-if="showModelTotal" @callback="goBackHome('total')"></totalChart><!-- 总货运量 -->
    <operationChart v-if="showModelOperation" @callback="goBackHome('operation')"></operationChart><!-- 船舶营运总览 -->
    <voyageChat v-if="showModelVoyage" @callback="goBackHome('voyage')"></voyageChat><!-- 航程统计 -->
    <goodsChart v-if="showModelGoods" @callback="goBackHome('goods')"></goodsChart><!-- 货运量/周转量 -->
    <loadrateChart v-if="showModelLoadrate" @callback="goBackHome('loadrate')"></loadrateChart><!-- 抛锚时长 装卸速率 -->
    <!-- <Spin size="large" fix v-if="spinShow">加载中...</Spin> -->
  </div>
</template>

<script>
import { ChartPie, ChartBar, ChartLine, ChartBarAndLine } from '_c/charts'
import CountTo from '_c/count-to'
import API from '@/api/statistics'
import goodsChart from './moreChart/goodsChart'
import loadrateChart from './moreChart/loadrateChart'
import operationChart from './moreChart/operationChart'
import totalChart from './moreChart/totalChart'
import voyageChat from './moreChart/voyageChat'
export default {
  components: {
    ChartPie,
    ChartBar,
    ChartLine,
    CountTo,
    ChartBarAndLine,
    goodsChart,
    loadrateChart,
    operationChart,
    totalChart,
    voyageChat
  },
  data () {
    return {
      // spinShow: false,
      totalSpinShow: false,
      shipSpinShow: false,
      voyageSpinShow: false,
      numSpinShow: false,
      anchorSpinShow: false,
      loadSpinShow: false,
      showHomePage: true, // 首页展示状态
      showModelTotal: false,
      showModelOperation: false,
      showModelVoyage: false,
      showModelGoods: false,
      showModelToanchor: false,
      showModelLoadrate: false,
      statisticGoodsList: {
        amount_sum: 0, // 总货运量
        chemical_amount_sum: 0, // 化学品货运量
        oils_amount_sum: 0, // 油品货运量
        voyage_total: 0 // 总航次
      },
      dynamicListData: [], // 动态数据
      dynamicDetailResult: '-', // 航线信息
      dynamicGoodsResult: '-', // 货品信息
      goodsNameData: [{
        goods_name: '纯苯'
      }], // 货品信息
      pieData: [],
      barData: {
        xAxis: [],
        data: []
      },
      decimals2: 2, // 保留两位小数
      decimals3: 3, // 保留三位小数
      decimals4: 4, // 保留四位小数
      chartGoodsNum: 0, // 货运量
      chartTurnoverNum: 0, // 周转量
      chartVolumeNum: 0, // 航次数
      chartVoyageNum: 0, // 航程
      chartWaitingTime: 0, // 待泊时长
      chartAverageWaiting: 0, // 平均待泊
      chartTotalLoss: 0, // 总损耗
      chartAverageLossRate: 0, // 平均损耗率
      voyageNumData: { // 航程/航次
        xAxis: [],
        legend: ['航程', '航次'],
        data: [[], []],
        symbol: ['circle', 'triangle'],
        smooth: 0.2,
        yAxis: [
          { name: '航程(海里)' },
          { name: '航次(次)' }
        ]
      },
      voyageGoodsNumData: { // 货运量/周转量
        xAxis: [],
        yAxis: [
          { name: '货运量(万吨)' },
          { name: '周转量(万吨/公里)' }
        ],
        legend: ['货运量', '周转量'],
        seriesData: [
          {
            name: '货运量',
            type: 'bar',
            smooth: 0.2,
            symbol: 'circle',
            data: []
          },
          {
            name: '周转量',
            type: 'line',
            smooth: 0.2,
            symbol: 'circle',
            data: []
          }
        ]
      },
      anchorTimeData: { // 抛锚时长
        xAxis: [],
        data: []
      },
      voyageRateData: { // 装卸速率
        xAxis: [],
        legend: ['装货速率', '卸货速率'],
        smooth: 0,
        data: [[], []],
        symbol: ['circle', 'triangle']
      }
    }
  },
  created () {
    // this.spinShow = true
    this.getChartsData()
  },
  methods: {
    getChartsData () {
      // 获取货运问题及价格数据
      this.totalSpinShow = true
      this.shipSpinShow = true
      this.voyageSpinShow = true
      this.numSpinShow = true
      this.anchorSpinShow = true
      this.loadSpinShow = true
      API.queryStatisticSumReport().then(res => {
        if (res.data.Code === 10000) {
          this.totalSpinShow = false
          this.statisticGoodsList = Object.assign(res.data, {
            amount_sum: res.data.amount_sum === '' ? 0 : parseFloat(res.data.amount_sum), // 总货运量
            chemical_amount_sum: res.data.chemical_amount_sum === '' ? 0 : parseFloat(res.data.chemical_amount_sum), // 化学品货运量
            oils_amount_sum: res.data.oils_amount_sum === '' ? 0 : parseFloat(res.data.oils_amount_sum), // 油品货运量
            voyage_total: res.data.voyage_total === '' ? 0 : parseFloat(res.data.voyage_total) // 总航次
          })
        }
      })
      // 船舶运营总览
      API.queryStatisticAmountSummary().then(res => {
        if (res.data.Code === 10000) {
          this.shipSpinShow = false
          this.chartGoodsNum = parseFloat(res.data.amount_sum) // 货运量
          this.chartTurnoverNum = parseFloat(res.data.turnover_sum) // 周转量
          this.chartVolumeNum = res.data.voyage_sum // 航次数
          this.chartVoyageNum = parseFloat(res.data.mile_sum) // 航程
          this.chartWaitingTime = parseFloat(res.data.wait_berth_sum) // 待泊时长
          this.chartAverageWaiting = parseFloat(res.data.wait_berth_average) // 平均待泊时长
          this.chartTotalLoss = parseFloat(res.data.goods_loss_sum) // 总损耗
          this.chartAverageLossRate = parseFloat(res.data.goods_loss_average) // 平均损耗
          res.data.shipAmountSummary.forEach(item => {
            this.barData.xAxis.push(item.ship_name)
            this.barData.data.push(item.amount_sum)
          })
          res.data.goodsAmountArray.forEach(item => {
            this.pieData.push({
              value: item.goods_amount_sum,
              name: item.goods_name
            })
          })
        }
      })
      // 航程、航次
      API.queryStatisticVoyageSummary().then(res => {
        if (res.data.Code === 10000) {
          this.voyageSpinShow = false
          let mileMonthArr = []
          res.data.monthMileSummary.forEach((list, idx) => {
            mileMonthArr.push(list.voyage_over_month)
          })
          res.data.monthVoyageSummary.forEach((item, index) => {
            this.voyageNumData.xAxis.push(item.voyage_over_month)
            if (mileMonthArr.includes(item.voyage_over_month)) {
              let _curIndex = mileMonthArr.findIndex((val) => { return val === item.voyage_over_month })
              let curMileSum = parseFloat(res.data.monthMileSummary[_curIndex].month_mile_sum).toFixed(2)
              this.voyageNumData.data[0].push(curMileSum)
            } else {
              this.voyageNumData.data[0].push(0)
            }
            // this.voyageNumData.data[0].push(res.data.monthMileSummary[index] ? res.data.monthMileSummary[index].month_mile_sum : 0)
            this.voyageNumData.data[1].push(item.month_voyage_sum)
          })
        }
      })
      // 货运量、周转量
      API.queryStatisticTurnoverSummary().then(res => {
        if (res.data.Code === 10000) {
          this.numSpinShow = false
          if (!res.data.Result || res.data.Result.length <= 0) return
          res.data.Result.forEach(item => {
            this.voyageGoodsNumData.xAxis.push(item.voyage_over_month)
            this.voyageGoodsNumData.seriesData[0].data.push(item.month_amount_sum)
            this.voyageGoodsNumData.seriesData[1].data.push(item.month_turnover_sum)
          })
        }
      })
      // 抛锚时长
      API.queryStatisticWaitBerthMonthSummary().then(res => {
        if (res.data.Code === 10000) {
          this.anchorSpinShow = false
          if (!res.data.Result || res.data.Result.length <= 0) return
          res.data.Result.forEach(item => {
            this.anchorTimeData.xAxis.push(item.voyage_over_month)
            this.anchorTimeData.data.push(item.month_wait_berth_sum)
          })
        }
      })
      // 装卸速率
      API.queryStatisticRateMonthSummary().then(res => {
        if (res.data.Code === 10000) {
          this.loadSpinShow = false
          res.data.monthRate.forEach(item => {
            this.voyageRateData.xAxis.push(item.voyage_over_month)
            this.voyageRateData.data[0].push(item.month_load_rate)
            this.voyageRateData.data[1].push(item.month_unload_rate)
          })
        }
      })
    },
    // 点击事件
    showModelBtn (d) {
      if (d === 'total') {
        this.showModelTotal = true
      } else if (d === 'operation') {
        this.showModelOperation = true
      } else if (d === 'voyage') {
        this.showModelVoyage = true
      } else if (d === 'goods') {
        this.showModelGoods = true
      } else if (d === 'toanchor') {
        this.showModelToanchor = true
      } else if (d === 'loadrate') {
        this.showModelLoadrate = true
      }
      this.showHomePage = false
    },
    // 返回首页
    goBackHome (d) {
      if (d === 'total') {
        this.showModelTotal = false
      } else if (d === 'operation') {
        this.showModelOperation = false
      } else if (d === 'voyage') {
        this.showModelVoyage = false
      } else if (d === 'goods') {
        this.showModelGoods = false
      } else if (d === 'toanchor') {
        this.showModelToanchor = false
      } else if (d === 'loadrate') {
        this.showModelLoadrate = false
      }
      this.showHomePage = true
    }
  }
}
</script>
<style lang="less">
.goods-area {
  margin: 10px 0 10px;
  overflow-x: auto;
  overflow-y: hidden;
  white-space: nowrap;
  flex-wrap: nowrap;
  .goods-area-span {
    display: inline-block;
    padding: 7px 9px;
    background: #fff;
    border-radius: 6px;
    margin-right: 20px;
    font-size: 14px;
    .goods-name {
      color: #2B304C;
      font-weight: bold;
    }
    .goods-up {
      color: #F42323;
    }
    .goods-down {
      color: #2ACB97;
    }
    .goods-trend {
      margin-left: 9px;
    }
  }
}
.cargoVolumeDiv {
  margin-bottom: 10px;
  position: relative;
  .morebtn {
    right: 5px;
    top: 5px;
    position: absolute;
    font-size: 12px;
    color: #666;
    cursor: pointer;
    font-family: Arial, Helvetica, sans-serif;
  }
  .count-to-wrapper .content-outer .count-to-unit-text {
    font-size: 11px;
    color: #2B304C;
  }
  .leftborder {
    position: relative;
    &::before {
      content: '';
      width: 1px;
      height: 20px;
      left: -15px;
      top: 0;
      position: absolute;
      display: inline-block;
      background-color: #E9E9E9;
    }
  }
}
.tool-num {
  .numDate {
    font-size: 12px;
    color: #7C8093;
  }
  .num-data {
    color: #2B304C;
    margin-top: 13px;
    font-size: 20px;
    font-weight: bold;
  }
}
.chartNumClass {
  .ivu-col-span-3 {
    position: relative;
    &::after {
      content: '';
      width: 1px;
      height: 20px;
      display: inline-block;
      position: absolute;
      right: 20%;
      top: 23%;
      background-color: rgba(233,233,233,1);
    }
  }
  .ivu-col-span-3:last-child::after {
    display: none;
  }
}
.num-unit {
  font-size: 10px;
  font-weight: 100;
  margin-left: 6px;
}
.dynamicdatas {
  overflow: hidden;
  color: #383D4A;
  margin-bottom: 10px;
  .ivu-card-body {
    padding: 10px 16px;
    display: flex;
    align-items: center;
  }
}
.leftborder {
  vertical-align: middle;
}
.playVoyage {
  padding: 5px 15px;
  overflow: hidden;
  background-color: #D3E3F8;
}
.demo-spin-col {
  height: 100px;
  position: relative;
  border: 1px solid #eee;
}
.demo-spin-icon-load{
  animation: ani-demo-spin 1s linear infinite;
}
@keyframes ani-demo-spin {
  from { transform: rotate(0deg);}
  50%  { transform: rotate(180deg);}
  to   { transform: rotate(360deg);}
}
</style>
