<template>
  <Drawer
    :title="drawerTitle"
    v-model="visible"
    width="520"
    :mask-closable="false"
    :styles="drawerStyles"
  >
    <Form :model="formData" :rules="formRules" :label-width="100" ref="shipForm">
      <FormItem label="船舶名称" prop="name">
        <Input v-model="formData.name" placeholder="请输入船舶名称" />
      </FormItem>
      
      <FormItem label="船舶类型" prop="type">
        <Select v-model="formData.type" placeholder="请选择船舶类型">
          <Option value="chemical">化学品船</Option>
          <Option value="cargo">货运船</Option>
          <Option value="oil">油轮</Option>
        </Select>
      </FormItem>
      
      <FormItem label="载重能力" prop="capacityValue">
        <InputNumber v-model="formData.capacityValue" :min="1" :max="100000" style="width: 150px"></InputNumber>
        <span style="margin-left: 8px">吨</span>
      </FormItem>
      
      <FormItem label="当前位置" prop="location">
        <Input v-model="formData.location" placeholder="请输入当前位置" />
      </FormItem>
      
      <FormItem label="可用时间" prop="availableTime">
        <DatePicker type="date" v-model="formData.availableTime" placeholder="选择可用日期" style="width: 200px"></DatePicker>
      </FormItem>
      
      <FormItem label="适配货物" prop="cargoTypes">
        <Select v-model="formData.cargoTypes" multiple placeholder="请选择适配货物类型">
          <Option value="甲醇">甲醇</Option>
          <Option value="乙醇">乙醇</Option>
          <Option value="丙烯">丙烯</Option>
          <Option value="丁烯">丁烯</Option>
          <Option value="原油">原油</Option>
          <Option value="柴油">柴油</Option>
          <Option value="汽油">汽油</Option>
        </Select>
      </FormItem>
      
      <FormItem label="船舶状态" prop="status">
        <RadioGroup v-model="formData.status">
          <Radio label="空闲">空闲</Radio>
          <Radio label="航行中">航行中</Radio>
          <Radio label="装卸中">装卸中</Radio>
          <Radio label="维修中">维修中</Radio>
        </RadioGroup>
      </FormItem>
      
      <FormItem label="船舶图片">
        <Upload
          action="//jsonplaceholder.typicode.com/posts/"
          :max-size="2048"
          :format="['jpg','jpeg','png']"
          type="drag"
          :on-success="handleUploadSuccess"
          :on-format-error="handleFormatError"
          :on-exceeded-size="handleMaxSize"
        >
          <div style="padding: 20px 0">
            <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
            <p>点击或拖拽图片到此处上传</p>
          </div>
        </Upload>
      </FormItem>
      
      <FormItem label="备注说明">
        <Input v-model="formData.remark" type="textarea" :rows="4" placeholder="请输入备注说明" />
      </FormItem>
      
      <div class="drawer-footer">
        <Button @click="handleCancel" style="margin-right: 8px">取消</Button>
        <Button type="primary" @click="handleSubmit" :loading="submitting">提交</Button>
      </div>
    </Form>
  </Drawer>
</template>

<script>
export default {
  name: 'ShipDrawer',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    shipData: {
      type: Object,
      default: () => ({})
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      visible: false,
      submitting: false,
      formData: {
        name: '',
        type: '',
        capacityValue: 5000,
        location: '',
        availableTime: '',
        cargoTypes: [],
        status: '空闲',
        imageUrl: '',
        remark: ''
      },
      formRules: {
        name: [
          { required: true, message: '请输入船舶名称', trigger: 'blur' },
          { type: 'string', max: 20, message: '船舶名称不能超过20个字符', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择船舶类型', trigger: 'change' }
        ],
        capacityValue: [
          { 
            required: true, 
            message: '请输入载重能力', 
            trigger: 'blur',
            type: 'number',
            validator: (rule, value, callback) => {
              if (value === 0 || value) {
                callback();
              } else {
                callback(new Error('请输入载重能力'));
              }
            }
          }
        ],
        location: [
          { required: true, message: '请输入当前位置', trigger: 'blur' }
        ],
        availableTime: [
          { required: true, message: '请选择可用时间', trigger: 'change', type: 'date' }
        ],
        cargoTypes: [
          { required: true, type: 'array', min: 1, message: '请至少选择一种适配货物', trigger: 'change' }
        ],
        status: [
          { required: true, message: '请选择船舶状态', trigger: 'change' }
        ]
      },
      drawerStyles: {
        height: 'calc(100% - 55px)',
        overflow: 'auto',
        paddingBottom: '53px',
        position: 'static'
      }
    };
  },
  computed: {
    drawerTitle() {
      return this.isEdit ? '编辑船舶信息' : '添加新船舶';
    }
  },
  watch: {
    value(val) {
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
      if (!val) {
        this.$refs.shipForm.resetFields();
      }
    },
    shipData: {
      handler(val) {
        if (val && Object.keys(val).length > 0) {
          this.formData = {
            name: val.name || '',
            type: val.type || '',
            capacityValue: parseInt(val.capacity.replace(/,/g, '')) || 5000,
            location: val.location || '',
            availableTime: val.availableTime ? new Date(val.availableTime.split('起')[0]) : '',
            cargoTypes: val.cargoTypes ? val.cargoTypes.split(', ') : [],
            status: val.status || '空闲',
            imageUrl: val.imageUrl || '',
            remark: val.remark || ''
          };
        } else {
          this.resetForm();
        }
      },
      immediate: true
    }
  },
  methods: {
    handleCancel() {
      this.visible = false;
      this.$emit('cancel');
    },
    handleSubmit() {
      this.$refs.shipForm.validate((valid) => {
        if (valid) {
          this.submitting = true;
          
          // 构建提交的数据
          const submitData = {
            id: this.isEdit ? this.shipData.id : Date.now(),
            name: this.formData.name,
            type: this.formData.type,
            capacity: this.formData.capacityValue.toLocaleString(),
            location: this.formData.location,
            availableTime: this.formData.availableTime ? this.formatDate(this.formData.availableTime) + '起' : '',
            cargoTypes: this.formData.cargoTypes.join(', '),
            status: this.formData.status,
            imageUrl: this.formData.imageUrl,
            remark: this.formData.remark,
            // 根据状态设置颜色和样式类
            statusColor: this.getStatusColor(this.formData.status),
            statusClass: this.getStatusClass(this.formData.status)
          };
          
          // 模拟API请求
          setTimeout(() => {
            this.submitting = false;
            this.$emit('submit', submitData);
            this.visible = false;
            this.$Message.success(`${this.isEdit ? '更新' : '添加'}船舶成功！`);
          }, 1000);
        }
      });
    },
    resetForm() {
      this.formData = {
        name: '',
        type: '',
        capacityValue: 5000,
        location: '',
        availableTime: '',
        cargoTypes: [],
        status: '空闲',
        imageUrl: '',
        remark: ''
      };
      if (this.$refs.shipForm) {
        this.$refs.shipForm.resetFields();
      }
    },
    formatDate(date) {
      const d = new Date(date);
      return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
    },
    getStatusColor(status) {
      const statusMap = {
        '空闲': 'success',
        '航行中': 'primary',
        '装卸中': 'warning',
        '维修中': 'error'
      };
      return statusMap[status] || 'default';
    },
    getStatusClass(status) {
      const statusMap = {
        '空闲': 'status-free',
        '航行中': 'status-sailing',
        '装卸中': 'status-loading',
        '维修中': 'status-maintenance'
      };
      return statusMap[status] || '';
    },
    handleUploadSuccess(res, file) {
      // 实际项目中应该使用服务器返回的URL
      this.formData.imageUrl = URL.createObjectURL(file.raw);
      this.$Message.success('上传成功');
    },
    handleFormatError() {
      this.$Message.error('文件格式不正确，请上传 jpg、jpeg 或 png 格式的图片');
    },
    handleMaxSize() {
      this.$Message.error('文件大小超出限制（最大2MB）');
    }
  }
};
</script>

<style scoped>
.drawer-footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  left: 0;
  background: #fff;
}
</style> 