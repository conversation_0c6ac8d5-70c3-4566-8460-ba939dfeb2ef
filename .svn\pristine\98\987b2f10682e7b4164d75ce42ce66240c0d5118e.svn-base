<template>
  <div>
    <p slot='title' class="bold-font">核对记录</p>
    <Card>
      <search @searchResults='searchResults' @selectOnChanged='selectOnChanged' :setSearch='setSearchData' @keydown.enter.native='searchResults' @resetResults='resetResults'></search>
      <div class="extra" slot="extra">
        <Button type="primary" @click="exportData" style="float: right;" :disabled="!list || list.length === 0">导出</Button>
      </div>
    </Card>
    <Card>
      <Table border :loading="loading" :columns="columns" :data="list" :span-method="handleSpan"></Table>
    </Card>
  </div>
</template>

<script>
import search from '_c/search'
import { queryVoyageCheckHisList, exportTemplateInfo } from '@/api/voyageManage/voyageCheckHis'

export default {
  components: {
    search
  },
  data () {
    return {
      setSearchData: {
        check_month: {
          type: 'month',
          label: '月份',
          selected: '',
          width: 130,
          value: '',
          isdisable: false
        }
      },
      loading: false,
      list: [],
      columns: [
        {
          title: '日期',
          key: 'check_month',
          align: 'center'
        },
        {
          title: '船舶',
          key: 'ship_name',
          align: 'center'
        },
        {
          title: '航次数',
          key: 'voyage_num',
          align: 'center'
        },
        {
          title: '货运量',
          key: 'load_amount_sum',
          align: 'center'
        },
        {
          title: '周转量',
          key: 'turnover_volume_sum',
          align: 'center'
        },
        {
          title: '合同平均损耗率',
          key: 'goods_loss_average',
          align: 'center'
        },
        {
          title: '船板平均损耗率',
          key: 'goods_ship_loss_average',
          align: 'center'
        },
        {
          title: '核对人',
          key: 'check_person',
          align: 'center'
        }
      ]
    }
  },
  created () {
    if (this.setSearchData.check_month.selected === '') {
      this.getCurDate()
    }
    this.getList()
  },
  methods: {
    // 获取列表
    getList () {
      this.loading = true
      queryVoyageCheckHisList({ check_month: this.setSearchData.check_month.selected }).then(response => {
        if (response.data.Code === 10000) {
          this.list = response.data.Result
        } else {
          this.$Message.error(response.data.Message)
        }
        setTimeout(() => {
          this.loading = false
        }, 1 * 800)
      }).catch(
        setTimeout(() => {
          this.loading = false
        }, 1 * 800)
      )
    },
    // 合并单元格
    handleSpan ({ row, column, rowIndex, columnIndex }) {
      if (rowIndex === 0 && columnIndex === 0) {
        return {
          rowspan: this.list.length,
          colspan: 1
        }
      } else if (rowIndex !== 0 && columnIndex === 0) {
        return [0, 0]
      }
    },
    // 查询
    searchResults (e) {
      if (this.setSearchData.check_month.selected === '' || this.check_month === undefined) {
        this.getCurDate()
      } else {
        this.setSearchData.check_month.selected = this.check_month
      }
      if (e.target) delete e.target
      this.getList()
    },
    // 时间格式
    selectOnChanged (e) {
      this.check_month = e.key
    },
    // 获取当前时间
    getCurDate () {
      let d = new Date()
      let month = new Date().getMonth() + 1
      month = month < 10 ? '0' + month : month
      this.setSearchData.check_month.selected = d.getFullYear() + '-' + month
      this.check_month = d.getFullYear() + '-' + month
    },
    // 重置
    resetResults () {
      this.setSearchData.check_month.selected = ''
      this.getCurDate()
      this.getList()
    },
    // 导出
    exportData () {
      exportTemplateInfo({ check_month: this.check_month }).then(res => {
        if (res.data.Code === 10000) {
          window.open(res.data.fileUrl, '_blank')
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    }
  }
}
</script>
<style lang="less">
</style>
