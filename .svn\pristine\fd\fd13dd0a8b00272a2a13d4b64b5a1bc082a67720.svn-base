<template>
  <Modal
    v-model="modalData.modal" class="berth_area" :styles="{position: 'absolute', top: '110px', left: '215px'}" :mask="false"
    :transfer="false" width="400px" footer-hide>
    <p slot="header">
      <!-- <img src="@/assets/images/berth_back.png" alt=""/> -->
      <div class="berth_title">{{ modalData.name }}泊位</div>
    </p>
    <div>
      <div v-for="(item,idx) in modalData.detail" :key="idx" style="margin-bottom: 8px;">
        <span class="berth_inner_title">{{ item.title }}:</span>
        <span class="berth_inner_detail" v-html="item.detail"></span>
      </div>
    </div>
  </Modal>
</template>
<script>
export default {
  props: {
    modalData: Object
  },
  data () {
    return {

    }
  }
}
</script>
<style>
  .berth_area .ivu-modal-header {
    height: 50px;
    padding: 0 !important;
    border-bottom: none;
  }
  .berth_area .ivu-modal-header p {
    background: #007dff;
    height: 50px;
  }
  .berth_area .ivu-modal-header p img {
    width: 100%;
    height: 100%;
    pointer-events: none;
  }
  .berth_area .berth_title {
    position: absolute;
    /* background: #0770e799; */
    padding: 5px 15px 0 5px;
    top: 5px;
    left: 15px;
    color: #fff;
    font-size: 18px;
    /* font-weight: bold; */
  }
  .berth_inner_title {
    text-align: left;
    font-size: 12px;
    font-weight: bold;
  }
  .berth_inner_detail {
    white-space: pre-wrap;
    text-align: left;
    font-size: 12px;
    color: #333;
  }
</style>
