import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 获取作业效率折线图
export function queryEfficiencyChart (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/head/queryStatPortsEfficiency', 
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取作业效率表
export function queryEfficiencyReport (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/head/queryStatPortsEfficiencyReport', 
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取作业效率表单项详情
export function queryEfficiencyReportDetail (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/head/queryStatPortsEfficiencyReportDetailsPage', 
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

