<template>
  <div class="filter-container">
    <Form ref="formInline" inline>
      <template v-for="(item, key, index) in setSearch">
        <!-- <FormItem class="formItem" :key="index" :label="item.label" v-if="item.type==='button'">
          <Button :type="item.buttonType" @click="item.click ? item.click() : ''">{{ item.value }}</Button>
        </FormItem> -->
        <FormItem class="formItem" :key="index" :label="item.label" v-if="item.type==='text'">
          <Input :placeholder="item.placeholder" :style="{width: item.width+'px'}" v-model="item.value"
                 :readonly='item.readonly' :disabled='item.disabled' clearable @on-focus='item.focus?item.focus("search", item):""' @on-change='item.change?item.change("search", item):""' @keydown.enter.native.prevent="searchEnterFun"></Input>
        </FormItem>
        <FormItem class="formItem" :key="index" :label="item.label" v-if="item.type==='select'">
          <Select v-model="item.selected" clearable :style="{width: item.width+'px'}" :placeholder="item.placeholder" :filterable="item.filterable === true" :disabled="item.isdisabled"
                  @on-change="item.change ? item.change(item) : selectOnChanged(item.flag)" @keydown.enter.native.prevent="searchEnterFun">
            <Option v-for="(item, n) in item.selectData" :value="item.value" :key="n">{{item.label}}</Option>
          </Select>
        </FormItem>
        <FormItem class="formItem" :key="index" :label="item.label" v-if="item.type==='date'">
          <Date-picker v-model="item.selected" :style="{width: item.width+'px'}" type="date" :disabled="item.isdisabled"
                       :start-date="item.start_date"
                       @on-change="selectOnChangedStart" placeholder="选择日期" @keydown.enter.native.prevent="searchEnterFun"></Date-picker>
        </FormItem>
        <FormItem class="formItem" :key="index" :label="item.label" v-if="item.type==='month'">
          <Date-picker v-model="item.selected" :style="{width: item.width+'px'}" type="month" :disabled="item.isdisabled"
                       :start-date="item.start_date"
                       @on-change="selectOnChangedMonth" placeholder="选择日期" @keydown.enter.native.prevent="searchEnterFun"></Date-picker>
        </FormItem>
        <FormItem class="formItem" :key="index" :label="item.label" v-if="item.type==='year'">
          <Date-picker v-model="item.selected" :style="{width: item.width+'px'}" type="year" :disabled="item.isdisabled"
                       :start-date="item.start_date"
                       @on-change="selectOnChangedYear" placeholder="选择日期" @keydown.enter.native.prevent="searchEnterFun"></Date-picker>
        </FormItem>
        <FormItem class="formItem" :key="index" :label="item.label" v-if="item.type==='date_end'">
          <Date-picker v-model="item.selected" :style="{width: item.width+'px'}" :type="item.type_date" :disabled="item.isdisabled"
                       :start-date="item.start_date"
                       @on-change="selectOnChangedEnd" placeholder="选择日期" @keydown.enter.native.prevent="searchEnterFun"></Date-picker>
        </FormItem>
        <FormItem class="formItem" :key="index" :label="item.label" v-if="item.type==='daterange'">
          <Date-picker v-model="item.selected" :style="{width: item.width+'px'}" type="daterange" :disabled="item.isdisabled"
                       :start-date="item.start_date"
                       @on-change="selectOnChangedRange" placeholder="选择日期" @keydown.enter.native.prevent="searchEnterFun"></Date-picker>
        </FormItem>
        <FormItem class="formItem" :key="index" :label="item.label" v-if="item.type==='autoComplete'">
          <AutoComplete v-model="item.autoComplete" style="width:200px" @keydown.enter.native.prevent="searchEnterFun" class="showmodalbtn" @on-click="showTemplate"
            @on-change="item.change ? item.change(item) : selectOnChanged(item.flag)">
            <Option v-for="(item1, index1) in item.autoCompleteData" :value="item1" :key="index1">
              <span>{{ item1 }}</span>
              <span style="float: right;" @click="showTemplate(index1)">预览</span>
            </Option>
          </AutoComplete>
        </FormItem>
      </template>
      <FormItem class="formItem searchBtn">
        <Button type="primary" @click="searchResults">查询</Button>
        <Button type="primary" @click="resetResults" style="margin-left: 10px;">重置</Button>
      </FormItem>
    </Form>
  </div>
</template>

<script>
export default {
  props: {
    setSearch: {
      type: Object,
      default: function () {
        return { type_date: 'date', isdisabled: false }
      }
    }
  },
  methods: {
    searchEnterFun: function (e) {
      var keyCode = window.event ? e.keyCode : e.which
      if (keyCode === 13) {
        this.searchResults()
      }
    },
    // 查询
    searchResults () {
      let searchResultsData = {}
      Object.keys(this.setSearch).forEach(key => {
        if (this.setSearch[key].type === 'select') {
          searchResultsData[key] = this.setSearch[key].selected
        } else if (this.setSearch[key].type === 'autoComplete') {
          searchResultsData[key] = this.setSearch[key].autoComplete
        } else {
          searchResultsData[key] = this.setSearch[key].value
        }
      })
      searchResultsData.target = this.setSearch
      this.$emit('searchResults', searchResultsData)
    },
    // 重置
    resetResults () {
      this.$emit('resetResults')
    },
    selectOnChanged (e) {
      let onChangedResultData = {}
      Object.keys(this.setSearch).forEach(key => {
        if (this.setSearch[key].type === 'select') {
          onChangedResultData[key] = this.setSearch[key].selected
        } else if (this.setSearch[key].type === 'autoComplete') {
          onChangedResultData[key] = this.setSearch[key].autoComplete
        }
      })
      onChangedResultData.key = e
      onChangedResultData.flag = ''
      onChangedResultData.target = this.setSearch
      this.$emit('selectOnChanged', onChangedResultData)
    },
    selectOnChangedStart (e) {
      let onChangedResultData = {}
      onChangedResultData.key = e
      onChangedResultData.flag = 'date_start'
      this.$emit('selectOnChanged', onChangedResultData)
    },
    selectOnChangedMonth (e) {
      let onChangedResultData = {}
      onChangedResultData.key = e
      onChangedResultData.flag = 'month_start'
      this.$emit('selectOnChanged', onChangedResultData)
    },
    selectOnChangedYear (e) {
      let onChangedResultData = {}
      onChangedResultData.key = e
      onChangedResultData.flag = 'year_start'
      this.$emit('selectOnChanged', onChangedResultData)
    },
    selectOnChangedEnd (e) {
      let onChangedResultData = {}
      onChangedResultData.key = e
      onChangedResultData.flag = 'date_end'
      this.$emit('selectOnChanged', onChangedResultData)
    },
    selectOnChangedRange (e) {
      let onChangedResultData = {}
      onChangedResultData.key = e
      onChangedResultData.flag = 'daterange'
      this.$emit('selectOnChanged', onChangedResultData)
    },
    // 预览
    showTemplate (index) {
      this.$emit('showTemplate', index)
    }
  }
}
</script>

<style lang='less'>
  .filter-container {
    .ivu-form-item-content {
      float: left;
    }
  }
</style>
<style lang="less" scoped>
.formItem {
  margin: 5px 15px 5px 0;
}
.searchBtn {
  margin-left: 5px;
}
.ivu-select-item {
  position: relative;
}
.showmodal {
  position: absolute;
  right: 0;
}
</style>
