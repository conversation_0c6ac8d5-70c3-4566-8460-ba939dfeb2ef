import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 获取泊位管理列表
export function queryBerthPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/berth/record/queryBasicBerthManagePage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 删除
export function queryDeleteBerth (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/berth/record/delBasicBerth',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 新增
export function addBerth (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/berth/record/addBasicBerth',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 修改
export function updateBerth (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/berth/record/updateBasicBerth',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}
