<template>
  <div>
    <!-- 查询 -->
    <Card>
      <search @searchResults='searchResults' :setSearch='setSearchData' @keydown.enter.native='searchResults' @resetResults='resetResults'></search>
    </Card>
    <Card class="leftListTable">
      <p slot='title' class="bold-font">客户管理</p>
      <div class="extra" slot="extra" >
        <formAction :setFormAction='setFormAction' @handleCreate="handleCreate('create')" @handleUpdateTable="resetList"></formAction>
      </div>
      <Row :gutter="50">
        <Col span="12"><!-- 公司列表 -->
          <Table border :loading="listLoading" ref="selection" :columns="columns" :data="list" class="alignTable" highlight-row @on-row-click="clickHandleCustomer"></Table>
          <Page :styles="{marginTop:'16px',textAlign: 'right'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
          :total="total" prev-text="< 上一页" next-text="下一页 >"
          @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
        </Col>
        <Col span="12"><!-- 成员列表 -->
          <Table border :loading="customerListLoading" ref="selection" :columns="customerColumns" :data="customerList" no-data-text="暂无数据，请点击左侧公司列表添加" class="alignTable"></Table>
          <Page :styles="{marginTop:'16px',textAlign: 'right'}" :page-size="this.customerListQuery.pageSize" :current.sync="customerCurrent"
          :total="customerTotal" prev-text="< 上一页" next-text="下一页 >"
          @on-change='customerHandleCurrentChange' @on-page-size-change='customerHandleSizeChange'/>
        </Col>
      </Row>
    </Card>
    <Drawer
      v-model="addCompanyModal"
      :title="title"
      :dialogType="dialogType"
      :mask-closable="false"
      @on-visible-change="drawChange"
      :width="800">
      <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="70" inline>
        <Row>
          <Col span="11">
            <FormItem label="公司名称" prop="customer_company_id" style="width: 100%">
              <Select v-model="formValidate.customer_company_id" clearable filterable @on-change="getCompanyType" v-show="dialogType!=='update'">
                <Option v-for="(item, index) in companyListData" :key="index" :value="item.value">{{ item.label }}</Option>
              </Select>
              <Input v-show="dialogType==='update'" v-model="formValidate.customer_company_name" readonly></Input>
            </FormItem>
          </Col>
          <Col span="11" offset="2">
            <FormItem label="公司类型" prop="company_type_name" style="width: 100%">
              <Input v-model="formValidate.company_type_name" disabled></Input>
            </FormItem>
          </Col>
        </Row>
        <FormItem label="备注" prop="company_remark" style="width: 100%">
          <Input type="textarea" :autosize="true" v-model="formValidate.company_remark" :readonly="dialogType==='update'"></Input>
        </FormItem>
      </Form>
      <!-- 节点 -->
      <div v-if="isOpen">
        <h3 class="divtitleclass">推送节点:</h3>
        <Table border height="600" :columns="nodeColumns" :data="nodeData" class="nodecolumnstable"></Table>
      </div>
      <div class="demo-drawer-footer">
        <Button @click="cancel" style="margin-right:10px;">取消</Button>
        <Button type="primary" v-if="dialogType==='create'" @click="addOkBtn">保存</Button>
        <Button type="primary" v-else @click="updateOkBtn">保存</Button>
      </div>
    </Drawer>
    <!-- 成员弹窗 -->
    <customerManagementEdit ref="customerManagementEdit" @addSuccess="changeCustomer"></customerManagementEdit>
  </div>
</template>

<script>
import search from '_c/search' // 查询组件
import formAction from '_c/formAction' // 表单操作组件
import API from '@/api/customerManagement/customerManagement'
import { queryNodeList } from '@/api/setting/SMSSetting' // 短信推送是否已开通
import { queryCustomerList, queryCustomerPage, queryCustomerMemberPage } from '@/api/basicData'
import customerManagementEdit from './customerManagementEdit'

export default {
  components: {
    search,
    formAction,
    customerManagementEdit
  },
  data () {
    return {
      drawShow: false,
      checkAll: false, // 全选当前打勾
      checkAllClick: false, // 所有已经全选的数据
      isOpen: false, // 短信推送是否已开通
      node_info_ids: [], // 储存已选择推送节点id
      setSearchData: { // 查询设置，对象key值为回调参数
        customer_company_name: {
          type: 'select',
          label: '公司',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 150,
          value: '',
          filterable: true
        }
      },
      setFormAction: {
        operation: ['create', 'updateTable']
      },
      addCompanyModal: false,
      companyListData: [], // 储存公司数据
      formValidate: {
        customer_company_name: '',
        customer_company_id: '',
        company_type_name: '',
        company_remark: ''
      },
      curIndex: 0, // 公司当前选中行
      ruleValidate: {},
      title: '', // 模态框标题
      dialogType: null, // 弹窗类型
      listLoading: true, // 表单列表loding状态
      companyIsOpen: false, // 存储公司列表数据
      columns: [
        {
          title: '公司',
          key: 'customer_company_name',
          align: 'center'
        },
        {
          title: '类型',
          key: 'company_type_name',
          align: 'center',
          width: 70
        },
        {
          title: '短信功能',
          align: 'center',
          width: 100,
          render: (h, params) => {
            return h('i-switch', {
              props: {
                value: params.row.is_open === '1'
              },
              on: {
                'on-change': () => {
                  params.row.is_open = params.row.is_open === '0' ? '1' : '0'
                  // e.stopPropagation()
                  this.changeCompanyIsOpen(params.row)
                }
              }
            })
          }
        },
        {
          title: '操作',
          key: 'operation',
          align: 'center',
          width: 270,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                style: {
                  margin: '4px'
                },
                props: {
                  icon: 'md-brush',
                  size: 'small'
                },
                on: {
                  click: (e) => {
                    e.stopPropagation()
                    this.handleCreate('update', params.row)
                  }
                }
              }, '编辑'),
              h('Button', {
                style: {
                  margin: '4px'
                },
                props: {
                  icon: 'md-add',
                  size: 'small'
                },
                on: {
                  click: (e) => {
                    e.stopPropagation()
                    this.handleCustomerBtn('create', params.row)
                  }
                }
              }, '添加成员'),
              h('Button', {
                style: {
                  margin: '4px'
                },
                props: {
                  icon: 'md-trash',
                  size: 'small'
                },
                on: {
                  click: (e) => {
                    e.stopPropagation()
                    this.handleDelete(params.row)
                  }
                }
              }, '删除')
            ])
          }
        }
      ],
      list: [], // 表单列表数据
      total: null, // 列表数据条数
      listQuery: {// 列表请求参数
        pageSize: 10,
        pageIndex: 1,
        customer_company_id: '' // 公司名称
      },
      listCurrent: 1, // 当前页码
      subMenuDisabled: [], // 按钮权限
      // 成员列表
      customerListLoading: false,
      customerList: [],
      customerColumns: [
        {
          title: '客户姓名',
          key: 'customer_name',
          align: 'center'
        },
        {
          title: '岗位',
          key: 'customer_post',
          align: 'center'
        },
        {
          title: '短信功能',
          align: 'center',
          width: 100,
          render: (h, params) => {
            return h('i-switch', {
              props: {
                disabled: !this.companyIsOpen,
                value: params.row.is_push === '1'
              },
              on: {
                'on-change': () => {
                  params.row.is_push = params.row.is_push === '0' ? '1' : '0'
                  this.changeUserIsOpen(params.row)
                }
              }
            })
          }
        },
        {
          title: '操作',
          key: 'operation',
          align: 'center',
          width: 180,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                style: {
                  margin: '4px'
                },
                props: {
                  icon: 'md-brush',
                  size: 'small'
                },
                on: {
                  click: (e) => {
                    e.stopPropagation()
                    this.handleCustomerBtn('update', params.row)
                  }
                }
              }, '修改'),
              h('Button', {
                style: {
                  margin: '4px'
                },
                props: {
                  icon: 'md-trash',
                  size: 'small'
                },
                on: {
                  click: (e) => {
                    e.stopPropagation()
                    this.handleCustomerDelete(params.row)
                  }
                }
              }, '删除')
            ])
          }
        }
      ],
      customerTotal: null,
      customerCurrent: 1,
      customerListQuery: { // 成员列表请求参数
        vy_customer_company_id: '',
        pageSize: 10,
        pageIndex: 1
      },
      // customerCompanyId: '',
      nodeColumns: [ // 节点列表
        {
          title: '节点名称',
          key: 'node_name',
          align: 'center'
        },
        {
          title: '是否开启推送',
          key: 'is_push',
          align: 'center',
          renderHeader: (h, params) => {
            return h('Checkbox', {
              props: {
                value: this.checkAll
              },
              on: {
                'on-change': () => {
                  this.checkAllClick = !this.checkAll
                  // 强制更新
                  this.checkAllSelect()
                }
              }
            }, '是否开启推送')
          },
          render: (h, params) => {
            return h('Checkbox', {
              props: {
                value: params.row.is_push === '1'
              },
              on: {
                'on-change': () => {
                  this.nodeData[params.index].is_push = this.nodeData[params.index].is_push === '1' ? '0' : '1'
                  this.isAllCheck() // 判断是否已经全选
                }
              }
            }, '开启')
          }
        }
      ],
      nodeData: [] // 节点列表
    }
  },
  created () {
    this.getCompanyList('1')
    this.getList()
  },
  methods: {
    // 显示隐藏列表短信功能
    getIsShipownerOpen (d) {
      if (d === '0') {
        this.columns.splice(2, 1)
        this.customerColumns.splice(3, 1)
      }
    },
    // 更新
    resetList () {
      this.listQuery.pageIndex = 1
      this.listCurrent = 1
      this.customerCurrent = 1
      this.getList()
    },
    // 获取公司列表
    getList () {
      this.listLoading = true
      this.curIndex = 0
      queryCustomerPage(this.listQuery).then(response => {
        if (response.data.Code === 10000) {
          this.list = response.data.Result
          this.total = response.data.Total
          this.customerListQuery.pageIndex = 1
          this.customerCurrent = 1
          if (this.list.length < 0) return
          this.list[this.curIndex]._highlight = true
          this.companyIsOpen = this.list[this.curIndex].is_open === '1'
          this.getIsShipownerOpen(this.list[this.curIndex].is_shipowner_open)
          this.clickHandleCustomer(this.list[this.curIndex])
        } else {
          this.$Message.error(response.data.Message)
        }
        setTimeout(() => {
          this.listLoading = false
        }, 1 * 800)
      }).catch(
        setTimeout(() => {
          this.listLoading = false
        }, 1 * 800)
      )
    },
    // 查询
    searchResults (e) {
      if (e.target) delete e.target
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      Object.assign(this.listQuery, e)
      this.getList()
    },
    // 获取公司数据
    getCompanyList (d) {
      if (d === '1') { // 1:查询，0：新增  find_all: 1
        queryCustomerList().then(response => {
          if (response.data.Code === 10000) {
            response.data.Result.map(item => {
              this.setSearchData.customer_company_name.selectData.push({
                value: item.customer_company_name,
                label: item.customer_company_name
              })
            })
          } else {
            this.setSearchData.name.selectData = []
          }
        })
      } else {
        API.queryAllCompanyList({ find_all: 0 }).then(response => {
          if (response.data.Code === 10000) {
            response.data.Result.map(item => {
              this.companyListData.push({
                value: item.id,
                label: item.name,
                companyType: item.company_type_name
              })
            })
          } else {
            this.companyListData = []
          }
        })
      }
    },
    // 重置
    resetResults () {
      this.listCurrent = 1
      this.customerCurrent = 1
      this.listQuery = Object.assign(this.listQuery, {
        pageIndex: 1,
        customer_company_name: ''
      })
      this.setSearchData.customer_company_name.selected = ''
      this.getList()
    },
    // 新增公司
    handleCreate (d, row) {
      this.dialogType = d
      if (d === 'create') {
        this.addCompanyModal = true
        this.title = '新增公司信息'
        this.getCompanyList('0')
        this.companyListData = []
      } else { // update
        this.addCompanyModal = true
        this.title = '修改公司信息'
        this.getCompanyList('0')
        this.getNodeList()
        this.formValidate = row
        this.formValidate.customer_company_name = row.customer_company_name
      }
    },
    // 根据选择的公司获取公司类型
    getCompanyType () {
      if (!this.drawShow) return
      this.companyListData.map(item => {
        if (this.formValidate.customer_company_id === item.value) {
          this.formValidate.company_type_name = item.companyType
        }
      })
      this.getNodeList()
    },
    // 判断短信推送是否已开通
    getNodeList () {
      queryNodeList({ pageSize: 100, pageIndex: 1 }).then(res => {
        if (res.data.Code === 10000 && res.data.Result.length > 0) {
          this.isOpen = true
          // 获取推送节点列表
          API.queryFullMsgNodeConfigList({ customer_company_id: this.formValidate.customer_company_id }).then(response => {
            if (response.data.Code === 10000) {
              this.nodeData = response.data.Result
            }
          })
        }
      })
    },
    // 新增公司
    addOkBtn () {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确定添加公司？</p>',
        loading: true,
        onOk: () => {
          this.nodeData.map(item => {
            if (item.is_push === '1') {
              this.node_info_ids.push(item.node_info_id)
            }
          })
          let data = {
            customer_company_id: this.formValidate.customer_company_id,
            company_remark: this.formValidate.company_remark
          }
          let nodeDatas = {
            node_info_ids: this.node_info_ids.join(','),
            customer_company_id: this.formValidate.customer_company_id
          }
          API.addCustomerCompany(data).then(Response => {
            if (Response.data.Code === 10000) {
              API.updateBatchMsgNodeConfig(nodeDatas).then(response => {
                if (response.data.Code === 10000) {
                  this.getList()
                  this.clearData()
                  this.$Message.success(Response.data.Message)
                  this.addCompanyModal = false
                  this.$nextTick(() => {
                    this.isAllCheck()
                  })
                } else {
                  this.$Message.error(Response.data.Message)
                  this.addCompanyModal = true
                }
              })
              this.$Modal.remove()
            } else {
              this.$Message.error(Response.data.Message)
              this.addCompanyModal = true
              this.$Modal.remove()
            }
          })
        }
      })
    },
    // 修改保存公司数据
    updateOkBtn () {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确定修改公司信息？</p>',
        loading: true,
        onOk: () => {
          this.nodeData.map(item => {
            if (item.is_push === '1') {
              this.node_info_ids.push(item.node_info_id)
            }
          })
          let data = {
            id: this.formValidate.id,
            customer_company_id: this.formValidate.customer_company_id,
            company_remark: this.formValidate.company_remark
          }
          let nodeDatas = {
            node_info_ids: this.node_info_ids.join(','),
            customer_company_id: this.formValidate.customer_company_id
          }
          API.updateCustomerCompany(data).then(Response => {
            if (Response.data.Code === 10000) {
              API.updateBatchMsgNodeConfig(nodeDatas).then(response => {
                if (response.data.Code === 10000) {
                  this.getList()
                  this.clearData()
                  this.$Message.success(Response.data.Message)
                  this.addCompanyModal = false
                  this.$nextTick(() => {
                    this.isAllCheck()
                  })
                } else {
                  this.$Message.error(Response.data.Message)
                  this.addCompanyModal = true
                }
              })
              this.$Modal.remove()
            } else {
              this.$Message.error(Response.data.Message)
              this.addCompanyModal = true
              this.$Modal.remove()
            }
          })
        }
      })
    },
    drawChange (val) {
      if (val) {
        this.drawShow = true
      } else {
        this.drawShow = false
        this.cancel()
      }
    },
    // 关闭公司弹窗
    cancel () {
      this.addCompanyModal = false
      this.clearData()
    },
    // 清除公司数据
    clearData () {
      this.formValidate = {
        customer_company_name: '',
        customer_company_id: '',
        company_type_name: '',
        company_remark: ''
      }
      this.companyListData = []
      this.nodeData = []
      this.isOpen = false
      this.checkAll = false
      this.checkAllClick = false
      this.node_info_ids = []
    },
    // 删除公司信息
    handleDelete (d) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>是否删除该公司？</p>',
        loading: true,
        onOk: () => {
          API.deleteCustomerCompany({ id: d.id }).then((response) => {
            if (response.data.Code === 10000) {
              this.$Message.success(response.data.Message)
              this.getList()
              this.$Modal.remove()
              this.loading = false
            } else {
              this.$Message.error(response.data.Message)
              this.$Modal.remove()
              this.loading = false
            }
          })
        }
      })
    },
    // 公司列表页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 公司列表分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    },
    // 成员列表页面跳转
    customerHandleSizeChange (val) {
      this.customerListQuery.pageSize = val
      this.getCustomerList()
    },
    // 成员列表分页跳转
    customerHandleCurrentChange (val) {
      this.customerListQuery.pageIndex = val
      this.getCustomerList()
    },
    // 点击左侧公司查看成员列表
    clickHandleCustomer (row, index) {
      this.curIndex = index
      // this.customerCompanyId = row.id // row.vy_customer_company_id ? row.vy_customer_company_id : row.id
      this.customerListQuery.vy_customer_company_id = row.id // this.customerCompanyId
      this.companyIsOpen = row.is_open === '1'
      this.customerListQuery.pageIndex = 1
      this.customerCurrent = 1
      this.getCustomerList()
    },
    // 渲染成员列表
    changeCustomer (row) {
      this.customerListQuery.vy_customer_company_id = row.vy_customer_company_id
      this.customerListQuery.pageIndex = 1
      this.customerCurrent = 1
      this.getCustomerList()
    },
    // 获取成员数据
    getCustomerList () {
      this.customerListLoading = true
      queryCustomerMemberPage(this.customerListQuery).then(response => {
        if (response.data.Code === 10000) {
          this.customerList = response.data.Result
          this.customerTotal = response.data.Total
        } else {
          this.$Message.error(response.data.Message)
        }
        setTimeout(() => {
          this.customerListLoading = false
        }, 1 * 800)
      }).catch(
        setTimeout(() => {
          this.customerListLoading = false
        }, 1 * 800)
      )
    },
    // 开启成员弹窗操作
    handleCustomerBtn (d, row) {
      this.$refs.customerManagementEdit.dialogType = d
      if (d === 'create') {
        this.$refs.customerManagementEdit.formTitle = '新增成员'
      } else if (d === 'update') {
        this.$refs.customerManagementEdit.formTitle = '编辑成员'
      }
      this.$refs.customerManagementEdit.modalData = Object.assign({}, row)
      this.$refs.customerManagementEdit.formModal = true
    },
    // 删除成员
    handleCustomerDelete (d) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>是否删除该成员？</p>',
        loading: true,
        onOk: () => {
          API.customerInfoDelete({ id: d.id }).then((response) => {
            if (response.data.Code === 10000) {
              this.$Message.success(response.data.Message)
              this.changeCustomer(d)
              this.$Modal.remove()
              this.loading = false
            } else {
              this.$Message.error(response.data.Message)
              this.$Modal.remove()
              this.loading = false
            }
          })
        }
      })
    },
    // 重置成员列表数据
    resetCustomerData () {
      this.customerCurrent = 1
      this.customerListQuery.vy_customer_company_id = ''
      this.customerListQuery.pageIndex = 1
    },
    // 修改货主公司短信推送状态
    changeCompanyIsOpen (row) {
      API.addBatchMsgShipowner({ customer_company_id: row.customer_company_id, is_open: row.is_open }).then(response => {
        if (response.data.Code === 10000) {
          this.getList()
          this.$Message.success(response.data.Message)
        } else {
          this.$Message.error(response.data.Message)
        }
      })
    },
    // 修改成员短信推送状态
    changeUserIsOpen (row) {
      let data = {
        customer_company_id: row.customer_company_id,
        customer_user_id: row.id,
        is_push: row.is_push
      }
      API.changeMsgUserConfig(data).then(response => {
        if (response.data.Code === 10000) {
          this.getCustomerList()
          this.$Message.success(response.data.Message)
        } else {
          this.$Message.error(response.data.Message)
        }
      })
    },
    // 判断是否全部选中
    isAllCheck () {
      if (!this.drawShow) return
      this.checkAll = this.nodeData.every(lis => { return lis.is_push === '1' })
    },
    // 全部选中与取消
    checkAllSelect () {
      this.nodeData.map(lis => { lis.is_push = this.checkAllClick ? '1' : '0' })
      this.checkAll = this.checkAllClick
    }
  },
  watch: {
    checkAllClick: {
      handler () {
        this.checkAllSelect()
      }
    }
  }
}
</script>
<style>
  .ivu-card-head {
    border-bottom: 1px solid #e8eaec;
    padding: 14px 16px 0;
    line-height: 1;
  }
</style>
<style scoped>
  .leftListTable {
    margin-top: 10px;
    padding-top: 6px;
  }
  .demo-drawer-footer {
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    border-top: 1px solid #e8e8e8;
    padding: 10px 16px;
    text-align: right;
    background: #fff;
  }
  .nodecolumnstable {
    width: 100%;
  }
</style>
