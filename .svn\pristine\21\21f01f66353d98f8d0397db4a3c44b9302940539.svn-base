<template>
  <Drawer
    :title="drawerTitle"
    v-model="visible"
    width="520"
    :mask-closable="false"
    :styles="drawerStyles"
  >
    <Form :model="formData" :rules="formRules" :label-width="100" ref="cargoForm">
      <FormItem label="船舶" prop="ship_id" :rules="formData.is_dock_repair === '0' ? {} : { required: true, message: '请选择船舶', trigger: 'change' }">
        <Select filterable clearable v-model="formData.ship_id" placeholder="请选择船舶">
          <Option v-for="(item, n) in shipList" :value="item.ship_id" :key="n">{{ item.ship_name }}</Option>
        </Select>
      </FormItem>

      <FormItem label="是否坞修" prop="is_dock_repair">
        <Select v-model="formData.is_dock_repair" placeholder="请选择坞修情况">
          <Option value="0">否</Option>
          <Option value="1">是</Option>
        </Select>
      </FormItem>
      
      <FormItem v-if="formData.is_dock_repair === '0'" label="货品" prop="goods_id">
        <Select filterable v-model="formData.goods_id" placeholder="请选择货品">
          <Option v-for="(item, n) in goodsList" :value="item.id" :key="n">{{ item.cargo_name }}</Option>
        </Select>
      </FormItem>
      
      <FormItem v-if="formData.is_dock_repair === '0'" label="货量" prop="amounts" @on-change="getFee">
        <InputNumber v-model="formData.amounts" style="width: 150px"></InputNumber>
        <span style="margin-left: 8px">吨</span>
      </FormItem>

      <FormItem v-if="formData.is_dock_repair === '0'" label="运价" prop="freight_rate">
        <InputNumber v-model="formData.freight_rate" style="width: 150px" @on-change="getFee"></InputNumber>
        <span style="margin-left: 8px">元/吨</span>
      </FormItem>

      <FormItem v-if="formData.is_dock_repair === '0'" label="运费" prop="shipping_fee">
        <InputNumber disabled v-model="formData.shipping_fee" :min="1" style="width: 150px"></InputNumber>
        <span style="margin-left: 8px">元</span>
      </FormItem>
      
      <FormItem v-if="formData.is_dock_repair === '0'" label="装货港" prop="load_port_id">
        <Select filterable v-model="formData.load_port_id" placeholder="请选择装货港" @on-change="loadPortChange">
          <Option v-for="(item, n) in portNameList" :value="item.id" :key="n">{{ item.port_name }}</Option>
        </Select>
      </FormItem>
      
      <FormItem v-if="formData.is_dock_repair === '0'" label="卸货港" prop="unload_port_id">
        <Select filterable v-model="formData.unload_port_id" placeholder="请选择卸货港" @on-change="unloadPortChange">
          <Option v-for="(item, n) in portNameList" :value="item.id" :key="n">{{ item.port_name }}</Option>
        </Select>
      </FormItem>

      <FormItem v-if="visible" label="归属月份" prop="belong_month">
        <DatePicker type="month" v-model="formData.belong_month" placeholder="选择归属月份" style="width: 300px" @on-change="data => formData.belong_month = data"></DatePicker>
      </FormItem>
      
      <FormItem v-if="visible" :label="formData.is_dock_repair === '0' ? '受载开始' : '坞修开始'" prop="start_plan_date">
        <DatePicker type="date" v-model="formData.start_plan_date" placeholder="选择受载开始时间" style="width: 300px" @on-change="data => formData.start_plan_date = data"></DatePicker>
      </FormItem>
      
      <FormItem v-if="visible && formData.is_dock_repair === '0'" :label="formData.is_dock_repair === '0' ? '受载结束' : '坞修结束'" prop="end_plan_date">
        <DatePicker type="date" v-model="formData.end_plan_date" placeholder="选择受载结束时间" style="width: 300px" @on-change="endPlanDateChange"></DatePicker>
      </FormItem>

      <FormItem label="结束时间" prop="estimated_over_day" :rules="{required: true, message: '请选择承运结束时间', trigger: 'change'}">
        <DatePicker type="date" v-model="estimated_over_day" format="yyyy-MM-dd" placeholder="选择承运结束时间" style="width: 300px" @on-change="estimatedOverDayChange"></DatePicker>
      </FormItem>
      
      <FormItem v-if="formData.is_dock_repair === '0'" label="托运方">
        <Select filterable v-model="formData.shipper" placeholder="请输入联系人姓名">
          <Option v-for="(item, n) in agentList" :value="item.customer_company_id" :key="n">{{ item.customer_company_name }}</Option>
        </Select>
        <!-- <Input v-model="formData.shipper" placeholder="请输入联系人姓名" /> -->
      </FormItem>
      
      <FormItem v-if="formData.is_dock_repair === '0'" label="收货方">
        <Select filterable v-model="formData.recipient" placeholder="请输入联系电话">
          <Option v-for="(item, n) in agentList" :value="item.customer_company_id" :key="n">{{ item.customer_company_name }}</Option>
        </Select>
        <!-- <Input v-model="formData.recipient" placeholder="请输入联系电话" /> -->
      </FormItem>
      
      <FormItem label="备注说明">
        <Input v-model="formData.remarks" type="textarea" :rows="4" placeholder="请输入备注说明" />
      </FormItem>
      
      <div class="drawer-footer">
        <Button @click="handleCancel" style="margin-right: 8px">取消</Button>
        <Button type="primary" @click="handleSubmit" :loading="submitting">提交</Button>
      </div>
    </Form>
  </Drawer>
</template>

<script>
import API from '@/api/shipSchedule'
import { queryCustomerList, queryPortList, queryBasicCargoList } from '@/api/basicData'

export default {
  name: 'CargoDrawer',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    cargoData: {
      type: Object,
      default: () => ({})
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      estimated_over_day: null,
      shipList: [], // 船舶列表
      goodsList: [], // 货品列表
      portNameList: [], // 港口列表
      agentList: [], // 货主列表
      visible: false,
      submitting: false,
      formData: {
        ship_id_plan: '',
        ship_id: '',
        belong_month: '',
        goods_id: '',
        is_dock_repair: '0',
        amounts: 0,
        freight_rate: 0,
        shipping_fee: 0,
        load_port_id: '',
        unload_port_id: '',
        start_plan_date: '',
        end_plan_date: '',
        estimated_over_day: '',
        shipper: '',
        recipient: '',
        remarks: ''
      },
      formRules: {
        goods_id: [
          { required: true, message: '请选择货物类型', trigger: 'change' }
        ],
        amounts: [
          { 
            required: true, 
            message: '请输入货物数量', 
            trigger: 'blur',
            type: 'number',
            validator: (rule, value, callback) => {
              if (value === 0 || value) {
                callback();
              } else {
                callback(new Error('请输入货物数量'));
              }
            }
          }
        ],
        freight_rate: [
        { 
            required: true, 
            message: '请输入运价', 
            trigger: 'blur',
            type: 'number',
            validator: (rule, value, callback) => {
              if (value === 0 || value) {
                callback();
              } else {
                callback(new Error('请输入运价'));
              }
            }
          }
        ],
        load_port_id: [
          { required: true, message: '请输入装货港', trigger: 'change' }
        ],
        unload_port_id: [
          { required: true, message: '请输入卸货港', trigger: 'change' }
        ],
        belong_month: [
          { required: true, message: '请选择归属月份', trigger: 'change' }
        ],
        start_plan_date: [
          { required: true, message: '请选择受载开始时间', trigger: 'change' }
        ],
        end_plan_date: [
          { required: true, message: '请选择受载结束时间', trigger: 'change' }
        ]
        // estimated_over_day: [
        //   { required: true, message: '请选择承运结束时间', trigger: 'change' } 
        // ]
      },
      drawerStyles: {
        height: 'calc(100% - 55px)',
        overflow: 'auto',
        paddingBottom: '53px',
        position: 'static'
      }
    };
  },
  computed: {
    drawerTitle() {
      return this.isEdit ? '编辑货源信息' : '添加新货源';
    }
  },
  watch: {
    value(val) {
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
      if (!val) {
        this.resetForm()
      } else {
        this.getBaseData();
      }
    },
    cargoData: {
      handler(val) {
        this.resetForm()
        if (val && Object.keys(val).length > 0) {
          this.estimated_over_day = val.estimated_over_day
          this.formData = {
            ship_id_plan: val.ship_id_plan || '',
            ship_id: val.ship_id || '',
            belong_month: val.belong_month,
            goods_id: val.goods_id || '',
            is_dock_repair: val.is_dock_repair || '0',
            amounts: parseInt(val.amounts.replace(/,/g, '')) || 0,
            freight_rate: parseInt(val.freight_rate.replace(/,/g, '')) || 0,
            shipping_fee: parseInt(val.shipping_fee.replace(/,/g, '')) || 0,
            load_port_id: val.load_port_id || '',
            unload_port_id: val.unload_port_id || '',
            start_plan_date: val.start_plan_date,
            end_plan_date: val.end_plan_date,
            estimated_over_day: val.estimated_over_day,
            shipper: val.shipper || '',
            recipient: val.recipient || '',
            remarks: val.remarks || ''
          };
          if(this.isEdit) Object.assign(this.formData, {voyage_month_plan_id: val.voyage_month_plan_id});
        } else {
          this.resetForm()
        }
      },
      immediate: true
    }
  },
  methods: {
    // 获取基础信息
    getBaseData(){
      this.shipList = JSON.parse(localStorage.getItem('shipNameList')) || [];
      // 获取港口
      queryPortList(this.loadPortListQuery).then(res => {
        if (res.data.Code === 10000) {
          this.portNameList = res.data.Result
        }
      })
      // 获取货物
      queryBasicCargoList({ cargo_name: '' }).then(res => {
        if (res.data.Code === 10000) {
          this.goodsList = res.data.Result
        }
      })
      // 获取代理公司 公司类型（1、船东；2、货主；3、代理）
      queryCustomerList({ company_type: 2 }).then(res => {
        this.agentList = []
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.agentList.push(item)
          })
        }
      })
    },
    estimatedOverDayChange(item) {
      this.estimated_over_day = item
      this.formData.estimated_over_day = item
    },
    // 计算运费
    getFee() {
      this.formData.shipping_fee = parseFloat(this.formData.amounts) * parseFloat(this.formData.freight_rate)
    },
    loadPortChange(e) {
      this.formData.load_port_id = e
      this.estimatedDayCalc()
    },
    unloadPortChange(e) {
      this.formData.unload_port_id = e
      this.estimatedDayCalc()
    },
    endPlanDateChange(e) {
      this.formData.end_plan_date = e
      this.estimatedDayCalc()
    },
    // 计算承运结束日期
    estimatedDayCalc(e) {
      if(this.formData.load_port_id && this.formData.unload_port_id && this.formData.end_plan_date) { // 如果装货港、卸货港、卸货时间都有值
        API.queryStatLinePortGroupList({ load_port_id: this.formData.load_port_id, unload_port_id: this.formData.unload_port_id, end_plan_date: this.formData.end_plan_date }).then(res => {
          if (res.data.Code === 10000 && res.data.Result.length > 0) {
            this.estimated_over_day = res.data.Result[0].estimated_over_day
            this.formData.estimated_over_day = this.estimated_over_day
          } else {
            this.estimated_over_day = null
            this.formData.estimated_over_day = null
          }
        })
      }
    },
    handleCancel() {
      this.visible = false;
      this.resetForm()
      this.$emit('cancel');
    },
    formatDate(date) {
      if (!date) return '';
      const d = new Date(date);
      return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`; 
    },
    formatMonth(date) {
      if (!date) return '';
      const d = new Date(date);
      return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}`; 
    },
    handleSubmit() {
      this.formData.belong_month = this.formatMonth(this.formData.belong_month)
      this.formData.start_plan_date = this.formatDate(this.formData.start_plan_date)
      this.formData.end_plan_date = this.formatDate(this.formData.end_plan_date)
      this.formData.estimated_over_day = this.formatDate(this.formData.estimated_over_day)
      this.$refs['cargoForm'].validate((valid) => {
        if (valid) {
          this.submitting = true;
          if(this.isEdit) {
            API.updateVoyageMonthPlan(this.formData).then(res => {
              if (res.data.Code === 10000) {
                this.submitting = false;
                this.$emit('submit')
                this.visible = false 
                this.$Message.success(res.data.Message)
              } else {
                this.$Message.error(res.data.Message)
              }
            })
          } else {
            API.addVoyageMonthPlan(this.formData).then(res => {
              if (res.data.Code === 10000) {
                this.submitting = false;
                this.$emit('submit')
                this.visible = false
                this.$Message.success(res.data.Message)
              } else {
                this.$Message.error(res.data.Message)
              }
            })
          }
        }
      });
    },
    resetForm() {
      this.estimated_over_day = null
      this.formData = {
        ship_id_plan: '',
        ship_id: '',
        belong_month: '',
        goods_id: '',
        is_dock_repair: '0',
        amounts: 0,
        freight_rate: 0,
        shipping_fee: 0,
        load_port_id: '',
        unload_port_id: '',
        start_plan_date: '',
        end_plan_date: '',
        estimated_over_day: '',
        shipper: '',
        recipient: '',
        remarks: ''
      };
      if (this.$refs.cargoForm) {
        this.$refs.cargoForm.resetFields();
      }
    },
    formatDate(date) {
      if (!date) return '';
      const d = new Date(date);
      return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
    },
    formatDateRange(dateRange) {
      if (!dateRange || dateRange.length !== 2) return '';
      return `${this.formatDate(dateRange[0])} ~ ${this.formatDate(dateRange[1])}`;
    }
  }
};
</script>

<style scoped>
.drawer-footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  left: 0;
  background: #fff;
}
</style> 