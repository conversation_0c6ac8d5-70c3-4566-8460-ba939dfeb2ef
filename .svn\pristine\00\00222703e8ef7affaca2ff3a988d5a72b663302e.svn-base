import Main from '@/components/main'

/**
 * 这里可以把一些不需要管的路由留下，比如首页，登录，401 ，405。404别留这里，要最后一个动态加载到最末尾
 */
export default [
  {
    path: '/login',
    name: 'login',
    meta: {
      title: '登录',
      hideInMenu: true,
      access: ['vistor']
    },
    component: () => import ('@/view/login/login.vue')
  },
  {
    path: '/manageLogin',
    name: 'manageLogin',
    meta: {
      title: '管理员登录',
      hideInMenu: true,
      access: ['super_admin']
    },
    component: () => import ('@/view/manageLogin/manageLogin.vue')
  },
  {
    path: '/searchDetail/:id',
    name: 'searchDetail',
    meta: {
      title: '航次详情页',
      hideInMenu: true
    },
    component: () => import ('@/view/single-page/detail/searchDetail.vue')
  },
  {
    path: '/tce',
    name: 'tce',
    meta: {
      title: 'tce预算',
      hideInMenu: true
    },
    component: () => import ('@/view/unLoginPage/tce.vue')
  },
  {
    path: '/sanction',
    name: 'sanction',
    meta: {
      title: '制裁名单查询',
      hideInMenu: true
    },
    component: () => import ('@/view/unLoginPage/sanction.vue')
  },
  {
    path: '/viewFile',
    name: 'viewFile',
    meta: {
      title: '文件预览编辑',
      hideInMenu: true
    },
    component: () => import ('@/view/wpsView/viewFile.vue')
  },
  {
    path: '/',
    name: '_home',
    redirect: '/home',
    component: Main,
    meta: {
      hideInMenu: false,
      notCache: true,
      access: ['vistor', 'super_admin']
    },
    children: [{
      path: '/home',
      name: 'home',
      meta: {
        hideInMenu: false,
        title: '控制台',
        notCache: true,
        icon: 'md-apps'
      },
      component: () => import ('@/view/single-page/home')
    }]
  },
  {
    path: '/',
    name: 'AIS',
    component: Main,
    meta: {
      icon: 'ios-map-outline',
      title: 'AIS动态',
      access: ['vistor']
    },
    children: [
      {
        path: 'ais',
        name: 'ais',
        meta: {
          icon: 'md-map',
          title: 'AIS动态'
        },
        component: () => import('@/view/single-page/AIS/ais.vue')
      }
    ]
  },
  {
    path: '/',
    name: 'shipAisManage',
    component: Main,
    meta: {
      icon: 'md-boat',
      title: '船舶管理',
      access: ['vistor']
    },
    children: [
      {
        path: 'voyageSchedule',
        name: 'voyageSchedule',
        meta: {
          icon: 'ios-book',
          title: '航次信息'
        },
        component: () => import('@/view/single-page/shipSchedule/voyageIndex.vue')
      },
      {
        path: 'shipSchedule',
        name: 'shipSchedule',
        meta: {
          icon: 'md-boat',
          title: '船舶排期'
        },
        component: () => import('@/view/single-page/shipSchedule/index.vue')
      },
      {
        path: 'scheduleCargo',
        name: 'scheduleCargo',
        meta: {
          icon: 'md-bookmarks',
          title: '货盘管理'
        },
        component: () => import('@/view/single-page/shipSchedule/cargoIndex.vue')
      },
      {
        path: 'packageShipping',
        name: 'packageShipping',
        meta: {
          icon: 'md-subway',
          title: '包运管理'
        },
        component: () => import('@/view/single-page/shipSchedule/packageShipping.vue')
      },
      {
        path: 'voyageTimeSet',
        name: 'voyageTimeSet',
        meta: {
          icon: 'ios-clock-outline',
          title: '航时管理'
        },
        component: () => import('@/view/single-page/shipSchedule/voyageTimeSet.vue')
      },
      {
        path: 'shipAis',
        name: 'shipAis',
        meta: {
          icon: 'md-map',
          title: '船舶AIS'
        },
        component: () => import('@/view/ShipMapSystem/index.vue')
      }
    ]
  },
  {
    path: '/',
    name: 'ship',
    component: Main,
    meta: {
      icon: 'ios-map-outline',
      title: '船舶信息',
      access: ['super_set', 'business']
    },
    children: [
      {
        path: 'shipMessDetail',
        name: 'shipMessDetail',
        meta: {
          icon: 'md-barcode',
          title: '船舶信息'
        },
        component: () => import('@/view/single-page/ship/shipMessDetail.vue')
      }
    ]
  },
  {
    path: '/',
    name: 'voyageManage',
    component: Main,
    meta: {
      icon: 'md-boat',
      title: '航次管理',
      access: ['business']
    },
    children: [
      {
        path: 'voyageManagement',
        name: 'voyageManagement',
        meta: {
          icon: 'custom voyagefont iconvoyage-sail',
          title: '航次管理'
        },
        component: () => import('@/view/voyage/voyageManage.vue')
      },
      {
        path: 'voyageCheck/:id',
        name: 'voyageCheck',
        meta: {
          icon: 'ios-construct',
          title: '数据核对'
        },
        component: () => import('@/view/voyage/voyageCheck.vue')
      },
      {
        path: 'voyageCheckHis',
        name: 'voyageCheckHis',
        meta: {
          icon: 'md-list-box',
          title: '核对记录'
        },
        component: () => import('@/view/voyage/voyageCheckHis.vue')
      }
    ]
  },
  {
    path: '/401',
    name: 'error_401',
    meta: {
      hideInMenu: true
    },
    component: () => import ('@/view/error-page/401.vue')
  },
  {
    path: '/500',
    name: 'error_500',
    meta: {
      hideInMenu: true
    },
    component: () => import ('@/view/error-page/500.vue')
  },
  {
    path: '/',
    name: 'detail',
    component: Main,
    meta: {
      icon: 'md-sad',
      title: '航次详情页',
      hideInMenu: true,
      access: ['vistor']
    },
    children: [
      {
        path: 'voyageDetail',
        name: 'voyageDetail',
        meta: {
          icon: 'md-apps',
          title: '航次详情页'
        },
        component: () => import('@/view/single-page/detail/voyageDetail.vue')
      }
    ]
  },
  {
    path: '/',
    name: 'detailSearch',
    component: Main,
    meta: {
      icon: 'md-sad',
      title: '搜索详情页',
      hideInMenu: true,
      access: ['vistor']
    },
    children: [
      {
        path: 'searchInDetail/:id',
        name: 'searchInDetail',
        meta: {
          icon: 'md-apps',
          title: '搜索详情页'
        },
        component: () => import('@/view/single-page/detail/searchInDetail.vue')
      }
    ]
  },
  // {
  //   path: '/superPermission',
  //   name: 'superPermission',
  //   component: Main,
  //   meta: {
  //     icon: 'md-browsers',
  //     title: '超级权限',
  //     access: ['super_admin']
  //   },
  //   children: [
  //     {
  //       path: 'rightsManagement',
  //       name: 'rightsManagement',
  //       meta: {
  //         icon: 'md-person',
  //         title: '超级权限'
  //       },
  //       component: () => import('@/view/superPermission/index.vue')
  //     }
  //   ]
  // },
  {
    path: '/',
    name: 'account',
    component: Main,
    meta: {
      icon: 'md-browsers',
      title: '用户管理',
      access: ['super_admin']
    },
    children: [
      {
        path: 'accountManagement',
        name: 'accountManagement',
        meta: {
          icon: 'md-person',
          title: '用户管理'
        },
        component: () => import('@/view/accountManagement/index.vue')
      }
    ]
  },
  {
    path: '/',
    name: 'company_management',
    component: Main,
    meta: {
      icon: 'md-cog',
      title: '公司管理',
      access: ['super_admin']
    },
    children: [
      {
        path: 'companyManagement',
        name: 'companyManagement',
        meta: {
          icon: 'md-cog',
          title: '公司管理'
        },
        component: () => import('@/view/companyManagement/index.vue')
      }
    ]
  },
  {
    path: '/',
    name: 'voyage_history',
    component: Main,
    meta: {
      icon: 'md-cog',
      title: '历史航次',
      access: ['super_set', 'super_admin']
    },
    children: [
      {
        path: 'voyageHistory',
        name: 'voyageHistory',
        meta: {
          icon: 'md-cog',
          title: '历史航次'
        },
        component: () => import('@/view/voyageHistory/index.vue')
      }
    ]
  },
  {
    path: '/',
    name: 'dynamic_management',
    component: Main,
    meta: {
      icon: 'ios-loading',
      title: '动态管理',
      access: ['super_set', 'super_admin']
    },
    children: [
      {
        path: 'dynamicManagement',
        name: 'dynamicManagement',
        meta: {
          icon: 'ios-loading',
          title: '动态管理'
        },
        component: () => import('@/view/dynamicManagement/index.vue')
      }
    ]
  },
  {
    path: '/',
    name: 'port',
    component: Main,
    meta: {
      icon: 'md-ionic',
      title: '港口管理',
      access: ['super_admin']
    },
    children: [
      {
        path: 'portManagement',
        name: 'portManagement',
        meta: {
          icon: 'md-ionic',
          title: '港口管理'
        },
        component: () => import('@/view/portManagement/index.vue')
      }
    ]
  },
  {
    path: '/',
    name: 'wharf',
    component: Main,
    meta: {
      icon: 'md-link',
      title: '码头管理',
      access: ['super_admin']
    },
    children: [
      {
        path: 'wharfManagement',
        name: 'wharfManagement',
        meta: {
          icon: 'md-link',
          title: '码头管理'
        },
        component: () => import('@/view/wharfManagement/index.vue')
      }
    ]
  },
  {
    path: '/',
    name: 'berth',
    component: Main,
    meta: {
      icon: 'md-compass',
      title: '泊位管理',
      access: ['super_admin']
    },
    children: [
      {
        path: 'berthManagement',
        name: 'berthManagement',
        meta: {
          icon: 'md-compass',
          title: '泊位管理'
        },
        component: () => import('@/view/berthManagement/index.vue')
      }
    ]
  },
  {
    path: '/',
    name: 'ship_manage',
    component: Main,
    meta: {
      icon: 'ios-boat',
      title: '船舶管理',
      access: ['super_admin']
    },
    children: [
      {
        path: 'shipManagement',
        name: 'shipManagement',
        meta: {
          icon: 'md-boat',
          title: '船舶库'
        },
        component: () => import('@/view/shipManagement/index.vue')
      },
      {
        path: 'companyShipSet',
        name: 'companyShipSet',
        meta: {
          icon: 'ios-boat-outline',
          title: '公司船舶'
        },
        component: () => import('@/view/companyShipSet/index.vue')
      }
    ]
  },
  {
    path: '/basicConfig',
    name: 'basicConfig',
    component: Main,
    meta: {
      icon: 'md-cog',
      title: '基础配置',
      access: ['super_admin']
    },
    children: [
      {
        path: 'goodsType',
        name: 'goodsType',
        meta: {
          icon: 'md-cube',
          title: '货品种类'
        },
        component: () => import('@/view/basicConfig/goodsType')
      },
      {
        path: 'goodsPrice',
        name: 'goodsPrice',
        meta: {
          icon: 'md-pricetag',
          title: '货品价格'
        },
        component: () => import('@/view/basicConfig/goodsPrice')
      }
    ]
  },
  // {
  //   path: '/',
  //   name: 'berthing',
  //   component: Main,
  //   meta: {
  //     icon: 'md-calendar',
  //     title: '靠泊计划',
  //     access: ['vistor']
  //   },
  //   children: [
  //     {
  //       path: 'berthingPlan',
  //       name: 'berthingPlan',
  //       meta: {
  //         icon: 'custom voyagefont iconvoyage-mooring',
  //         title: '靠泊计划'
  //       },
  //       component: () => import('@/view/berthingPlan/index.vue')
  //     }
  //   ]
  // },
  {
    path: '/',
    name: 'history',
    component: Main,
    meta: {
      icon: 'md-time',
      title: '历史航次',
      access: ['vistor']
    },
    children: [
      {
        path: 'historyVoyage',
        name: 'historyVoyage',
        meta: {
          icon: 'md-time',
          title: '历史航次'
        },
        component: () => import('@/view/voyage/historyVoyage.vue')
      }
    ]
  },
  {
    path: '/',
    name: 'smsSetting',
    component: Main,
    meta: {
      icon: 'md-mail',
      title: '短信管理',
      access: ['business']
    },
    children: [
      {
        path: 'messageSetting',
        name: 'messageSetting',
        meta: {
          icon: 'md-mail-open',
          title: '短信配置'
        },
        component: () => import('@/view/setting/settingManage/index')
      },
      {
        path: 'historicalSMS',
        name: 'historicalSMS',
        meta: {
          icon: 'ios-send',
          title: '推送历史'
        },
        component: () => import('@/view/setting/pushHistorical/index')
      }
    ]
  },
  {
    path: '/',
    name: 'setting',
    component: Main,
    meta: {
      icon: 'md-settings',
      title: '用户管理',
      access: ['business']
    },
    children: [
      {
        path: 'customerManagement',
        name: 'customerManagement',
        meta: {
          icon: 'md-people',
          title: '客户管理'
        },
        component: () => import('@/view/customerManagement/index')
      }
    ]
  },
  {
    path: '/dynamicReport',
    name: 'dynamicReport',
    component: Main,
    meta: {
      icon: 'md-grid',
      title: '动态报表',
      showAlways: true,
      access: ['business']
    },
    children: [
      {
        path: 'exportReport',
        name: 'exportReport',
        meta: {
          icon: 'md-arrow-down',
          title: '动态导出'
        },
        component: () => import('@/view/dynamicReport/exportReport/index.vue')
      },
      {
        path: 'exportHistory',
        name: 'exportHistory',
        meta: {
          icon: 'md-archive',
          title: '导出历史'
        },
        component: () => import('@/view/dynamicReport/exportHistory/index.vue')
      }
    ]
  },
  {
    path: '/',
    name: 'statistics',
    component: Main,
    meta: {
      icon: 'md-pulse',
      title: '统计分析',
      access: ['vistor']
    },
    children: [
      {
        path: '/statisticsOverview',
        name: 'statisticsOverview',
        meta: {
          icon: 'md-filing',
          title: '统计总览'
        },
        component: () => import('@/view/single-page/echart/index')
      },
      {
        path: '/voyageAnalysis',
        name: 'voyageAnalysis',
        meta: {
          icon: 'md-fastforward',
          title: '航程分析'
        },
        component: () => import('@/view/single-page/echart/voyageAnalysis')
      },
      {
        path: '/operationAnalysis',
        name: 'operationAnalysis',
        meta: {
          icon: 'ios-options',
          title: '营运分析'
        },
        component: () => import('@/view/single-page/echart/operationAnalysis')
      },
      {
        path: '/transportOverview',
        name: 'transportOverview',
        meta: {
          icon: 'ios-train',
          title: '货运总览'
        },
        component: () => import('@/view/single-page/echart/transportOverview')
      },
      {
        path: '/anchorStatistics',
        name: 'anchorStatistics',
        meta: {
          icon: 'ios-locate',
          title: '锚泊时长'
        },
        component: () => import('@/view/single-page/echart/anchorStatistics')
      },
      {
        path: '/operationEfficiency',
        name: 'operationEfficiency',
        meta: {
          icon: 'md-clock',
          title: '作业效率'
        },
        component: () => import('@/view/single-page/echart/operationEfficiency')
      }
    ]
  },
  {
    path: '/',
    name: 'monthly',
    component: Main,
    meta: {
      icon: 'md-browsers',
      title: '月度报表',
      access: ['business']
    },
    children: [
      {
        path: 'monthlyVoyageReport',
        name: 'monthlyVoyageReport',
        meta: {
          icon: 'ios-document-outline',
          title: '航次报表'
        },
        component: () => import('@/view/monthlyReport/monthlyVoyageReport/index')
      },
      {
        path: 'monthlyTransportReport',
        name: 'monthlyTransportReport',
        meta: {
          icon: 'md-paper',
          title: '月度运输表'
        },
        component: () => import('@/view/monthlyReport/monthlyTransportReport/index')
      }
    ]
  },
  {
    path: '/',
    name: 'companyMember',
    component: Main,
    meta: {
      icon: 'md-people',
      title: '成员管理',
      access: ['companyAdmin']
    },
    children: [
      {
        path: 'companyMemberManage',
        name: 'companyMemberManage',
        meta: {
          icon: 'ios-people',
          title: '成员管理'
        },
        component: () => import('@/view/companyManagement/companyUser.vue')
      }
    ]
  },
  {
    path: '/',
    name: 'companyShip',
    component: Main,
    meta: {
      icon: 'ios-boat-outline',
      title: '船舶管理',
      access: ['companyAdmin']
    },
    children: [
      {
        path: 'companyShipManage',
        name: 'companyShipManage',
        meta: {
          icon: 'ios-boat-outline',
          title: '船舶管理'
        },
        component: () => import('@/view/companyManagement/companyShip.vue')
      }
    ]
  },
  {
    path: '/',
    name: '_document',
    component: Main,
    meta: {
      hideInMenu: false,
      notCache: true,
      access: ['business']
    },
    children: [{
      path: '/document',
      name: 'document',
      meta: {
        hideInMenu: false,
        title: '文件资料',
        notCache: true,
        icon: 'md-folder-open'
      },
      component: () => import ('@/view/document/index')
    }]
  }
]
