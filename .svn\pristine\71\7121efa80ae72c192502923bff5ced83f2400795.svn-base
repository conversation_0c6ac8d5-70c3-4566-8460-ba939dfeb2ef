<template>
  <Drawer
    v-model="modalData.modal"
    title="添加用户"
    width="750"
    :mask-closable="false"
    @on-visible-change="modalShow"
  >
    <Form ref="formData" :model="formData" :rules="ruleForm" :label-width="115">
      <Row>
        <Col span="11">
          <FormItem label="用户姓名" prop="full_name">
            <Input v-model="formData.full_name"></Input>
          </FormItem>
        </Col>
        <Col span="11">
          <FormItem label="用户账号" prop="mobile">
            <Input v-model="formData.mobile"></Input>
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span="11">
          <FormItem label="入驻公司" prop="join_company_id">
            <Select v-model="formData.join_company_id" filterable @on-select="getCompanyType">
              <Option v-for="(item1, idx) in joinCompanyList" :key="idx" :value="item1.value">{{ item1.label }}</Option>
            </Select>
          </FormItem>
        </Col>
        <Col span="11" style="margin: 7px 0 0 20px;">{{ company_type }}</Col>
      </Row>
    </Form>
    <div class="demo-drawer-footer">
      <Button @click="clearData" style="margin-right:10px;">取消</Button>
      <Button type="primary" @click="createData">保存</Button>
    </div>
  </Drawer>
</template>
<script>
import API from '@/api/accountManagement'
import { queryAllCompanyList } from '@/api/customerManagement/customerManagement' // 获取公司名称下拉
import { validateMobilePhone } from '@/libs/iViewValidate'

export default {
  props: {
    modalData: Object
  },
  data () {
    return {
      company_type: '',
      joinCompanyList: [],
      formData: {
        full_name: '',
        mobile: '',
        join_company_id: ''
      },
      ruleForm: {
        full_name: [
          { required: true, message: '用户姓名不能为空', trigger: 'blur' }
        ],
        mobile: [
          { required: true, message: '用户账号不能为空', trigger: 'blur' },
          { validator: validateMobilePhone, trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    // 保存用户
    createData () {
      this.$refs['formData'].validate((valid) => {
        if (valid) {
          this.$Modal.confirm({
            title: '提示',
            content: '<p>是否确认添加用户？</p>',
            loading: true,
            onOk: () => {
              API.addAccount(this.formData).then(res => {
                if (res.data.Code === 10000) {
                  this.$Message.success(res.data.Message)
                  this.$Modal.remove()
                  this.$emit('callback')
                  this.modalData.modal = false
                } else {
                  this.modalData.modal = true
                  this.$Message.error(res.data.Message)
                  this.$Modal.remove()
                }
              }).catch()
            }
          })
        }
      })
    },
    // 取消
    clearData () {
      this.modalData.modal = false
    },
    // 模态框展示
    modalShow (val) {
      if (val) {
        queryAllCompanyList ({ find_all: 1 }).then(res => { // 获取所有公司
          if (res.data.Code === 10000) {
            this.joinCompanyList = res.data.Result.map(item => {
              return {
                value: item.id,
                label: item.name,
                companyType: item.company_type_name
              }
            })
          }
        })
      } else {
        this.$nextTick(() => {
          this.formData = {}
          this.$refs['formData'].resetFields()
        })
        this.company_type = ''
        this.joinCompanyList = []
      }
    },
    // 获取公司类型
    getCompanyType (val) {
      let curIndex = this.joinCompanyList.findIndex(item => {
        return val.value === item.value
      })
      this.company_type = this.joinCompanyList[curIndex].companyType
    }
  }
}
</script>