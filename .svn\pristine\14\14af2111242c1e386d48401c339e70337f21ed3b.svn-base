self.onmessage = async function () {
  try {
    // 获取基础船舶数据
    // const baseRes = await fetch('/console/home/<USER>/queryBusinessVoyageConsolePage?pageSize=100&pageIndex=1');
    // const baseJson = await baseRes.json();
    // const shipList = baseJson.Result || [];

    // if (!shipList.length) {
    //   return self.postMessage({ success: true, data: [] });
    // }

    // const mmsis = shipList.map(s => s.mmsi).filter(Boolean);
    // const shipIds = shipList.map(s => s.ship_id).filter(Boolean);

    // // 拼接 GetManyShip 参数
    // const statusQuery = `/apicall/GetManyShip?k=a5bb8f37140d428391e1546d7b704413&id=${mmsis[0]}&enc=1&v=4`;
    // const statusRes = await fetch(statusQuery);
    // const statusJson = await statusRes.json();
    // const statusList = statusJson.data || [];

    // // 获取计划
    // const planRes = await fetch('/console/home/<USER>/queryVoyagePlanConsolePage?pageSize=100&pageIndex=1');
    // const planJson = await planRes.json();
    // const planList = planJson.Result || [];

    // // 获取预警（简单处理为用第一个 mmsi 示例）
    // const now = new Date();
    // const end = now.toISOString().slice(0, 19).replace('T', ' ');
    // const start = new Date(now.getTime() - 6 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' ');
    // const warnRes = await fetch(`/ais/hifleet/getShipsTrajectory?mmsis=${mmsis.join(',')}&zoom=1&startdates=${encodeURIComponent(start)}&endates=${encodeURIComponent(end)}`);
    // const warnJson = await warnRes.json();
    // const warnList = warnJson.data || [];

    // // 整合数据
    // const merged = shipList.map(ship => {
    //   const status = statusList.find(s => String(s.mmsi) === String(ship.mmsi));
    //   const plan = planList.find(p => String(p.ship_id) === String(ship.ship_id));
    //   const warning = warnList.find(w => String(w.data?.[0]?.m) === String(ship.mmsi));

    //   return {
    //     ...ship,
    //     statusInfo: status || null,
    //     planInfo: plan || null,
    //     warningInfo: warning || null,
    //   };
    // });

    self.postMessage({ success: true, data: {a:1} });
  } catch (err) {
    self.postMessage({ success: false, error: err.message });
  }
};