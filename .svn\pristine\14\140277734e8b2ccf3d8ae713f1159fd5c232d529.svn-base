import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 获取锚泊时长整体对比
export function queryAnchorOverall (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/head/queryAnchorInfoOverall', 
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取锚泊时长单船对比
export function queryShipAnchor (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/head/queryStatShipAnchorTimeInfo', 
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取锚泊时长港口统计
export function querySumOrderByPorts (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/head/queryStatLineSumOrderByPorts', 
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取锚泊时长报表统计
export function queryAnchorReport (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/head/queryAnchorTimeReport', 
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  queryAnchorOverall,
  queryShipAnchor,
  querySumOrderByPorts,
  queryAnchorReport
}