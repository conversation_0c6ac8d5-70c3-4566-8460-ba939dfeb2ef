<template>
  <div>
    <div v-if="list.length !== 0" style="min-height: 180px;">
      <div v-for="(item, idx) in list" :key="idx" class="voyage-con">
        <Row class="voyage-parent" justify="start" align="middle">
          <Col span="4">
            <span class="voyage-no">{{ item.ship_name }} {{ item.voyage_no }}</span>
          </Col>
          <Col span="8">
            <span>
              {{ getPort(item.portResult, '1') }}
            </span>
            -
            <span>
              {{ getPort(item.portResult, '2') }}
            </span>
          </Col>
          <Col span="9" v-if="item.status !== '2'"> <!-- status: 1为计划航次  2为执行航次-->
            <span v-for="(item, idx) in getGoods(item.cargoResult)" :key="idx">
              <span>{{ item }}</span>
            </span>
          </Col>
          <Col span="12" v-if="item.status === '2'">
            <nodeList :nodeObj="item" :fontSize="12" :nameColor="'#007DFF'" :textColor="'#333'" :dateShow="true"></nodeList>
          </Col>
          <Col span="3" v-if="item.status !== '2'">
            <div v-if="item.send_num === '0'"><Button class="btn-area" type="text" @click="sendVoyage(item)">发送</Button></div>
            <div v-if="item.send_num !== '0'"><Button class="btn-area"  type="text"  @click="sendAgainVoyage(item)">再次发送</Button></div>
          </Col>
        </Row>
      </div>
    </div>
    <div v-if="list.length === 0" style="min-height: 180px;">
      <Row class="no-list" justify="center" align="middle">
      <img src="../../../../assets/images/no-data.png" />
      <div>暂无列表数据</div>
      </Row>
    </div>
    <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="pageSize" :current.sync="pageCur"
          :total="total" prev-text="< 上一页" next-text="下一页 >"
          @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
  </div>
</template>
<script>
import './list.less'
import API from '@/api/control'
import planAPI from '@/api/voyageManage/planVoyage'
import nodeList from '@/components/nodeList/nodeNList'
export default {
  components: {
    nodeList
  },
  data () {
    return {
      pageCur: 1, // 当前页
      pageSize: 3, // 每页个数
      total: 0, // 总页数
      queryParam: {
        pageSize: 3,
        pageIndex: 1
      },
      list: []
    }
  },
  computed: {
    getPort () { // 港口,码头解析
      return function (list, type) {
        if (!list || list.length <= 0) return ''
        // 按类型提取需要的港头数据列表,type = 1为装港, type = 2为卸港
        let curList = list.filter(item => item.port_type === type)
        let portList = curList.map(d => {
          return d.wharf_name // === '' ? d.port_name : d.port_name + '/' + d.wharf_name
        })
        return portList.join(' ')
      }
    },
    getGoods () { // 货品,货量解析
      return function (list) {
        if (!list || list.length <= 0) return []
        let curList = list.map((item, idx) => {
          if (idx === list.length - 1) {
            return item.goods_name + ' - ' + item.amounts
          } else {
            return item.goods_name + ' - ' + item.amounts + ','
          }
        })
        return curList
      }
    }
  },
  created () {
    this.getList()
  },
  methods: {
    getList () {
      API.queryBusinessVoyageConsolePage(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          this.list = res.data.Result
          this.total = res.data.Total
        }
      })
    },
    // 发送计划航次
    sendVoyage (row) {
      planAPI.sendVoyage({ id: row.id }).then(res => {
        if (res.data.Code === 10000) {
          this.$Message.success(res.data.Message)
          this.getList()
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 再次发送计划航次
    sendAgainVoyage (row) {
      planAPI.sendAgainVoyage({ id: row.id }).then(res => {
        if (res.data.Code === 10000) {
          this.$Message.success(res.data.Message)
          this.getList()
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 页面跳转
    handleSizeChange (val) {
      this.queryParam.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.queryParam.pageIndex = val
      this.getList()
    }
  }
}
</script>
