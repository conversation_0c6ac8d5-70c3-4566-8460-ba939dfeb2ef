<template>
  <div>
    <Card>
      <search @searchResults='searchResults' :setSearch='setSearchData' @keydown.enter.native='searchResults' @resetResults='resetResults'></search>
    </Card>
    <Card class="cardDiv">
      <p slot='title' class="bold-font">船舶管理</p>
      <div class="extra" slot="extra">
        <formAction :setFormAction='setFormAction' @handleUpdateTable="getList" @handleCreate="handleCreate"></formAction>
      </div>
      <Table border :loading="loading" :columns="columns" :data="listData"></Table>
      <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
          :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
    </Card>
    <editShipModal :modalData="modalData" @callback="getList"></editShipModal>
  </div>
</template>

<script>
import search from '_c/search' // 查询组件
import editShipModal from './editShipModal'
import formAction from '_c/formAction' // 表单操作组件
import API from '@/api/shipManagement'

export default {
  components: { 
    search,
    formAction,
    editShipModal
  },
  data () {
    return {
      setSearchData: {// 查询设置，对象key值为回调参数
        ship_name: {
          type: 'text',
          label: '船舶名',
          width: 150,
          value: '',
          isdisable: false
        }
      },
      setFormAction: {
        operation: ['create', 'updateTable']
      },
      total: null, // 列表数据条数
      listData: [],
      loading: false, // 表单列表loding状态
      columns: [
        {
          title: '船舶名称',
          key: 'ship_name',
          align: 'center'
        },
        {
          title: '类型',
          key: 'ship_type_name',
          align: 'center'
        },
        {
          title: 'MMIS',
          key: 'mmsi',
          align: 'center'
        },
        {
          title: '载重吨',
          key: 'deadweight_tonnage',
          align: 'center'
        },
        {
          title: '总吨位',
          key: 'gross_tonage',
          align: 'center'
        },
        {
          title: '建造时间',
          key: 'build_date',
          align: 'center'
        },
        {
          title: '操作',
          align: 'center',
          width: 260,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: {
                  icon: 'md-paper',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handlePreview(params.row)
                  }
                }
              }, '查看'),
              h('Button', {
                style: {
                  margin: '0 5px'
                },
                props: {
                  icon: 'md-brush',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleUpdate(params.row)
                  }
                }
              }, '修改'),
              h('Button', {
                style: {
                  margin: '0 5px'
                },
                props: {
                  icon: 'md-trash',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleDelete(params.row.ship_id)
                  }
                }
              }, '删除')
            ])
          }
        }
      ],
      listQuery: {// 列表请求参数
        ship_name: '',
        pageSize: 10,
        pageIndex: 1
      },
      listCurrent: 1, // 当前页码
      modalData: {
        modal: false,
        title: '',
        data: undefined,
        dialogType: null
      }
    }
  },
  created () {
    this.getList()
  },
  methods: {
    // 获取列表
    getList () {
      this.loading = true
      this.listQuery.ship_name = this.setSearchData.ship_name.value
      API.queryBasicShipPage(this.listQuery).then(response => {
        if (response.data.Code === 10000) {
          this.listData = response.data.Result
          this.total = response.data.Total
        } else {
          this.$Message.error(response.data.Message)
        }
        setTimeout(() => {
          this.loading = false
        }, 1 * 800)
      }).catch(
        setTimeout(() => {
          this.loading = false
        }, 1 * 800)
      )
    },
    // 查询
    searchResults (e) {
      if (e.target) delete e.target
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      Object.assign(this.listQuery, e)
      this.getList()
    },
    // 重置
    resetResults () {
      this.listCurrent = 1
      this.listQuery = {
        pageSize: 10,
        pageIndex: 1,
        ship_name: ''
      }
      this.setSearchData.ship_name.value = ''
      this.getList()
    },
    // 新增
    handleCreate () {
      this.modalData = {
        modal: true,
        title: '新增',
        dialogType: 'create'
      }
    },
    // 查看
    handlePreview (row) {
      this.modalData = {
        modal: true,
        title: '详情',
        dialogType: 'detail',
        data: row
      }
    },
    // 修改
    handleUpdate (row) {
      this.modalData = {
        modal: true,
        title: '编辑',
        dialogType: 'update',
        data: row
      }
    },
    // 删除
    handleDelete (d) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确认删除所选项？</p>',
        loading: true,
        onOk: () => {
          API.delBasicShip({ ship_id: d }).then(res => {
            if (res.data.Code === 10000) {
              this.loading = false
              this.$Modal.remove()
              this.$Message.success(res.data.Message)
              this.getList()
            } else {
              this.loading = false
              this.$Modal.remove()
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    }
  }
}
</script>
<style scoped>
.cardDiv {
  margin-top: 20px;
  border: none;
  background: transparent;
}
.shiplist {
  cursor: pointer;
  color: #333;
  height: 60px;
  line-height: 60px;
  text-align: center;
  margin-bottom: 10px;
  background-color: white;
}
.curidx {
  color: white;
  background-color: #007DFF;
}
</style>

