import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 新增TCE信息
export function addTce (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/open/api/yzj/tce/addTce',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询TCE信息 分页
export function queryTcePage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/open/api/yzj/tce/queryTcePage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  addTce,
  queryTcePage
}