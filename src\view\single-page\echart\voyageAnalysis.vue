<template>
  <div>
    <card>
      <div class="btn-area">
        时间：<month-select class="month-select" @on-change="dateSelect" v-model="curDate"></month-select>
        <!-- <div @click="showModelBtn" class="morebtn">more &gt;</div> -->
      </div>
      <Row>
        <Col span="8">
          <chart-pie style="height: 300px;" :value="pieData1" text="航程占比" :color="pieColor1" unit="航次"></chart-pie>
          <div class="morebtn" @click="showVoyageProportion">more &gt;</div>
        </Col>
        <Col span="16">
          <!-- <chart-line style="height: 300px;" unit="航次/次" :value="lineData1" :color="pieColor1" text="短中远次数对比"/> -->
          <ChartBar style="height: 300px;" subtext="航次/次" unit="次" :value="lineData1" text="短中远次数对比" rotate="45"></ChartBar>
        </Col>
      </Row>
      <Row>
        <Col span="8">
          <chart-pie style="height: 300px;" :value="pieData2" text="南上北下占比" :color="pieColor2" unit="航次"></chart-pie>
          <div class="morebtn" @click="showVoyageNorthsouth">more &gt;</div>
        </Col>
        <Col span="16">
          <!-- <chart-line style="height: 300px;" unit="航次/次" :value="lineData2" :color="pieColor2" text="南上北下航次"/> -->
          <ChartBar style="height: 300px;" subtext="航次/次" unit="次" :value="lineData2" text="南上北下航次" rotate="45"></ChartBar>
        </Col>
      </Row>
      <Row>
        <Col span="8">
          <chart-pie style="height: 300px;" :value="pieData3" :isOnlyValue="true" text="空/重载分析" :color="pieColor2" unit="%"></chart-pie>
          <div class="morebtn" @click="showVoyageWightLoad">more &gt;</div>
        </Col>
        <Col span="16">
          <!-- <chart-line style="height: 300px;" unit="航次/次" :value="lineData3" :color="pieColor2" text="重载率"/> -->
          <ChartBar style="height: 300px;" unit="%" :value="lineData3" text="重载率" rotate="45"></ChartBar>
        </Col>
      </Row>
    </card>
    <VoyageProportion :modalData="voyageModal" :startDate="queryParam.start_month" :endDate="queryParam.end_month" :defaultStartDate="start_default_date" :defaultEndDate="end_default_date"></VoyageProportion>
    <NorthsouthDrawer :modalData="northsouthModal" :startDate="queryParam.start_month" :endDate="queryParam.end_month" :defaultStartDate="start_default_date" :defaultEndDate="end_default_date"></NorthsouthDrawer>
    <WeightnoLoadDrawer :modalData="weightModal" :startDate="queryParam.start_month" :endDate="queryParam.end_month" :defaultStartDate="start_default_date" :defaultEndDate="end_default_date"></WeightnoLoadDrawer>
  </div>
</template>
<script>
import API from '@/api/statistics/voyageAnalysis'
import { queryStatTime } from '@/api/basicData'
import { ChartPie, ChartBar, ChartLine } from '_c/charts'
import MonthSelect from '@/components/monthSelect'
import VoyageProportion from './drawer/voyageProportionDrawer.vue'
import NorthsouthDrawer from './drawer/northsouthDrawer.vue'
import WeightnoLoadDrawer from './drawer/weightNoLoadDrawer.vue'

export default {
  components: {
    ChartPie,
    ChartBar,
    ChartLine,
    MonthSelect,
    VoyageProportion,
    NorthsouthDrawer,
    WeightnoLoadDrawer
  },
  computed: {
    curDate () {
      if (this.queryParam.start_month !== '' && this.queryParam.end_month !== '') {
        return this.queryParam.start_month + '~' + this.queryParam.end_month
      }
      if (this.queryParam.start_month !== '' && this.queryParam.end_month === '') {
        return this.queryParam.start_month
      }
      return ''
    }
  },
  data () {
    return {
      voyageModal: {
        modal: false,
        title: '航程占比',
        data: {}
      },
      northsouthModal: {
        modal: false,
        title: '南上北下占比',
        data: {}
      },
      weightModal: {
        modal: false,
        title: '重/空载情况',
        data: {}
      },
      pieColor1: ['#5D7092', '#5B8FF9', '#5AD8A6'],
      pieData1: [],
      lineData1: {
        xAxis: [],
        legend: ['远程', '中程', '短程'],
        data: [[], [], []]
      },
      pieColor2: ['#5B8FF9', '#5AD8A6'],
      pieData2: [],
      lineData2: {
        xAxis: [],
        legend: ['南上', '北下'],
        smooth: 0,
        data: [[], []],
        symbol: ['circle', 'triangle']
      },
      pieData3: [],
      lineData3: {
        xAxis: [],
        legend: ['重载率'],
        smooth: 0,
        data: [],
        symbol: ['circle']
      },
      queryParam: {
        start_month: '',
        end_month: ''
      },
      start_default_date: '', // 默认开始时间
      end_default_date: '' // 默认结束时间
    }
  },
  methods: {
    // 日期变化触发
    dateSelect (dateObj) {
      this.queryParam.start_month = dateObj[0] ? dateObj[0] : ''
      this.queryParam.end_month = dateObj[1] ? dateObj[1] : ''
      this.resetData()
      this.getList()
    },
    resetData () {
      this.pieData1 = []
      this.lineData1.xAxis = []
      this.lineData1.data = [[], [], []]

      this.pieData2 = []
      this.lineData2.xAxis = []
      this.lineData2.data = [[], []]

      this.pieData3 = []
      this.lineData3.xAxis = []
      this.lineData3.data = []
    },
    // 获取时间区间
    getSysDate () {
      queryStatTime().then(res => {
        if (res.data.Code === 10000) {
          if (res.data.start_month && res.data.end_month) {
            this.queryParam.start_month = res.data.start_month
            this.queryParam.end_month = res.data.end_month
            this.getList()
          }
        }
      })
    },
    // 获取数据
    getList () {
      API.queryVoyageMileOverall(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          // 航程占比数据
          res.data.mileLevelRatioArray.forEach(item => {
            this.pieData1.push({
              value: parseInt(item.count_level),
              name: item.mile_level_name
            })
          })
          let shortMileArr = [] // 短程数据
          let middleMileArr = [] // 中程数据
          let longMileArr = [] // 远程数据
          if (res.data.shortMileShipsArray.length > 0) { // 剔除数据为0内容
            res.data.shortMileShipsArray.forEach((item, idx) => {
              if (res.data.shortMileShipsArray[idx].count_level !== '0' || res.data.middleMileShipsArray[idx].count_level !== '0' || res.data.longMileShipsArray[idx].count_level !== '0') {
                shortMileArr.push(res.data.shortMileShipsArray[idx])
                middleMileArr.push(res.data.middleMileShipsArray[idx])
                longMileArr.push(res.data.longMileShipsArray[idx])
              }
            })
          }
          // 短中远次数对比数据
          this.resetVoyageDis(shortMileArr, 0)
          this.resetVoyageDis(middleMileArr, 1)
          this.resetVoyageDis(longMileArr, 2)
          // 南上北下占比
          let goNorthArr = [] // 北下数据
          let goSouthArr = [] // 南上数据
          res.data.directionRatioArray.forEach(item => {
            this.pieData2.push({
              value: parseInt(item.direction_count),
              name: item.direction_name
            })
          })
          if (res.data.shipsGoNorthArray.length > 0) { // 剔除南上北下数据为0船舶
            res.data.shipsGoNorthArray.forEach((item, idx) => {
              if (res.data.shipsGoNorthArray[idx].direction_count !== '0' || res.data.shipsGoSouthArray[idx].direction_count !== '0') {
                goNorthArr.push(res.data.shipsGoNorthArray[idx])
                goSouthArr.push(res.data.shipsGoSouthArray[idx])
              }
            })
          }
          this.resetDirect(goNorthArr, 0)
          this.resetDirect(goSouthArr, 1)
          // 空/重载占比
          res.data.heaveAndLightRatioArray.forEach(item => {
            this.pieData3.push({
              value: parseFloat(item.mile_rate).toFixed(2),
              name: item.mile_rate_name
            })
          })
          // 重、空载航次对比数据
          res.data.heaveMileRateAverage.forEach(item => {
            if (!item.ship_name.includes('万华') && item.heave_mile_rate_average !== '0') { // 剔除万华8和值为0内容
              this.lineData3.xAxis.push(item.ship_name)
              this.lineData3.data.push(item.heave_mile_rate_average)
            }
          })
        }
      })
    },
    // 航航占比折线
    resetVoyageDis (data, backIdx) {
      data.forEach(item => {
        if (!item.ship_name.includes('万华')) { // 剔除万华8
          if (this.lineData1.xAxis.includes(item.ship_name)) {
            let _curIdx = this.lineData1.xAxis.findIndex(list => { return item.ship_name === list.ship_name })
            if (_curIdx > 0 && this.lineData1.xAxis.length > 1) { // 判断是否有数据  无数据补0
              this.lineData1.xAxis.forEach((item, idx) => {
                if (idx < _curIdx) {
                  this.lineData1.data[backIdx][idx] = 0
                } else {
                  this.lineData1.data[_curIdx] = item.count_level
                }
              })
            } else {
              this.lineData1.data[backIdx].push(item.count_level)
            }
          } else {
            this.lineData1.xAxis.push(item.ship_name)
            this.lineData1.data[backIdx].push(item.count_level)
          }
        }
      })
    },
    // 南上北下折线占比
    resetDirect (data, backIdx) {
      data.forEach(item => {
        if (!item.ship_name.includes('万华')) { // 剔除万华8
          if (this.lineData2.xAxis.includes(item.ship_name)) {
            let _curIdx = this.lineData2.xAxis.findIndex(list => { return item.ship_name === list.ship_name })
            if (_curIdx > 0 && this.lineData2.xAxis.length > 1) { // 判断是否有数据  无数据补0
              this.lineData2.xAxis.forEach((item, idx) => {
                if (idx < _curIdx) {
                  this.lineData2.data[backIdx][idx] = 0
                } else {
                  this.lineData2.data[_curIdx] = item.direction_count
                }
              })
            } else {
              this.lineData2.data[backIdx].push(item.direction_count)
            }
          } else {
            this.lineData2.xAxis.push(item.ship_name)
            this.lineData2.data[backIdx].push(item.direction_count)
          }
        }
      })
    },
    // 航程占比显示
    showVoyageProportion () {
      this.voyageModal.modal = true
    },
    // 南上北下显示
    showVoyageNorthsouth () {
      this.northsouthModal.modal = true
    },
    // 重/空载率显示
    showVoyageWightLoad () {
      this.weightModal.modal = true
    }
  },
  created () {
    // this.getList()
    this.getSysDate()
  }
}
</script>
<style lang="less" scoped>
  .btn-area {
    text-align: right;
    margin-bottom: 10px;
    .month-select {
      margin-right: 32px;
    }
    .morebtn {
      right: 25px;
      top: 20px;
      position: absolute;
      font-size: 12px;
      color: #666;
      cursor: pointer;
      font-family: Arial, Helvetica, sans-serif;
    }
  }
  .morebtn {
    right: 55px;
    top: 55px;
    position: absolute;
    font-size: 12px;
    color: rgb(16, 154, 228);
    cursor: pointer;
    font-family: Arial, Helvetica, sans-serif;
  }
</style>
