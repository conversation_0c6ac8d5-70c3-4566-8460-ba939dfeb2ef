<template>
  <div class="statistics-detail">
    <div class="stats-tabs">
      <Tabs v-model="activeTab" @on-click="handleTabChange">
        <TabPane label="月度" name="monthly">
          <div class="stats-charts">
            <div class="chart-wrapper">
              <h3>月度货运量统计 (吨)</h3>
              <div ref="monthlyVolumeChart" class="stats-chart"></div>
            </div>
            <div class="chart-wrapper">
              <h3>月度营业额统计 (万元)</h3>
              <div ref="monthlyRevenueChart" class="stats-chart"></div>
            </div>
            <div class="chart-wrapper">
              <h3>月度利润统计 (万元)</h3>
              <div ref="monthlyProfitChart" class="stats-chart"></div>
            </div>
          </div>
        </TabPane>
        <TabPane label="季度" name="quarterly">
          <div class="stats-charts">
            <div class="chart-wrapper">
              <h3>季度货运量统计 (吨)</h3>
              <div ref="quarterlyVolumeChart" class="stats-chart"></div>
            </div>
            <div class="chart-wrapper">
              <h3>季度营业额统计 (万元)</h3>
              <div ref="quarterlyRevenueChart" class="stats-chart"></div>
            </div>
            <div class="chart-wrapper">
              <h3>季度利润统计 (万元)</h3>
              <div ref="quarterlyProfitChart" class="stats-chart"></div>
            </div>
          </div>
        </TabPane>
        <TabPane label="年度" name="yearly">
          <div class="stats-charts">
            <div class="chart-wrapper">
              <h3>年度货运量统计 (吨)</h3>
              <div ref="yearlyVolumeChart" class="stats-chart"></div>
            </div>
            <div class="chart-wrapper">
              <h3>年度营业额统计 (万元)</h3>
              <div ref="yearlyRevenueChart" class="stats-chart"></div>
            </div>
            <div class="chart-wrapper">
              <h3>年度利润统计 (万元)</h3>
              <div ref="yearlyProfitChart" class="stats-chart"></div>
            </div>
          </div>
        </TabPane>
      </Tabs>
    </div>
    
    <Divider />
    
    <div class="data-tables">
      <Tabs>
        <TabPane label="按船舶类型">
          <Table :columns="shipTypeColumns" :data="shipTypeData" />
        </TabPane>
        <TabPane label="按货物类型">
          <Table :columns="cargoTypeColumns" :data="cargoTypeData" />
        </TabPane>
        <TabPane label="按航线">
          <Table :columns="routeColumns" :data="routeData" />
        </TabPane>
      </Tabs>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: 'StatisticsDetail',
  props: {
    statistics: {
      type: Object,
      default: () => ({
        months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
        volumeData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
        revenueData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
        profitData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
        quarters: ['Q1', 'Q2', 'Q3', 'Q4'],
        quarterlyVolumeData: [0, 0, 0, 0],
        quarterlyRevenueData: [0, 0, 0, 0],
        quarterlyProfitData: [0, 0, 0, 0],
        years: ['2020', '2021', '2022', '2023'],
        yearlyVolumeData: [0, 0, 0, 0],
        yearlyRevenueData: [0, 0, 0, 0],
        yearlyProfitData: [0, 0, 0, 0]
      })
    }
  },
  data() {
    return {
      activeTab: 'monthly',
      charts: {
        monthly: [],
        quarterly: [],
        yearly: []
      },
      shipTypeColumns: [
        { title: '船舶类型', key: 'type' },
        { title: '货运量(吨)', key: 'volume' },
        { title: '营业额(万元)', key: 'revenue' },
        { title: '利润(万元)', key: 'profit' },
        { title: '占比(%)', key: 'percentage' }
      ],
      cargoTypeColumns: [
        { title: '货物类型', key: 'type' },
        { title: '货运量(吨)', key: 'volume' },
        { title: '营业额(万元)', key: 'revenue' },
        { title: '利润(万元)', key: 'profit' },
        { title: '占比(%)', key: 'percentage' }
      ],
      routeColumns: [
        { title: '航线', key: 'route' },
        { title: '货运量(吨)', key: 'volume' },
        { title: '营业额(万元)', key: 'revenue' },
        { title: '利润(万元)', key: 'profit' },
        { title: '占比(%)', key: 'percentage' }
      ],
      shipTypeData: [],
      cargoTypeData: [],
      routeData: [],
      defaultMonths: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
      defaultQuarters: ['Q1', 'Q2', 'Q3', 'Q4'],
      defaultYears: ['2020', '2021', '2022', '2023']
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initMonthlyCharts();
    });
    
    // 监听窗口大小变化，调整图表大小
    window.addEventListener('resize', this.resizeAllCharts);
  },
  beforeDestroy() {
    // 清除事件监听
    window.removeEventListener('resize', this.resizeAllCharts);
    
    // 销毁所有图表实例
    Object.keys(this.charts).forEach(period => {
      this.charts[period].forEach(chart => {
        if (chart && !chart.isDisposed()) {
          chart.dispose();
        }
      });
    });
  },
  methods: {
    handleTabChange(name) {
      // 切换标签页时初始化相应图表
      if (name === 'monthly' && this.charts.monthly.length === 0) {
        this.initMonthlyCharts();
      } else if (name === 'quarterly' && this.charts.quarterly.length === 0) {
        this.initQuarterlyCharts();
      } else if (name === 'yearly' && this.charts.yearly.length === 0) {
        this.initYearlyCharts();
      }
      
      // 重新调整当前标签页的图表大小
      this.$nextTick(() => {
        this.resizeCharts(name);
      });
    },
    
    resizeAllCharts() {
      // 调整所有已初始化的图表大小
      Object.keys(this.charts).forEach(period => {
        this.charts[period].forEach(chart => {
          if (chart && !chart.isDisposed()) {
            chart.resize();
          }
        });
      });
    },
    
    resizeCharts(period) {
      // 调整指定周期的图表大小
      if (this.charts[period]) {
        this.charts[period].forEach(chart => {
          if (chart && !chart.isDisposed()) {
            chart.resize();
          }
        });
      }
    },
    
    getMonthlyData() {
      return {
        months: this.statistics.months || this.defaultMonths,
        volumeData: this.statistics.volumeData || Array(12).fill(0),
        revenueData: this.statistics.revenueData || Array(12).fill(0),
        profitData: this.statistics.profitData || Array(12).fill(0)
      };
    },
    
    getQuarterlyData() {
      return {
        quarters: this.statistics.quarters || this.defaultQuarters,
        volumeData: this.statistics.quarterlyVolumeData || Array(4).fill(0),
        revenueData: this.statistics.quarterlyRevenueData || Array(4).fill(0),
        profitData: this.statistics.quarterlyProfitData || Array(4).fill(0)
      };
    },
    
    getYearlyData() {
      return {
        years: this.statistics.years || this.defaultYears,
        volumeData: this.statistics.yearlyVolumeData || Array(4).fill(0),
        revenueData: this.statistics.yearlyRevenueData || Array(4).fill(0),
        profitData: this.statistics.yearlyProfitData || Array(4).fill(0)
      };
    },
    
    initMonthlyCharts() {
      // 清空现有图表
      this.charts.monthly = [];
      
      const monthlyData = this.getMonthlyData();
      
      // 月度货运量图表
      if (this.$refs.monthlyVolumeChart) {
        const chart = echarts.init(this.$refs.monthlyVolumeChart);
        const option = {
          grid: {
            top: 30,
            right: 40,
            bottom: 30,
            left: 60,
            containLabel: true
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          xAxis: {
            type: 'category',
            data: monthlyData.months,
            axisLabel: {
              color: '#333',
              interval: 0,
              rotate: 30
            },
            axisLine: {
              lineStyle: {
                color: '#ccc'
              }
            }
          },
          yAxis: {
            type: 'value',
            name: '吨',
            nameTextStyle: {
              color: '#666'
            },
            axisLabel: {
              color: '#666'
            },
            axisLine: {
              lineStyle: {
                color: '#ccc'
              }
            },
            splitLine: {
              lineStyle: {
                color: '#eee'
              }
            }
          },
          series: [{
            name: '货运量',
            type: 'bar',
            barWidth: '40%',
            data: monthlyData.volumeData,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#83bff6' },
                { offset: 1, color: '#188df0' }
              ])
            },
            emphasis: {
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#188df0' },
                  { offset: 1, color: '#83bff6' }
                ])
              }
            }
          }]
        };
        chart.setOption(option);
        this.charts.monthly.push(chart);
      }
      
      // 月度营业额图表
      if (this.$refs.monthlyRevenueChart) {
        const chart = echarts.init(this.$refs.monthlyRevenueChart);
        const option = {
          grid: {
            top: 30,
            right: 40,
            bottom: 30,
            left: 60,
            containLabel: true
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          xAxis: {
            type: 'category',
            data: monthlyData.months,
            axisLabel: {
              color: '#333',
              interval: 0,
              rotate: 30
            },
            axisLine: {
              lineStyle: {
                color: '#ccc'
              }
            }
          },
          yAxis: {
            type: 'value',
            name: '万元',
            nameTextStyle: {
              color: '#666'
            },
            axisLabel: {
              color: '#666'
            },
            axisLine: {
              lineStyle: {
                color: '#ccc'
              }
            },
            splitLine: {
              lineStyle: {
                color: '#eee'
              }
            }
          },
          series: [{
            name: '营业额',
            type: 'bar',
            barWidth: '40%',
            data: monthlyData.revenueData,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#ff9a9e' },
                { offset: 1, color: '#F76B1C' }
              ])
            },
            emphasis: {
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#F76B1C' },
                  { offset: 1, color: '#ff9a9e' }
                ])
              }
            }
          }]
        };
        chart.setOption(option);
        this.charts.monthly.push(chart);
      }
      
      // 月度利润图表
      if (this.$refs.monthlyProfitChart) {
        const chart = echarts.init(this.$refs.monthlyProfitChart);
        const option = {
          grid: {
            top: 30,
            right: 40,
            bottom: 30,
            left: 60,
            containLabel: true
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          xAxis: {
            type: 'category',
            data: monthlyData.months,
            axisLabel: {
              color: '#333',
              interval: 0,
              rotate: 30
            },
            axisLine: {
              lineStyle: {
                color: '#ccc'
              }
            }
          },
          yAxis: {
            type: 'value',
            name: '万元',
            nameTextStyle: {
              color: '#666'
            },
            axisLabel: {
              color: '#666'
            },
            axisLine: {
              lineStyle: {
                color: '#ccc'
              }
            },
            splitLine: {
              lineStyle: {
                color: '#eee'
              }
            }
          },
          series: [{
            name: '利润',
            type: 'bar',
            barWidth: '40%',
            data: monthlyData.profitData,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#84fab0' },
                { offset: 1, color: '#4CAF50' }
              ])
            },
            emphasis: {
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#4CAF50' },
                  { offset: 1, color: '#84fab0' }
                ])
              }
            }
          }]
        };
        chart.setOption(option);
        this.charts.monthly.push(chart);
      }
    },
    
    initQuarterlyCharts() {
      // 清空现有图表
      this.charts.quarterly = [];
      
      const quarterlyData = this.getQuarterlyData();
      
      // 季度图表初始化代码
      // 类似月度图表的实现，但使用季度数据
      // ...
    },
    
    initYearlyCharts() {
      // 清空现有图表
      this.charts.yearly = [];
      
      const yearlyData = this.getYearlyData();
      
      // 年度图表初始化代码
      // 类似月度图表的实现，但使用年度数据
      // ...
    }
  }
};
</script>

<style lang="less" scoped>
.statistics-detail {
  width: 100%;
  height: 100%;
  
  .stats-tabs {
    height: 100%;
    
    .stats-charts {
      display: flex;
      flex-direction: column;
      gap: 20px;
      padding: 10px 0;
      
      .chart-wrapper {
        width: 100%;
        background: #fff;
        border-radius: 4px;
        padding: 15px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        
        h3 {
          margin-top: 0;
          margin-bottom: 15px;
          font-size: 16px;
          color: #333;
          text-align: center;
        }
        
        .stats-chart {
          width: 100%;
          height: 300px;
        }
      }
    }
  }
}

// 确保图表容器在模态框中正确显示
:deep(.ivu-tabs-content) {
  height: auto !important;
}

:deep(.ivu-tabs-tabpane) {
  padding: 0 !important;
}
</style> 