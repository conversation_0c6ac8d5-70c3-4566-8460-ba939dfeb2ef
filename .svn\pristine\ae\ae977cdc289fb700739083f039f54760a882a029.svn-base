<template>
  <div>
    <!-- 福炼船舶模板 -->
    <Table border :columns="fuLianTemplateColumns" :data="templateData" :max-height="`${winHeight}`" class="fuliantable"></Table>
  </div>
</template>
<script>
export default {
  props: {
    templateData: Array
  },
  data () {
    return {
      winHeight: 500,
      fuLianTemplateColumns: [
        {
          title: '序号',
          type: 'index',
          align: 'center',
          width: 65,
          fixed: 'left'
        },
        {
          title: '船名',
          key: 'ship_name',
          align: 'center',
          width: 95,
          fixed: 'left'
        },
        {
          title: '航次',
          key: 'voyage_no',
          align: 'center',
          width: 75,
          fixed: 'left'
        },
        {
          title: '品名',
          key: 'goods_name',
          align: 'center',
          width: 70,
          fixed: 'left'
        },
        {
          title: '数量',
          key: 'amounts',
          align: 'center',
          width: 70,
          fixed: 'left'
        },
        {
          title: '卸货港',
          key: 'unload_port_name',
          align: 'center',
          width: 80,
          fixed: 'left'
        },
        {
          title: '装货港',
          align: 'center',
          children: [
            {
              title: '流量计',
              key: 'load_flowmeter_amount',
              align: 'center',
              width: 80
            },
            {
              title: '船板量',
              key: 'load_ship_amount',
              align: 'center',
              width: 80
            },
            {
              title: '预抵时',
              key: 'expect_load_port_date',
              align: 'center',
              width: 80
            },
            {
              title: '抵锚时',
              key: 'cast_load_date',
              align: 'center',
              width: 80
            },
            {
              title: '靠泊计划',
              key: 'berth_load_date',
              align: 'center',
              width: 80
            },
            {
              title: '靠泊时',
              key: 'ladder_load_date',
              align: 'center',
              width: 80
            },
            {
              title: '装货时',
              key: 'start_load_date',
              align: 'center',
              width: 80
            },
            {
              title: '装妥时',
              key: 'end_load_date',
              align: 'center',
              width: 80
            },
            {
              title: '离泊时',
              key: 'leave_load_date',
              align: 'center',
              width: 80
            },
            {
              title: '速率T/H',
              key: 'load_current_speed',
              align: 'center',
              width: 80
            }
          ]
        },
        {
          title: '卸货港',
          align: 'center',
          children: [
            {
              title: '船板量',
              key: 'unload_ship_amount',
              align: 'center',
              width: 80
            },
            {
              title: '靠泊时',
              key: 'ladder_unload_date',
              align: 'center',
              width: 80
            },
            {
              title: '离泊时',
              key: 'leave_unload_date',
              align: 'center',
              width: 80
            }
          ]
        },
        {
          title: '前三载货',
          align: 'center',
          children: [
            {
              title: '前一载',
              key: 'one_goods_name',
              align: 'center',
              width: 85
            },
            {
              title: '前二载',
              key: 'two_goods_name',
              align: 'center',
              width: 85
            },
            {
              title: '前三载',
              key: 'three_goods_name',
              align: 'center',
              width: 85
            }
          ]
        },
        {
          title: '载重吨',
          key: 'deadweight_tonnage',
          align: 'center',
          width: 95
        },
        {
          title: '船上电话',
          key: 'shipper_phone',
          align: 'center',
          width: 100
        },
        {
          title: '发票抬头',
          key: 'invoice',
          align: 'center',
          width: 100
        }
      ]
    }
  },
  mounted () {
    // 挂载浏览器高度获取方法
    const that = this
    that.winHeight = window.innerHeight - 280
    window.onresize = () => {
      return (() => {
        that.winHeight = window.innerHeight - 280
      })()
    }
  }
}
</script>
<style>
  .fuliantable .ivu-table-cell {
    padding-left: 8px;
    padding-right: 8px;
  }
</style>
