<template>
  <div>
    <Drawer
      v-model="formModal"
      :title="title"
      width="1000"
      @on-visible-change="visibleChange">
      <Table border :loading="listLoading" :columns="columns" :data="historyList" @on-row-click="handleDetail" class="alignTable"></Table>
      <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="listQuery.pageSize" :current.sync="listCurrent"
          :total="total" prev-text="< 上一页" next-text="下一页 >"
          @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
      <!-- <Page :total="total" :current.sync="listCurrent" :page-size-opts='[5, 10, 15, 20]' show-sizer :styles="{margin:'15px -10px 0 0',textAlign: 'right'}" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/> -->
    </Drawer>
    <!-- 弹窗信息 -->
    <Modal
      v-model="playFormModal"
      :title="playFormTitle" :mask-closable="dialogType==='detail'"
      @on-cancel="closeModal" width="800px">
      <Form ref="playFormValidate" :model="playFormValidate" :rules="playRuleValidate" :label-width="70" inline class="formMaxHight">
        <Row>
          <Col span="7">
            <FormItem label="船名" prop="ship_name" style="width: 100%">
              <Input v-model="playFormValidate.ship_name" readonly></Input>
            </FormItem>
          </Col>
          <Col span="7" offset="1">
            <FormItem label="航次" prop="voyage_no" style="width: 100%">
              <Input v-model="playFormValidate.voyage_no" readonly></Input>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="7">
            <FormItem label="港口" prop="port_id" style="width: 100%">
              <Select v-model="playFormValidate.port_id" v-show="dialogType!=='detail'" filterable @on-change="portSelector">
                <Option v-for="(item, index) in portNameList" :key="index" :value="item.id">{{ item.port_name }}</Option>
              </Select>
              <Input v-show="dialogType==='detail'" v-model="playFormValidate.port_name" readonly></Input>
            </FormItem>
          </Col>
          <Col span="7" offset="1">
            <FormItem label="码头" prop="wharf_id" style="width: 100%">
              <Select v-model="playFormValidate.wharf_id" v-show="dialogType!=='detail'" filterable @on-change="wharfSelector" :disabled="showWharf">
                <Option v-for="(item, index) in wharfNameList" :key="'wharf'+index" :value="item.value">{{ item.label }}</Option>
              </Select>
              <Input v-show="dialogType==='detail'" v-model="playFormValidate.wharf_name" readonly></Input>
            </FormItem>
          </Col>
          <Col span="7" offset="1">
            <FormItem label="泊位" prop="berth_id" style="width: 100%">
              <Select v-model="playFormValidate.berth_id" v-show="dialogType!=='detail'" filterable :disabled="showBerth">
                <Option v-for="(item, index) in berthNameList" :key="'berth'+index" :value="item.value">{{ item.label }}</Option>
              </Select>
              <Input v-show="dialogType==='detail'" v-model="playFormValidate.berth_name" readonly></Input>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="7">
            <FormItem label="计划时间" prop="plan_date" style="width:100%">
              <DatePicker type="datetime" format="yyyy-MM-dd HH:mm" :value="playFormValidate.plan_date" @on-change="(datetime) =>{ changePlanDate(datetime)}" @on-ok="handleok" v-show="dialogType!=='detail'"></DatePicker>
              <Input v-model="playFormValidate.plan_date" readonly v-show="dialogType==='detail'"></Input>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="23">
            <FormItem label="备注" prop="remark" style="width: 100%">
              <Input type="textarea" :autosize="true" v-model="playFormValidate.remark" :rows="4" :readonly="dialogType=='detail'"></Input>
            </FormItem>
          </Col>
        </Row>
      </Form>
      <div slot="footer">
        <Button style="margin-right: 8px" @click="closeModal">取消</Button>
        <Button v-if="dialogType==='create'" type="primary" @click="createData">保存</Button>
        <Button v-if="dialogType==='update'" type="primary" @click="updateData">保存</Button>
      </div>
    </Modal>
    <!-- 推送历史弹窗 -->
    <pushHistoryDrawer ref="pushHistoryDrawer"></pushHistoryDrawer>
  </div>
</template>
<script>
import API from '@/api/berthingPlan/berthingPlan'
import pushHistoryDrawer from './pushHistoryDrawer'
import { queryPortList, queryWharfList, queryBerthList } from '@/api/basicData'

export default {
  components: {
    pushHistoryDrawer
  },
  data () {
    return {
      listLoading: false, // table loading status
      formModal: false, // 模态框显示状态
      title: '', // 模态框标题
      total: 0, // page number
      listCurrent: 1, // 当前页码
      historyList: [], // 列表数据
      listQuery: {
        voyage_id: '', // 航次id
        pageSize: 10, // 页数
        pageIndex: 1 // 页码
      },
      playFormModal: false, // 模态框显示状态
      playFormTitle: '', // 模态框标题
      dialogType: null,
      portNameList: [], // 存储港口数据
      wharfNameList: [], // 存储码头
      berthNameList: [],
      loadPortListQuery: { // 港口请求数据
        port_name: ''
      },
      loadWharfListQuery: { // 码头请求数据
      },
      loadBerthListQuery: { // 泊位请求数据
      },
      showWharf: true, // 码头不可编辑
      showBerth: true, // 泊位不可编辑
      playFormValidate: {
        ship_name: '',
        voyage_no: '',
        port_id: '',
        wharf_id: '',
        berth_id: '',
        plan_date: '',
        remark: ''
      },
      playRuleValidate: {
        ship_name: [{ required: true, message: '此处不能为空', trigger: 'change' }],
        voyage_no: [{ required: true, message: '此处不能为空', trigger: 'change' }],
        port_id: [{ required: true, message: '此处不能为空', trigger: 'change' }],
        wharf_id: [{ required: true, message: '此处不能为空', trigger: 'change' }],
        berth_id: [{ required: true, message: '此处不能为空', trigger: 'change' }],
        plan_date: [{ required: true, message: '此处不能为空', trigger: 'change' }]
      },
      columns: [
        {
          title: '靠泊信息',
          key: 'send_user_name',
          align: 'center',
          render: (h, params) => {
            // if (params.row.result === '1') {
            //   return [h('img', {
            //     style: {
            //       position: 'absolute',
            //       width: '42px',
            //       height: '42px',
            //       left: '0',
            //       marginTop: '-37px'
            //     },
            //     attrs: {
            //       src: require('../../assets/images/voy.png')
            //     }
            //   }), h('div', {}, `${params.row.port_name}${params.row.wharf_name}${params.row.berth_name}`)]
            // } else {
            return h('div', {}, `${params.row.port_name}${params.row.wharf_name}${params.row.berth_name}`)
            // }
          }
        },
        {
          title: '预计靠泊时间',
          key: 'plan_date',
          align: 'center'
        },
        {
          title: '最新发送时间',
          key: 'send_date',
          align: 'center'
        },
        {
          title: '操作',
          key: 'push_result_name',
          align: 'center',
          width: 160,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                style: {
                  display: params.row.send_num === '0' && params.row.voyage_status !== '3' ? 'inline-block' : 'none',
                  margin: '4px',
                  border: 'none'
                },
                props: {
                  icon: 'md-brush',
                  size: 'small'
                },
                on: {
                  click: (e) => {
                    e.stopPropagation()
                    this.handleUpdate(params.row)
                  }
                }
              }, '修改'),
              h('Button', {
                style: {
                  display: params.row.send_num === '0' && params.row.voyage_status !== '3' ? 'inline-block' : 'none',
                  margin: '4px',
                  border: 'none'
                },
                props: {
                  icon: 'ios-send',
                  size: 'small'
                },
                on: {
                  click: (e) => {
                    e.stopPropagation()
                    this.sendVoyageHandle(params.row.id)
                  }
                }
              }, '发送'),
              h('Button', {
                style: {
                  display: params.row.send_num !== '0' && params.row.voyage_status !== '3' ? 'inline-block' : 'none',
                  margin: '4px',
                  border: 'none'
                },
                props: {
                  icon: 'ios-send',
                  size: 'small'
                },
                on: {
                  click: (e) => {
                    e.stopPropagation()
                    this.sendAgainVoyage(params.row.id)
                  }
                }
              }, '再次发送'),
              h('Button', {
                style: {
                  display: params.row.send_num === '0' ? 'inline-block' : 'none', // 推送过就不能删除记录
                  border: 'none',
                  margin: '4px'
                },
                props: {
                  icon: 'md-trash',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleDelete(params.row)
                  }
                }
              }, '删除'),
              h('Button', {
                style: {
                  display: params.row.send_num !== '0' ? 'inline-block' : 'none',
                  border: 'none',
                  margin: '4px'
                },
                props: {
                  icon: 'md-book',
                  size: 'small'
                },
                on: {
                  click: (e) => {
                    e.stopPropagation()
                    this.handlePushHistory(params.row)
                  }
                }
              }, '推送历史')
            ])
          }
        }
      ]
    }
  },
  methods: {
    visibleChange (val) {
      if (val) {
        this.getList()
      }
    },
    getList () {
      API.berthplanByVoyageIdList(this.listQuery).then(res => {
        if (res.data.Code === 10000) {
          this.historyList = res.data.Result
          this.total = res.data.Total
        } else {
          this.$Message.error(this.data.Message)
        }
      })
    },
    // 获取码头
    portSelector () {
      this.showWharf = false
      this.loadWharfListQuery.port_id = this.playFormValidate.port_id
      this.wharfNameList = []
      queryWharfList(this.loadWharfListQuery).then(response => {
        if (response.data.Code === 10000) {
          response.data.Result.map(item => {
            this.wharfNameList.push({
              value: item.id,
              label: item.terminal_name
            })
          })
        }
      })
    },
    // 获取泊位
    wharfSelector () {
      this.showBerth = false
      this.loadBerthListQuery.terminal_id = this.playFormValidate.wharf_id
      this.berthNameList = []
      queryBerthList(this.loadBerthListQuery).then(response => {
        if (response.data.Code === 10000) {
          response.data.Result.map(item => {
            this.berthNameList.push({
              value: item.id,
              label: item.berth_name
            })
          })
        }
      })
    },
    // 查看详情
    handleDetail (row) {
      this.formModalState('detail', row)
    },
    // 修改
    handleUpdate (row) {
      this.formModalState('update', row)
    },
    // 删除
    handleDelete (d) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>是否删除这条内容？</p>',
        loading: true,
        onOk: () => {
          let data = {
            'id': d.id
          }
          API.berthplanDelete(data).then((response) => {
            if (response.data.Code === 10000) {
              this.$Message.success(response.data.Message)
              this.getList()
              this.$Modal.remove()
              this.loading = false
            } else {
              this.$Message.error(response.data.Message)
              this.$Modal.remove()
              this.loading = false
            }
          })
        }
      })
    },
    // 发送
    sendVoyageHandle (id) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>是否发送靠泊计划？</p>',
        loading: true,
        onOk: () => {
          API.sendBerthPlan({ id: id }).then(response => {
            if (response.data.Code === 10000) {
              this.$Message.success(response.data.Message)
              this.$Modal.remove()
              this.loading = false
              this.getList()
            } else {
              this.$Message.error(response.data.Message)
              this.$Modal.remove()
              this.loading = false
            }
          })
        }
      })
    },
    // 再次发送
    sendAgainVoyage (id) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>是否再次发送靠泊计划？</p>',
        loading: true,
        onOk: () => {
          API.sendAgainBerthPlan({ id: id }).then(response => {
            if (response.data.Code === 10000) {
              this.$Message.success(response.data.Message)
              this.$Modal.remove()
              this.loading = false
              this.getList()
            } else {
              this.$Message.error(response.data.Message)
              this.$Modal.remove()
              this.loading = false
            }
          })
        }
      })
    },
    // 推送历史
    handlePushHistory (d) {
      this.$refs.pushHistoryDrawer.formModal = true
      this.$refs.pushHistoryDrawer.title = '靠泊计划推送历史'
      this.$refs.pushHistoryDrawer.listQuery.key_type = 2
      this.$refs.pushHistoryDrawer.listQuery.key_id = d.id
    },
    // 开启组件
    formModalState (d, row) {
      this.dialogType = d
      this.playFormValidate.voyage_id = row.voyage_id
      this.playFormValidate.voyage_no = row.voyage_no
      this.playFormValidate.ship_id = row.ship_id
      this.playFormValidate.ship_name = row.ship_name
      this.clearData()
      // 获取港口
      queryPortList(this.loadPortListQuery).then(response => {
        if (response.data.Code === 10000) {
          response.data.Result.map(item => {
            this.portNameList.push(item)
          })
        }
      })
      if (d === 'create') {
        this.playFormTitle = '新增'
      } else if (d === 'detail') {
        this.playFormValidate = Object.assign({}, row)
        this.playFormTitle = '查看'
      } else {
        this.playFormValidate = Object.assign({}, row)
        this.playFormTitle = '修改'
        this.wharfNameList.push({ // 码头回显
          value: row.wharf_id,
          label: row.wharf_name
        })
        this.berthNameList.push({ // 泊位回显
          value: row.berth_id,
          label: row.berth_name
        })
        if (this.playFormValidate.port_id !== '') {
          this.showWharf = false
        } else {
          this.showWharf = true
        }
        if (this.playFormValidate.wharf_id !== '') {
          this.showBerth = false
        } else {
          this.showBerth = true
        }
      }
      this.playFormModal = true
    },
    // 日期回显
    changePlanDate (dateTime) {
      this.playFormValidate.plan_date = dateTime
    },
    handleok () {
      let d = new Date()
      if (this.playFormValidate.plan_date === '') {
        this.playFormValidate.plan_date = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate() + ' ' + d.getHours() + ':' + d.getMinutes()
      }
    },
    // 编辑保存
    updateData () {
      this.$refs['playFormValidate'].validate((valid) => {
        if (valid) {
          this.$Modal.confirm({
            title: '提示',
            content: '<p>是否修改靠泊计划？</p>',
            loading: true,
            onOk: () => {
              let data = Object.assign({}, this.playFormValidate)
              API.berthplantUpdate(data).then((response) => {
                if (response.data.Code === 10000) {
                  this.$Message.success(response.data.Message)
                  this.closeModal()
                  this.getList()
                  this.$Modal.remove()
                } else {
                  this.formModal = true
                  this.$Message.error(response.data.Message)
                  this.$Modal.remove()
                }
              })
            }
          })
        }
      })
    },
    // 关闭模态框
    closeModal () {
      this.playFormModal = false
      this.$nextTick(() => {
        this.$refs['playFormValidate'].resetFields()
      })
      this.clearData()
    },
    clearData () {
      this.showWharf = true
      this.showBerth = true
      this.playFormValidate.wharf_name = ''
      this.playFormValidate.port_name = ''
      this.playFormValidate.berth_name = ''
      this.playFormValidate.berth_id = ''
      this.playFormValidate.port_id = ''
      this.playFormValidate.wharf_id = ''
      this.playFormValidate.plan_date = ''
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    }
  }
}
</script>
