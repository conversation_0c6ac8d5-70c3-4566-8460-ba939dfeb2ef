<template>
  <Drawer
    v-model="modalData.modal"
    title="详情"
    width="820"
    :mask-closable="false"
    @on-visible-change="modalShow">
      <Tabs type="card" v-model="tabName" name="tab1">
        <TabPane label="企业信息" name="userInfo" tab="tab1">
          <Form ref="formData" :model="formData" :label-width="85">
            <Row>
              <Col span="11">
                <FormItem label="公司名称" prop="name">
                  <Input v-model="formData.name" :readonly="readonly"></Input>
                </FormItem>
                <FormItem label="公司类型">
                  <Input v-model="formData.company_type_name" readonly></Input>
                </FormItem>
                <FormItem label="创建人">
                  <Input v-model="formData.insert_user_name" readonly></Input>
                </FormItem>
                <FormItem label="联系方式">
                  <Input v-model="formData.insert_user_mobile" readonly></Input>
                </FormItem>
                <FormItem label="创建时间">
                  <Input v-model="formData.insert_time" readonly></Input>
                </FormItem>
                <FormItem label="入驻状态">
                  <Col :span="formData.status === '0' ? 12 : 24">
                    <Input v-model="formData.status_name" readonly></Input>
                  </Col>
                  <Col span="12">
                    <Button v-if="formData.status === '0'" @click="changeAuditCompany('adopt')" class="btn_class">通过</Button>
                    <Button v-if="formData.status === '0'" @click="changeAuditCompany('refuse')" class="btn_class">拒绝</Button>
                  </Col>
                </FormItem>
                <FormItem label="是否管理员">
                  <Input v-model="formData.is_manage" readonly></Input>
                </FormItem>
                <FormItem label="备注">
                  <Input type="textarea" :autosize="true" v-model="formData.comments" :readonly="readonly"></Input>
                </FormItem>
              </Col>
              <Col span="8" offset="5">
                <Button @click="handleUpdate" style="float: right">编辑</Button>
                <FormItem style="display:none;">
                  <Input type="text" v-model='formData.business_licence_image'></Input>
                </FormItem>
                <div class="userPic" style="clear:both;">
                  <img src="@/assets/images/business.png" alt="" v-if="imgBaseUrl === ''">
                  <img :src="imgBaseUrl" alt="" v-else>
                </div>
                <Upload action=''
                  :show-upload-list='false'
                  accept=".jpg, .jpeg, .png"
                  :format="['jpg','jpeg','png']"
                  :max-size="2048"
                  :before-upload="handleImgUpload"
                  style="margin:10px 40px 0 20px;">
                    <Button v-if="!readonly">营业执照</Button>
                </Upload>
              </Col>
            </Row>
          </Form>
        </TabPane>
        <TabPane label="成员管理" name="userManage" tab="tab1">
          <companyUser :listIdData="modalData" :changeTabName="tabName"></companyUser>
        </TabPane>
        <TabPane label="船舶管理" name="shipManage" tab="tab1" v-if="company_type === '1'">
          <companyShip :listIdData="modalData" :changeTabName="tabName"></companyShip>
        </TabPane>
      </Tabs>
      <div class="demo-drawer-footer">
        <Button @click="clearData" style="margin-right:10px;">取消</Button>
        <Button type="primary" @click="updateData" v-if="!readonly">保存</Button>
      </div>
  </Drawer>
</template>
<script>
import API from '@/api/companyManagement'
import { avatarImage } from '@/api/basicData'
import companyUser from './companyUser'
import companyShip from './companyShip'

export default {
  props: {
    modalData: Object
  },
  components: {
    companyUser,
    companyShip
  },
  data () {
    return {
      imgBaseUrl: '',
      uploadData: '',
      readonly: true, // 是否可编辑
      tabName: '',
      company_type: '',
      formData: {
        id: '',
        name: '',
        company_type_name: '',
        insert_user_name: '',
        insert_user_mobile: '',
        insert_time: '',
        status: '',
        status_name: '',
        is_manage: '是',
        comments: '',
        business_licence_image: '',
        is_through: ''
      }
    }
  },
  methods: {
    // 保存企业信息
    updateData () {
      this.$refs['formData'].validate((valid) => {
        if (valid) {
          this.$Modal.confirm({
            title: '提示',
            content: '<p>是否确1认修改公司名称？</p>',
            loading: true,
            onOk: () => {
              const userModifyFn = () => {
                API.updateCompany(this.formData).then(res => {
                  if (res.data.Code === 10000) {
                    this.$Message.success(res.data.Message)
                    this.$Modal.remove()
                    this.$emit('callback')
                    this.modalData.modal = false
                  } else {
                    this.modalData.modal = true
                    this.$Message.error(res.data.Message)
                    this.$Modal.remove()
                  }
                }).catch()
              }
              if (this.uploadData === '') {
                userModifyFn()
              } else {
                avatarImage({ base64File: this.imgBaseUrl }).then(e => {
                  this.formData.business_licence_image = e.data.fileUrl
                  if (e.data.Code === -10000) {
                    this.$Message.warning(e.data.Message)
                  } else {
                    userModifyFn()
                  }
                })
              }
            }
          })
        }
      })
    },
    // 企业信息审核
    changeAuditCompany (type) {
      if (type === 'adopt') {
        this.formData.is_through = '1' // 1.审核通过，0审核
      } else {
        this.formData.is_through = '0'
      }
      let data = {
        id: this.formData.id,
        is_through: this.formData.is_through
      }
      API.auditCompany(data).then(res => {
        if (res.data.Code === 10000) {
          this.$Message.success(res.data.Message)
          this.$emit('callback')
          this.$nextTick(() => {
            this.formData.status = type === 'adopt' ? '10' : '20' // 0待审核；10审核通过；20审核失败
            this.formData.status_name = type === 'adopt' ? '审核通过' : '审核失败'
          })
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 取消
    clearData () {
      this.modalData.modal = false
    },
    // 编辑
    handleUpdate (row) {
      this.readonly = false
    },
    // 模态框展示
    modalShow (val) {
      if (val) {
        this.tabName = 'userInfo'
        this.company_type = this.modalData.data.company_type
        this.formData = {
          id: this.modalData.data.id,
          name: this.modalData.data.name,
          company_type_name: this.modalData.data.company_type_name,
          insert_user_name: this.modalData.data.insert_user_name,
          insert_user_mobile: this.modalData.data.insert_user_mobile,
          insert_time: this.modalData.data.insert_time,
          status_name: this.modalData.data.status_name,
          status: this.modalData.data.status,
          is_manage: this.modalData.data.passport_id === this.modalData.data.insert_user_id ? '是' : '否',
          comments: this.modalData.data.comments,
          business_licence_image: this.modalData.data.business_licence_image
        }
        this.imgBaseUrl = this.modalData.data.business_licence_image !== 'business.png' ? this.modalData.data.business_licence_image : business
      } else {
        this.imgBaseUrl = ''
        this.$nextTick(() => {
          this.formData = {}
          this.readonly = true
          this.$refs['formData'].resetFields()
        })
      }
    },
    // 上传图片
    handleImgUpload (file) {
      const isLt2M = file.size / 1024 / 1024 < 2
      const fileExt = file.name.split('.').pop().toLocaleLowerCase()
      if (!isLt2M) {
        this.$Message.warning('附件过大，附件最大2M')
      } else {
        if (fileExt === 'jpg' || fileExt === 'jpeg' || fileExt === 'png') {
          let that = this
          let reader = new FileReader()
          reader.readAsDataURL(file)
          reader.onload = function (e) {
            that.imgBaseUrl = e.target.result
          }
          this.uploadData = new FormData()
          this.uploadData.append('file', file)
          return false
        } else {
          this.$Message.warning(`${file.name}格式错误`)
          return false
        }
      }
    }
  }
}
</script>
<style scoped lang="less">
  .btn_class {
    margin-left: 10px;
    color: white;
    background-color: #2d8cf0;
  }
  .userPic {
    overflow: hidden;
    width: 120px;
    margin-top: 20px;
    height: 160px;
    display: flex;
    justify-content: center;
    align-items: center;
    img {
      width: 100%;
    }
  }
</style>
