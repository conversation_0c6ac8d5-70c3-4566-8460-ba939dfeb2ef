import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 获取公司名称(过滤已添加的货主公司)
export function companyOutOfSelected (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/config/msg/company/queryCompaniesOutOfSelected',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询节点列表
export function msgNodeConfigList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/config/msg/template/queryMsgTemplateList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取编辑节点列表
// export function updateMsgNodeConfigList (data) {
//   let qsData = Qs.stringify(data)
//   return axios.request({
//     url: '/config/msg/node/queryMsgNodeConfigList',
//     method: 'post',
//     headers: config.ajaxHeader,
//     data: qsData
//   })
// }

// 回显成员列表
export function companyUserConfigList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/config/msg/user/queryMsgUserConfigList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 修改&保存 节点列表 notodo
export function saveMsgNodeConfigList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/config/msg/node/updateBatchMsgNodeAndUserConfig',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  companyOutOfSelected,
  companyUserConfigList,
  msgNodeConfigList,
  // updateMsgNodeConfigList,
  saveMsgNodeConfigList
}
