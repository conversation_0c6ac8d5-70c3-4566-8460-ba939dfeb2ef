import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 获取月度运输表列表
export function queryCarriageMPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/queryCarriageMonthReportPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取左侧日期栏
export function queryReportDates (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/queryCarriageMonthReportDates',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取运输表船舶列表
export function queryReportShips (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/queryVoyageMonthReportShips',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 添加运输表
export function addCarriageReport (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/addCarriageMonthReport',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 删除运输表
export function delCarriageReport (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/delCarriageMonthReport',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  queryCarriageMPage,
  queryReportDates,
  queryReportShips,
  addCarriageReport,
  delCarriageReport
}