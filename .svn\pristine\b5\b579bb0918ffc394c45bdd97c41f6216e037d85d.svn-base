<template>
  <div>
    <!-- 作业效率 -->
    <Card>
      <div class="btn_area">
        <span>时间：</span>
        <month-select class="month-select" v-model="baseDate" @on-change="dateSelect"></month-select>
        <Button size="small" type="primary" @click="clearData" style="padding: 3px 7px">重置</Button>
      </div>
      <Row>
        <Col span="8">
          <div class="area_btn">
            <Button size="small" @click="changePortType('all')" :class="{'curType': chartParam.port_type === '3'}">全部</Button>
            <Button size="small" @click="changePortType('load')" :class="{'curType': chartParam.port_type === '1'}">装货</Button>
            <Button size="small" @click="changePortType('unload')" :class="{'curType': chartParam.port_type === '2'}">卸货</Button>
          </div>
          <div class="line-area">
            <ChartBar style="height: 300px;" unit="吨/小时" :value="anchorTimeData" @clickBack="lineBack" :clickable="true" :color="lineColor" text="作业效率"/>
          </div>
          <Spin size="large" fix v-if="spinShow">
            <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
            <div>Loading</div>
          </Spin>
        </Col>
        <Col span="16">
          <search @searchResults='searchResults' :setSearch='setSearchData' @keydown.enter.native='searchResults' @resetResults='resetResults'></search>
          <Table :data="listData" border :loading="loading" :columns="columns" class="list_table" @on-row-click="showRowDetail"></Table>
          <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.queryParam.pageSize" :current.sync="listCurrent"
          :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
        </Col>
      </Row>
    </Card>
    <OperationEfficDrawer :drawerData="efficDrawerData"></OperationEfficDrawer>
  </div>
</template>
<script>
import { ChartBar } from '_c/charts'
import search from '_c/search' // 查询组件
import MonthSelect from '@/components/monthSelect'
import OperationEfficDrawer from './drawer/operationEfficDrawer.vue'
import { queryStatTime, queryPortList, queryWharfList, queryBerthList, queryBasicCargoList } from '@/api/basicData'
import { queryEfficiencyChart, queryEfficiencyReport } from '@/api/statistics/operationEfficiency'

export default {
  components: {
    search,
    ChartBar,
    MonthSelect,
    OperationEfficDrawer
  },
  data () {
    return {
      spinShow: false,
      chartParam: {
        start_month: '',
        end_month: '',
        port_type: '3' // （3全部；1装；2卸，默认3全部）
      },
      lineColor: ['#6699FF'],
      anchorList: [], // 柱状图点击定位使用
      anchorTimeData: {
        xAxis: [],
        data: []
      },
      setSearchData: {
        port_id: {
          type: 'select',
          label: '港口',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 150,
          value: '',
          filterable: true,
          change: this.getWharf
        },
        wharf_id: {
          type: 'select',
          label: '码头',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 150,
          value: '',
          filterable: true,
          isdisabled: true,
          change: this.getBerth
        },
        berth_id: {
          type: 'select',
          label: '泊位',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 100,
          value: '',
          filterable: true,
          isdisabled: true
        },
        goods_id: {
          type: 'select',
          label: '货品',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 150,
          value: '',
          filterable: true
        },
        ship_id: {
          type: 'select',
          label: '船舶',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 110,
          value: '',
          filterable: true
        }
      },
      efficDrawerData: {
        modal: false,
        drawerParam: {}
      },
      queryParam: {
        ship_id: '',
        pageSize: 20,
        pageIndex: 1,
        port_id: '',
        wharf_id: '',
        berth_id: '',
        goods_id: '',
        start_month: '',
        end_month: ''
      },
      loading: false,
      total: null,
      listCurrent: 1,
      listData: [],
      columns: [
        {
          title: '港口',
          key: 'port_name',
          align: 'center'
        },
        {
          title: '码头',
          key: 'wharf_name',
          align: 'center'
        },
        {
          title: '泊位',
          key: 'berth_name',
          align: 'center'
        },
        {
          title: '货品',
          key: 'goods_name',
          align: 'center'
        },
        {
          title: '装货平均速率',
          key: 'load_efficiency_average',
          align: 'center',
          width: 80,
          render: (h, params) => {
            return h('div', {}, parseInt(params.row.load_efficiency_average) === 0 ? '-' : params.row.load_efficiency_average)
          }
        },
        {
          title: '装货最大速率',
          key: 'load_efficiency_max',
          align: 'center',
          width: 80,
          render: (h, params) => {
            return h('div', {}, parseInt(params.row.load_efficiency_max) === 0 ? '-' : params.row.load_efficiency_max)
          }
        },
        {
          title: '装货最小速率',
          key: 'load_efficiency_min',
          align: 'center',
          width: 80,
          render: (h, params) => {
            return h('div', {}, parseInt(params.row.load_efficiency_min) === 0 ? '-' : params.row.load_efficiency_min)
          }
        },
        {
          title: '卸货平均速率',
          key: 'unload_efficiency_average',
          align: 'center',
          width: 80,
          render: (h, params) => {
            return h('div', {}, parseInt(params.row.unload_efficiency_average) === 0 ? '-' : params.row.unload_efficiency_average)
          }
        },
        {
          title: '卸货最大速率',
          key: 'unload_efficiency_max',
          align: 'center',
          width: 80,
          render: (h, params) => {
            return h('div', {}, parseInt(params.row.unload_efficiency_max) === 0 ? '-' : params.row.unload_efficiency_max)
          }
        },
        {
          title: '卸货最小速率',
          key: 'unload_efficiency_min',
          align: 'center',
          width: 80,
          render: (h, params) => {
            return h('div', {}, parseInt(params.row.unload_efficiency_min) === 0 ? '-' : params.row.unload_efficiency_min)
          }
        }
      ]
    }
  },
  computed: {
    baseDate () {
      if (this.chartParam.start_month !== '' && this.chartParam.end_month !== '') {
        return this.chartParam.start_month + '~' + this.chartParam.end_month
      }
      if (this.chartParam.start_month !== '' && this.chartParam.end_month === '') {
        return this.chartParam.start_month
      }
      return ''
    }
  },
  created () {
    if (window.localStorage.shipNameList) {
      let shipList = JSON.parse(window.localStorage.shipNameList)
      shipList.map(item => {
        if ((item.business_model === '1' || item.business_model === '2') && !item.ship_name.includes('善')) { // 只要自营和船期船舶且不需要万邦船舶  2024/09/09调整
          this.setSearchData.ship_id.selectData.push({
            value: item.ship_id,
            label: item.ship_name
          })
        }
      })
    } else {
      this.setSearchData.ship_id.selectData = []
    }
    queryBasicCargoList().then(res => {
      if (res.data.Code === 10000) {
        res.data.Result.map(item => {
          this.setSearchData.goods_id.selectData.push({
            value: item.id,
            label: item.cargo_name
          })
        })
      }
    })
    queryPortList().then(res => {
      if (res.data.Code === 10000) {
        res.data.Result.map(item => {
          this.setSearchData.port_id.selectData.push({
            value: item.id,
            label: item.port_name
          })
        })
      }
    })
    this.getSysDate()
  },
  methods: {
    // 折线图点击触发
    lineBack (val) {
      this.setSearchData.port_id.selected = this.anchorList[val].port_id
      this.queryParam.port_id = this.anchorList[val].port_id
      this.getWharf()
      this.getList()
    },
    changePortType (type) {
      if (type === 'all') {
        this.chartParam.port_type = '3' // 3全部；1装；2卸，默认3全部）
      } else if (type === 'load') {
        this.chartParam.port_type = '1'
      } else if (type === 'unload') {
        this.chartParam.port_type = '2'
      }
      this.getChartList()
    },
    // 获取统计折线图
    getChartList () {
      this.resetChart()
      this.spinShow = true
      queryEfficiencyChart(this.chartParam).then(res => {
        if (res.data.Code === 10000) {
          this.spinShow = false
          if (res.data.mileLevelRatioArray.length > 0) { // 船舶抛锚时长
            res.data.mileLevelRatioArray.forEach(item => {
              this.anchorList.push({
                port_id: item.port_id,
                port_name: item.port_name
              })
              this.anchorTimeData.xAxis.push(item.port_name)
              this.anchorTimeData.data.push(item.operation_efficiency_average)
            })
          }
        } else {
          this.spinShow = false
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 获取报表
    getList () {
      this.loading = true
      this.queryParam.start_month = this.chartParam.start_month
      this.queryParam.end_month = this.chartParam.end_month
      queryEfficiencyReport(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          this.loading = false
          this.listData = res.data.Result
          this.total = res.data.Total
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 表格行数据详情
    showRowDetail (item) {
      this.efficDrawerData.modal = true
      this.efficDrawerData.drawerParam = {
        port_name: item.port_name,
        wharf_name: item.wharf_name,
        berth_name: item.berth_name,
        start_month: this.queryParam.start_month,
        end_month: this.queryParam.end_month,
        goods_id: item.goods_id,
        berth_id: item.berth_id,
        port_type: this.chartParam.port_type
      }
    },
    // 数据清空
    resetChart () {
      this.anchorTimeData.xAxis = []
      this.anchorTimeData.data = []
    },
    clearData () {
      this.chartParam = {
        start_month: '',
        end_month: '',
        port_type: '3'
      }
      this.getSysDate()
      this.resetResults()
    },
    // 重置报表
    resetResults () {
      this.queryParam = {
        ship_id: '',
        pageSize: 20,
        pageIndex: 1,
        port_id: '',
        wharf_id: '',
        berth_id: '',
        goods_id: '',
        start_month: '',
        end_month: ''
      }
      this.listCurrent = 1
      this.setSearchData.port_id.selected = ''
      this.setSearchData.ship_id.selected = ''
      this.setSearchData.goods_id.selected = ''
      this.getList()
    },
    // 全局查询
    searchData () {
      this.queryParam.base_year = this.chartParam.base_year
      this.getList()
      this.getChartList()
    },
    // 报表查询
    searchResults () {
      this.listCurrent = 1
      this.queryParam.port_id = this.setSearchData.port_id.selected
      this.queryParam.wharf_id = this.setSearchData.wharf_id.selected
      this.queryParam.berth_id = this.setSearchData.berth_id.selected
      this.queryParam.ship_id = this.setSearchData.ship_id.selected
      this.queryParam.goods_id = this.setSearchData.goods_id.selected
      this.getList()
    },
    // 系统年月
    getSysDate () {
      queryStatTime().then(res => {
        if (res.data.Code === 10000) {
          if (res.data.start_month && res.data.end_month) {
            this.chartParam.start_month = res.data.start_month
            this.chartParam.end_month = res.data.end_month
            this.getChartList()
            this.getList()
          }
        }
      })
      // querySysDate().then(res => {
      //   if (res.data.Code === 10000 && res.data.systemDate !== '') {
      //     let _year = res.data.systemDate.substring(0, 4)
      //     this.chartParam.start_month = _year + '-01'
      //     this.chartParam.end_month = res.data.systemDate.substring(0, 7)
      //     this.baseDate = this.chartParam.start_month + '~' + this.chartParam.end_month
      //     this.getChartList()
      //     this.getList()
      //   }
      // })
    },
    // 获取码头
    getWharf () {
      this.setSearchData.wharf_id.isdisabled = this.setSearchData.port_id.selected === undefined
      this.setSearchData.wharf_id.selectData = []
      this.setSearchData.wharf_id.selected = ''
      if (this.setSearchData.port_id.selected === undefined) return
      queryWharfList({ port_id: this.setSearchData.port_id.selected }).then(res => {
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.setSearchData.wharf_id.selectData.push({
              value: item.id,
              label: item.terminal_name
            })
          })
        }
      })
    },
    // 获取泊位
    getBerth () {
      this.setSearchData.berth_id.isdisabled = this.setSearchData.wharf_id.selected === undefined
      this.setSearchData.berth_id.selectData = []
      this.setSearchData.berth_id.selected = ''
      if (this.setSearchData.wharf_id.selected === undefined) return
      queryBerthList({ terminal_id: this.setSearchData.wharf_id.selected }).then(res => {
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.setSearchData.berth_id.selectData.push({
              value: item.id,
              label: item.berth_name
            })
          })
        }
      })
    },
    // 日期变化触发
    dateSelect (val) {
      this.chartParam.start_month = val[0]
      this.chartParam.end_month = val[1]
      this.getChartList()
      this.getList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.queryParam.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.queryParam.pageIndex = val
      this.getList()
    }
  }
}
</script>
<style lang="less" scoped>
.btn_area {
  margin-bottom: 15px;
   button {
    margin-left: 12px;
  }
}
.line-area,
.list_table {
  margin-top: 10px;
}
.area_btn {
  margin-top: 10px;
  button {
    margin-right: 15px;
    padding: 2px 15px;
  }
}
.curType {
  color: #fff;
  background-color: #2d8cf0;
}
</style>
<style lang="less">
.btn_area {
  .month-select-input .ivu-input {
    color: #515a6e;
    line-height: 30px;
    height: 30px;
    background-color: #fff;
    border: 1px solid #dcdee2;
  }
}
.ivu-poptip-popper {
  z-index: 9999 !important;
}
</style>
