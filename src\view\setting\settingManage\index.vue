<template>
  <div>
    <p slot='title' class="bold-font">短信配置</p>
    <Button v-if="!isOpen" @click="openMessage" type="primary">开通短信推送</Button>
    <Card v-if="isOpen">
      <search @searchResults='searchResults' :setSearch='setSearchData' @keydown.enter.native='searchResults' @resetResults='resetResults' class="alignTable"></search>
      <Table border :loading="listLoading" :width="800" ref="messageRef" :columns="columns" :data="list" class="alignTable"></Table>
      <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
          :total="total" prev-text="< 上一页" next-text="下一页 >"
          @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
    </Card>
    <sms-node-modify :modalData="smsModalData" @callback="smsModifyBack"></sms-node-modify>
  </div>
</template>

<script>
import API from '@/api/setting/SMSSetting'
import search from '_c/search' // 查询组件
import SmsNodeModify from './smsNodeModify'

export default {
  components: {
    search,
    SmsNodeModify
  },
  data () {
    return {
      isOpen: false, // 是否已经开通短信推送
      listLoading: true,
      smsModalData: {
        modal: false
      },
      columns: [
        {
          title: '适用节点',
          key: 'node_name',
          align: 'center'
        },
        {
          title: '操作',
          key: 'operation',
          align: 'center',
          width: 200,
          render: (h, params) => {
            return [
              h('Button', {
                style: {
                  margin: '0 4px'
                },
                props: {
                  icon: 'md-paper',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleInfo(params.row)
                  }
                }
              }, '查看'),
              h('Button', {
                style: {
                  margin: '0 4px'
                },
                props: {
                  icon: 'md-brush',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleUpdate(params.row)
                  }
                }
              }, '修改')
            ]
          }
        }
      ],
      setSearchData: {// 查询设置，对象key值为回调参数
        node_name: {
          type: 'text',
          label: '适用节点',
          width: 150,
          value: ''
        }
      },
      total: 0, // 总页数
      listCurrent: 1, // 当前页数
      list: [], // 列表
      listQuery: { // 列表请求入参
        pageSize: 10,
        pageIndex: 1,
        node_name: ''
      }
    }
  },
  created () {
    this.getList()
  },
  methods: {
    // 开通短信功能
    openMessage () {
      API.addSMSNotice().then(res => {
        if (res.data.Code === 10000) {
          this.isOpen = true
          this.getList()
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    getList () {
      // 判断是否已经开通节点状态 并获取节点列表
      API.queryNodeList(this.listQuery).then(res => {
        this.listLoading = false
        if (res.data.Code === 10000 && res.data.Result.length > 0) {
          this.isOpen = true
          this.list = res.data.Result
          this.total = res.data.Total
        }
      })
    },
    // 查看详情
    handleInfo (row) {
      this.$Modal.info({
        title: row.node_name + '节点短信样式',
        content: row.smsContent
      })
    },
    // 修改
    handleUpdate (row) {
      this.smsModalData.modal = true
      this.smsModalData.title = '短信配置修改'
      this.smsModalData.data = row
    },
    smsModifyBack () {
      this.getList()
    },
    // 查询
    searchResults (e) {
      if (e.target) delete e.target
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      Object.assign(this.listQuery, e)
      this.getList()
    },
    // 重置
    resetResults () {
      this.listCurrent = 1
      this.listQuery = Object.assign(this.listQuery, {
        pageIndex: 1,
        node_name: ''
      })
      this.setSearchData.node_name.value = ''
      this.getList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    }
  }
}
</script>

<style lang="less">
  .alignTable {
    text-align: center;
    margin: 20px auto;
  }
</style>
