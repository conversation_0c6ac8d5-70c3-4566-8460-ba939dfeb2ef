import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 获取统计总数和货品价格
export function queryStatisticSumReport (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/queryYearAmounts',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取船舶营运总览
export function queryStatisticAmountSummary (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/queryYearShipService',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取航程统计
export function queryStatisticVoyageSummary (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/queryMileAndCountGroupByMonth',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 内页统计
// 货品总量数据接口
export function queryStatisticGoodsInnerReport (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/queryAmountsGroupByMonth',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 船舶营运效率
export function queryShipServiceByMonth (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/queryShipServiceByMonth',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 航次/航程查询
export function queryStatisticVoyageInnerReport (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/inner/voyagedetail/queryVoyageDetailInnerMonthVoyageReport',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 货运量/周转量
export function queryStatisticTurnoverSummary (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/queryAmountAndTurnoverGroupByMonth',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 抛锚时长
export function queryStatisticWaitBerthMonthSummary (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/queryWaitBerthMonthSummary',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 装卸速率
export function queryStatisticRateMonthSummary (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/queryLoadAndUnloadRateMonthSummary',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

/**
 * 报表下载 预览
*/
// 货运量总体详情数据模板
export function voyageHylztxqReportTemplate (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/report/template/voyageHylztxqReportTemplate',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 货运量周转量详情 模板
export function voyageHylzzlReportTeplate (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/report/template/voyageHylzzlReportTeplate',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 航程航次详情 模板
export function voyageHchcxqReportTeplate (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/report/template/voyageHchcxqReportTeplate',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 抛锚时长-装卸速率详情 模板
export function voyagePmsczxslReportTeplate (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/report/template/voyagePmsczxslReportTeplate',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 船舶营运总览详情 模板
export function voyageCbyyzlReportTeplate (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/report/template/voyageCbyyzlReportTeplate',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 船舶营运总览详情 模板 - 可查询全部船舶
export function voyageCbyyzlReportTeplateAll (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/report/template/voyageCbyyzlReportTeplateAll',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  queryStatisticSumReport,
  queryStatisticAmountSummary,
  queryStatisticVoyageSummary,
  queryStatisticGoodsInnerReport,
  queryShipServiceByMonth,
  queryStatisticVoyageInnerReport,
  queryStatisticTurnoverSummary,
  queryStatisticWaitBerthMonthSummary,
  queryStatisticRateMonthSummary,
  voyageHylztxqReportTemplate,
  voyageHylzzlReportTeplate,
  voyageHchcxqReportTeplate,
  voyagePmsczxslReportTeplate,
  voyageCbyyzlReportTeplate,
  voyageCbyyzlReportTeplateAll
}
