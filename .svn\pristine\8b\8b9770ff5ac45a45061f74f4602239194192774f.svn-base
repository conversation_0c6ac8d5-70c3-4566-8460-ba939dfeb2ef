import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 获取靠泊计划列表
export function berthplanList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/berth/plan/queryBerthPlanPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 通过航次id获取靠泊计划
export function berthplanByVoyageIdList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/berth/plan/getBerthPlanByVoyageId',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 新增靠泊计划信息
export function berthplanAdd (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/berth/plan/addBerthPlan',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 编辑靠泊计划
export function berthplantUpdate (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/berth/plan/updateBerthPlan',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 删除靠泊计划
export function berthplanDelete (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/berth/plan/delBerthPlan',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 发送
export function sendBerthPlan (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/berth/plan/sendBerthPlan',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 再次发送
export function sendAgainBerthPlan (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/berth/plan/sendAgainBerthPlan',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 推送历史
export function queryAppPushRecordPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/push/app/queryAppPushRecordPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  berthplanList,
  berthplanByVoyageIdList,
  berthplanAdd,
  berthplantUpdate,
  berthplanDelete,
  sendBerthPlan,
  sendAgainBerthPlan,
  queryAppPushRecordPage
}
