<template>
  <div v-if="isShow">
    <ButtonGroup class="draw_box">
      <Button :disabled="isModify || isAdd" :type="toolStr === '标注' ? 'primary' : 'default'" @click="drawChange('标注')">标注</Button>
      <Button :disabled="isModify || isAdd" :type="toolStr === '折线' ? 'primary' : 'default'" @click="drawChange('折线')">折线</Button>
      <Button :disabled="isModify || isAdd" :type="toolStr === '区域' ? 'primary' : 'default'" @click="drawChange('区域')">区域</Button>
    </ButtonGroup>
    <Table :data="signList" :columns="tableColumns" @on-row-click="signClick"></Table>
    <!-- 标注点绘制弹窗 -->
    <Modal v-model="markerModalShow" :transfer="false" draggable scrollable :closable="false" title="标注定位" width="400" @on-visible-change="markerModalVisible">
      <div class="modal_area">
        <div class="modal_line_con">
          <span>图标：</span>
          <span v-for="(item, index) in markIconList" :key="index" :class="index === curIcon ? 'marker_icon cur_icon' : 'marker_icon'" @click="markerIconClick(index)">
            <Tooltip :content="item.tip" theme="light">
              <van-icon :name="item.url" size="16"/>
            </Tooltip>
          </span>
        </div>
        <div class="modal_line_con">
          <span>名称：</span>
          <Input v-model="markerName" placeholder="请输入标注的名称" style="width: 300px;"/>
        </div>
        <div>
          <div style="font-weight: bold;">操作说明：</div>
          <div style="color: #DC143C;">1、先选择图标再点击地图进行添加标注</div>
          <div style="color: #DC143C;">2、添加至地图后可对标注进行拖动操作</div>
        </div>
      </div>
      <div slot='footer'>
        <Button @click="markerCancel">取消</Button>
        <Button :disabled="!markerLayer" type="primary" @click="markerSave">保存</Button>
      </div>
    </Modal>
    <!-- 折线绘制弹窗 -->
    <Modal v-model="lineModalShow" :transfer="false" draggable scrollable :closable="false" title="折线绘制" width="400" @on-visible-change="lineModalVisible">
      <div class="modal_area">
        <!-- <div class="modal_line_con">
          <span>颜色：</span>
          <ColorPicker v-model="lineColor" alpha @on-change="lineColorChange" :transfer="false"/>
          <span>{{ lineColor }}</span>
        </div> -->
        <div class="modal_line_con">
          <span>名称：</span>
          <Input v-model="lineName" placeholder="请输入折线的名称" style="width: 300px;" @on-change="lineTextChange"/>
        </div>
        <div>
          <div style="font-weight: bold;">操作说明：</div>
          <div style="color: #DC143C;">1、点击地图可进行折线绘制，双击可结束绘制</div>
          <div style="color: #DC143C;">2、折线绘制后可拖拽点进行编辑</div>
          <div style="color: #DC143C;">3、鼠标放置折线点右键点击可删除当前节点</div>
        </div>
      </div>
      <div slot='footer'>
        <Button @click="lineCancel">取消</Button>
        <Button :disabled="!lineLayer" type="primary" @click="lineSave">保存</Button>
      </div>
    </Modal>
    <!-- 区域绘制弹窗 -->
    <Modal v-model="areaModalShow" :transfer="false" draggable scrollable :closable="false" title="区域绘制" width="400" @on-visible-change="areaModalVisible">
      <div class="modal_area">
        <!-- <div class="modal_line_con">
          <span>颜色：</span>
          <ColorPicker v-model="areaColor" alpha @on-change="areaColorChange" :transfer="false"/>
          <span>{{ areaColor }}</span>
        </div> -->
        <div class="modal_line_con">
          <span>名称：</span>
          <Input v-model="areaName" placeholder="请输入区域的名称" style="width: 300px;" @on-change="areaTextChange"/>
        </div>
        <div>
          <div style="font-weight: bold;">操作说明：</div>
          <div style="color: #DC143C;">1、点击地图可进行区域绘制，双击可结束绘制并自动形成封闭区域</div>
          <div style="color: #DC143C;">2、区域绘制后可拖拽点进行编辑</div>
          <div style="color: #DC143C;">3、鼠标放置区域点右键点击可删除当前节点</div>
        </div>
      </div>
      <div slot='footer'>
        <Button @click="areaCancel">取消</Button>
        <Button :disabled="!areaLayer" type="primary" @click="areaSave">保存</Button>
      </div>
    </Modal>
  </div>
</template>
<script>

import API from '@/api/shipManagement/aisSign'

export default ({
  props: {
    map: Object,
    isShow: Boolean
  },
  data () {
    return {
      isAdd: false, // 判断当前是否为新增状态
      isModify: false, // 是否为编辑状态
      toolStr: '', // 当前在哪个绘制图标
      zoomLevel: 12, // 显示层级，默认当前层级
      layerList: [], // 存放已经绘制标记数组
      curIcon: -1, // 当前选中标注图标标识
      markerName: '', // 标注名称
      markerLayer: null, // 标注图层
      markerModalShow: false, // 标注弹窗显示标识
      markerPoint: [], // 标注点度坐标
      lineModalShow: false, // 拆线弹窗显示标识
      lineColor: '#DC143C',
      lineName: '', // 折线名称
      lineDrawer: null,
      lineLayer: null,
      linePoint: [], // 折线坐标
      areaModalShow: false, // 区域弹窗显示标识
      areaColor: '#DC143C',
      areaName: '', // 区域名称
      areaDrawer: null,
      areaLayer: null,
      areaPoint: [], // 区域坐标
      curLayer: null, // 当前编辑层级
      signList: [], // 所有标记列表
      tableColumns: [
        {
          title: '名称',
          align: 'center',
          key: 'name',
          render: (h, params) => {
            return h('div', {}, params.row.config_json.name)
          }
        },
        {
          title: '类型',
          align: 'center',
          key: 'type',
          render: (h, params) => {
            let typeStr = ''
            if (params.row.type === '0') typeStr = '标注'
            if (params.row.type === '1') typeStr = '折线'
            if (params.row.type === '2') typeStr = '区域'
            return h('div', {}, typeStr)
          }
        },
        {
          title: '操作',
          width: 180,
          align: 'center',
          key: '',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                style: {
                  margin: '4px'
                },
                props: {
                  icon: params.row.is_visible === '1' ? 'md-eye' : 'md-eye-off',
                  size: 'small',
                  type: 'text'
                },
                on: {
                  click: (e) => {
                    e.stopPropagation()
                    this.handleShowHide(params.row)
                  }
                }
              }, ''),
              h('Button', {
                style: {
                  margin: '4px'
                },
                props: {
                  icon: 'md-brush',
                  size: 'small',
                  type: 'text'
                },
                on: {
                  click: (e) => {
                    e.stopPropagation()
                    this.handleEdit(params.row)
                  }
                }
              }, ''),
              h('Button', {
                style: {
                  margin: '4px'
                },
                props: {
                  icon: 'md-trash',
                  size: 'small',
                  type: 'text'
                },
                on: {
                  click: () => {
                    this.handleDelete(params.row)
                  }
                }
              }, '')
            ])
          }
        }
      ], // 列表字段
      markIconList: [ // 标注图标列表
        {
          url: require('@/assets/images/port.png'),
          tip: '港口',
          type: 0
        },
        {
          url: require('@/assets/images/wharf.png'),
          tip: '码头',
          type: 1
        },
        {
          url: require('@/assets/images/berth.png'),
          tip: '泊位',
          type: 2
        },
        {
          url: require('@/assets/images/anchorage.png'),
          tip: '锚地',
          type: 3
        }
      ]
    }
  },
  methods: {
    // 获取标注列表
    getSignList () {
      API.queryAisUserConfigList().then(res => {
        if (res.data.Code === 10000) {
          this.signList = res.data.Result
          if (this.layerList.length > 0) {
            this.layerList.forEach(item => {
              this.map.removeLayer(item.layer)
            })
          }
          this.showSignList() // 遍历显示
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 列表点击事件
    signClick (item) {
      if (this.curLayer) {
        this.map.Draw.cancelEdit(this.curLayer.layer)
      }
      let curLayer = this.layerList.filter(list => list.id === item.ais_user_config_id)
      switch (curLayer[0].type) {
        case '0': // 标注
          this.map.flyTo(curLayer[0].layer.getLatLng())
          break
        case '1': // 折线
          this.map.fitBounds(curLayer[0].layer.getBounds())
          break
        case '2': // 区域
          this.map.fitBounds(curLayer[0].layer.getBounds())
          break
        default:
      }
    },
    // 遍历显示所有标记
    showSignList () {
      this.layerList = []
      if (this.signList.length > 0) {
        this.signList.forEach(item => {
          if (item.is_visible === '1') { // 只有状态是显示的才渲染
            switch (item.type) {
              case '0': // 标注显示
                let myIcon = L.icon({
                  iconUrl: this.markIconList[item.config_json.icon].url
                })
                // eslint-disable-next-line no-undef
                this.markerLayer = L.marker([ item.config_json.pos[0], item.config_json.pos[1] ], { icon: myIcon, draggable: false, interactive: true }).bindTooltip(item.config_json.name, {
                  direction: 'bottom',
                  offset: [6, 12],
                  permanent: true,
                  opacity: '1',
                  className: 'div_tip_marker'
                }).openTooltip()
                this.layerList.push({
                  id: item.ais_user_config_id,
                  type: '0',
                  layer: this.markerLayer
                })
                this.map.addLayer(this.markerLayer)
                break
              case '1': // 折线显示
                // 创建线条并添加到地图
                let polyLine = this.map.Draw.show(item.config_json.pos, {
                  shape: 'Line',
                  locate: true,
                  pathOptions: {
                    weight: 2,
                    color: item.config_json.color
                  }
                })
                let txtLen = item.config_json.name.length * 12
                let lineMarker = L.marker([item.config_json.pos[0].lat, item.config_json.pos[0].lng], { icon: L.divIcon({ className: 'div_tip_line', html: '<div style="width: ' + txtLen + 'px;">' + item.config_json.name + '</div>' }) })
                this.layerList.push({
                  id: item.ais_user_config_id,
                  type: '1',
                  layer: polyLine
                })
                this.layerList.push({
                  id: item.ais_user_config_id + '_marker',
                  layer: lineMarker
                })
                this.map.addLayer(lineMarker)
                // this.map.Draw.edit(polyLine)
                break
              case '2': // 区域显示
                let polygon = this.map.Draw.show(item.config_json.pos, {
                  shape: 'Poly',
                  locate: true,
                  pathOptions: {
                    color: item.config_json.color,
                    fillOpacity: 0.5,
                    opacity: 0
                  }
                })
                this.layerList.push({
                  id: item.ais_user_config_id,
                  type: '2',
                  layer: polygon
                })
                // let zommLevel = this.map.getZoom()
                // let size = 12 / zommLevel
                let geojson = polygon.toGeoJSON()
                let bestPoint = turf.centroid(geojson)
                let centerMarker = L.marker([bestPoint.geometry.coordinates[1], bestPoint.geometry.coordinates[0]], { icon: L.divIcon({ className: 'div_tip_marker', html: '<div style="color: #fff; min-width:80px;">' + item.config_json.name + '</div>', iconAnchor: [42, 5] }) })
                this.layerList.push({
                  id: item.ais_user_config_id + '_marker',
                  layer: centerMarker
                })
                this.map.addLayer(centerMarker)
                break
              default:
            }
          }
        })
      }
    },
    // 主绘制区域选中
    drawChange (val) {
      this.mapDefault()
      switch (val) {
        case '标注':
          if (this.lineDrawer) {
            this.map.Draw.end(this.lineDrawer)
          }
          if (this.lineLayer) {
            this.map.removeLayer(this.lineLayer)
          }
          if (this.areaDrawer) {
            this.map.Draw.end(this.areaDrawer)
          }
          if (this.areaLayer) {
            this.map.removeLayer(this.areaLayer)
          }
          this.toolStr = '标注'
          this.markToolShow()
          break
        case '折线':
          if (this.areaDrawer) {
            this.map.Draw.end(this.areaDrawer)
          }
          if (this.areaLayer) {
            this.map.removeLayer(this.areaLayer)
          }
          this.toolStr = '折线'
          this.lineToolShow()
          break
        case '区域':
          if (this.lineDrawer) {
            this.map.Draw.end(this.lineDrawer)
          }
          if (this.lineLayer) {
            this.map.removeLayer(this.lineLayer)
          }
          this.toolStr = '区域'
          this.areaToolShow()
          break
        default:
          break
      }
    },
    // 标记显隐
    handleShowHide (row) {
      let _param = {
        ais_user_config_id: row.ais_user_config_id,
        config_json: JSON.stringify(row.config_json),
        type: row.type,
        is_visible: row.is_visible === '1' ? '0' : '1'
      }
      API.updateAisUserConfig(_param).then(res => {
        if (res.data.Code === 10000) {
          this.getSignList()
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 编辑标记
    handleEdit (item) {
      this.isModify = true
      let curLayer = this.layerList.filter(list => list.id === item.ais_user_config_id)
      this.curLayer = curLayer[0]
      if (!this.curLayer) return
      switch (curLayer[0].type) {
        case '0': // 标注
          this.markerModalShow = true
          this.curIcon = item.config_json.icon
          this.markerName = item.config_json.name
          this.markerLayer = curLayer[0].layer
          this.markerLayer.dragging.enable()
          // curLayer[0].layer.dragging.enable()
          break
        case '1': // 折线
          this.lineModalShow = true
          this.lineName = item.config_json.name
          this.lineColor = item.config_json.color
          this.lineLayer = curLayer[0].layer
          this.map.Draw.edit(this.lineLayer)
          let lineMarker = this.layerList.filter(list => list.id === (this.curLayer.id + '_marker'))
          this.map.removeLayer(lineMarker[0].layer)
          // this.map.Draw.edit(curLayer[0].layer)
          break
        case '2': // 区域
          this.areaModalShow = true
          this.areaName = item.config_json.name
          this.areaColor = item.config_json.color
          this.areaLayer = curLayer[0].layer
          this.map.Draw.edit(this.areaLayer)
          let areaMarker = this.layerList.filter(list => list.id === (this.curLayer.id + '_marker'))
          this.map.removeLayer(areaMarker[0].layer)
          // this.map.Draw.edit(curLayer[0].layer)
          break
        default:
      }
    },
    // 删除标记
    handleDelete (row) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确定删除该条记录？</p>',
        loading: true,
        onOk: () => {
          API.delAisUserConfig({ ais_user_config_id: row.ais_user_config_id }).then(res => {
            this.$Modal.remove()
            res.data.Code === 10000 ? this.$Message.success(res.data.Message) : this.$Message.warning(res.data.Message)
            this.getSignList()
          })
        }
      })
    },
    // 配置项重置
    mapDefault () {
      this.isMapClick = false
      this.isAdd = false
      this.isModify = false
      this.curIcon = -1
      this.markerName = ''
      this.lineName = ''
      this.areaName = ''
      this.map.off('click')
      if (!this.markerModalShow && !this.lineModalShow && !this.areaModalShow) {
        this.toolStr = ''
      }
      this.$nextTick(() => {
        if (this.markerLayer && !this.isModify) {
          // this.map.Draw.cancelEdit(this.markerLayer)
          this.markerLayer = null
        }
        if (this.lineLayer && !this.isModify) {
          this.map.Draw.cancelEdit(this.lineLayer)
          // this.map.removeLayer(this.lineLayer)
          this.lineLayer = null
        }
        if (this.areaLayer && !this.isModify) {
          this.map.Draw.cancelEdit(this.areaLayer)
          // this.map.removeLayer(this.areaLayer)
          this.areaLayer = null
        }
      })
    },
    // 标注功能选项列表
    markToolShow () {
      this.markerModalShow = true
      this.lineModalShow = false
      this.areaModalShow = false
      this.map.on('click', e => {
        this.isAdd = true
        if (this.curIcon === -1 || this.isMapClick || this.isModify) return
        this.isMapClick = true
        // eslint-disable-next-line no-undef
        let myIcon = L.icon({
          iconUrl: this.markIconList[this.curIcon].url
        })
        // eslint-disable-next-line no-undef
        this.markerLayer = L.marker([ e.latlng.lat, e.latlng.lng ], { icon: myIcon, draggable: true, interactive: true })
        this.layerList.push({
          type: '0',
          layer: this.markerLayer
        })
        this.map.addLayer(this.markerLayer)
      })
    },
    // 标注图标点击切换
    markerIconClick (idx) {
      this.curIcon = idx
      if (!this.markerLayer) return
      // 重置标注的图标样式
      // eslint-disable-next-line no-undef
      this.markerLayer.setIcon(L.icon({ iconUrl: this.markIconList[idx].url }))
    },
    // 标注窗口显隐
    markerModalVisible (val) {
      if (!val) {
        if (this.markerLayer && this.markerLayer.dragging) this.markerLayer.dragging.disable()
        // if (this.markerLayer && !this.isModify) { // 编辑状态不要做移除操作
        //   this.map.removeLayer(this.markerLayer)
        // }
        this.mapDefault()
      }
    },
    // 标注取消操作
    markerCancel () {
      this.markerModalShow = false
      if (this.isAdd) { // 如果是新增情况下关闭窗口，需要移除当前内容
        this.map.removeLayer(this.markerLayer)
      }
      this.mapDefault()
    },
    // 标注点保存
    markerSave () {
      if (!this.markerLayer || this.markerLayer === null) {
        this.$Message.warning('请在地图上点击选择放置图标位置')
        return
      }
      let markerJson = {
        icon: this.curIcon,
        name: this.markerName,
        pos: [ this.markerLayer._latlng.lat, this.markerLayer._latlng.lng ]
      }
      let _param = {
        config_json: JSON.stringify(markerJson),
        type: 0,
        is_visible: 1
      }
      this.map.Draw.cancelEdit(this.markerLayer)
      if (this.isModify) { // 编辑保存
        Object.assign(_param, {
          ais_user_config_id: this.curLayer.id
        })
        API.updateAisUserConfig(_param).then(res => {
          this.markerModalShow = false
          this.mapDefault()
          this.markerModalShow = false
          if (res.data.Code === 10000) {
            this.getSignList()
            this.$Message.success(res.data.Message)
          } else {
            this.$Message.error(res.data.Message)
          }
        })
      } else { // 新增保存
        API.addAisUserConfig(_param).then(res => {
          this.markerModalShow = false
          this.mapDefault()
          this.markerModalShow = false
          if (res.data.Code === 10000) {
            this.getSignList()
            this.$Message.success(res.data.Message)
          } else {
            this.$Message.error(res.data.Message)
          }
        })
      }
    },
    // 颜色选择器变化
    lineColorChange (val) {
      if (!this.lineLayer) return
      this.lineLayer.options.color = val
      this.lineLayer.setStyle({
        color: val
      })
    },
    // 折线名称变化
    lineTextChange () {
      // if (!this.lineLayer) return
      // this.lineLayer.setText('').setText(this.lineName)
    },
    // 折线窗口显隐
    lineModalVisible (val) {
      if (!val) {
        if (this.lineDrawer) {
          this.map.Draw.end(this.lineDrawer)
        }
        this.mapDefault()
      }
    },
    // 折线取消操作
    lineCancel () {
      this.lineModalShow = false
      if (this.isAdd) { // 如果是新增情况下关闭窗口，需要移除当前内容
        this.map.removeLayer(this.lineLayer)
      }
      this.mapDefault()
    },
    // 折线数据保存
    lineSave () {
      if (!this.lineLayer) {
        this.$Message.warning('请在地图上绘制折线后再保存')
        return
      }
      let lineJson = {
        name: this.lineName,
        color: this.lineLayer.options.color,
        pos: this.lineLayer._latlngs
      }
      let _param = {
        config_json: JSON.stringify(lineJson),
        type: 1,
        is_visible: 1
      }
      this.map.Draw.cancelEdit(this.lineLayer)
      if (this.isModify) { // 编辑保存
        Object.assign(_param, {
          ais_user_config_id: this.curLayer.id
        })
        API.updateAisUserConfig(_param).then(res => {
          this.lineModalShow = false
          this.mapDefault()
          if (res.data.Code === 10000) {
            this.lineModalShow = false
            this.getSignList()
            this.$Message.success(res.data.Message)
          } else {
            this.$Message.error(res.data.Message)
          }
        })
      } else { // 新增保存
        this.lineModalShow = false
        this.mapDefault()
        API.addAisUserConfig(_param).then(res => {
          if (res.data.Code === 10000) {
            this.lineModalShow = false
            this.getSignList()
            this.$Message.success(res.data.Message)
          } else {
            this.$Message.error(res.data.Message)
          }
        })
      }
    },
    // 折线功能选项列表
    lineToolShow () {
      this.markerModalShow = false
      this.lineModalShow = true
      this.areaModalShow = false
      if (!this.lineModalShow || this.isMapClick) return
      this.isMapClick = true
      let lineOptions = {
        shape: 'Line',
        locate: false,
        pathOptions: {
          color: this.lineColor,
          weight: 2
        }
      }
      this.lineDrawer = this.map.Draw.begin(lineOptions)
      this.map.on('pm:create', item => {
        this.isAdd = true
        if (item.shape === 'Line') {
          this.lineLayer = this.map.Draw.getLayer(this.lineDrawer)
          this.layerList.push({
            type: '1',
            layer: this.lineLayer
          })
          this.lineLayer.setText('').setText(this.lineName)
          this.map.Draw.edit(this.lineLayer) // 编辑
          item.sourceTarget.on('click', list => {

          })
        }
      })
    },
    // 区域颜色选择器变化
    areaColorChange (val) {
      if (!this.areaLayer) return
      this.areaLayer.options.fillColor = val
      this.areaLayer.setStyle({
        color: val
      })
    },
    // 区域名称变化
    areaTextChange () {
      if (!this.areaLayer) return
      this.areaLayer.setText('').setText(this.areaName)
    },
    // 区域窗口显隐
    areaModalVisible (val) {
      if (!val) {
        if (this.areaDrawer) {
          this.map.Draw.end(this.areaDrawer)
        }
        this.mapDefault()
      }
    },
    // 区域取消操作
    areaCancel () {
      this.areaModalShow = false
      if (this.isAdd) { // 如果是新增情况下关闭窗口，需要移除当前内容
        this.map.removeLayer(this.areaLayer)
      }
      this.mapDefault()
      // this.map.Draw.cancelEdit()
    },
    // 区域数据保存
    areaSave () {
      if (!this.areaLayer) {
        this.$Message.warning('请在地图上绘制区域后再保存')
        return
      }
      let areaJson = {
        name: this.areaName,
        color: this.areaLayer.options.color,
        pos: this.areaLayer._latlngs[0]
      }
      let _param = {
        config_json: JSON.stringify(areaJson),
        type: 2,
        is_visible: 1
      }
      this.map.Draw.cancelEdit(this.areaLayer)
      if (this.isModify) { // 编辑保存
        Object.assign(_param, {
          ais_user_config_id: this.curLayer.id
        })
        API.updateAisUserConfig(_param).then(res => {
          this.areaModalShow = false
          this.mapDefault()
          if (res.data.Code === 10000) {
            this.getSignList()
            this.$Message.success(res.data.Message)
          } else {
            this.$Message.error(res.data.Message)
          }
        })
      } else { // 新增保存
        API.addAisUserConfig(_param).then(res => {
          this.areaModalShow = false
          this.mapDefault()
          if (res.data.Code === 10000) {
            this.getSignList()
            this.$Message.success(res.data.Message)
          } else {
            this.$Message.error(res.data.Message)
          }
        })
      }
    },
    // 区域功能选项列表
    areaToolShow () {
      this.markerModalShow = false
      this.lineModalShow = false
      this.areaModalShow = true
      if (!this.areaModalShow || this.isMapClick) return
      this.isMapClick = true
      let areaOptions = {
        shape: 'Poly',
        locate: false,
        pathOptions: {
          color: this.areaColor,
          weight: 0,
          fillColor: this.areaColor,
          fillOpacity: 0.5
        }
      }
      this.areaDrawer = this.map.Draw.begin(areaOptions)
      this.map.on('pm:create', item => {
        this.isAdd = true
        if (item.shape === 'Poly') {
          this.areaLayer = this.map.Draw.getLayer(this.areaDrawer)
          this.layerList.push({
            type: '2',
            layer: this.areaLayer
          })
          // this.areaLayer.setText('').setText(this.areaName)
          this.map.Draw.edit(this.areaLayer) // 编辑
          item.sourceTarget.on('click', list => {
            // console.log(list)
          })
        }
      })
    }
  },
  created () {
    this.getSignList()
  }
})
</script>
<style>
  .draw_box {
    margin-right: 10px;
  }
  .modal_area .modal_line_con {
    margin-bottom: 20px;
  }
  .modal_area .marker_icon {
    margin-right: 10px;
    cursor: pointer;
  }
  .modal_area .cur_icon {
    background: #eee;
    padding: 4px 5px 3px;
  }
  .div_tip_line {
    /* min-width: 100px !important; */
    text-align: center;
    width: auto !important;
    height: auto !important;
    background: rgba(255, 255, 255,0.8);
    padding: 4px 5px;
    color: #333;
    font-size: 12px;
    /* font-weight: bold; */
    line-height: 14px;
    /* border: 1px solid #333; */
    z-index: 1;
  }
  .div_tip_marker {
    /* min-width: 100px !important; */
    text-align: center;
    width: auto !important;
    height: auto !important;
    /* background: rgba(255, 255, 255,0.8); */
    padding: 4px 5px;
    color: #333;
    font-size: 12px;
    /* font-weight: bold; */
    line-height: 14px;
    /* border: 1px solid #333; */
    z-index: 1;
  }
</style>
