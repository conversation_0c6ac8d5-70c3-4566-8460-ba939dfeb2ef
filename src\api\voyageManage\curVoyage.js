import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 查询当前航次列表 分页
export function queryVoyagePage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/record/execute/queryVoyagePage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询当前航次列表
export function queryVoyageList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/record/execute/queryVoyageList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 完结当前航次
export function generateVoyage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/record/execute/generateVoyage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 当前航次生成SOF
export function createSOF (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/record/execute/createSOF',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询历史航次列表
export function queryVoyageHistoryPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/record/history/queryVoyageHistoryPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询当前航次详情数据
export function getVoyageDetailById (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/record/execute/getVoyageDetailById',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 当前航次变更保存并发送
export function changeVoyage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/record/execute/changeVoyage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 当前航次再次发送功能
export function sendChangeVoyage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/record/execute/sendChangeVoyage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 航次命令
export function previewVoyageWord (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/record/execute/previewVoyageWord',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  queryVoyagePage,
  queryVoyageList,
  generateVoyage,
  createSOF,
  queryVoyageHistoryPage,
  getVoyageDetailById,
  changeVoyage,
  sendChangeVoyage,
  previewVoyageWord
}
