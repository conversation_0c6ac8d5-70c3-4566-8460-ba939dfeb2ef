<template>
  <div>
    <Card>
      <search @searchResults='searchResults' :setSearch='setSearchData' @keydown.enter.native='searchResults' @resetResults='resetResults'></search>
    </Card>
    <Card style="margin-top:10px;padding-top:6px;">
      <p slot='title' class="bold-font">港口管理</p>
      <div class="extra" slot="extra">
        <formAction :setFormAction='setFormAction' @handleUpdateTable="getList" @handleCreate="handleCreate"></formAction>
      </div>
      <div>
        <Table
          border
          :loading="loading"
          :columns="columns"
          :data="list"
          ref="selection"
          @on-select-all="tableSelectAll"
          @on-select-all-cancel="tableSelectCancel"
          @on-select="tableSelectAll"
          @on-select-cancel="tableSelectCancel"></Table>
        <div class="select_all">
          <Checkbox v-model="selectAll" @on-change="handleSelectAll(selectAll)">全选</Checkbox>
          <Button @click="handleDelete(selectionData, '1')">删除</Button>
        </div>
        <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
          :total="total" prev-text="< 上一页" next-text="下一页 >"
          @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
      </div>
    </Card>
    <updatePortModal :modalData="modalData" @callback="getList"></updatePortModal>
  </div>
</template>

<script>
import search from '_c/search' // 查询组件
import formAction from '_c/formAction' // 表单操作组件
import API from '@/api/portManagement'
import updatePortModal from './updatePortModal'

export default {
  components: {
    search,
    formAction,
    updatePortModal
  },
  data () {
    return {
      selectAll: false,
      selectionData: [], // 存储已选中数据
      setSearchData: {// 查询设置，对象key值为回调参数
        port_name: {
          type: 'text',
          label: '名称',
          value: '',
          width: 150
        },
        port_province_key: {
          type: 'select',
          label: '港口所属省份',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 150,
          value: '',
          filterable: true
        },
        port_area_key: {
          type: 'select',
          label: '港口所属区域',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 150,
          value: '',
          filterable: true
        },
        timezone_key: {
          type: 'select',
          label: '时区',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 150,
          value: '',
          filterable: true
        },
        tidal_datum: {
          type: 'text',
          label: '潮高基准面',
          value: '',
          width: 150
        }
      },
      setFormAction: {
        operation: ['create', 'updateTable']
      },
      loading: false, // 表单列表loding状态
      columns: [
        {
          type: 'selection',
          width: 60,
          align: 'center'
        },
        {
          title: '港口名称',
          key: 'port_name',
          align: 'center'
        },
        {
          title: '港口所属省份',
          key: 'port_province_name',
          align: 'center'
        },
        {
          title: '港口所属区域',
          key: 'port_area_name',
          align: 'center'
        },
        {
          title: '时区',
          key: 'timezone_name',
          align: 'center',
          width: 85
        },
        {
          title: '潮高基准面',
          key: 'tidal_datum',
          align: 'center',
          maxWidth: 100
        },
        {
          title: '附近港口',
          key: 'nearby_port_name',
          align: 'center',
          minWidth: 200
        },
        {
          title: '关联潮汐港口',
          key: 'tide_port_name',
          align: 'center',
          maxWidth: 115
        },
        {
          title: '排序号',
          key: 'order_num',
          align: 'center',
          width: 80
        },
        {
          title: '操作',
          key: 'operation',
          align: 'center',
          width: 200,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                style: {
                  margin: '4px'
                },
                props: {
                  icon: 'md-brush',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleUpdate(params.row)
                  }
                }
              }, '修改'),
              h('Button', {
                style: {
                  margin: '4px'
                },
                props: {
                  icon: 'md-trash',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleDelete(params.row, '2')
                  }
                }
              }, '删除')
            ])
          }
        }
      ],
      list: [], // 表单列表数据
      total: null, // 列表数据条数
      listQuery: {// 列表请求参数
        pageSize: 10,
        pageIndex: 1,
        port_name: '',
        port_province_key: '',
        port_area_key: '',
        timezone_key: '',
        tidal_datum: ''
      },
      listCurrent: 1, // 当前页码
      modalData: {
        modal: false,
        title: '',
        data: undefined,
        dialogType: null
      }
    }
  },
  created () {
    // 获取省份、区域、时区
    API.queryDicListByCode ({ dic_code: 'portProvince' }).then(res => {
      if (res.data.Code === 10000) {
        res.data.Result.map(item => {
          this.setSearchData.port_province_key.selectData = res.data.Result.map(item => {
            return {
              value: item.port_province_key,
              label: item.port_province_name
            }
          })
        })
      }
    })
    API.queryDicListByCode ({ dic_code: 'portArea' }).then(res => {
      if (res.data.Code === 10000) {
        this.setSearchData.port_area_key.selectData = res.data.Result.map(item => {
          return {
            value: item.port_area_key,
            label: item.port_area_name
          }
        })
      }
    })
    API.queryDicListByCode ({ dic_code: 'timezone' }).then(res => {
      if (res.data.Code === 10000) {
        this.setSearchData.timezone_key.selectData = res.data.Result.map(item => {
          return {
            value: item.timezone_key,
            label: item.timezone_name
          }
        })
      }
    })
    this.getList()
  },
  methods: {
    // 查询
    searchResults (e) {
      if (e.target) delete e.target
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      Object.assign(this.listQuery, e)
      this.getList()
    },
    // 重置
    resetResults () {
      this.listQuery = {// 列表请求参数
        pageSize: 10,
        pageIndex: 1,
        port_name: '',
        port_province_key: '',
        port_area_key: '',
        timezone_key: '',
        tidal_datum: ''
      },
      this.listCurrent = 1
      this.setSearchData.port_name.value = ''
      this.setSearchData.port_province_key.selected = ''
      this.setSearchData.port_area_key.selected = ''
      this.setSearchData.timezone_key.selected = ''
      this.setSearchData.tidal_datum.value = ''
      this.getList()
    },
    // 获取管理列表
    getList () {
      this.loading = true
      this.selectAll = false
      API.queryPortPage(this.listQuery).then(response => {
        if (response.data.Code === 10000) {
          this.list = response.data.Result
          this.total = response.data.Total
        } else {
          this.$Message.error(response.data.Message)
        }
        setTimeout(() => {
          this.loading = false
        }, 1 * 800)
      }).catch(
        setTimeout(() => {
          this.loading = false
        }, 1 * 800)
      )
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    },
    // 新增
    handleCreate () {
      this.modalData = {
        modal: true,
        title: '新增',
        dialogType: 'create'
      }
    },
    // 编辑
    handleUpdate (row) {
      this.modalData = {
        modal: true,
        title: '编辑',
        dialogType: 'update',
        data: row
      }
    },
    // 删除
    handleDelete (row, type) {
      if (row.length < 1) {
        this.$Message.warning('请至少选中一条港口数据！')
        return
      }
      this.$Modal.confirm({
        title: '删除港口',
        content: type === '2' ? '<p>确认后会将港口' + row.port_name + '删除</p>' : '<p>确定删除选中项？</p>',
        loading: true,
        onOk: () => {
          let data = ''
          if (type === '1') {
            let portIds = row.map(item => {
              return item.port_id
            })
            data = portIds.join(',')
          } else {
            data = row.port_id
          }
          API.queryDeletePort({ port_ids: data }).then(res => {
            if (res.data.Code === 10000) {
              this.$Message.success(res.data.Message)
              this.getList()
              this.$Modal.remove()
              this.loading = false
            } else {
              this.$Message.error(res.data.Message)
              this.$Modal.remove()
              this.loading = false
            }
          })
        }
      })
    },
    // 全选
    handleSelectAll (status) {
      this.$refs.selection.selectAll(status)
    },
    // 取消勾选
    tableSelectCancel (selection, row) {
      this.selectAll = false
      this.selectionData = selection
    },
    // 选中勾选触发
    tableSelectAll (selection, row) {
      if (selection.length === this.list.length) this.selectAll = true
      this.selectionData = selection
    }
  }
}
</script>
<style lang="less">
  .extra {
    float: right;
    margin-top: 5px;
  }
  .ivu-table-wrapper {
    clear: both;
  }
  .select_all {
    margin: 15px 0 0 19px;
    button {
      color: white;
      border-color: #2d8cf0;
      background-color: #2d8cf0;
      margin-left: 10px;
    }
  }
</style>
