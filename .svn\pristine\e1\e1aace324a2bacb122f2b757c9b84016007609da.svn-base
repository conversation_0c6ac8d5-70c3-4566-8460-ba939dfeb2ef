<template>
  <div>
    <Tabs name="tab1-1" v-model="tabShow" @on-click="changeClick">
      <TabPane label="成员信息" tab="tab1-1" name="memberTab">
        <Card>
          <search @searchResults='searchResults' :setSearch='setSearchData' @keydown.enter.native='searchResults' @resetResults='resetResults' class="searchdiv"></search>
          <Button type="primary" @click="getUserList" style="margin-right: 10px;">岸基用户</Button>
          <Button type="primary" @click="handleCreate" v-if="!operateShow">添加成员</Button>
        </Card>
        <Card style="margin-top:10px;padding-top:6px;">
          <Table
            border
            :loading="loading"
            :columns="columns"
            :data="list"
            ref="selection1"
            @on-select-all="tableSelectAll"
            @on-select-all-cancel="tableSelectCancel"
            @on-select="tableSelectAll"
            @on-select-cancel="tableSelectCancel"></Table>
          <div class="select_all" v-if="!operateShow">
            <Checkbox v-model="selectAll" @on-change="handleSelectAll(selectAll)">全选</Checkbox>
            <Button @click="handleAllDelete(selectionData)">全部移除</Button>
          </div>
          <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
            :total="total" prev-text="< 上一页" next-text="下一页 >"
            @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
        </Card>
      </TabPane>
      <TabPane label="新成员" tab="tab1-1" name="newMember">
        <Table border :loading="newMemberLoading" :columns="newMemberColumns" :data="newMemberList" ref="selection"></Table>
        <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.newMemberListQuery.pageSize" :current.sync="newMemberListCurrent"
          :total="newMemberTotal" prev-text="< 上一页" next-text="下一页 >"
          @on-change='newMemberHandleCurrentChange' @on-page-size-change='newMemberHandleSizeChange'/>
      </TabPane>
    </Tabs>
    <!-- 添加成员 -->
    <Modal
      v-model="addModal"
      @on-cancel="createCancel"
      title="添加成员">
      <Form ref="formDynamic" :model="formDynamic">
        <FormItem
          v-for="(item, index) in formDynamic.items"
          :key="index"
          label="账号"
          :prop="'items.' + index + '.fullNamm'"
          :rules="{required: true, message: '此处不能为空', trigger: 'change'}">
          <Row>
            <Col span="8">
              <Select v-model="item.fullNamm" label-in-value filterable @on-change="getFullNamm" @on-select='fullNammSelect(index)'>
                <Option v-for="(item1, idx) in accountData" :key="idx" :value="item1.fullNamm" :disabled="item1.disabled">{{ item1.mobile }}</Option>
              </Select>
            </Col>
            <Col span="8" offset="1">姓名：{{ item.fullNamm }}</Col>
            <Col span="4" offset="1" style="margin: -3px 0;">
              <Button @click="handleRemove(item, index)" icon="md-remove-circle" type="text" class="formbtn"></Button>
              <Button @click="handleAdd" icon="md-add-circle" type="text" class="formbtn"></Button>
            </Col>
          </Row>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button @click="createCancel">取消</Button>
        <Button type="primary" @click="createData">确认</Button>
      </div>
    </Modal>
    <!-- 查看 -->
    <Modal
      v-model="detailModal"
      @on-cancel="detailCancel"
      title="详情"
      width="620">
      <Form ref="formData" :model="formData" :label-width="115">
        <Row>
          <Col span="7">
            <Button @click="handleUpdate">编辑</Button>
            <FormItem style="display:none">
              <Input type="text" v-model='formData.avatar'></Input>
            </FormItem>
            <div class="userPic" style="margin:35px 0 0 80px;">
              <img src="@/assets/images/picture.png" alt="" v-if="imgBaseUrl === ''">
              <img :src="imgBaseUrl" alt="" v-else>
            </div>
            <Upload action=''
              :show-upload-list='false'
              accept=".jpg, .jpeg, .png"
              :format="['jpg','jpeg','png']"
              :max-size="2048"
              :before-upload="handleImgUpload"
              style="margin:10px 40px 0 95px;">
                <Button v-if="!readonly">更换头像</Button>
            </Upload>
          </Col>
          <Col span="15" offset="2">
            <FormItem label="昵称" prop="nickname">
              <Input v-model="formData.nickname" :readonly="readonly"></Input>
            </FormItem>
            <FormItem label="账号" prop="mobile">
              <Input v-model="formData.mobile" readonly></Input>
            </FormItem>
            <FormItem label="姓名" prop="full_name">
              <Input v-model="formData.full_name" :readonly="readonly"></Input>
            </FormItem>
            <FormItem label="性别" prop="gender">
              <Select v-model="formData.gender" :disabled="readonly">
                <Option value="1">女</Option>
                <Option value="0">男</Option>
              </Select>
            </FormItem>
            <FormItem label="生日" prop="birthday">
              <DatePicker type="date" v-model="formData.birthday" format="yyyy-MM-dd" @on-change="data=>formData.birthday=data" :readonly="readonly" style="width: 100%"></DatePicker>
            </FormItem>
            <FormItem label="所在地" prop="detail_address">
              <Input v-model="formData.detail_address" :readonly="readonly"></Input>
            </FormItem>
            <FormItem label="注册时间" prop="register_time">
              <Input v-model="formData.register_time" readonly></Input>
            </FormItem>
            <FormItem label="最后登录时间" prop="last_login_time">
              <Input v-model="formData.last_login_time" readonly></Input>
            </FormItem>
          </Col>
        </Row>
      </Form>
      <div slot="footer">
        <Button @click="detailCancel">取消</Button>
        <Button type="primary" @click="createDetail" v-if="!readonly">保存</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import search from '_c/search' // 查询组件
import formAction from '_c/formAction' // 表单操作组件
import API from '@/api/companyManagement'
import { avatarImage } from '@/api/basicData'
import Cookies from 'js-cookie'
import { configUser, handoverManage, removeMember, agreeJoinCompany, refuseJoinCompany, updateAccount } from '@/api/accountManagement'

export default {
  props: {
    listIdData: Object,
    changeTabName: String
  },
  components: {
    search,
    formAction
  },
  data () {
    return {
      operateShow: false,
      // 成员信息
      companyId: '', // 存储company_id
      tabShow: '',
      selectAll: false,
      selectionData: [], // 存储已选中的列表数据
      setSearchData: { // 查询设置，对象key值为回调参数
        passport_name: {
          type: 'text',
          label: '姓名',
          value: '',
          width: 150
        },
        mobile: {
          type: 'text',
          label: '账号',
          value: '',
          width: 150
        }
      },
      setFormAction: {
        operation: ['updateTable']
      },
      loading: false, // 表单列表loding状态
      columnsSpliceData: {}, // 存储岸基权限列
      columns: [
        {
          type: 'selection',
          width: 60,
          align: 'center'
        },
        {
          title: '姓名',
          key: 'passport_name',
          align: 'center'
        },
        {
          title: '账号',
          key: 'mobile',
          align: 'center',
          width: this.$route.name === 'companyMemberManage' ? '' : 145
        },
        {
          type: 'show',
          title: '岸基权限',
          key: 'is_open',
          align: 'center',
          width: this.$route.name === 'companyMemberManage' ? '' : 85,
          render: (h, params) => {
            return h('i-switch', {
              props: {
                value: params.row.shore_auth === '1' // 1开启，0关闭
              },
              on: {
                'on-change': () => {
                  params.row.shore_auth = params.row.shore_auth === '0' ? '1' : '0'
                  this.changeShoreAuth(params.row)
                }
              }
            })
          }
        },
        {
          type: 'show',
          title: '操作',
          key: 'operation',
          align: 'center',
          width: this.$route.name === 'companyMemberManage' ? '' : 310,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                style: {
                  margin: '4px'
                },
                props: {
                  icon: 'md-paper',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleDetail(params.row)
                  }
                }
              }, '查看'),
              h('Button', {
                style: {
                  margin: '4px'
                },
                props: {
                  icon: 'md-trash',
                  size: 'small',
                  disabled: params.row.role === '0' ? true : false // role:0管理员，1默认角色
                },
                on: {
                  click: () => {
                    this.handleAloneDelete(params.row)
                  }
                }
              }, '移除公司'),
              h('Button', {
                style: {
                  margin: '4px'
                },
                props: {
                  icon: 'md-settings',
                  size: 'small',
                  disabled: params.row.role === '0' ? true : false
                },
                on: {
                  click: () => {
                    this.handleAdminConfig(params.row)
                  }
                }
              }, '移交管理员')
            ])
          }
        }
      ],
      list: [], // 表单列表数据
      total: null, // 列表数据条数
      listQuery: {// 列表请求参数
        pageSize: 10,
        pageIndex: 1,
        company_id: '',
        join_company_status: '',
        shore_auth: '',
        passport_name: '',
        mobile: '',
      },
      listCurrent: 1, // 当前页码
      addModal: false,
      formDynamic: {
        items: [{
          mobile: '',
          fullNamm: '',
          passportId: ''
        }]
      },
      index: 1, // 默认显示一个成员
      accountData: [], // 账号下拉数据
      detailModal: false,
      readonly: true,
      imgBaseUrl: '',
      uploadData: '',
      formData: {
        avatar: '',
        nickname: '',
        mobile: '',
        full_name: '',
        gender: '',
        birthday: '',
        detail_address: '',
        register_time: '',
        last_login_time: ''
      },
      // 新成员
      newMemberLoading: false, // 表单列表loding状态
      newMemberColumns: [
        {
          title: '姓名',
          key: 'passport_name',
          align: 'center'
        },
        {
          title: '账号',
          key: 'mobile',
          align: 'center'
        },
        {
          type: 'operate',
          title: '操作',
          key: 'operation',
          align: 'center',
          width: this.$route.name === 'companyMemberManage' ? '' : 205,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                style: {
                  margin: '4px'
                },
                props: {
                  // icon: 'md-paper',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.isJoinCompany(params.row, 'adopt')
                  }
                }
              }, '通过'),
              h('Button', {
                style: {
                  margin: '4px'
                },
                props: {
                  // icon: 'md-trash',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.isJoinCompany(params.row, 'refuse')
                  }
                }
              }, '拒绝')
            ])
          }
        }
      ],
      newMemberList: [], // 表单列表数据
      newMemberTotal: null, // 列表数据条数
      newMemberListQuery: {// 列表请求参数
        pageSize: 10,
        pageIndex: 1,
        company_id: ''
      },
      newMemberListCurrent: 1 // 当前页码
    }
  },
  created () {
    if (this.$route.name !== 'companyMemberManage') return
    this.companyId = Cookies.get('company_id')
    this.listQuery.join_company_status = 20
    this.tabShow = 'memberTab'
    if (window.localStorage.userDataId) {
      this.operateShow = window.localStorage.userDataId === '9'
      if (window.localStorage.userDataId === '9') {
        this.columns = this.columns.filter(col => col.type !== 'selection' && col.type !== 'show' )
        this.newMemberColumns = this.newMemberColumns.filter(col => col.type !== 'operate' )
      }
    }
    this.getList()
  },
  mounted () {
    this.columnsSpliceData = this.columns[3]
  },
  methods: {
    // 获取成员信息列表
    getList () {
      this.loading = true
      this.selectAll = false
      this.listQuery.company_id = this.companyId
      API.queryCompanyUserPage(this.listQuery).then(response => {
        if (response.data.Code === 10000) {
          if (this.tabShow === 'memberTab') {
            this.list = response.data.Result
            this.total = response.data.Total
          } else {
            this.newMemberList = response.data.Result
            this.newMemberTotal = response.data.Total
          }
        } else {
          this.$Message.error(response.data.Message)
        }
        setTimeout(() => {
          this.loading = false
        }, 1 * 800)
      }).catch(
        setTimeout(() => {
          this.loading = false
        }, 1 * 800)
      )
    },
    // 岸基权限显示
    showColumns (d) {
      if (d !== '1') {
        if (this.columns.length < 5) return
        this.columns.splice(3, 1)
      } else {
        if (this.columns.length > 4) return
        this.columns.splice(3, 0, this.columnsSpliceData)
      }
    },
    // 过滤岸基用户
    getUserList () {
      this.listQuery.pageIndex = 1
      this.listCurrent = 1
      this.listQuery.shore_auth = '1'
      this.getList()
    },
    // 船东公司-岸基权限配置
    changeShoreAuth (row) {
      let data = {
        ship_company_config_auth_id: row.ship_company_config_auth_id,
        ship_company_id: row.company_id,
        ship_user_id: row.passport_id,
        shore_auth: row.shore_auth
      }
      configUser(data).then(res => {
        if (res.data.Code === 10000) {
          this.$Message.success('岸基权限修改成功')
          this.getList()
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 添加成员
    handleCreate () {
      this.addModal = true
      this.getAccountList()
    },
    // 获取账号下拉数据
    getAccountList () {
      API.accountList({ exclude_company_id: this.listQuery.company_id }).then(res => {
        if (res.data.Code === 10000) {
          this.accountData = res.data.Result.map(item => {
            return {
              mobile: item.mobile,
              value: item.id,
              fullNamm: item.full_name
            }
          })
        }
      })
    },
    // 获取姓名+储存已选择的账号
    getFullNamm (val) {
      if (!this.addModal || !val) return
      let curIndex = this.accountData.findIndex(item => { return item.mobile === val.label })
      let curList = []
      this.formDynamic.items[this.index] = {
        mobile: val.label,
        fullNamm: val.value,
        passportId: this.accountData[curIndex].value
      }
      this.formDynamic.items.forEach(item => {
        curList.push(item.mobile)
      })
      this.accountData.forEach(item => {
        if (curList.includes(item.mobile)) {
          Object.assign(item, {
            disabled: true
          })
        } else {
          Object.assign(item, {
            disabled: false
          })
        }
      })
      this.$forceUpdate()
    },
    fullNammSelect (idx) {
      this.index = idx
    },
    handleAdd () {
      this.index++
      this.formDynamic.items.push({
        fullNamm: '',
        mobile: '',
        passportId: ''
      })
    },
    handleRemove (item, idx) {
      if (this.formDynamic.items.length > 1) {
        this.formDynamic.items.splice(idx, 1)
        if (item.passportId === '') return
        let curIndex = this.accountData.findIndex(list => { return list.value === item.passportId })
        Object.assign(this.accountData[curIndex], {
          disabled: false
        })
      } else {
        this.$Message.warning('请至少保留一条成员数据！')
      }
    },
    // 新增成员
    createData () {
      this.$refs['formDynamic'].validate((valid) => {
        if (valid) {
          this.$Modal.confirm({
            title: '提示',
            content: '<p>是否确认添加成员？</p>',
            loading: true,
            onOk: () => {
              let data = {
                passport_ids: this.formDynamic.items.map(e => { return e.passportId }).join(','),
                join_company_id: this.listQuery.company_id
              }
              API.joinCompanyBatch(data).then(res => {
                if (res.data.Code === 10000) {
                  this.$Message.success(res.data.Message)
                  this.$Modal.remove()
                  this.addModal = false
                  this.getList()
                  this.createCancel()
                } else {
                  this.addModal = true
                  this.$Message.error(res.data.Message)
                  this.$Modal.remove()
                }
              }).catch()
            }
          })
        }
      })
    },
    // 关闭添加成员弹窗
    createCancel () {
      this.accountData = []
      this.formDynamic = {
        items: [{
          mobile: '',
          fullNamm: '',
          passportId: ''
        }]
      }
      this.$refs['formDynamic'].resetFields()
      this.addModal = false
    },
    // 编辑
    handleUpdate (row) {
      this.readonly = false
    },
    // 成员信息查看
    handleDetail (row) {
      this.detailModal = true
      this.formData = row
      this.imgBaseUrl = row.avatar !== 'picture.png' ? row.avatar : picture
    },
    // 关闭成员信息查看弹窗
    detailCancel () {
      this.detailModal = false
      this.readonly = true
    },
    // 清除成员信息数据
    clearDetail () {
      this.readonly = true
      this.imgBaseUrl = ''
    },
    // 保存成员信息编辑
    createDetail () {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>是否确认修改成员信息？</p>',
        loading: true,
        onOk: () => {
          let tempData = Object.assign({}, this.formData)
          if (typeof this.formData.birthday !== 'string') {
            let _nowDate = new Date(this.formData.birthday)
            let _year = _nowDate.getFullYear()
            let _month = _nowDate.getMonth() + 1
            let _day = _nowDate.getDate()
            this.formData.birthday = _year + '-' + _month + '-' + _day
          }
          const userModifyFn = () => {
            let data = {
              id: this.formData.passport_id,
              full_name: this.formData.full_name,
              nickname: this.formData.nickname,
              gender: this.formData.gender,
              birthday: this.formData.birthday,
              region_id: this.formData.region_id,
              detail_address: this.formData.detail_address,
              avatar: this.formData.avatar
            }
            updateAccount(data).then(res => {
              if (res.data.Code === 10000) {
                this.$Message.success(res.data.Message)
                this.$Modal.remove()
                this.detailModal = false
                this.clearDetail()
                this.getList()
              } else {
                this.detailModal = true
                this.$Message.error(res.data.Message)
                this.$Modal.remove()
              }
            }).catch()
          }
          if (this.uploadData === '') {
            userModifyFn()
          } else {
            avatarImage({base64File: this.imgBaseUrl}).then(e => {
              this.formData.avatar = e.data.fileUrl
              tempData = Object.assign({}, this.formData)
              if (e.data.Code === -10000) {
                this.$Message.warning(response.data.Message)
              } else {
                userModifyFn()
              }
            })
          }
        }
      })
    },
    // 上传图片
    handleImgUpload (file) {
      const isLt2M = file.size / 1024 / 1024 < 2
      const fileExt = file.name.split('.').pop().toLocaleLowerCase()
      if (!isLt2M) {
        this.$Message.warning('附件过大，附件最大2M')
      } else {
        if (fileExt === 'jpg' || fileExt === 'jpeg' || fileExt === 'png') {
          let that = this
          let reader = new FileReader()
          reader.readAsDataURL(file)
          reader.onload = function (e) {
            that.imgBaseUrl = e.target.result
          }
          this.uploadData = new FormData()
          this.uploadData.append('file', file)
          return false
        } else {
          this.$Message.warning(`${file.name}格式错误`)
          return false
        }
      }
    },
    // 移除某个公司
    handleAloneDelete (row) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>是否确认移除公司？</p>',
        loading: true,
        onOk: () => {
          let data = {
            company_id: row.company_id,
            member_id: row.passport_id
          }
          removeMember(data).then(res => {
            if (res.data.Code === 10000) {
              this.$Modal.remove()
              this.getList()
              this.$Message.success(res.data.Message)
            } else {
              this.$Modal.remove()
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
    },
    // 移除多个公司
    handleAllDelete (row) {
      if (row.length < 1) {
        this.$Message.warning('请至少选中一条成员数据！')
        return
      }
      this.$Modal.confirm({
        title: '提示',
        content: '<p>是否移除所选项？</p>',
        loading: true,
        onOk: () => {
          let data = {
            company_id: this.listQuery.company_id,
            member_ids: row.map(item => { return item.passport_id }).join(',')
          }
          API.delectSelectedCompany(data).then(res => {
            if (res.data.Code === 10000) {
              this.$Modal.remove()
              this.getList()
              this.$Message.success(res.data.Message)
            } else {
              this.$Modal.remove()
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
    },
    // 移交管理员
    handleAdminConfig (row) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>是否确认移交管理员？</p>',
        loading: true,
        onOk: () => {
          let data = {
            company_id: row.company_id,
            new_admin_id: row.passport_id
          }
          handoverManage(data).then(res => {
            if (res.data.Code === 10000) {
              this.$Modal.remove()
              this.getList()
              this.$Message.success(res.data.Message)
            } else {
              this.$Modal.remove()
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
    },
    // 全选
    handleSelectAll (status) {
      this.$refs.selection1.selectAll(status)
    },
    // 取消勾选
    tableSelectCancel (selection, row) {
      this.selectAll = false
      this.selectionData = selection
    },
    // 选中勾选触发
    tableSelectAll (selection, row) {
      if (selection.length === this.list.length) this.selectAll = true
      this.selectionData = selection
    },
    // 查询
    searchResults (e) {
      if (e.target) delete e.target
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      Object.assign(this.listQuery, e)
      this.getList()
    },
    // 重置查询条件
    resetResults () {
      this.listQuery.pageSize = 10
      this.listQuery.pageIndex = 1
      this.listQuery.shore_auth = ''
      this.listQuery.passport_name = ''
      this.listQuery.mobile = ''
      this.listQuery.join_company_status = 20
      this.listCurrent = 1
      this.setSearchData.passport_name.value = ''
      this.setSearchData.mobile.value = ''
      this.getList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    },
    // 新成员部分
    // 点击TAB切换
    changeClick (name) {
      if (name === 'memberTab') {
        this.listQuery.join_company_status = 20
      } else if (name === 'newMember') {
        this.listQuery.join_company_status = 10
      }
      this.getList()
    },
    // 新成员审核
    isJoinCompany (row, type) {
      this.$Modal.confirm({
        title: '提示',
        content: type === 'adopt' ? '<p>是否确认通过审核' : '<p>是否确认拒绝审核？</p>',
        loading: true,
        onOk: () => {
          if (type === 'adopt') {
            agreeJoinCompany({ user_company_id: row.id }).then(res => {
              if (res.data.Code === 10000) {
                this.$Modal.remove()
                this.getList()
                this.$Message.success(res.data.Message)
              } else {
                this.$Modal.remove()
                this.$Message.error(res.data.Message)
              }
            })
          } else {
            refuseJoinCompany({ user_company_id: row.id }).then(res => {
              if (res.data.Code === 10000) {
                this.$Modal.remove()
                this.getList()
                this.$Message.success(res.data.Message)
              } else {
                this.$Modal.remove()
                this.$Message.error(res.data.Message)
              }
            })
          }
        }
      })
    },
    // 页面跳转
    newMemberHandleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    newMemberHandleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    }
  },
  watch: {
    listIdData (val) {
      this.listQuery.company_id = val.data.id
      this.showColumns(val.data.company_type)
    },
    changeTabName (val) {
      if (val === 'userManage') {
        this.listQuery.join_company_status = 20
        this.companyId = this.listIdData.data.id
        this.tabShow = 'memberTab'
        this.getList()
      }
    }
  }
}
</script>
<style lang="less">
  .extra {
    float: right;
    margin-top: 5px;
  }
  .ivu-table-wrapper {
    clear: both;
  }
  .select_all {
    margin: 15px 0 0 19px;
    button {
      color: white;
      border-color: #2d8cf0;
      background-color: #2d8cf0;
      margin-left: 10px;
    }
  }
  .searchdiv {
    display: inline-block;
    vertical-align: middle;
    margin-right: -5px;
  }
  .formbtn {
    font-size: 24px;
    padding: 0 5px;
    display: inline-block;
  }
  .userPic {
    overflow: hidden;
    width: 120px;
    margin-top: 20px;
    height: 160px;
    display: flex;
    justify-content: center;
    align-items: center;
    img {
      width: 100%;
    }
  }
</style>
