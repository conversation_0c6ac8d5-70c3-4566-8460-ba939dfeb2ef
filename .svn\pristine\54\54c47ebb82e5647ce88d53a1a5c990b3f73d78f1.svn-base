<template>
  <Modal
    v-model="modalData.modal" class="wharf_area" :styles="{position: 'absolute', top: '110px', left: '215px'}" :mask="false"
    :transfer="false" width="400px" footer-hide>
    <p slot="header">
      <!-- <img src="@/assets/images/wharf_back.png" alt=""/> -->
      <div class="wharf_title">{{ modalData.name }}</div>
    </p>
    <div style="boder-bottom: 1px solid #333">
      <div class="wharf_inner_title">联系方式</div>
      <div class="wharf_inner_detail" v-html="modalData.contact"></div>
    </div>
    <div>
      <div class="wharf_inner_title">泊位介绍</div>
      <Table :columns="columns" :data="modalData.detail" border></Table>
    </div>
  </Modal>
</template>
<script>
export default {
  props: {
    modalData: Object
  },
  data () {
    return {
      columns: [
        {
          title: '泊位',
          key: 'name'
        },
        {
          title: '泊位类型',
          key: 'berthType'
        },
        {
          title: '靠泊条件',
          key: 'berthLevel'
        }
        // {
        //   title: '装卸能力',
        //   key: 'berthAbility'
        // }
      ]
    }
  }
}
</script>
<style>
  .wharf_area .ivu-modal-header {
    height: 50px;
    padding: 0 !important;
  }
  .wharf_area .ivu-table {
    font-size: 12px;
  }
  .wharf_area .ivu-modal-header p {
    background: #007dff;
    height: 50px;
  }
  .wharf_area .ivu-modal-header p img {
    width: 100%;
    height: 100%;
    pointer-events: none;
  }
  .wharf_title {
    position: absolute;
    /* background: #0770e799; */
    padding: 5px 15px 0 5px;
    top: 5px;
    left: 15px;
    color: #fff;
    font-size:18px;
    /* font-weight: bold; */
  }
  .wharf_inner_title {
    text-align: left;
    font-size: 14px;
    color: #027DB4;
    font-weight: bold;
    margin-bottom: 5px;
  }
  .wharf_inner_detail {
    white-space: pre-wrap;
    text-align: left;
    color: #333;
    margin-bottom: 20px;
  }
</style>
