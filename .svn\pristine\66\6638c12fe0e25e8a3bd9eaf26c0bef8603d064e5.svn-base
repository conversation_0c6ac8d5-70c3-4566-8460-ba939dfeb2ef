<template>
  <div>
    <Card>
      <Tabs type="card" :animated="false" v-model="tabId">
        <TabPane label="整体对比">
          <anchorOverallModal v-if="tabId===0"></anchorOverallModal>
        </TabPane>
        <TabPane label="单船对比">
          <shipAnchorModal v-if="tabId===1"></shipAnchorModal>
        </TabPane>
        <TabPane label="港口统计">
          <orderByPortsModal v-if="tabId===2"></orderByPortsModal>
        </TabPane>
      </Tabs>
    </Card>
  </div>
</template>
<script>
import anchorOverallModal from './anchorOverallModal'
import shipAnchorModal from './shipAnchorModal'
import orderByPortsModal from './orderByPortsModal'
export default {
  components: {
    anchorOverallModal,
    shipAnchorModal,
    orderByPortsModal
  },
  data () {
    return {
      tabId: 0
    }
  }
}
</script>
