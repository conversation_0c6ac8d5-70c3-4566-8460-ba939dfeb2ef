<template>
  <div>
    <Drawer
      v-model="memberModal"
      :data="modalData"
      title="成员管理"
      :width="800"
      @on-visible-change="visibleChange">
      <Card>
        <search @searchResults='searchResults' :setSearch='setSearchData' @keydown.enter.native='searchResults' @resetResults='resetResults' class="searchdiv"></search>
        <Button type="primary" @click="getUserList">岸基用户</Button>
      </Card>
      <Card style="margin-top:10px;padding-top:6px;">
        <Table border :loading="memberLoading" :columns="memberColumns" :data="memberList"></Table>
        <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
          :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
      </Card>
    </Drawer>
  </div>
</template>

<script>
import search from '_c/search' // 查询组件
import { spComConfigUserPage, spComConfigUserList, changeSpComConfigUserAuth } from '@/api/superPermission/spComConfigUser'

export default {
  components: {
    search
  },
  data () {
    return {
      memberModal: false,
      modalData: {},
      setSearchData: {// 查询设置，对象key值为回调参数
        ship_user_name: {
          type: 'select',
          label: '姓名',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 150,
          value: '',
          filterable: true
        },
        mobile: {
          type: 'text',
          label: '账号',
          value: '',
          width: 150
        }
      },
      memberLoading: false, // 表单列表loding状态
      memberColumns: [
        {
          title: '姓名',
          key: 'ship_user_name',
          align: 'center'
        },
        {
          title: '账号',
          key: 'mobile',
          align: 'center'
        },
        {
          title: '岸基权限',
          key: 'member_name',
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('Checkbox', {
                props: {
                  value: params.row.shore_auth !== '0'
                },
                on: {
                  'on-change': () => {
                    params.row.shore_auth = params.row.shore_auth === '1' ? '0' : '1'
                    this.changeShoreAuthBtn(params.row)
                  }
                }
              })
            ])
          }
        }
      ],
      memberList: [], // 表单列表数据
      total: null, // 列表数据条数
      listQuery: {// 列表请求参数
        pageSize: 10,
        pageIndex: 1,
        ship_company_id: '',
        ship_user_name: '',
        ship_user_id: '',
        mobile: '',
        shore_auth: '' // 岸基开通权限(1:开通 0：关闭)
      },
      listCurrent: 1 // 当前页码
    }
  },
  methods: {
    // 开启模态框
    visibleChange (d) {
      if (d === true) {
        this.setSearchUserData()
        this.getmemberList()
      }
    },
    // 查询
    searchResults (e) {
      if (e.target) delete e.target
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      Object.assign(this.listQuery, e)
      this.getmemberList()
    },
    // 重置
    resetResults () {
      this.setSearchData.ship_user_name.selected = ''
      this.setSearchData.mobile.value = ''
      this.listQuery.pageIndex = 1
      this.listQuery.ship_company_id = ''
      this.listQuery.ship_user_name = ''
      this.listQuery.ship_user_id = ''
      this.listQuery.mobile = ''
      this.listQuery.shore_auth = ''
      this.listCurrent = 1
      this.getmemberList()
    },
    // 人员姓名查询
    setSearchUserData () {
      let data = {
        ship_company_id: this.modalData.ship_company_id,
        ship_user_name: '',
        ship_user_id: '',
        mobile: '',
        shore_auth: '',
        exclude_ship_config_flag: 0 // 0不排除，1排除
      }
      spComConfigUserList(data).then(response => {
        if (response.data.Code === 10000) {
          response.data.Result.map(item => {
            this.setSearchData.ship_user_name.selectData.push({
              value: item.ship_user_name,
              label: item.ship_user_name
            })
          })
        } else {
          this.setSearchData.ship_user_name.selectData = []
        }
      })
    },
    // 获取列表
    getmemberList () {
      this.memberLoading = true
      this.listQuery.ship_company_id = this.modalData.ship_company_id
      this.listQuery.ship_company_config_auth_id = this.modalData.ship_company_config_auth_id
      spComConfigUserPage(this.listQuery).then(response => {
        if (response.data.Code === 10000) {
          this.memberList = response.data.Result
          this.total = response.data.Total
        } else {
          this.$Message.error(response.data.Message)
        }
        setTimeout(() => {
          this.memberLoading = false
        }, 1 * 800)
      }).catch(
        setTimeout(() => {
          this.memberLoading = false
        }, 1 * 800)
      )
    },
    // 获取岸基用户
    getUserList () {
      this.listQuery.pageIndex = 1
      this.listCurrent = 1
      this.listQuery.shore_auth = '1'
      this.getmemberList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getmemberList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getmemberList()
    },
    // 修改岸基人员状态
    changeShoreAuthBtn (row) {
      let data = {
        ship_company_config_auth_id: this.modalData.ship_company_config_auth_id,
        ship_company_id: row.ship_company_id,
        ship_user_id: row.ship_user_id,
        shore_auth: row.shore_auth
      }
      changeSpComConfigUserAuth(data).then(response => {
        if (response.data.Code === 10000) {
          this.$Message.success(response.data.Message)
        } else {
          this.$Message.error(response.data.Message)
        }
      })
    }
  }
}
</script>
<style scoped>
  .searchdiv {
    display: inline-block;
    vertical-align: middle;
    margin-right: -5px;
  }
</style>
