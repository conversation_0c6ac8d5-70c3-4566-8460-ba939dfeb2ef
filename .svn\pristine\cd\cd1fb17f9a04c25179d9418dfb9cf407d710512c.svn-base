<template>
  <div class="fleet-monitor">
    <!-- 顶部标题栏 -->
    <header class="header">
      <div class="logo">
        <i class="fas fa-ship logo-icon"></i>
        <span class="logo-text">Fleet Monitor</span>
      </div>
      <div class="datetime">{{ currentDateTime }}</div>
      <div class="notification-area">
        <div class="notification-container">
          <transition-group name="notification-transition" tag="div" class="notification-scroll">
            <div class="alert" v-for="(notice, index) in notifications" :key="index" v-show="currentNotificationIndex === index">
              <i class="fas" :class="notice.icon" :style="{ color: getNoticeColor(notice.type) }"></i>
              <div class="alert-text-container">
                <div class="alert-text" :class="{ 'scrolling-text': isTextOverflow(notice.text) }">
                  <span>{{ notice.text }}</span>
                </div>
              </div>
            </div>
          </transition-group>
        </div>
        <div class="bell">
          <i class="fas fa-bell"></i>
          <span class="badge">{{ notifications.length }}</span>
        </div>
      </div>
    </header>

    <!-- 统计卡片区域 -->
    <div class="stat-cards">
      <div class="stat-card">
        <div class="stat-icon blue">
          <i class="fas fa-chart-line"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">90 <span class="unit">次</span></div>
          <div class="stat-label">月航次数</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon purple">
          <i class="fas fa-ship"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">73,261 <span class="unit">万吨</span></div>
          <div class="stat-label">吨位量</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon green">
          <i class="fas fa-sync-alt"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">45,042 <span class="unit">万公里</span></div>
          <div class="stat-label">周转量</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon orange">
          <i class="fas fa-clock"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">5595 <span class="unit">小时</span></div>
          <div class="stat-label">锚泊时长</div>
        </div>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 左侧表格区域 -->
      <div class="left-panel">
        <div class="fleet-table-card">
          <div class="card-title">
            <div class="title-text">
              <i class="fas fa-list-ul"></i> 船队列表
            </div>
            <div class="autoplay-toggle">
              <input type="checkbox" id="autoplay-toggle" v-model="autoPlay" @change="toggleAutoPlay">
              <label for="autoplay-toggle">
                <div class="toggle-track">
                  <div class="toggle-indicator"></div>
                </div>
                <span class="toggle-label">{{ autoPlay ? '自动播放' : '手动控制' }}</span>
              </label>
            </div>
          </div>
          <div class="fleet-table">
            <table>
              <thead>
                <tr>
                  <th>船名</th>
                  <th>状态</th>
                  <th>当前航次</th>
                  <th>装货/卸货</th>
                  <th>计划航次</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(ship, index) in fleetData" :key="index" 
                    :class="[getRowClass(ship.status, ship.anchorHours), {'highlighted-ship': autoPlay && currentShipIndex === index}]"
                    @mouseenter="pauseAutoHighlight"
                    @mouseleave="resumeAutoHighlight"
                    @click="openShipDetail(ship, index)">
                  <td>{{ ship.name }}</td>
                  <td>
                    <div :class="['status-tag', getStatusClass(ship.status)]">
                      <i :class="getStatusIcon(ship.status)"></i> {{ ship.status }}
                      <span v-if="ship.status === '锚泊'" class="anchor-hours">{{ ship.anchorHours }}小时</span>
                    </div>
                  </td>
                  <td>{{ ship.currentRoute }}</td>
                  <td>{{ ship.cargo }}</td>
                  <td>{{ ship.plannedRoute }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      
      <!-- 右侧地图和港口信息区域 -->
      <div class="right-panel">
        <!-- 地图区域 -->
        <div class="map-card">
          <div class="card-title">
            <i class="fas fa-map-marked-alt"></i> 船舶分布
          </div>
          <div class="map-container">
            <div class="map-content">
              <!-- 这里应该放地图，但为了简化，使用背景图 -->
              <div class="ship-marker ship-marker-1">
                <i class="fas fa-map-marker-alt"></i>
                <div class="marker-label">宁波</div>
              </div>
              <div class="ship-marker ship-marker-2">
                <i class="fas fa-map-marker-alt"></i>
                <div class="marker-label">上海</div>
              </div>
              <div class="ship-marker ship-marker-3">
                <i class="fas fa-map-marker-alt marker-highlight"></i>
                <div class="marker-label">兴通66</div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 港口信息区域 -->
        <div class="port-info-card">
          <div class="card-title">
            <i class="fas fa-anchor"></i> 港口信息
          </div>
          
          <!-- 天气信息 -->
          <div class="weather-section">
            <div class="section-title">
              <i class="fas fa-cloud-sun"></i> 上海 天气预报
            </div>
            <div class="weather-current">
              <i class="fas fa-sun weather-icon sunny"></i>
              <div class="weather-info">
                <div class="city-name">上海</div>
                <div class="temperature">晴天, 22°C</div>
              </div>
              <div class="weather-update">东北风 3级</div>
            </div>
            
            <div class="weather-forecast">
              <div class="forecast-item">
                <div class="day">周天</div>
                <i class="fas fa-sun weather-icon-small sunny"></i>
                <div class="temp">23°/19°</div>
              </div>
              <div class="forecast-item">
                <div class="day">周一</div>
                <i class="fas fa-cloud-sun weather-icon-small partly-cloudy"></i>
                <div class="temp">24°/18°</div>
              </div>
              <div class="forecast-item">
                <div class="day">周二</div>
                <i class="fas fa-cloud-rain weather-icon-small rainy"></i>
                <div class="temp">22°/17°</div>
              </div>
              <div class="forecast-item">
                <div class="day">周三</div>
                <i class="fas fa-cloud-sun weather-icon-small partly-cloudy"></i>
                <div class="temp">23°/18°</div>
              </div>
              <div class="forecast-item">
                <div class="day">周四</div>
                <i class="fas fa-sun weather-icon-small sunny"></i>
                <div class="temp">26°/19°</div>
              </div>
            </div>
          </div>
          
          <div class="divider"></div>
          
          <!-- 船舶统计 -->
          <div class="ship-stats-section">
            <div class="section-title">
              <i class="fas fa-info-circle"></i> 上海 港口船舶信息
            </div>
            <div class="ship-stats">
              <div class="stat-item">
                <i class="fas fa-ship stat-item-icon ship-blue"></i>
                <div class="stat-number">12</div>
                <div class="stat-item-label">到达船舶</div>
              </div>
              <div class="stat-item">
                <i class="fas fa-arrow-up stat-item-icon departure-blue"></i>
                <div class="stat-number">8</div>
                <div class="stat-item-label">离开</div>
              </div>
              <div class="stat-item">
                <i class="fas fa-anchor stat-item-icon dock-green"></i>
                <div class="stat-number">15</div>
                <div class="stat-item-label">码头中</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 船舶详情弹窗 -->
    <div class="ship-detail-modal" v-if="showShipDetail" @click.self="closeShipDetail">
      <div class="ship-detail-container" :class="{'show': showShipDetail}">
        <div class="modal-header">
          <div class="ship-title">
            <i class="fas fa-ship"></i> {{ selectedShip.name }} #{{ selectedShipIndex + 1 }}
          </div>
          <button class="close-button" @click="closeShipDetail">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <div class="modal-content">
          <!-- 船舶基本信息 -->
          <div class="info-section">
            <div class="section-header">
              <i class="fas fa-info-circle"></i> 船舶基本信息
            </div>
            <div class="info-grid">
              <div class="info-item">
                <div class="info-label">状态</div>
                <div class="info-value">
                  <span :class="['status-tag-small', getStatusClass(selectedShip.status)]">
                    <i :class="getStatusIcon(selectedShip.status)"></i> {{ selectedShip.status }}
                    <span v-if="selectedShip.status === '锚泊'" class="anchor-hours">{{ selectedShip.anchorHours }}小时</span>
                  </span>
                </div>
              </div>
              <div class="info-item">
                <div class="info-label">货物/容量</div>
                <div class="info-value">{{ selectedShip.cargo }}</div>
              </div>
              <div class="info-item">
                <div class="info-label">当前航次</div>
                <div class="info-value">{{ selectedShip.currentRoute }}</div>
              </div>
              <div class="info-item">
                <div class="info-label">计划</div>
                <div class="info-value">{{ selectedShip.plannedRoute }}</div>
              </div>
            </div>
          </div>

          <!-- 船舶运行指标 -->
          <div class="info-section">
            <div class="section-header">
              <i class="fas fa-chart-line"></i> 船舶运行指标
            </div>
            <div class="info-grid">
              <div class="info-item">
                <div class="info-label">近期航次</div>
                <div class="info-value">4 次</div>
              </div>
              <div class="info-item">
                <div class="info-label">平均航行时间</div>
                <div class="info-value">5 天</div>
              </div>
              <div class="info-item">
                <div class="info-label">准点率</div>
                <div class="info-value">92%</div>
              </div>
              <div class="info-item">
                <div class="info-label">燃油效率</div>
                <div class="info-value">
                  <span class="efficiency-good">良好</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 历史航次 -->
          <div class="info-section">
            <div class="section-header">
              <i class="fas fa-history"></i> 历史航次
            </div>
            <div class="route-list">
              <div class="route-item">
                <i class="fas fa-arrow-circle-right"></i>
                <span>舟山 → 宁波</span>
              </div>
            </div>
          </div>

          <!-- 当前航次 -->
          <div class="info-section">
            <div class="section-header">
              <i class="fas fa-map-marked-alt"></i> 当前航次
            </div>
            <div class="route-current">
              <i class="fas fa-arrow-circle-right"></i>
              <span>宁波 → 嘉兴</span>
            </div>
          </div>

          <!-- 计划航次 -->
          <div class="info-section">
            <div class="section-header">
              <i class="fas fa-calendar-alt"></i> 计划航次
            </div>
            <div class="route-planned">
              <div class="route-item planned">
                <i class="fas fa-arrow-circle-right"></i>
                <span>宁波 → 嘉兴</span>
                <div class="cargo-info">PX:10350</div>
                <button class="plan-button">计划中</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { queryShipsAmounts } from '@/api/statistics/operationAnalysis'
import { queryCurVoyageMmsi, queryVoyagePlanConsolePage } from '@/api/ais'
import { getToken } from '@/libs/util'
import config from '@/config'

export default {
  name: 'FleetMonitor',
  data() {
    return {
      worker: null,
      currentDateTime: '',
      currentShipIndex: 0,
      currentNotificationIndex: 0,
      notificationInterval: null,
      horizontalScrollActive: false,
      textWidths: {},
      highlightInterval: null,
      isHighlightPaused: false,
      autoPlay: true,
      showShipDetail: false,
      selectedShip: {},
      selectedShipIndex: 0,
      notifications: [
        { text: '兴通66 - 剩余时长超过48小时', icon: 'fa-exclamation-triangle', type: 'warning' },
        { text: '兴通739 - 锚泊时长达到50小时，请及时关注船舶动态并联系相关负责人进行处理', icon: 'fa-exclamation-circle', type: 'danger' },
        { text: '兴通17 - 锚泊时长超过72小时需要处理', icon: 'fa-exclamation-triangle', type: 'danger' }
      ],
      fleetData: [
        {
          name: '兴通66',
          status: '在航',
          currentRoute: '宁波→嘉兴',
          cargo: '对二甲苯 (10,250)',
          plannedRoute: '宁波→嘉兴 (PX:10350)'
        },
        {
          name: '兴通79',
          status: '在航',
          currentRoute: '揭阳→惠州',
          cargo: '对二甲苯 (7500)',
          plannedRoute: '惠州→江阴 (PEG:5000)'
        },
        {
          name: '兴通99',
          status: '靠泊',
          currentRoute: '揭阳→惠州',
          cargo: '对二甲苯 (7950)',
          plannedRoute: '揭阳→惠州 (PX:7950)'
        },
        {
          name: '兴通7',
          status: '靠泊',
          currentRoute: '可门→德澳江门',
          cargo: '环己酮 (3000)',
          plannedRoute: '德院→南通 (纯苯:3000)'
        },
        {
          name: '兴通19',
          status: '靠泊',
          currentRoute: '宁波→惠州→中山',
          cargo: '液碱 (3700)',
          plannedRoute: '抚顺→珠海 (OX:2600)'
        },
        {
          name: '兴通729',
          status: '锚泊',
          anchorHours: 20,
          currentRoute: '连云港→大连徐力',
          cargo: '对二甲苯 (1000)',
          plannedRoute: '连云港→大连造船 (PX:10000)'
        },
        {
          name: '兴通59',
          status: '锚泊',
          anchorHours: 36,
          currentRoute: '大连徐力→东莞',
          cargo: '异己烷 (5700)',
          plannedRoute: '揭阳→惠州 (PX:7000)'
        },
        {
          name: '兴通6',
          status: '在航',
          currentRoute: '惠州欧美→八所新港',
          cargo: '苯酚 (5000)',
          plannedRoute: '八所新港→钦定 (苯酚:5000)'
        },
        {
          name: '兴通16',
          status: '在航',
          currentRoute: '德清万达→洋浦欧盛',
          cargo: '液碱 (3500)',
          plannedRoute: '惠州→东海港 (OX:3000)'
        },
        {
          name: '兴通56',
          status: '在航',
          currentRoute: '揭阳→惠州',
          cargo: 'PX (7200)',
          plannedRoute: '惠州→江阴 (MEG:5500)'
        },
        {
          name: '兴通759',
          status: '在航',
          currentRoute: '揭阳→惠州',
          cargo: '对二甲苯 (25500)',
          plannedRoute: '惠州→钦定 (PX:25500)'
        },
        {
          name: '兴通739',
          status: '锚泊',
          anchorHours: 50,
          currentRoute: '揭阳中石油→宁波荣峰',
          cargo: '对二甲苯 (10000)',
          plannedRoute: '连云港→钦定 (PX:10000)'
        },
        {
          name: '兴通17',
          status: '锚泊',
          anchorHours: 85,
          currentRoute: '舟山鱼山→八所',
          cargo: '苯酚 (3000)',
          plannedRoute: '舟山鱼山→八所 (苯酚:3000)'
        },
        {
          name: '兴通96',
          status: '在航',
          currentRoute: '连云港→南通洋口',
          cargo: '对二甲苯 (8400)',
          plannedRoute: '南通洋口→钦定 (PX:8400)'
        }
      ]
    };
  },
  created() {
    const token = getToken()
    const baseUrl = process.env.NODE_ENV === 'development' ? config.baseUrl.dev : config.baseUrl.pro
    this.worker = new Worker('/worker.js')
    const requests = [
      { id: 1, url: `${baseUrl}report/voyage/head/queryShipsServiceOverall?token=${token}&ship_company_id=2929`, headers: config.ajaxHeader, data: {start_month: '2025-05', end_month: '2025-05'}},
      { id: 2, url: `${baseUrl}voyage/ais/record/queryAisVoyageGetShipMMis?token=${token}&ship_company_id=2929`, headers: config.ajaxHeader, data: {mmsi: '*********'}}
    ]
    this.worker.postMessage(requests)
  },
  mounted() {
    this.startClock();
    this.startAutoHighlight();
    this.startNotificationRotation();
    this.$nextTick(() => {
      this.calculateTextWidths();
    });
    this.worker.onmessage = (e) => {
      console.log(e)
    }
  },
  beforeDestroy() {
    clearInterval(this.clockInterval);
    clearInterval(this.highlightInterval);
    clearInterval(this.notificationInterval);
  },
  methods: {
    calculateTextWidths() {
      const container = document.querySelector('.alert-text-container');
      if (!container) return;
      
      const containerWidth = container.offsetWidth;
      this.notifications.forEach((notice, index) => {
        const tempSpan = document.createElement('span');
        tempSpan.style.visibility = 'hidden';
        tempSpan.style.position = 'absolute';
        tempSpan.style.whiteSpace = 'nowrap';
        tempSpan.style.font = window.getComputedStyle(container).font;
        tempSpan.textContent = notice.text;
        document.body.appendChild(tempSpan);
        this.textWidths[index] = tempSpan.offsetWidth;
        document.body.removeChild(tempSpan);
      });
    },
    isTextOverflow(text) {
      const index = this.notifications.findIndex(n => n.text === text);
      if (index === -1) return false;
      
      const container = document.querySelector('.alert-text-container');
      if (!container) return false;
      
      // 增加安全边距，确保有足够空间才不滚动
      return (this.textWidths[index] || 0) > (container.offsetWidth - 20);
    },
    getNoticeColor(type) {
      switch(type) {
        case 'warning': return '#ffb73f';
        case 'danger': return '#ff5a5a';
        case 'info': return '#38b0ff';
        default: return '#ff5a5a';
      }
    },
    startNotificationRotation() {
      this.notificationInterval = setInterval(() => {
        // 完成横向滚动后再切换通知
        const currentNotice = this.notifications[this.currentNotificationIndex];
        if (this.isTextOverflow(currentNotice.text)) {
          if (this.horizontalScrollActive) return;
          
          this.horizontalScrollActive = true;
          // 根据文本长度设置延迟，给足够时间完成横向滚动
          const textLength = currentNotice.text.length;
          // 最少6秒，每个字符150毫秒，确保长文本有足够时间展示
          const scrollTime = Math.max(6000, textLength * 150);
          
          setTimeout(() => {
            this.horizontalScrollActive = false;
            this.currentNotificationIndex = (this.currentNotificationIndex + 1) % this.notifications.length;
          }, scrollTime);
        } else {
          // 如果文本不需要横向滚动，直接切换
          this.currentNotificationIndex = (this.currentNotificationIndex + 1) % this.notifications.length;
        }
      }, 5000); // 增加基础切换间隔，让用户有更多时间阅读
    },
    toggleAutoPlay() {
      if (this.autoPlay) {
        this.startAutoHighlight();
      } else {
        clearInterval(this.highlightInterval);
        this.highlightInterval = null;
      }
    },
    startAutoHighlight() {
      if (this.highlightInterval) {
        clearInterval(this.highlightInterval);
      }
      if (this.autoPlay) {
        this.highlightInterval = setInterval(() => {
          if (!this.isHighlightPaused) {
            this.currentShipIndex = (this.currentShipIndex + 1) % this.fleetData.length;
          }
        }, 3000); // 每3秒切换一次高亮船舶
      }
    },
    pauseAutoHighlight() {
      this.isHighlightPaused = true;
    },
    resumeAutoHighlight() {
      this.isHighlightPaused = false;
    },
    getRowClass(status, anchorHours) {
      if (status === '锚泊') {
        if (anchorHours >= 72) return 'row-danger';
        if (anchorHours >= 48) return 'row-warning';
        if (anchorHours >= 24) return 'row-caution';
      }
      return '';
    },
    getStatusClass(status) {
      if (status === '在航') return 'sailing';
      if (status === '靠泊') return 'docked';
      if (status === '锚泊') return 'anchored';
      return '';
    },
    getStatusIcon(status) {
      if (status === '在航') return 'fas fa-ship';
      if (status === '靠泊') return 'fas fa-anchor';
      if (status === '锚泊') return 'fas fa-life-ring';
      return 'fas fa-circle';
    },
    startClock() {
      this.updateDateTime();
      this.clockInterval = setInterval(this.updateDateTime, 1000);
    },
    updateDateTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');
      this.currentDateTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    openShipDetail(ship, index) {
      this.selectedShip = { ...ship }; // 克隆对象，避免直接引用
      this.selectedShipIndex = index;
      this.showShipDetail = true;
      
      // 暂停自动高亮
      if (this.autoPlay) {
        this.pauseAutoHighlight();
      }
      
      // 添加禁止滚动类到body
      document.body.classList.add('modal-open');
    },
    closeShipDetail() {
      this.showShipDetail = false;
      
      // 恢复自动高亮
      if (this.autoPlay) {
        this.resumeAutoHighlight();
      }
      
      // 移除禁止滚动类
      document.body.classList.remove('modal-open');
    }
  }
};
</script>

<style scoped>
/* 导入Font Awesome */
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css');

/* 基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.fleet-monitor {
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Microsoft YaHei", sans-serif;
  background-color: #011628;
  color: #e6f7ff;
  min-height: 100vh;
  padding: 0;
  display: flex;
  flex-direction: column;
}

/* 头部样式 */
.header {
  position: relative;
  height: 60px;
  background-color: #001529;
  border-bottom: 1px solid #1a3d6a;
  display: flex;
  align-items: center;
  padding: 0 20px;
}

.logo {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #072040, #0a345e);
  padding: 0 20px;
  height: 40px;
  border-radius: 4px;
  box-shadow: 0 0 15px rgba(30, 192, 187, 0.3);
  border: 1px solid #1a3d6a;
}

.logo-icon {
  font-size: 24px;
  color: #1ec0bb;
  margin-right: 10px;
}

.logo-text {
  color: #1ec0bb;
  font-size: 22px;
  font-weight: 500;
  text-shadow: 0 0 8px rgba(30, 192, 187, 0.5);
}

.datetime {
  font-weight: 600;
  padding: 8px 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: pulse 2s infinite;
}

.datetime::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: shimmer 3s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 15px rgba(30, 192, 187, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(30, 192, 187, 0.5);
  }
  100% {
    box-shadow: 0 0 15px rgba(30, 192, 187, 0.3);
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.notification-area {
  display: flex;
  align-items: center;
  height: 40px;
  background: linear-gradient(135deg, #072040, #0a345e);
  padding: 0 15px;
  border-radius: 4px;
  box-shadow: 0 0 15px rgba(30, 192, 187, 0.3);
  border: 1px solid #1a3d6a;
  position: absolute;
  right: 40px;
  overflow: hidden;
}

.notification-area::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(56, 176, 255, 0.4), transparent);
  animation: glow-line 3s infinite;
}

@keyframes glow-line {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.8; }
}

.notification-container {
  width: 580px;
  height: 100%;
  overflow: hidden;
  position: relative;
}

.notification-scroll {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  position: relative;
}

.alert {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  padding-right: 10px;
}

.alert i {
  font-size: 16px;
  margin-right: 8px;
  flex-shrink: 0;
}

.alert-text-container {
  overflow: hidden;
  width: 100%;
}

.alert-text {
  white-space: nowrap;
  display: inline-block;
  padding: 4px 0;
  transition: all 0.3s ease;
}

.scrolling-text {
  display: inline-block;
  padding-right: 50px; /* 确保滚动文本末尾有足够空白 */
  animation: scroll-text 15s linear infinite;
  animation-delay: 1s; /* 给用户时间先阅读开头 */
}

@keyframes scroll-text {
  0%, 10% {
    transform: translateX(0);
  }
  90%, 100% {
    transform: translateX(calc(-100% - 50px));
  }
}

.notification-transition-enter-active {
  transition: all 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: absolute;
  width: 100%;
}

.notification-transition-leave-active {
  transition: all 0.8s cubic-bezier(0.36, 0, 0.66, -0.56);
  position: absolute;
  width: 100%;
}

.notification-transition-enter-from {
  opacity: 0;
  transform: translateY(20px) scale(0.95);
  filter: blur(2px);
}

.notification-transition-leave-to {
  opacity: 0;
  transform: translateY(-20px) scale(0.95);
  filter: blur(2px);
}

.bell {
  position: relative;
  margin-left: 20px;
}

.bell i {
  font-size: 20px;
  color: #ffb73f;
}

.badge {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 16px;
  height: 16px;
  background-color: #ff4d4f;
  color: white;
  border-radius: 50%;
  font-size: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 统计卡片区域样式 */
.stat-cards {
  display: flex;
  padding: 20px;
  gap: 20px;
}

.stat-card {
  flex: 1;
  background: linear-gradient(160deg, #0c294b, #072040);
  border-radius: 6px;
  padding: 15px;
  display: flex;
  align-items: center;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
  border: 1px solid #0e3461;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15px;
}

.blue { 
  background-color: rgba(24, 144, 255, 0.15); 
  color: #38b0ff;
}
.purple { 
  background-color: rgba(114, 46, 209, 0.15); 
  color: #a37feb;
}
.green { 
  background-color: rgba(19, 194, 194, 0.15); 
  color: #22e1e1;
}
.orange { 
  background-color: rgba(250, 140, 22, 0.15); 
  color: #ffa246;
}

.stat-icon i {
  font-size: 20px;
}

.icon-chart { color: #38b0ff; }
.icon-ship { color: #a37feb; }
.icon-cycle { color: #22e1e1; }
.icon-time { color: #ffa246; }

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 500;
  line-height: 1.2;
  color: #fff;
}

.unit {
  font-size: 14px;
  color: #8eb0d1;
  margin-left: 4px;
}

.stat-label {
  font-size: 14px;
  color: #8eb0d1;
}

/* 主内容区域样式 */
.main-content {
  display: flex;
  flex: 1;
  padding: 0 20px 20px;
  gap: 20px;
}

.left-panel {
  flex: 7;
}

.right-panel {
  flex: 3;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.fleet-table-card, .map-card, .port-info-card {
  background: linear-gradient(140deg, #0c294b, #072040);
  border-radius: 6px;
  height: 100%;
  padding: 15px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
  border: 1px solid #0e3461;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #1ec0bb;
  border-bottom: 1px solid #1a3d6a;
  padding-bottom: 10px;
  display: flex;
  align-items: center;
  min-height: 24px;
}

.title-text {
  display: flex;
  align-items: center;
  height: 100%;
}

.card-title i {
  margin-right: 6px;
  color: #1ec0bb;
}

/* 自动播放开关 */
.autoplay-toggle {
  display: flex;
  align-items: center;
  margin-left: auto;
  height: 100%;
}

.autoplay-toggle input {
  opacity: 0;
  width: 0;
  height: 0;
  position: absolute;
}

.autoplay-toggle label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.toggle-track {
  width: 40px;
  height: 20px;
  background-color: rgba(0, 20, 40, 0.7);
  border: 1px solid #1a3d6a;
  border-radius: 20px;
  position: relative;
  transition: all 0.3s ease;
  cursor: pointer;
  display: inline-block;
  margin-right: 8px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.4);
  flex-shrink: 0;
}

.toggle-indicator {
  width: 16px;
  height: 16px;
  background-color: #555;
  border-radius: 50%;
  position: absolute;
  top: 1px;
  left: 2px;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
}

input:checked + label .toggle-track {
  background-color: rgba(56, 176, 255, 0.3);
  border-color: #38b0ff;
}

input:checked + label .toggle-indicator {
  transform: translateX(20px);
  background-color: #38b0ff;
}

.toggle-label {
  font-size: 13px;
  color: #8eb0d1;
  cursor: pointer;
  line-height: 20px;
  position: relative;
  top: 0;
}

input:checked + label .toggle-label {
  color: #e6f7ff;
}

/* 表格样式 */
.fleet-table {
  width: 100%;
  overflow-x: hidden;
}

table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  table-layout: fixed; /* 固定表格布局 */
}

thead {
  background-color: #081f3a;
}

th {
  padding: 12px 8px;
  text-align: left;
  color: #8eb0d1;
  font-weight: 500;
  font-size: 14px;
  border-bottom: 1px solid #1a3d6a;
}

td {
  padding: 12px 8px;
  border-bottom: 1px solid #1a3d6a;
  font-size: 14px;
  color: #e6f7ff;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.status-tag {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  text-align: center;
}

.status-tag i {
  margin-right: 4px;
  font-size: 10px;
}

.anchor-hours {
  margin-left: 4px;
  font-size: 10px;
  opacity: 0.9;
}

.sailing {
  background-color: #22c55e;
  color: white;
}

.docked {
  background-color: #0b84e5;
  color: white;
}

.anchored {
  background-color: #f59e0b;
  color: white;
}

/* 整行预警样式 */
.row-caution {
  background-color: rgba(234, 179, 8, 0.1);
  animation: row-pulse-slow 2s infinite;
}

.row-warning {
  background-color: rgba(249, 115, 22, 0.15);
  animation: row-pulse-medium 1.5s infinite;
}

.row-danger {
  background-color: rgba(239, 68, 68, 0.2);
  animation: row-pulse-fast 1s infinite;
}

/* 预警行动画 */
@keyframes row-pulse-slow {
  0%, 100% { background-color: rgba(234, 179, 8, 0.1); }
  50% { background-color: rgba(234, 179, 8, 0.2); }
}

@keyframes row-pulse-medium {
  0%, 100% { background-color: rgba(249, 115, 22, 0.15); }
  50% { background-color: rgba(249, 115, 22, 0.25); }
}

@keyframes row-pulse-fast {
  0%, 100% { background-color: rgba(239, 68, 68, 0.2); }
  50% { background-color: rgba(239, 68, 68, 0.3); }
}

/* 确保表格交替行的颜色与预警不冲突 */
tr:nth-child(even) {
  background-color: rgba(9, 30, 53, 0.3);
}

tr:hover {
  background-color: rgba(26, 61, 106, 0.3);
}

/* 预警行的悬停效果 */
.row-caution:hover {
  background-color: rgba(234, 179, 8, 0.25) !important;
}

.row-warning:hover {
  background-color: rgba(249, 115, 22, 0.3) !important;
}

.row-danger:hover {
  background-color: rgba(239, 68, 68, 0.35) !important;
}

/* 船舶高亮浮动效果 */
.highlighted-ship {
  position: relative;
  z-index: 20;
  box-shadow: 0 6px 16px rgba(56, 176, 255, 0.5);
  background: linear-gradient(90deg, rgba(16, 52, 97, 0.4), rgba(56, 176, 255, 0.15), rgba(16, 52, 97, 0.4)) !important;
  border-radius: 4px;
  transition: all 0.3s ease;
  transform: translateY(-2px) scale(1.01);
  height: 56px; /* 增加行高 */
}

.highlighted-ship td {
  color: #ffffff !important;
  text-shadow: 0 0 8px rgba(56, 176, 255, 0.8);
  border-bottom: 1px solid #1a3d6a;
  font-weight: 600;
  letter-spacing: 0.3px;
  font-size: 15.5px; /* 增加字体大小 */
  padding: 16px 8px; /* 增加上下内边距 */
}

.highlighted-ship::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-left: 2px solid #38b0ff;
  border-right: 2px solid #38b0ff;
  pointer-events: none;
  animation: border-pulse 1.5s infinite;
}

@keyframes border-pulse {
  0%, 100% { 
    border-left-color: rgba(56, 176, 255, 0.8);
    border-right-color: rgba(56, 176, 255, 0.8);
  }
  50% { 
    border-left-color: rgba(56, 176, 255, 1);
    border-right-color: rgba(56, 176, 255, 1);
  }
}

.highlighted-ship::after {
  content: '';
  position: absolute;
  top: 0;
  left: -150%;
  width: 35%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(56, 176, 255, 0.2), transparent);
  animation: scan-effect 1.5s linear infinite;
  pointer-events: none;
}

.highlighted-ship .status-tag {
  box-shadow: 0 0 10px rgba(56, 176, 255, 0.7);
  transform: scale(1.05);
  transition: all 0.3s ease;
}

@keyframes scan-effect {
  0% {
    left: -40%;
  }
  100% {
    left: 100%;
  }
}

/* 确保预警样式与高亮船舶浮动效果兼容 */
.row-caution.highlighted-ship {
  background: linear-gradient(90deg, rgba(234, 179, 8, 0.2), rgba(234, 179, 8, 0.3), rgba(234, 179, 8, 0.2)) !important;
  box-shadow: 0 5px 15px rgba(234, 179, 8, 0.3);
}

.row-warning.highlighted-ship {
  background: linear-gradient(90deg, rgba(249, 115, 22, 0.2), rgba(249, 115, 22, 0.3), rgba(249, 115, 22, 0.2)) !important;
  box-shadow: 0 5px 15px rgba(249, 115, 22, 0.3);
}

.row-danger.highlighted-ship {
  background: linear-gradient(90deg, rgba(239, 68, 68, 0.2), rgba(239, 68, 68, 0.35), rgba(239, 68, 68, 0.2)) !important;
  box-shadow: 0 5px 15px rgba(239, 68, 68, 0.3);
}

tbody tr {
  position: relative;
  height: 48px; /* 固定行高，确保列表长度一致 */
  transition: all 0.3s ease;
}

/* 设置表格列宽 */
table th:nth-child(1), table td:nth-child(1) { width: 10%; } /* 船名 */
table th:nth-child(2), table td:nth-child(2) { width: 15%; } /* 状态 */
table th:nth-child(3), table td:nth-child(3) { width: 25%; } /* 当前航次 */
table th:nth-child(4), table td:nth-child(4) { width: 25%; } /* 装货/卸货 */
table th:nth-child(5), table td:nth-child(5) { width: 25%; } /* 计划航次 */

/* 地图区域样式 */
.map-card {
  flex: 6;
  position: relative;
}

.map-container {
  height: calc(100% - 40px);
  position: relative;
  background-size: cover;
  background-position: center;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #1a3d6a;
}

.map-content {
  width: 100%;
  height: 100%;
  position: relative;
}

.ship-marker {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.ship-marker i {
  color: #38b0ff;
  font-size: 18px;
  filter: drop-shadow(0 0 3px rgba(0, 0, 0, 0.7));
}

.marker-highlight {
  color: #ffa246 !important;
  text-shadow: 0 0 10px rgba(255, 162, 70, 0.5);
}

.marker-label {
  background-color: rgba(0, 10, 20, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 2px;
  font-size: 12px;
  margin-top: 2px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.ship-marker-1 {
  top: 40%;
  left: 30%;
}

.ship-marker-2 {
  top: 30%;
  left: 60%;
}

.ship-marker-3 {
  top: 50%;
  left: 45%;
}

.map-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.map-control-btn {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  background-color: white;
  border: none;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.map-control-btn i {
  color: #555;
  font-size: 14px;
}

.map-zoom-controls {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

/* 港口信息区域样式 */
.port-info-card {
  flex: 4;
}

.section-title {
  font-weight: 500;
  margin-bottom: 15px;
  color: #b7c5d6;
}

.section-title i {
  margin-right: 6px;
  color: #1ec0bb;
}

.weather-section {
  padding: 0 10px;
}

.weather-current {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.weather-icon {
  font-size: 32px;
  margin-right: 15px;
}

.weather-info {
  flex: 1;
}

.city-name {
  font-weight: 500;
  margin-bottom: 5px;
  color: #e6f7ff;
}

.temperature {
  color: #8eb0d1;
  font-size: 14px;
}

.weather-update {
  color: #8eb0d1;
  font-size: 14px;
}

.weather-forecast {
  display: flex;
  justify-content: space-between;
}

.forecast-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.day {
  font-size: 14px;
  color: #8eb0d1;
  margin-bottom: 8px;
}

.weather-icon-small {
  font-size: 18px;
  margin-bottom: 8px;
}

.sunny { color: #ffa246; }
.partly-cloudy { color: #38b0ff; }
.rainy { color: #8eb0d1; }

.temp {
  font-size: 12px;
  color: #8eb0d1;
}

.divider {
  height: 1px;
  background-color: #1a3d6a;
  margin: 20px 0;
}

.ship-stats-section {
  padding: 0 10px;
}

.ship-stats {
  display: flex;
  justify-content: space-between;
  gap: 10px;
  margin-top: 15px;
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: rgba(8, 29, 49, 0.7);
  border-radius: 6px;
  padding: 15px;
  border: 1px solid #1a3d6a;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.stat-item-icon {
  font-size: 24px;
  margin-bottom: 10px;
}

.ship-blue { color: #38b0ff; }
.departure-blue { color: #38b0ff; }
.dock-green { color: #22c55e; }

.stat-number {
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 5px;
  color: #fff;
}

.stat-item-label {
  font-size: 14px;
  color: #8eb0d1;
}

/* 船舶详情弹窗样式 */
.ship-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 13, 26, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.ship-detail-container {
  width: 700px;
  max-height: 85vh;
  background: linear-gradient(135deg, #07213f, #0a3055);
  border-radius: 8px;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.5);
  border: 1px solid #1a3d6a;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: modal-in 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
}

@keyframes modal-in {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: rgba(13, 41, 71, 0.7);
  border-bottom: 1px solid #1a3d6a;
}

.ship-title {
  display: flex;
  align-items: center;
  color: #38b0ff;
  font-size: 20px;
  font-weight: 600;
}

.ship-title i {
  margin-right: 10px;
}

.close-button {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: rgba(56, 176, 255, 0.1);
  border: 1px solid rgba(56, 176, 255, 0.3);
  color: #8eb0d1;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-button:hover {
  background-color: rgba(56, 176, 255, 0.2);
  color: #fff;
}

.modal-content {
  padding: 20px;
  overflow-y: auto;
  max-height: calc(85vh - 60px);
}

.info-section {
  margin-bottom: 20px;
  background-color: rgba(9, 30, 54, 0.5);
  border-radius: 6px;
  padding: 15px;
  border: 1px solid rgba(26, 61, 106, 0.6);
}

.section-header {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 15px;
  color: #e6f7ff;
  display: flex;
  align-items: center;
  border-bottom: 1px solid rgba(26, 61, 106, 0.6);
  padding-bottom: 10px;
}

.section-header i {
  margin-right: 8px;
  color: #38b0ff;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 13px;
  color: #8eb0d1;
  margin-bottom: 5px;
}

.info-value {
  font-size: 15px;
  color: #e6f7ff;
  font-weight: 500;
}

.status-tag-small {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  text-align: center;
}

.efficiency-good {
  color: #22c55e;
}

.route-list, .route-current, .route-planned {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.route-item {
  display: flex;
  align-items: center;
  color: #e6f7ff;
  padding: 8px 12px;
  background-color: rgba(13, 41, 71, 0.5);
  border-radius: 4px;
  border: 1px solid rgba(26, 61, 106, 0.4);
}

.route-item i {
  margin-right: 10px;
  color: #38b0ff;
}

.route-item.planned {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #e6f7ff;
}

.cargo-info {
  color: #8eb0d1;
  margin-left: auto;
  margin-right: 15px;
}

.plan-button {
  background-color: rgba(56, 176, 255, 0.2);
  color: #38b0ff;
  border: 1px solid rgba(56, 176, 255, 0.4);
  padding: 4px 10px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
}

/* 表格行光标样式 */
tbody tr {
  cursor: pointer;
}

/* 禁止滚动body */
:global(.modal-open) {
  overflow: hidden;
}
</style> 