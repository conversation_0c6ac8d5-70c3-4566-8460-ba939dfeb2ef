import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 船舶库查询
export function queryBasicShipPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/ship/queryBasicShipPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询船舶详情
export function queryBasicShipList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/ship/queryBasicShipList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 添加船舶
export function addBasicShip (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/ship/addBasicShip',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 编辑船舶
export function updateBasicShip (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/ship/updateBasicShip',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 删除
export function delBasicShip (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/ship/delBasicShip',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 后台=>获取三方港口码头泊位数据来源本地 分页
export function queryMapBasePage (data) {
  const qsData = Qs.stringify(data)
  return axios.request({
    url: '/piisp/map/base/queryMapBasePage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 后台=>获取三方港口码头泊位数据来源本地 未分页
export function queryMapBaseList (data) {
  const qsData = Qs.stringify(data)
  return axios.request({
    url: '/piisp/map/base/queryMapBaseList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 后台=>获取三方港口码头泊位详情
export function queryShipFormulaInfo (data) {
  const qsData = Qs.stringify(data)
  return axios.request({
    url: '/piisp/ship/formula/queryShipFormulaInfo',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 后台=>获取港口码头搜索列表
export function queryInnerPortAndWharfList (data) {
  const qsData = Qs.stringify(data)
  return axios.request({
    url: '/inner/port/queryInnerPortAndWharfList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 后台=>新增、修改三方港口码头泊位数据至本地(批量)
export function batchAddOrUpdateMapBase (data) {
  const qsData = Qs.stringify(data)
  return axios.request({
    url: '/map/base/batchAddOrUpdateMapBase',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询锚地信息 列表
export function queryAisShipDynamicList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/ais/ship/dynamic/queryAisShipDynamicList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 导入国内动态信息
export function internalExcelReader (data) {
  return axios.request({
    url: '/ais/ship/dynamic/internalExcelReader',
    method: 'post',
    headers: config.ajaxHeader,
    data
  })
}

// 导入国际动态信息
export function aisMapImage (data) {
  return axios.request({
    url: '/ais/image/aismage',
    method: 'post',
    headers: config.ajaxHeader,
    data
  })
}

// 导入国际动态信息
export function internationExcelReader (data) {
  return axios.request({
    url: '/ais/ship/dynamic/internationExcelReader',
    method: 'post',
    headers: config.ajaxHeader,
    data
  })
}

// 提交航次预警信息接口
export function addBatchAisEarlyWarning (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/ais/early/warning/addBatchAisEarlyWarning',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询ais预警信息
export function queryAisEarlyWarningList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/ais/early/warning/queryAisEarlyWarningList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取船舶预警信息列表数据
export function getShipsTrajectory (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/ais/hifleet/getShipsTrajectory',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取船舶锚泊预警时长
export function getShipsTrajectorys (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/ais/hifleet/getShipsTrajectorys',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取指定船舶的历史记录
export function getRecentVoyage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/ais/hifleet/getRecentVoyage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取航程路径规划
export function getVoyageRoute (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/ais/hifleet/getVoyageRoute',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}
// export function getRecentVoyage (data) {
//   let qsData = Qs.stringify(data)
//   return axios.request({
//     url: '/ais/hifleet/getRecentVoyage?' + qsData,
//     method: 'get',
//     headers: config.ajaxHeader
//   })
// }

export default {
  queryBasicShipPage,
  queryBasicShipList,
  addBasicShip,
  updateBasicShip,
  delBasicShip,
  queryMapBasePage,
  queryMapBaseList,
  queryShipFormulaInfo,
  queryInnerPortAndWharfList,
  batchAddOrUpdateMapBase,
  queryAisShipDynamicList,
  internalExcelReader,
  aisMapImage,
  internationExcelReader,
  addBatchAisEarlyWarning,
  queryAisEarlyWarningList,
  getShipsTrajectory,
  getShipsTrajectorys,
  getRecentVoyage
}
