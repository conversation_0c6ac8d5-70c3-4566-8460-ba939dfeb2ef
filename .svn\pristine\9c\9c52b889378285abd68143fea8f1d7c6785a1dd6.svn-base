<template>
  <div>
    <!-- 每日动态 -->
    <Table border :columns="defaultColumns" :data="templateData" :max-height="`${winHeight}`" class="defaulttable"></Table>
  </div>
</template>
<script>
export default {
  props: {
    templateData: Array
  },
  data () {
    return {
      winHeight: 500,
      defaultColumns: [
        {
          title: '船名',
          key: 'ship_name',
          align: 'center',
          width: 90,
          fixed: 'left'
        },
        {
          title: '航次',
          key: 'voyage_no',
          align: 'center',
          width: 80,
          fixed: 'left'
        },
        {
          title: '始发港',
          key: 'load_port_name',
          align: 'center',
          width: 80,
          fixed: 'left'
        },
        {
          title: '目的港',
          key: 'unload_port_name',
          align: 'center',
          width: 80,
          fixed: 'left'
        },
        {
          title: '货名',
          key: 'goods_name',
          align: 'center',
          width: 80,
          fixed: 'left'
        },
        {
          title: '数量（吨）',
          key: 'load_mete_num',
          align: 'center',
          width: 80,
          fixed: 'left'
        },
        {
          title: '装港',
          align: 'center',
          children: [
            {
              title: '抵港时间',
              key: 'cast_load_date',
              align: 'center',
              width: 85
            },
            {
              title: '起锚时间',
              key: 'unmoor_load_date',
              align: 'center',
              width: 85
            },
            {
              title: '靠泊时间',
              key: 'ladder_load_date',
              align: 'center',
              width: 85
            },
            {
              title: '装货时间',
              key: 'start_load_date',
              align: 'center',
              width: 85
            },
            {
              title: '装妥时间',
              key: 'end_load_date',
              align: 'center',
              width: 85
            },
            {
              title: '离港时间',
              key: 'leave_load_date',
              align: 'center',
              width: 85
            }
          ]
        },
        {
          title: '卸港',
          align: 'center',
          children: [
            {
              title: '卸货量',
              key: 'unload_mete_num',
              align: 'center',
              width: 80
            },
            {
              title: '抵港时间',
              key: 'cast_unload_date',
              align: 'center',
              width: 85
            },
            {
              title: '起锚时间',
              key: 'unmoor_unload_date',
              align: 'center',
              width: 85
            },
            {
              title: '靠泊时间',
              key: 'ladder_unload_date',
              align: 'center',
              width: 85
            },
            {
              title: '卸货时间',
              key: 'start_unload_date',
              align: 'center',
              width: 85
            },
            {
              title: '卸妥时间',
              key: 'end_unload_date',
              align: 'center',
              width: 85
            },
            {
              title: '离港时间',
              key: 'leave_unload_date',
              align: 'center',
              width: 85
            }
          ]
        },
        {
          title: '船舶位置',
          key: 'export_ship_data',
          align: 'center',
          width: 200
        }
      ]
    }
  },
  mounted () {
    // 挂载浏览器高度获取方法
    const that = this
    that.winHeight = window.innerHeight - 280
    window.onresize = () => {
      return (() => {
        that.winHeight = window.innerHeight - 280
      })()
    }
  }
}
</script>
<style>
  .defaulttable .ivu-table-cell {
    padding-left: 8px;
    padding-right: 8px;
  }
</style>
