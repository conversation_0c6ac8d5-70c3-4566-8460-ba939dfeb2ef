import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 查询计划航次列表
export function queryVoyagePlanPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/record/plan/queryVoyagePlanPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询计划航次列表 不分页 排期使用的简洁版 2025-04-03
export function queryVoyagePlanList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/record/plan/queryVoyagePlanList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 导入计划航次查看
export function queryVoyagePlanViewPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/record/plan/queryVoyagePlanViewPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查看计划航次详情 id
export function getVoyagePlanById (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/record/plan/getVoyagePlanById',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 新增计划航次
export function addVoyagePlan (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/record/plan/addVoyagePlan',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 修改计划航次
export function updateVoyagePlan (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/record/plan/updateVoyagePlan',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 发送计划航次
export function sendVoyage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/record/plan/sendVoyage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 再次发送计划航次
export function sendAgainVoyage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/record/plan/sendAgainVoyage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 删除计划航次
export function deleteVoyagePlan (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/record/plan/deleteVoyagePlan',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  queryVoyagePlanPage,
  queryVoyagePlanList,
  queryVoyagePlanViewPage,
  getVoyagePlanById,
  addVoyagePlan,
  updateVoyagePlan,
  sendVoyage,
  sendAgainVoyage,
  deleteVoyagePlan
}
