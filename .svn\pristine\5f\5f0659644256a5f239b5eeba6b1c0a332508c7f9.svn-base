import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 航程分析》总览
export function queryVoyageMileOverall (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/head/queryVoyageMileOverall',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 航程分析》航程占比
export function queryVoyageMileRate (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/head/queryVoyageMileRate',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 航程分析》南上北下
export function queryVoyageDirectionDetail (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/head/queryVoyageDirectionDetail',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 航程分析》重载情况
export function queryVoyageHeaveMileDetail (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/head/queryVoyageHeaveMileDetail',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 航程分析》重/空载详情导出excel
export function heightDetailExport (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/stat/template/exportVoyageHeaveMileDetailsExcel',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  queryVoyageMileOverall,
  queryVoyageMileRate,
  queryVoyageDirectionDetail,
  queryVoyageHeaveMileDetail,
  heightDetailExport
}
