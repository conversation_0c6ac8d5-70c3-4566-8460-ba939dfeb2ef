<template>
  <div class="data-card">
    <div class="card-icon">
      <Icon :type="icon" />
    </div>
    <div class="card-content">
      <div class="card-title">{{ title }}</div>
      <div class="card-value">{{ formattedValue }}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DataCard',
  props: {
    title: {
      type: String,
      required: true
    },
    value: {
      type: [Number, String],
      required: true
    },
    unit: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: 'md-stats'
    }
  },
  computed: {
    formattedValue() {
      if (typeof this.value === 'number') {
        return `${this.value.toLocaleString()}${this.unit ? ' ' + this.unit : ''}`;
      }
      return `${this.value}${this.unit ? ' ' + this.unit : ''}`;
    }
  }
};
</script>

<style lang="less" scoped>
.data-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 15px;
  display: flex;
  align-items: center;
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
  }
  
  .card-icon {
    font-size: 28px;
    margin-right: 15px;
    color: #4CAF50;
  }
  
  .card-content {
    .card-title {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.7);
      margin-bottom: 5px;
    }
    
    .card-value {
      font-size: 20px;
      font-weight: bold;
      color: #fff;
    }
  }
}
</style> 