import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 港口空载耗时列表 分页
export function queryStatPortToPortSailTimePage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/stat/port/sail/queryStatPortToPortSailTimePage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 港口空载耗时列表
export function queryStatPortToPortSailTimeList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/stat/port/sail/queryStatPortToPortSailTimeList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 添加港口空载耗时数据
export function addStatPortToPortSailTime (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/stat/port/sail/addStatPortToPortSailTime',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 修改港口空载耗时数据
export function updateStatPortToPortSailTime (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/stat/port/sail/updateStatPortToPortSailTime',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 删除港口空载耗时数据
export function delStatPortToPortSailTime (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/stat/port/sail/delStatPortToPortSailTime',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 同步港口空载耗时历史数据
export function batchAddStatPortToPortSailTimeHistory (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/stat/port/sail/batchAddStatPortToPortSailTimeHistory',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 港口重载耗时列表 分页
export function queryStatPortToPortCarrierTimePage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/stat/port/carrier/queryStatPortToPortCarrierTimePage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 港口重载耗时列表 不分页
export function queryStatPortToPortCarrierTimeList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/stat/port/carrier/queryStatPortToPortCarrierTimeList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 添加港口重载耗时列表数据
export function addStatPortToPortCarrierTime (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/stat/port/carrier/addStatPortToPortCarrierTime',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 修改港口重载耗时列表数据
export function updateStatPortToPortCarrierTime (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/stat/port/carrier/updateStatPortToPortCarrierTime',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 删除港口重载耗时列表数据
export function delStatPortToPortCarrierTime (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/stat/port/carrier/delStatPortToPortCarrierTime',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 同步港口重载耗时列表数据
export function batchAddStatPortToPortCarrierTimeHistory (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/stat/port/carrier/batchAddStatPortToPortCarrierTimeHistory',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  queryStatPortToPortSailTimePage,
  queryStatPortToPortSailTimeList,
  addStatPortToPortSailTime,
  updateStatPortToPortSailTime,
  delStatPortToPortSailTime,
  batchAddStatPortToPortSailTimeHistory,
  queryStatPortToPortCarrierTimePage,
  queryStatPortToPortCarrierTimeList,
  addStatPortToPortCarrierTime,
  updateStatPortToPortCarrierTime,
  delStatPortToPortCarrierTime,
  batchAddStatPortToPortCarrierTimeHistory
}