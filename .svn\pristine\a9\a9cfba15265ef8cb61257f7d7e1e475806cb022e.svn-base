<template>
  <div id="map">
  </div>
</template>
<script>
export default {
  props: {
    mmsi: String
  },
  data () {
    return {
      map: ''
    }
  },
  beforeMount () {
    // 地图配置
    this.options = {
      ak: 'a5bb8f37140d428391e1546d7b704413', // '57df9eaa033b44809d4bdaf919af457e',
      // 初始中心点坐标
      centerPoint: [30.1431749158, 121.9380381277],
      // 初始缩放级别
      zoom: this.mapNowRoom,
      // 最小缩放级别
      minZoom: 4,
      // 最大缩放级别
      maxZoom: 18,
      //
      gratings: { isShow: false },
      // 公司版权信息( 支持html )，默认Elane Inc.
      attribution: {
        isShow: false,
        emptyString:
          '&copy;2019 &nbsp;<a >兴通海运股份有限公司 保留所有版权 闽ICP备15001600号-2</a>'
      },
      // 测距控件
      measureCtrl: {
        // 是否开启测距控件，默认：true
        isShow: false,
        // 是否显示测距按钮，默认：true
        showMeasurementsMeasureControl: false,
        // 是否显示删除按钮，默认：true
        showMeasurementsClearControl: false,
        // 是否显示切换单位按钮，默认：true
        showUnitControl: false,
        position: 'topleft'
      },
      // 鼠标移动悬浮经纬度控件
      mousePostionCtrl: { isShow: false, position: 'bottomright' },
      // 缩放工具控件的显示隐藏
      zoomControlElane: { isShow: false, position: 'bottomright' },
      // 缩放级别显示控件
      zoomviewControl: { isShow: false, position: 'bottomleft' },
      // 地图切换控件的位置
      basemapsControl: { isShow: false, position: 'bottomright' },
      // 比例尺，控件
      scaleCtrl: { isShow: true, position: 'bottomleft' }
    }
  },
  mounted () {
    // 创建地图示例
    this.map = new ShipxyAPI.Map('map', this.options)
    this.openShip(this.map)
    if (!this.mmsi || this.mmsi === '') return
    this.map.shipsService.locationShip(this.mmsi, true)
  },
  methods: {
    // 开启船视图
    openShip (map) {
      // 默认 MT_SATELLITE 卫星图 MT_GOOGLE 谷歌地图 MT_SEA 海图
      map.basemapsControl.changeMap('MT_SEA')
      // 开启区域船服务
      ShipxyAPI.ShipService(map, {
        enableAreaShip: true, // 区域船
        enableFleetShip: false, // 船队船
        lableFont: ['600 12px Arial', '600 30px 宋体'], // 船舶名称，文字字体，默认值：["600 12px Arial", "500 12px Arial"]
        lableTxtColor: ['#000', '#000'], // 船舶名称，文字颜色，默认值：["#000","#fff"]
        lableLineColor: ['rgba(1, 30, 62, 1)', 'rgba(1, 30, 62, 1)'], //  边框颜色，默认值：["#000","#000"]
        lableLinefillColor: ['rgba(255, 255, 255, 0.7)', 'rgba(1, 30, 62, 0.3)'], // 框内填充颜色，默认值：[null,null]
        obliqueLineColor: ['#000', '#000'], // 船舶名称，斜线颜色，默认值：[null,null]
        dShipColor: '#FF6437' // D+船颜色，默认：#ff6347
      })
    }
  }
}
</script>
<style lang="less">
  #map {
    width: 100%;
    height: 400px;
    overflow: hidden;
    background-color: #a3ccff;
 }
</style>
