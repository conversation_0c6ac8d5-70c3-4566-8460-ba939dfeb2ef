import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 获取历史短信推送列表
export function historicalSMSPushList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/smsconfig/push/record/queryMsgPushRecordNodePage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 历史短信重新推送
export function pushAgainSMS (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/smsconfig/push/record/pushAgain',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 短信推送接口
export function pushMsg (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/smsconfig/push/record/pushMsg',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}
