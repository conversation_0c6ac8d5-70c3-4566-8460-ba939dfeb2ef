<template>
  <div>
    <Drawer
      v-model="formModal"
      :title="title"
      width="1000"
      @on-visible-change="visibleChange">
      <Table border :loading="listLoading" :columns="columns" :data="historyList" class="alignTable"></Table>
      <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
          :total="total" prev-text="< 上一页" next-text="下一页 >"
          @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
    </Drawer>
  </div>
</template>
<script>
import { queryAppPushRecordPage } from '@/api/berthingPlan/berthingPlan'

export default {
  data () {
    return {
      listLoading: false, // table loading status
      formModal: false, // 模态框显示状态
      title: '', // 模态框标题
      total: 0, // page number
      listCurrent: 1, // 当前页码
      historyList: [], // 列表数据
      listQuery: {
        key_type: '', // 推送类型（1：航次 2：靠泊）
        key_id: '', // 航次id或靠泊id
        pageSize: 10, // 页数
        pageIndex: 1 // 页码
      },
      columns: [
        {
          title: '发送者',
          key: 'send_user_name',
          align: 'center',
          width: 80
        },
        {
          title: '船舶',
          key: 'ship_name_cn',
          align: 'center',
          width: 100
        },
        {
          title: '商务人员',
          key: 'bussiness_user_name',
          align: 'center',
          width: 100
        },
        {
          title: '接收者',
          key: 'push_user_name',
          align: 'center',
          width: 160,
          render: (h, param) => {
            return h('span', {}, `${param.row.push_user_name}(${param.row.mobile})`)
          }
        },
        {
          title: '个推客户端id',
          key: 'client_id',
          align: 'center'
        },
        {
          title: '发送时间',
          key: 'push_date',
          align: 'center',
          width: 150
        },
        {
          title: '同步时间',
          key: 'receive_date',
          align: 'center',
          width: 150
        },
        {
          title: '发送状态',
          key: 'push_result_name',
          align: 'center',
          render: (h, param) => {
            return h('span', {
              style: {
                color: param.row.result_type === '0' || param.row.result_type === 0 ? 'red' : ''
              }
            }, param.row.push_result_name)
          }
        }
      ]
    }
  },
  methods: {
    visibleChange (val) {
      if (val) {
        this.getList()
      }
    },
    getList () {
      queryAppPushRecordPage(this.listQuery).then(res => {
        if (res.data.Code === 10000) {
          this.historyList = res.data.Result
          this.total = res.data.Total
        } else {
          this.$Message.error(this.data.Message)
        }
      })
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    }
  }
}
</script>
