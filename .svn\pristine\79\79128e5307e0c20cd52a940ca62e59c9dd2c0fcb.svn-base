<template>
  <div>
    <!-- 浙石化模板 -->
    <Table border :columns="petrifactionColumns" :data="templateData" :max-height="`${winHeight}`" class="alignTable"></Table>
  </div>
</template>
<script>
export default {
  props: {
    templateData: Array
  },
  data () {
    return {
      winHeight: 500,
      petrifactionColumns: [
        {
          title: '序号',
          type: 'index',
          align: 'center',
          width: 65
        },
        {
          title: '船舶',
          key: 'ship_name',
          align: 'center',
          width: 100
        },
        {
          title: '航次',
          key: 'voyage_no',
          align: 'center',
          width: 80
        },
        {
          title: '航线',
          align: 'center',
          width: 120,
          render: (h, params) => {
            return h('div', [
              h('div', {}, params.row.load_port_name + '-' + params.row.unload_port_name)
            ])
          }
        },
        {
          title: '货品',
          key: 'goods_name',
          align: 'center',
          width: 100
        },
        {
          title: '计划量',
          key: 'amounts',
          align: 'center',
          width: 85
        },
        {
          title: '装货港',
          align: 'center',
          children: [
            {
              title: '预/抵港',
              key: 'expect_load_port',
              align: 'center'
            },
            {
              title: '装货',
              key: 'export_load_data',
              align: 'center'
            }
          ]
        },
        {
          title: '装船量',
          key: 'load_ship_amount',
          align: 'center',
          width: 85
        },
        {
          title: '卸船量',
          key: 'unload_ship_amount',
          align: 'center',
          width: 85
        },
        {
          title: '损耗率‰',
          key: 'allowable_loss',
          align: 'center',
          width: 95
        },
        {
          title: '卸货港',
          align: 'center',
          children: [
            {
              title: '预/抵港',
              key: 'expect_unload_port',
              align: 'center'
            },
            {
              title: '卸货',
              key: 'export_unload_data',
              align: 'center'
            }
          ]
        }
      ]
    }
  },
  mounted () {
    // 挂载浏览器高度获取方法
    const that = this
    that.winHeight = window.innerHeight - 280
    window.onresize = () => {
      return (() => {
        that.winHeight = window.innerHeight - 280
      })()
    }
  }
}
</script>
