<template>
  <div>
    <Card>
      <search @searchResults='searchResults' @selectOnChanged='selectOnChanged' :setSearch='setSearchData' @keydown.enter.native='searchResults' @resetResults='resetResults'></search>
    </Card>
    <Card style="margin-top: 20px;">
      <div slot="title">
        <h3 class="set_title_txt">包运列表</h3>
        <div class="set_add_btn">
          <Button type="primary" @click="setAdd">新增</Button>
        </div>
      </div>
      <Spin fix v-if="isSplit">正在拆分…</Spin>
      <Table border stripe :columns="columns"
            row-key="id"
            :data="cargoList"
            :load-data="handleLoadData"
            :default-expand-all="false"
            @on-row-dblclick="handleDetail"
            update-show-children
            class="tables-tree">
        <template slot-scope="{ row }" slot="action">
          <ButtonGroup v-if="row.ship_names" size="small">
            <Button type="success" icon="md-funnel" @click="cargoSplit(row)">货盘拆分</Button>
            <Button style="margin-left: 10px;" type="primary" icon="md-create" @click="editSetCargo(row)">编辑</Button>
            <Button style="margin-left: 10px;" type="error" icon="md-trash" @click="deleteSetCargo(row)">删除</Button>
          </ButtonGroup>
        </template>
      </Table>
      <Page show-sizer :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="queryParam.pageSize" :current.sync="queryParam.pageIndex"
          :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
    </Card>
    <CargoSetEditDrawer :modalData="editModal" @editClose="editClose"></CargoSetEditDrawer>
    <PackageModal :modalData="packageModal"></PackageModal>
  </div>
</template>
<script>
import API from '@/api/shipSchedule'
import { queryBasicCargoList, queryCustomerList, querySysDate  } from '@/api/basicData'
import search from '_c/search' // 查询组件
import CargoSetEditDrawer from './cargoSetEditDrawer'
import PackageModal from './packageModal'

export default ({
  components: {
    search,
    CargoSetEditDrawer,
    PackageModal
  },
  data () {
    return {
      defaultMonth: null,
      isSplit: false,
      belong_month: '',
      queryParam: {
        source: 1,
        pageSize: 10,
        pageIndex: 1
      },
      packageModal: {
        modal: false,
        title: '',
        voyage_guarantee_plan_id: ''
      },
      setSearchData: {
        belong_month: {
          type: 'month',
          label: '归属月份',
          selected: '',
          width: 130,
          value: '',
          isdisable: false
        },
        shipper: {
          type: 'select',
          label: '货主',
          filterable: true,
          selectData: [],
          selected: '',
          placeholder: '请选择货主',
          flag: 'shipper',
          selectName: '',
          width: 130,
          value: ''
        },
        goods_id: {
          type: 'select',
          label: '货品',
          filterable: true,
          selectData: [],
          selected: '',
          placeholder: '请选择货品',
          flag: 'goods_id',
          selectName: '',
          width: 130,
          value: ''
        }
      },
      total: 0,
      columns: [
        { title: '归属月份', key: 'belong_month', align: 'center', render:(h, params) => {
          let belongMonth = params.row.ship_names ? params.row.belong_month : ''
          return h('span', {}, belongMonth)
        } },
        { title: '船名', key: 'ship_names', align: 'center', render: (h, params) => {
          return h('div', {}, params.row.ship_names || params.row.ship_name)
         }},
        { title: '货主', key: 'shipper_name', align: 'center' },
        { title: '货品', key: 'goods_name', align: 'center' },
        { title: '货量', key: 'amounts', align: 'center' },
        { title: '未分配', key: 'remain_amounts', align: 'center' },
        { title: '运价(元/吨)', key: 'freight_rate', align: 'center' },
        { title: '运费(元)', key: 'shipping_fee', align: 'center' },
        { title: '装港', key: 'load_port_name', align: 'center' },
        { title: '卸港', key: 'unload_port_name', align: 'center' },
        { title: '是否分配', key: 'is_decomposition:', align: 'center', render: (h, params) => {
          let decStr = params.row.is_decomposition === '1' ? '已分配' : '未分配'
          return h('div', {}, decStr)
        } },
        { title: '操作', slot: 'action', width: 280, align: 'center' }
      ],
      cargoList: [],
      editModal: {
        type: '',
        title: '包运编辑',
        modal: false,
        data: {}
      }
    }
  },
  created() {
    // this.getSysDate()
    this.getBaseData()
    this.getCargoList()
  },
  methods: {
    // 获取包运配置列表
    getCargoList() {
      API.queryVoyageGuaranteePlanPage(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          this.cargoList = res.data.Result.map(item => {
            Object.assign(item, {
              _loading: false,
              children: []
            })
            return item
          })
          this.total = res.data.Total
        } else {
          this.$Message.warning(res.data.Message)
        }
      })
    },
    getSysDate () {
      querySysDate().then(res => {
        if (res.data.Code === 10000) {
          let sysDate = res.data.systemDate.split(' ')[0]
          this.defaultMonth = this.formatDate(new Date(sysDate))
          this.setSearchData.belong_month.selected = this.defaultMonth
          Object.assign(this.queryParam, {
            belong_month: this.defaultMonth
          })
        }
      })
    },
    // 格式化日期为 YYYY-MM
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      return `${year}-${month}`;
    },
    getBaseData() {
      // 获取货物
      queryBasicCargoList({ cargo_name: '' }).then(res => {
        if (res.data.Code === 10000) {
          let goodsList = res.data.Result
          this.setSearchData.goods_id.selectData = goodsList.map(item => {
            return {
              label: item.cargo_name,
              value: item.id
            }
          })
        }
      })
      // 获取代理公司 公司类型（1、船东；2、货主；3、代理）
      queryCustomerList({ company_type: 2 }).then(res => {
        let agentList = res.data.Result
        this.setSearchData.shipper.selectData = agentList.map(item => {
          return {
            label: item.customer_company_name,
            value: item.customer_company_id
          }
        })
      })
    },
    handleDetail(item) {
      this.packageModal = {
        modal: true,
        title: item.shipper_name + '(' + item.goods_name + ') - ' + item.belong_month,
        voyage_guarantee_plan_id: item.voyage_guarantee_plan_id,
        belong_month: item.belong_month
      }
    },
    handleLoadData (item, callback) {
      API.queryVoyageMonthPlanList({ source_id: item.voyage_guarantee_plan_id }).then(res => {
        if(res.data.Code === 10000) {
          const data = res.data.Result
          if(data.length === 0) {
            this.$Message.warning('货盘还未分配！')
          }
          callback(data)
        } else {
          this.$Message.warning(res.data.Message)
          callback([])
        }
      })
    },
    // 包运货盘拆分
    cargoSplit(row) {
      this.isSplit = true
      let _param = {
        voyage_guarantee_plan_id: row.voyage_guarantee_plan_id
      }
      API.autoDecompositionGuaranteePlanByShips(_param).then(res => {
        this.isSplit = false
        if(res.data.Code === 10000) {
          this.$Message.success(res.data.Message)
          this.getCargoList()
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 包运配置项新增
    setAdd() {
      this.editModal = {
        type: 'add',
        title: '包运新增',
        modal: true,
        data: {}
      }
    },
    // 编辑
    editSetCargo(item) {
      this.editModal = {
        type: 'modify',
        title: '包运编辑',
        modal: true,
        data: item
      }
    },
    // 删除
    deleteSetCargo(item) {
      this.$Modal.confirm({
        title: '确认删除',
        content: `确定要删除 ${item.goods_name} 为 ${item.amounts}吨 货源吗？`,
        onOk: () => {
          API.delVoyageGuaranteePlan({ voyage_guarantee_plan_id: item.voyage_guarantee_plan_id }).then(res => {
            if(res.data.Code === 10000) {
              this.$Message.success(res.data.Message)
              this.getCargoList()
            } else {
              this.$Message.error(res.data.Message)
            }}
          )
        }
      })
    },
    editClose() {
      this.editModal.modal = false
      this.getCargoList()
    },
    // 查询
    searchResults (e) {
      if (e.target) delete e.target
      this.queryParam.pageIndex = 1
      Object.assign(this.queryParam, e)
      this.queryParam.belong_month = this.belong_month
      this.getCargoList()
    },
    selectOnChanged (e) {
      if (e.flag === 'month_start') {
        this.belong_month = e.key
      }
    },
    // 重置查询条件
    resetResults () {
      this.setSearchData.belong_month.selected = ''
      this.setSearchData.shipper.selected = ''
      this.setSearchData.goods_id.selected = ''
      this.belong_month = this.defaultMonth
      this.setSearchData.belong_month.selected = this.defaultMonth
      this.queryParam = { // 列表请求参数
        source: 1,
        belong_month: this.defaultMonth,
        pageSize: 10,
        pageIndex: 1
      }
      this.getCargoList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.queryParam.pageSize = val
      this.getCargoList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.queryParam.pageIndex = val
      this.getCargoList()
    }
  }
})
</script>
<style lang="less">
  .set_title_txt {
    display: inline-block;
    width: 100%;
    height: 20px;
    line-height: 20px;
    font-size: 17px;
    color: #17233d;
    font-weight: bold;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .set_add_btn {
    position: absolute;
    top: 13px;
    right: 15px;
  }
  .tables-tree .ivu-table-cell-tree {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 1px solid #dcdee2;
    border-radius: 2px;
    background-color: #fff;
    line-height: 12px;
    cursor: pointer;
    vertical-align: middle;
    -webkit-transition: color .2s ease-in-out,border-color .2s ease-in-out;
    transition: color .2s ease-in-out,border-color .2s ease-in-out;
  }
  .tables-tree .ivu-table-cell-tree-empty {
    cursor: default;
    color: transparent;
    background-color: transparent;
    border-color: transparent;
  }
</style>
