<template>
  <div>
    <Card>
      <search @searchResults='searchResults' @selectOnChanged='selectOnChanged' :setSearch='setSearchData' @keydown.enter.native='searchResults' @resetResults='resetResults'></search>
    </Card>
    <Card class="cardDiv">
      <Row>
        <Col span="4">
          <div v-for="(item, index) in curBussiShipList" :key="index" @click="changeReportList(item.ship_id, index)" class="shiplist" :class="{'curidx': shiplistcolor === index}">{{item.ship_name}}</div>
        </Col>
        <Col span="16" offset="1">
          <Table border :loading="loading" :columns="columns" :data="curShipListData"></Table>
          <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
              :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
        </Col>
      </Row>
    </Card>
  </div>
</template>

<script>
import search from '_c/search' // 查询组件
import { monthReportList, previewHistory } from '@/api/monthlyReport/monthlyReport'

export default {
  components: { search },
  data () {
    return {
      shiplistcolor: 0, // 默认显示第一个船舶的数据
      curBussiShipList: [], // 登录账号下的船舶
      setSearchData: {// 查询设置，对象key值为回调参数
        report_year_month: {
          type: 'month',
          label: '时间',
          selected: '',
          width: 130,
          value: '',
          isdisable: false
        }
      },
      total: null, // 列表数据条数
      curShipListData: [],
      loading: false, // 表单列表loding状态
      columns: [
        {
          title: '年月',
          key: 'report_year_month',
          align: 'center'
        },
        {
          title: '航次报表',
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: {
                  disabled: params.row.is_voyage_ok === '0' || params.row.voyage_wps_url === ''
                },
                on: {
                  click: () => {
                    this.handlePreview(params.row, 'voyage')
                  }
                }
              }, '查看'),
              h('Button', {
                style: {
                  margin: '0 5px'
                },
                props: {
                  disabled: params.row.is_voyage_ok === '0' || params.row.voyage_wps_url === ''
                },
                on: {
                  click: () => {
                    this.handleDown(params.row, 'voyage')
                  }
                }
              }, '下载')
            ])
          }
        },
        {
          title: '能效报告',
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: {
                  disabled: params.row.is_effciency_ok === '0' || params.row.efficiency_wps_url === ''
                },
                on: {
                  click: () => {
                    this.handlePreview(params.row, 'efficiency')
                  }
                }
              }, '查看'),
              h('Button', {
                style: {
                  margin: '0 5px'
                },
                props: {
                  disabled: params.row.is_effciency_ok === '0' || params.row.efficiency_wps_url === ''
                },
                on: {
                  click: () => {
                    this.handleDown(params.row, 'efficiency')
                  }
                }
              }, '下载')
            ])
          }
        }
      ],
      listQuery: {// 列表请求参数
        ship_id: '',
        report_year_month: '', // 归属年月
        pageSize: 10,
        pageIndex: 1
      },
      listCurrent: 1 // 当前页码
    }
  },
  created () {
    if (localStorage.shipNameList) {
      this.curBussiShipList = JSON.parse(window.localStorage.shipNameList)
      this.curBussiShipList = this.curBussiShipList.filter( item => ((item.business_model === '1' || item.business_model === '2') && !item.ship_name.includes('善'))) // 只要自营和船期船舶且不需要万邦船舶  2024/09/09调整
    }
    this.listQuery.ship_id = this.curBussiShipList[0].ship_id
    this.getList()
  },
  methods: {
    // 查询
    searchResults (e) {
      if (e.target) delete e.target
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      Object.assign(this.listQuery, e)
      this.listQuery.report_year_month = this.report_year_month
      this.getList()
    },
    // 时间格式
    selectOnChanged (e) {
      this.report_year_month = e.key
    },
    // 重置
    resetResults () {
      this.listCurrent = 1
      this.listQuery = {
        pageSize: 10,
        pageIndex: 1,
        ship_id: this.curBussiShipList[0].ship_id
      }
      this.shiplistcolor = 0
      this.setSearchData.report_year_month.selected = ''
      this.getList()
    },
    // 获取列表
    getList () {
      this.loading = true
      monthReportList(this.listQuery).then(response => {
        if (response.data.Code === 10000) {
          this.curShipListData = response.data.Result
          this.total = response.data.Total
        } else {
          this.$Message.error(response.data.Message)
        }
        setTimeout(() => {
          this.loading = false
        }, 1 * 800)
      }).catch(
        setTimeout(() => {
          this.loading = false
        }, 1 * 800)
      )
    },
    // 切换船舶
    changeReportList (d, idx) {
      this.listQuery.ship_id = d
      this.shiplistcolor = idx
      this.getList()
    },
    // 预览
    handlePreview (row, type) {
      previewHistory({ wps_url: type === 'voyage' ? row.voyage_wps_url : row.efficiency_wps_url }).then(res => {
        if (res.data.Code === 10000) {
          sessionStorage.setItem('wpsUrl', res.data.wpsUrl)
          sessionStorage.setItem('token', res.data.token)
          const jump = this.$router.resolve({ name: 'viewFile' })
          window.open(jump.href, '_blank')
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 下载
    handleDown (row, type) {
      let wpsUrl = type === 'voyage' ? row.voyage_wps_url : row.efficiency_wps_url
      window.open(wpsUrl, '_blank')
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    }
  }
}
</script>
<style scoped>
.cardDiv {
  margin-top: 20px;
  border: none;
  background: transparent;
}
.shiplist {
  cursor: pointer;
  color: #333;
  height: 60px;
  line-height: 60px;
  text-align: center;
  margin-bottom: 10px;
  background-color: white;
}
.curidx {
  color: white;
  background-color: #007DFF;
}
</style>
