<template>
  <div>
    <Row :gutter="20" v-if="showHomePage">
      <!-- 图表数据展示 -->
      <div class="btn-area">
        时间：<month-select class="month-select" @on-change="dateSelect" :limitDate="limitData" v-model="curDate"></month-select>
      </div>
      <Col span="24">
        <!-- 货运量 -->
        <Card shadow class="cargoVolumeDiv tool-num" style="position: relative;">
          <!-- <div @click="showModelBtn('total')" class="morebtn">more &gt;</div> -->
          <Row>
            <Col span="5">
              <h4>总货运量</h4>
              <!-- <p class="numDate">{{ statisticGoodsList.now_date ? statisticGoodsList.now_date.split('/')[0] : '' }}/01/01 - {{ statisticGoodsList.now_date }}</p> -->
              <count-to class="num-data" :end="statisticGoodsList.amount_sum" unitText="万吨" unitClass="num-unit" :decimals="decimals3" usegroup/>
            </Col>
            <Col span="5">
              <h4>化学品货运量</h4>
              <!-- <p class="numDate">{{ statisticGoodsList.now_date ? statisticGoodsList.now_date.split('/')[0] : '' }}/01/01 - {{ statisticGoodsList.now_date }}</p> -->
              <count-to class="num-data" :end="statisticGoodsList.chemical_amount_sum" unitText="万吨" unitClass="num-unit" :decimals="decimals3" usegroup/>
            </Col>
            <Col span="5">
              <h4>油品货运量</h4>
              <!-- <p class="numDate">{{ statisticGoodsList.now_date ? statisticGoodsList.now_date.split('/')[0] : '' }}/01/01 - {{ statisticGoodsList.now_date }}</p> -->
              <count-to class="num-data" :end="statisticGoodsList.oils_amount_sum" unitText="万吨" unitClass="num-unit" :decimals="decimals3" usegroup/>
            </Col>
            <Col span="5">
              <h4>液化气货运量</h4>
              <count-to class="num-data" :end="statisticGoodsList.liquid_gas_amount_sum" unitText="万吨" unitClass="num-unit" :decimals="decimals3" usegroup/>
            </Col>
            <Col span="4">
              <h4>总航次</h4>
              <!-- <p class="numDate">{{ statisticGoodsList.now_date ? statisticGoodsList.now_date.split('/')[0] : '' }}/01/01 - {{ statisticGoodsList.now_date }}</p> -->
              <count-to class="num-data" :end="statisticGoodsList.voyage_total" unitText="次" unitClass="num-unit" usegroup/>
            </Col>
          </Row>
          <Spin size="large" fix v-if="totalSpinShow">
            <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
            <div>Loading</div>
          </Spin>
        </Card>
        <!-- 船舶营运总览 -->
        <Card class="cargoVolumeDiv" shadow style="position: relative;">
          <!-- <div @click="showModelBtn('operation')" class="morebtn">more &gt;</div> -->
          <Spin size="large" fix v-if="shipSpinShow">
            <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
            <div>Loading</div>
          </Spin>
          <Row :gutter="20">
            <Col span="15">
              <ChartBar style="height: 300px;" :value="barData" text="船舶营运总览" rotate="45" unit="万吨"></ChartBar>
            </Col>
            <Col span="9">
              <chart-pie style="height: 300px;" :value="pieData" :color="transColor" text="运输货品占比" unit="万吨"></chart-pie>
            </Col>
          </Row>
          <Row class="chartNumClass">
            <Col span="3" offset="2">
              <div class="num-title">货运量</div>
              <count-to class="num-chart" :end="chartGoodsNum" unitClass="num-unit" :decimals="decimals2"/>
              <p>万吨</p>
            </Col>
            <Col span="3">
              <div class="num-title">周转量</div>
              <count-to class="num-chart" :end="chartTurnoverNum" unitClass="num-unit" :decimals="decimals2"/>
              <p>万吨公里</p>
            </Col>
            <Col span="3">
              <div class="num-title">航次数</div>
              <count-to class="num-chart" :end="chartVolumeNum" unitClass="num-unit"/>
              <p>次</p>
            </Col>
            <Col span="3">
              <div class="num-title">航程</div>
              <count-to class="num-chart" :end="chartVoyageNum" unitClass="num-unit" :decimals="decimals2"/>
              <p>万海里</p>
            </Col>
            <Col span="3">
              <div class="num-title">待泊时长</div>
              <count-to class="num-chart" :end="chartWaitingTime" unitClass="num-unit"/>
              <p>小时</p>
            </Col>
            <Col span="3">
              <div class="num-title">总损耗</div>
              <count-to class="num-chart" :end="chartTotalLoss" unitClass="num-unit" :decimals="decimals4"/>
              <p>万吨</p>
            </Col>
            <Col span="3">
              <div class="num-title">总损耗率</div>
              <count-to class="num-chart" :end="chartAverageLossRate" unitClass="num-unit" :decimals="decimals2"/>
              <p>‰</p>
            </Col>
          </Row>
        </Card>
        <!-- 船舶本年度完结航次次数 -->
        <Card class="cargoVolumeDiv" shadow>
          <Row :gutter="20">
            <Col span="12">
              <Card shadow style="position: relative;">
                <!-- <div @click="showModelBtn('voyage')" class="morebtn">more &gt;</div> -->
                <Spin size="large" fix v-if="voyageSpinShow">
                  <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
                  <div>Loading</div>
                </Spin>
                <!-- <chart-line style="height: 300px;" unit="航次/次" :value="voyageNumData" :color="lineColor" rotate="45" text="船舶本年度完结航次次数"/> -->
                <ChartBar style="height: 300px;" :value="voyageNumData" text="船舶本年度完结航次次数" rotate="45" subtext="航次/次" unit="次"></ChartBar>
              </Card>
            </Col>
            <Col span="6">
              <Card shadow style="position: relative;">
                <!-- <div @click="showModelBtn('goods')" class="morebtn">more &gt;</div> -->
                <Spin size="large" fix v-if="voyageNumSpinShow">
                  <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
                  <div>Loading</div>
                </Spin>
                <chart-pie style="height: 300px;" :value="voyageNumbarData" :legend="transLegendShow" :color="transColor" text="航程次数占比" unit="次"></chart-pie>
              </Card>
            </Col>
            <Col span="6">
              <Card shadow style="position: relative;">
                <!-- <div @click="showModelBtn('goods')" class="morebtn">more &gt;</div> -->
                <Spin size="large" fix v-if="southNorthNumSpinShow">
                  <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
                  <div>Loading</div>
                </Spin>
                <chart-pie style="height: 300px;" :value="southNorthNumData" :legend="transLegendShow" :color="transColor" text="南上北下次数占比" unit="次"></chart-pie>
              </Card>
            </Col>
          </Row>
        </Card>
        <Card class="cargoVolumeDiv" shadow>
          <Row :gutter="20">
            <Col span="12">
              <Card shadow style="position: relative;">
                <!-- <div @click="showModelBtn('toanchor')" class="morebtn">more &gt;</div> -->
                <Spin size="large" fix v-if="anchorSpinShow">
                  <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
                  <div>Loading</div>
                </Spin>
                <!-- <chart-line style="height: 300px;" :value="anchorTimeData" :color="lineColor" rotate="45" text="船舶本年度完结航次总抛锚统计"/> -->
                <ChartBar style="height: 300px;" :value="anchorTimeData" text="船舶本年度完结航次总抛锚统计" :color="lineColor" rotate="45" unit="小时,次" legendPosition="30"></ChartBar>
              </Card>
            </Col>
            <Col span="12">
              <Card shadow style="position: relative;">
                <!-- <div @click="showModelBtn('loadrate')" class="morebtn">more &gt;</div> -->
                <Spin size="large" fix v-if="loadSpinShow">
                  <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
                  <div>Loading</div>
                </Spin>
                <!-- <chart-line style="height: 300px;" unit="重载率%" :value="voyageRateData" :color="lineColor" rotate="45" text="船舶本年度完结航次平均重载率"/> -->
                <ChartBar style="height: 300px;" :value="voyageRateData" text="船舶本年度完结航次平均重载率" rotate="45" unit="%"></ChartBar>
              </Card>
            </Col>
          </Row>
        </Card>
      </Col>
    </Row>
    <totalChart v-if="showModelTotal" @callback="goBackHome('total')"></totalChart><!-- 总货运量 -->
    <operationChart v-if="showModelOperation" @callback="goBackHome('operation')"></operationChart><!-- 船舶营运总览 -->
    <voyageChat v-if="showModelVoyage" @callback="goBackHome('voyage')"></voyageChat><!-- 航程统计 -->
    <goodsChart v-if="showModelGoods" @callback="goBackHome('goods')"></goodsChart><!-- 货运量/周转量 -->
    <loadrateChart v-if="showModelLoadrate" @callback="goBackHome('loadrate')"></loadrateChart><!-- 抛锚时长 装卸速率 -->
  </div>
</template>

<script>
import { ChartPie, ChartBar, ChartLine, ChartBarAndLine } from '_c/charts'
import CountTo from '_c/count-to'
import API from '@/api/statistics'
import { queryStatTime } from '@/api/basicData.js'
import goodsChart from './moreChart/goodsChart'
import loadrateChart from './moreChart/loadrateChart'
import operationChart from './moreChart/operationChart'
import totalChart from './moreChart/totalChart'
import voyageChat from './moreChart/voyageChat'
import MonthSelect from '@/components/monthSelect'

export default {
  components: {
    ChartPie,
    ChartBar,
    ChartLine,
    CountTo,
    ChartBarAndLine,
    goodsChart,
    loadrateChart,
    operationChart,
    totalChart,
    voyageChat,
    MonthSelect
  },
  data () {
    return {
      totalSpinShow: false,
      shipSpinShow: false,
      voyageSpinShow: false,
      voyageNumSpinShow: false,
      southNorthNumSpinShow: false,
      anchorSpinShow: false,
      loadSpinShow: false,
      showHomePage: true, // 首页展示状态
      showModelTotal: false,
      showModelOperation: false,
      showModelVoyage: false,
      showModelGoods: false,
      showModelToanchor: false,
      showModelLoadrate: false,
      statisticGoodsList: {
        amount_sum: 0, // 总货运量
        chemical_amount_sum: 0, // 化学品货运量
        oils_amount_sum: 0, // 油品货运量
        liquid_gas_amount_sum: 0, // 液化气货运量
        voyage_total: 0 // 总航次
      },
      dynamicListData: [], // 动态数据
      dynamicDetailResult: '-', // 航线信息
      dynamicGoodsResult: '-', // 货品信息
      goodsNameData: [{
        goods_name: '纯苯'
      }], // 货品信息
      pieData: [],
      barData: {
        xAxis: [],
        data: []
      },
      limitData: [],
      decimals2: 2, // 保留两位小数
      decimals3: 3, // 保留三位小数
      decimals4: 4, // 保留四位小数
      chartGoodsNum: 0, // 货运量
      chartTurnoverNum: 0, // 周转量
      chartVolumeNum: 0, // 航次数
      chartVoyageNum: 0, // 航程
      chartWaitingTime: 0, // 待泊时长
      chartTotalLoss: 0, // 总损耗
      chartAverageLossRate: 0, // 平均损耗率
      voyageNumData: { // 船舶本年度完结航次次数
        xAxis: [],
        data: []
      },
      voyageNumbarData: [], // 航程次数占比
      southNorthNumData: [], // 南上北下占比
      anchorTimeData: { // 船舶本年度完结航次总抛锚统计
        xAxis: [],
        legend: ['时长', '次数'],
        data: [[], []],
        yAxis: [
          { name: '抛锚时长/小时' },
          { name: '抛锚72h+次数' }
        ]
      },
      voyageRateData: { // 船舶本年度完结航次平均重载率
        xAxis: [],
        data: []
      },
      queryParam: {
        start_month: '',
        end_month: ''
      },
      setTimeParam: {
        start_month: '',
        end_month: ''
      },
      transLegendShow: false,
      lineColor: ['#6699FF', '#E74823'],
      transColor: ['#5B8FF9', '#5AD8A6', '#5D7092', '#F6BD16', '#E8684A', '#6DC8EC', '#9270CA', '#FF9D4D', '#269A99', '#FF99C3']
    }
  },
  created () {
    queryStatTime().then(res => { // 获取时间区间
      if (res.data.Code === 10000) {
        if (res.data.start_month && res.data.end_month) {
          this.queryParam.start_month = res.data.start_month
          this.queryParam.end_month = res.data.end_month
          // this.limitData = [ res.data.start_month, res.data.end_month ] // 沟通后暂时放开限制 （2022.2.14）
        }
      }
      this.getChartsData()
    })
  },
  methods: {
    // 获取统计总览所有数据
    getChartsData () {
      this.resetResults()
      // 获取货运问题及价格数据
      this.totalSpinShow = true
      this.shipSpinShow = true
      this.voyageSpinShow = true
      this.voyageNumSpinShow = true
      this.southNorthNumSpinShow = true
      this.anchorSpinShow = true
      this.loadSpinShow = true
      API.querySumAmounts(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          this.totalSpinShow = false
          this.statisticGoodsList = Object.assign(res.data, {
            amount_sum: res.data.amount_sum === '' ? 0 : parseFloat(res.data.amount_sum), // 总货运量
            chemical_amount_sum: res.data.chemical_amount_sum === '' ? 0 : parseFloat(res.data.chemical_amount_sum), // 化学品货运量
            oils_amount_sum: res.data.oils_amount_sum === '' ? 0 : parseFloat(res.data.oils_amount_sum), // 油品货运量
            liquid_gas_amount_sum: res.data.liquid_gas_amount_sum === '' ? 0 : parseFloat(res.data.liquid_gas_amount_sum),
            voyage_total: res.data.voyage_total === '' ? 0 : parseFloat(res.data.voyage_total) // 总航次
          })
        }
      })
      // 船舶运营总览
      API.queryShipService(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          this.shipSpinShow = false
          this.voyageNumSpinShow = false
          this.southNorthNumSpinShow = false
          this.chartGoodsNum = parseFloat(res.data.amount_sum) // 货运量
          this.chartTurnoverNum = parseFloat(res.data.turnover_sum) // 周转量
          this.chartVolumeNum = parseInt(res.data.voyage_sum) // 航次数
          this.chartVoyageNum = parseFloat(res.data.mile_sum) // 航程
          this.chartWaitingTime = parseFloat(res.data.wait_berth_sum) // 待泊时长
          this.chartTotalLoss = parseFloat(res.data.goods_loss_sum) // 总损耗
          this.chartAverageLossRate = parseFloat(res.data.goods_loss_average) // 平均损耗
          res.data.shipAmountSummary.forEach(item => { // 船舶运营总览
            if (!item.ship_name.includes('万华') && item.amount_sum !== '0') { // 剔除万华8 剔除数据为0船舶
              this.barData.xAxis.push(item.ship_name)
              this.barData.data.push(item.amount_sum)
            }
          })
          res.data.goodsAmountArray.forEach(item => { // 运输货品占比
            this.pieData.push({
              value: item.goods_amount_sum,
              name: item.goods_name
            })
          })
        }
      })
      // 船舶本年度完结航次次数
      API.queryShipVoyageSum(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          this.voyageSpinShow = false
          res.data.Result.forEach(item => {
            if (!item.ship_name.includes('万华') && item.voyage_total !== '0') { // 剔除万华8  剔除数据为0船舶
              this.voyageNumData.xAxis.push(item.ship_name)
              this.voyageNumData.data.push(item.voyage_total)
            }
          })
        }
      })
      // 航程占比
      API.queryVoyageRatio(this.queryParam).then(res => {
        if (res.data.Code === 10000 && res.data.Result.length > 0) {
          this.voyageNumSpinShow = false
          res.data.Result.forEach(item => {
            this.voyageNumbarData.push({
              value: item.count_level,
              name: item.mile_level_name
            })
          })
        }
      })
      // 南上北下占比
      API.queryDirectionNumRatio(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          this.southNorthNumSpinShow = false
          res.data.Result.forEach(item => {
            this.southNorthNumData.push({
              value: item.direction_count,
              name: item.direction_name
            })
          })
        }
      })
      // 船舶本年度完结航次总抛锚统计
      API.queryShipAnchor(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          this.anchorSpinShow = false
          res.data.shipAnchorTimeSumArray.forEach(item => {
            if (!item.ship_name.includes('万华') && item.anchor_time_sum !== '0') { // 剔除万华8  剔除数据为0船舶
              this.anchorTimeData.xAxis.push(item.ship_name)
              this.anchorTimeData.data[0].push(item.anchor_time_sum)
            }
          })
          res.data.shipAnchorTimeRedLineNumArray.forEach(item => {
            if (!item.ship_name.includes('万华') && item.count_num !== '0') { // 剔除万华8  剔除数据为0船舶
              this.anchorTimeData.data[1].push(item.count_num)
            }
          })
        }
      })
      // 船舶本年度完结航次平均重载率
      API.queryHeaveRateAverage(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          this.loadSpinShow = false
          res.data.Result.forEach(item => {
            if (!item.ship_name.includes('万华') && item.heave_mile_rate_average !== '0') { // 剔除万华8  剔除数据为0船舶
              this.voyageRateData.xAxis.push(item.ship_name)
              this.voyageRateData.data.push(item.heave_mile_rate_average)
            }
          })
        }
      })
    },
    // 日期变化触发
    dateSelect (dateObj) {
      this.queryParam.start_month = dateObj[0] ? dateObj[0] : ''
      this.queryParam.end_month = dateObj[1] ? dateObj[1] : ''
      this.getChartsData()
    },
    // 重置
    resetResults () {
      this.pieData = []
      this.voyageNumbarData = []
      this.southNorthNumData = []
      this.barData.data = []
      this.barData.xAxis = []
      this.voyageNumData.data = []
      this.voyageNumData.xAxis = []
      this.anchorTimeData.data = [[], []]
      this.anchorTimeData.xAxis = []
      this.voyageRateData.data = []
      this.voyageRateData.xAxis = []
    },
    // 点击事件
    showModelBtn (d) {
      if (d === 'total') {
        this.showModelTotal = true
      } else if (d === 'operation') {
        this.showModelOperation = true
      } else if (d === 'voyage') {
        this.showModelVoyage = true
      } else if (d === 'goods') {
        this.showModelGoods = true
      } else if (d === 'toanchor') {
        this.showModelToanchor = true
      } else if (d === 'loadrate') {
        this.showModelLoadrate = true
      }
      this.showHomePage = false
    },
    // 返回首页
    goBackHome (d) {
      if (d === 'total') {
        this.showModelTotal = false
      } else if (d === 'operation') {
        this.showModelOperation = false
      } else if (d === 'voyage') {
        this.showModelVoyage = false
      } else if (d === 'goods') {
        this.showModelGoods = false
      } else if (d === 'toanchor') {
        this.showModelToanchor = false
      } else if (d === 'loadrate') {
        this.showModelLoadrate = false
      }
      this.showHomePage = true
    }
  },
  computed: {
    curDate () {
      if (this.queryParam.start_month !== '' && this.queryParam.end_month !== '') {
        return this.queryParam.start_month + '~' + this.queryParam.end_month
      }
      if (this.queryParam.start_month !== '' && this.queryParam.end_month === '') {
        return this.queryParam.start_month
      }
      return ''
    }
  }
}
</script>
<style scoped>
.ivu-card-shadow {
  box-shadow: none;
}
</style>
<style lang="less" scoped>
.btn-area {
  text-align: right;
  margin-bottom: 10px;
  position: relative;
  .month-select {
    margin-right: 10px;
  }
}
.goods-area {
  margin: 10px 0 10px;
  overflow-x: auto;
  overflow-y: hidden;
  white-space: nowrap;
  flex-wrap: nowrap;
  .goods-area-span {
    display: inline-block;
    padding: 7px 9px;
    background: #fff;
    border-radius: 6px;
    margin-right: 20px;
    font-size: 14px;
    .goods-name {
      color: #2B304C;
      font-weight: bold;
    }
    .goods-up {
      color: #F42323;
    }
    .goods-down {
      color: #2ACB97;
    }
    .goods-trend {
      margin-left: 9px;
    }
  }
}
.cargoVolumeDiv {
  margin-bottom: 10px;
  position: relative;
  .morebtn {
    right: 5px;
    top: 5px;
    position: absolute;
    font-size: 12px;
    color: #666;
    cursor: pointer;
    font-family: Arial, Helvetica, sans-serif;
  }
  .count-to-wrapper .content-outer .count-to-unit-text {
    font-size: 11px;
    color: #2B304C;
  }
  .leftborder {
    position: relative;
    &::before {
      content: '';
      width: 1px;
      height: 20px;
      left: -15px;
      top: 0;
      position: absolute;
      display: inline-block;
      background-color: #E9E9E9;
    }
  }
}
.tool-num {
  .numDate {
    font-size: 12px;
    color: #7C8093;
  }
  .num-data {
    color: #2B304C;
    margin-top: 13px;
    font-size: 20px;
    font-weight: bold;
  }
}
.chartNumClass {
  .ivu-col-span-3 {
    position: relative;
    &::after {
      content: '';
      width: 1px;
      height: 20px;
      display: inline-block;
      position: absolute;
      right: 20%;
      top: 23%;
      background-color: rgba(233,233,233,1);
    }
  }
  .ivu-col-span-3:last-child::after {
    display: none;
  }
}
.num-unit {
  font-size: 10px;
  font-weight: 100;
  margin-left: 6px;
}
.dynamicdatas {
  overflow: hidden;
  color: #383D4A;
  margin-bottom: 10px;
  .ivu-card-body {
    padding: 10px 16px;
    display: flex;
    align-items: center;
  }
}
.leftborder {
  vertical-align: middle;
}
.playVoyage {
  padding: 5px 15px;
  overflow: hidden;
  background-color: #D3E3F8;
}
.demo-spin-col {
  height: 100px;
  position: relative;
  border: 1px solid #eee;
}
.demo-spin-icon-load{
  animation: ani-demo-spin 1s linear infinite;
}
@keyframes ani-demo-spin {
  from { transform: rotate(0deg);}
  50%  { transform: rotate(180deg);}
  to   { transform: rotate(360deg);}
}
</style>
