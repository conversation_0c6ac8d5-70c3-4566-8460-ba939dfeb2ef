import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 查询单号
export function queryWaybillNumber (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/sys/main/queryWaybillNumber',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询单货品港口动态信息
export function queryWaybillNumberByNamic (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/sys/main/queryWaybillNumberByNamic',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询航次进度
export function queryWaybillNumberByProgress (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/sys/main/queryWaybillNumberByProgress',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  queryWaybillNumber,
  queryWaybillNumberByNamic,
  queryWaybillNumberByProgress
}
