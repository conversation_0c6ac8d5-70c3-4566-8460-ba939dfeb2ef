<template>
  <Modal
    v-model="visible"
    title="生成排期"
    :loading="loading"
    :mask-closable="false"
    @on-ok="handleSubmit"
    @on-cancel="handleCancel">
    <Form ref="scheduleForm" :model="form" :rules="rules" :label-width="100">
      <FormItem label="开始时间" prop="belong_month">
        <DatePicker
          type="month"
          placeholder="请选择所属月份"
          v-model="form.belong_month"
          style="width: 100%">
        </DatePicker>
      </FormItem>
      <!-- <FormItem label="开始时间" prop="start_plan_date_st">
        <DatePicker
          type="date"
          placeholder="请选择开始时间"
          v-model="form.start_plan_date_st"
          style="width: 100%">
        </DatePicker>
      </FormItem>
      <FormItem label="结束时间" prop="start_plan_date_ed">
        <DatePicker
          type="date"
          placeholder="请选择结束时间"
          v-model="form.start_plan_date_ed"
          :options="dateOptions"
          style="width: 100%">
        </DatePicker>
      </FormItem> -->
    </Form>
    <div slot="footer">
      <Button @click="handleCancel">取消</Button>
      <Button type="primary" :loading="loading" @click="handleSubmit">确定</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'ScheduleModal',
  
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    // 日期验证规则
    const validateEndDate = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请选择结束时间'));
      }
      if (this.form.start_plan_date_st && value < this.form.start_plan_date_st) {
        return callback(new Error('结束时间不能早于开始时间'));
      }
      callback();
    };
    
    return {
      visible: this.value,
      loading: false,
      form: {
        belong_month: ''
        // start_plan_date_st: '',
        // start_plan_date_ed: ''
      },
      rules: {
        belong_month: [
          { required: true, type: 'date', message: '请选择所属月份', trigger: 'change' }
        ]
        // start_plan_date_st: [
        //   { required: true, type: 'date', message: '请选择开始时间', trigger: 'change' }
        // ],
        // start_plan_date_ed: [
        //   { required: true, type: 'date', message: '请选择结束时间', trigger: 'change' },
        //   { validator: validateEndDate, trigger: 'change' }
        // ]
      },
      // 日期选择器配置
      dateOptions: {
        disabledDate: (date) => {
          return date && date.valueOf() < Date.now() - 86400000; // 禁用小于当前日期的日期
        }
      }
    };
  },
  
  watch: {
    value(val) {
      this.visible = val;
      if (val) {
        this.resetForm();
      }
    },
    
    visible(val) {
      this.$emit('input', val);
    }
  },
  
  methods: {
    // 重置表单
    resetForm() {
      this.form = {
        belong_month: ''
        // start_plan_date_st: '',
        // start_plan_date_ed: ''
      };
      
      // 在下一个事件循环中重置表单验证
      this.$nextTick(() => {
        if (this.$refs.scheduleForm) {
          this.$refs.scheduleForm.resetFields();
        }
      });
    },
    
    // 处理表单提交
    handleSubmit() {
      this.loading = true;
      
      this.$refs.scheduleForm.validate((valid) => {
        if (valid) {
          // 格式化日期为字符串
          // const startDate = this.formatDate(this.form.start_plan_date_st);
          // const endDate = this.formatDate(this.form.start_plan_date_ed);
          const belong_month = this.formatDate(this.form.belong_month)
          // 发送成功事件，传递日期数据
          // this.$emit('on-success', { startDate, endDate });
          this.$emit('on-success', belong_month)
          
          // 重置状态
          this.loading = false;
          this.visible = false;
        } else {
          this.loading = false;
          this.$Message.error('请填写完整信息');
        }
      });
    },
    
    // 处理取消
    handleCancel() {
      this.visible = false;
      this.$emit('on-cancel');
    },
    
    // 格式化日期为YYYY-MM-DD字符串
    formatDate(date) {
      if (!date) return '';
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      return `${year}-${month}`;
    }
  }
};
</script>

<style>
/* Modal内部样式 - 使用全局选择器 */
.ivu-modal-body {
  padding: 16px;
}

.ivu-form-item {
  margin-bottom: 24px;
}

.ivu-form-item-error-tip {
  padding-top: 4px;
}

.ivu-modal-footer {
  border-top: 1px solid #e8eaec;
  padding: 12px 18px;
}
</style>