<template>
  <fullscreen ref="fullscreen" @change="fullscreenChange">

    <div class="fleet-monitor">
      <!-- 顶部标题栏 -->
      <header class="header">
        <div class="logo">
          <i class="fas fa-ship logo-icon"></i>
          <span class="logo-text">Fleet Monitor</span>
        </div>
        <div class="datetime">{{ currentDateTime }}</div>
        <div class="notification-area">
          <div class="notification-container">
            <transition-group name="notification-transition" tag="div" class="notification-scroll">
              <div class="alert" v-for="(notice, index) in notifications" :key="index"
                v-show="currentNotificationIndex === index">
                <i class="fas" :class="notice.icon" :style="{ color: getNoticeColor(notice.type) }"></i>
                <div class="alert-text-container">
                  <div class="alert-text" :class="{ 'scrolling-text': isTextOverflow(notice.text) }">
                    <span>{{ notice.text }}</span>
                  </div>
                </div>
              </div>
            </transition-group>
          </div>
          <div class="bell">
            <i class="fas fa-bell"></i>
            <span class="badge">{{ notifications.length }}</span>
          </div>
        </div>
        <Tooltip :content="fullscreen ? '退出全屏' : '全屏'" placement="bottom" style="position: absolute;right: 20px;">
          <Icon style="cursor: pointer; line-height: 30px; " @click.native="toggleFullScreen"
            :type="fullscreen ? 'md-contract' : 'md-expand'" :size="23"></Icon>
        </Tooltip>
      </header>

      <!-- 统计卡片区域 -->
      <div class="stat-cards">
        <div class="stat-card">
          <div class="stat-icon blue">
            <i class="fas fa-chart-line"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">
              <count-to class="num-chart" :end="statistic.chartVolumeNum" unitClass="num-unit" :usegroup="true" />
              <span class="unit">次</span>
            </div>
            <div class="stat-label">月航次数</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon purple">
            <i class="fas fa-ship"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value"> <count-to class="num-chart" :end="statistic.chartGoodsNum" unitClass="num-unit"
                :usegroup="true" :decimals="2" /> <span class="unit">万吨</span></div>
            <div class="stat-label">吨位量</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon green">
            <i class="fas fa-sync-alt"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value"> <count-to class="num-chart" :end="statistic.chartTurnoverNum" unitClass="num-unit"
                :usegroup="true" :decimals="2" /> <span class="unit">万公里</span></div>
            <div class="stat-label">周转量</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon orange">
            <i class="fas fa-clock"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value"> <count-to class="num-chart" :end="statistic.chartWaitingTime" unitClass="num-unit"
                :usegroup="true" />
              <span class="unit">小时</span>
            </div>
            <div class="stat-label">锚泊时长</div>
          </div>
        </div>
      </div>

      <!-- 主内容区域 -->
      <div class="main-content" :style="{ height: !fullscreen ? '800px' : 'calc(100vh - 135px)' }">
        <!-- 左侧表格区域 -->
        <div class="left-panel">
          <div class="fleet-table-card">
            <div class="card-title">
              <div class="title-text">
                <i class="fas fa-list-ul"></i> 船队列表
              </div>
              <div class="autoplay-toggle">
                <input type="checkbox" id="autoplay-toggle" v-model="autoPlay" @change="toggleAutoPlay">
                <label for="autoplay-toggle">
                  <div class="toggle-track">
                    <div class="toggle-indicator"></div>
                  </div>
                  <span class="toggle-label">{{ autoPlay ? '自动播放' : '手动控制' }}</span>
                </label>
              </div>
            </div>
            <div class="fleet-table" ref="tbodyRef">
              <table>
                <thead>
                  <tr>
                    <th>船名</th>
                    <th>状态</th>
                    <th>当前航次</th>
                    <th>装货/卸货</th>
                    <th>计划航次</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(ship, index) in dataList" :key="index"
                    :class="[getRowClass(ship.status, ship.anchorHours), { 'highlighted-ship': autoPlay && currentShipIndex === index }]"
                    @mouseenter="pauseAutoHighlight" @mouseleave="resumeAutoHighlight"
                    @click.stop="currentShipIndex = index">
                    <td> <a @click.stop="openShipDetail(ship, index)" style="cursor: pointer;font-weight: 600;">{{
                      ship.ship_name }}</a></td>
                    <td>
                      <div :class="['status-tag', getStatusClass(ship.status)]">
                        <i :class="getStatusIcon(ship.status)"></i> {{ ship.status }}
                        <span v-if="ship.status === '锚泊'" class="anchor-hours">{{ ship.anchorHours }}小时</span>
                      </div>
                    </td>
                    <td :class="{'text-danger': ship.currentRoute === '待下发'}">{{ ship.currentRoute }}</td>
                    <td>{{ ship.cargo }}</td>
                    <td>{{ ship.plannedRoute }} {{ ship.planCargo }}</td>
                  </tr>
                </tbody>
              </table>
              <Spin size="large" class="table-loading" fix v-if="loading"></Spin>
            </div>
          </div>
        </div>

        <!-- 右侧地图和港口信息区域 -->
        <div class="right-panel">
          <!-- 地图区域 -->
          <div class="map-card">
            <div class="card-title">
              <i class="fas fa-map-marked-alt"></i> 船舶分布
            </div>
            <div class="map-container">
              <div class="map-content">
                <div id="map"></div>
              </div>
            </div>
          </div>

          <!-- 港口信息区域 -->
          <div class="port-info-card">
            <div class="card-title">
              <i class="fas fa-anchor"></i> 港口信息
            </div>

            <!-- 天气信息 -->
            <div class="weather-section" style="position: relative;">
              <div class="section-title">
                <i class="fas fa-cloud-sun"></i> {{
                  dataList[currentShipIndex] ? dataList[currentShipIndex].unloadPortNames : '' }} 天气预报
              </div>
              <template v-if="weatherInfo[currentShipIndex]">
                <div class="weather-current">
                  <i class="fas weather-icon"
                    :class="[getWeatherIcon(weatherInfo[currentShipIndex][0].data[0].weather[0].description).icon, getWeatherIcon(weatherInfo[currentShipIndex][0].data[0].weather[0].description).color]"></i>
                  <div class="weather-info">
                    <div class="city-name">{{ dataList[currentShipIndex].unloadPortNames }}</div>
                    <div class="temperature">{{ weatherInfo[currentShipIndex][0].data[0].weather[0].description }}, {{
                      weatherInfo[currentShipIndex][0].data[0].main.temp }}°C</div>
                  </div>
                  <div class="weather-update">
                    <div>
                      {{ weatherInfo[currentShipIndex][0].data[0].wind.deg }} {{
                        weatherInfo[currentShipIndex][0].data[0].wind.speed.toFixed(0) }}级
                    </div>
                    <!-- <div>能见度 {{ weatherInfo[currentShipIndex][0].data[0].visibility }}km</div> -->
                  </div>
                </div>

                <div class="weather-forecast">
                  <div class="forecast-item" v-for="(item, index) in weatherInfo[currentShipIndex]" :key="index"
                    v-if="index !== 0">
                    <div class="day">{{ dayMap[item.day] }}</div>
                    <i class="fas weather-icon-small"
                      :class="[getWeatherIcon(item.data[0].weather[0].description).icon, getWeatherIcon(item.data[0].weather[0].description).color]"></i>
                    <div class="temp">{{ item.maxTemperature }}°/{{ item.minTemperature }}°</div>
                  </div>
                </div>
              </template>
              <Spin size="large" fix v-else></Spin>
            </div>

            <div class="divider"></div>

            <!-- 船舶统计 -->
            <div class="ship-stats-section">
              <div class="section-title">
                <i class="fas fa-info-circle"></i> {{
                  dataList[currentShipIndex] ? dataList[currentShipIndex].unloadPortNames : '' }} 港口船舶信息
              </div>
              <div class="ship-stats" style="position: relative;">
                <div class="stat-item">
                  <i class="fas fa-ship stat-item-icon ship-blue"></i>
                  <div class="stat-number">{{ portTrafficList.length ? portTrafficList[currentShipIndex].estimate : 0 }}
                  </div>
                  <div class="stat-item-label">预抵</div>
                </div>
                <div class="stat-item">
                  <i class="fas fa-arrow-up stat-item-icon departure-blue"></i>
                  <div class="stat-number">{{ portTrafficList.length ? portTrafficList[currentShipIndex].wait : 0 }}
                  </div>
                  <div class="stat-item-label">等泊</div>
                </div>
                <div class="stat-item">
                  <i class="fas fa-anchor stat-item-icon dock-green"></i>
                  <div class="stat-number">{{ portTrafficList.length ? portTrafficList[currentShipIndex].work : 0 }}
                  </div>
                  <div class="stat-item-label">作业</div>
                </div>
                <Spin size="large" fix v-if="portLoading"></Spin>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 船舶详情弹窗 -->
      <div class="ship-detail-modal" v-if="showShipDetail" @click.self="closeShipDetail">
        <div class="ship-detail-container" :class="{ 'show': showShipDetail }">
          <div class="modal-header">
            <div class="ship-title">
              <i class="fas fa-ship"></i> {{ selectedShip.ship_name }} #{{ selectedShipIndex + 1 }}
            </div>
            <button class="close-button" @click="closeShipDetail">
              <i class="fas fa-times"></i>
            </button>
          </div>

          <div class="modal-content">
            <!-- 船舶基本信息 -->
            <div class="info-section">
              <div class="section-header">
                <i class="fas fa-info-circle"></i> 船舶基本信息
              </div>
              <div class="info-grid">

                <div class="info-item">
                  <div class="info-label">状态</div>
                  <div class="info-value">
                    <span :class="['status-tag-small', getStatusClass(selectedShip.status)]">
                      <i :class="getStatusIcon(selectedShip.status)"></i> {{ selectedShip.status }}
                      <span v-if="selectedShip.status === '锚泊'" class="anchor-hours">{{ selectedShip.anchorHours
                      }}小时</span>
                    </span>
                  </div>
                </div>
                <div class="info-item">
                  <div class="info-label">类型</div>
                  <div class="info-value">{{ shipTypeTrans(selectedShip.shiptype) }}</div>
                </div>
                <div class="info-item">
                  <div class="info-label">货物/容量</div>
                  <div class="info-value">{{ selectedShip.cargo }}/{{ selectedShip.amounts }}</div>
                </div>
                <div class="info-item">
                  <div class="info-label">长*宽</div>
                  <div class="info-value">{{ selectedShip.length / 10 }}m*{{ selectedShip.width / 10 }}m</div>
                </div>
                <div class="info-item">
                  <div class="info-label">吃水/航速</div>
                  <div class="info-value">{{ selectedShip.draught / 1000 }}米/{{ (selectedShip.sog / 100).toFixed(2) }}
                    kn
                  </div>
                </div>
                <div class="info-item">
                  <div class="info-label">航首向</div>
                  <div class="info-value">{{ (selectedShip.hdg / 100).toFixed(0) }}°</div>
                </div>
                <div class="info-item">
                  <div class="info-label">航迹向</div>
                  <div class="info-value">{{ (selectedShip.cog / 100).toFixed(0) }}°</div>
                </div>
                <div class="info-item">
                  <div class="info-label">经度/纬度</div>
                  <div class="info-value">{{ toLnglat(selectedShip.lng) }}E/{{ toLnglat(selectedShip.lat) }}N</div>
                </div>
              </div>
            </div>

            <!-- 当前航次 -->
            <div class="info-section">
              <div class="section-header">
                <i class="fas fa-map-marked-alt"></i> 当前航次
              </div>
              <div class="route-current">
                <div class="route-item">
                  <i class="fas fa-arrow-circle-right"></i>
                  <span> {{ selectedShip.currentRoute }}</span>
                  <span style="margin-left: 10px;">{{ selectedShip.cargo }} - {{ selectedShip.amounts }}T</span>
                </div>
              </div>
            </div>

            <!-- 计划航次 -->
            <div v-if="selectedShip.planList.length > 0" class="info-section">
              <div class="section-header">
                <i class="fas fa-calendar-alt"></i> 计划航次
              </div>
              <div class="route-planned">
                <div class="route-item planned" v-for="(plan, index) in selectedShip.planList" :key="index">
                  <i class="fas fa-arrow-circle-right"></i>
                  <span>{{ plan.load_port_name }} → {{ plan.unload_port_name }}</span>
                  <span style="margin-left: 10px;">{{ plan.goods_name }} - {{ plan.amounts }}T</span>
                  <div class="cargo-info">{{ plan.voyage_no }}</div>
                  <button class="plan-button">计划中</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </fullscreen>
</template>

<script>
import axios from 'axios'
import { queryShipsAmounts } from '@/api/statistics/operationAnalysis'
import { queryCurVoyageMmsi, queryVoyagePlanConsolePage } from '@/api/ais'
import API from '@/api/control'
import { getShipsTrajectory, queryMapBaseList, queryShipFormulaInfo } from '@/api/shipManagement'
import { queryVoyageMonthPlanListAndVoyagesByShipGroup } from '@/api/shipSchedule'
import { queryShipService } from '@/api/statistics'
import { getToken } from '@/libs/util'
import config from '@/config'
import { forEach } from 'lodash'
import dayjs from 'dayjs'
import CountTo from '_c/count-to'

export default {
  name: 'FleetMonitor',
  components: {
    CountTo
  },
  data() {
    return {
      worker: null,
      currentDateTime: '',
      currentShipIndex: 0,
      currentNotificationIndex: 0,
      notificationInterval: null,
      horizontalScrollActive: false,
      textWidths: {},
      highlightInterval: null,
      isHighlightPaused: false,
      autoPlay: true,
      showShipDetail: false,
      selectedShip: {},
      selectedShipIndex: 0,
      notifications: [
        { text: '兴通66 - 剩余时长超过48小时', icon: 'fa-exclamation-triangle', type: 'warning' },
        { text: '兴通739 - 锚泊时长达到50小时，请及时关注船舶动态并联系相关负责人进行处理', icon: 'fa-exclamation-circle', type: 'danger' },
        { text: '兴通17 - 锚泊时长超过72小时需要处理', icon: 'fa-exclamation-triangle', type: 'danger' }
      ],
      dataList: [],
      selfShipList: [],
      // dataList: [], // 船舶显示列表
      shipDetailList: [], // 船舶详细信息
      anchorMmsiList: [],
      anchorIdx: [],
      loading: false,
      isWanBang: false,
      shipIgnoreList: ['413375790', '413375810', '413376840', '412379380', '412379370', '413376570', '413693020'],
      weatherInfo: [
      ],
      iconWeatherMap: {
        'fa-wind': ['有风', '平静', '微风', '和风', '清风', '强风/劲风', '疾风', '大风', '烈风', '风暴', '狂爆风', '飓风', '热带风暴', '龙卷风'],
        'fa-cloud-sun': ['少云', '晴间多云', '多云', '阴', '阴，多云'],
        'fa-snowflake': ['雪', '阵雪', '小雪', '中雪', '大雪', '暴雪', '小雪-中雪', '中雪-大雪', '大雪-暴雪', '冷', '雨雪天气', '雨夹雪', '阵雨夹雪'],
        'fa-smog': ['浮尘', '扬沙', '沙尘暴', '强沙尘暴', '雾', '浓雾', '强浓雾', '轻雾', '大雾', '特强浓雾'],
        'fa-sun': ['晴', '热'],
        'fa-cloud-rain': ['阵雨', '雷阵雨', '雷阵雨并伴有冰雹', '小雨', '中雨', '大雨', '暴雨', '大暴雨', '特大暴雨', '强阵雨', '强雷阵雨', '极端降雨', '毛毛雨/细雨', '雨', '小雨-中雨', '中雨-大雨', '大雨-暴雨', '暴雨-大暴雨', '大暴雨-特大暴雨', '冻雨'],
        'fa-water': ['霾', '中度霾', '重度霾', '严重霾', '未知']
      },
      dayMap: {
        0: '周日',
        1: '周一',
        2: '周二',
        3: '周三',
        4: '周四',
        5: '周五',
        6: '周六'
      },
      searchPortList: [],
      portTrafficList: [],
      portTrafficNum: {
        estimate: 0,
        wait: 0,
        work: 0
      },
      portLoading: false,
      map: null,
      options: {
        ak: 'a5bb8f37140d428391e1546d7b704413', // '57df9eaa033b44809d4bdaf919af457e',
        // 初始中心点坐标
        centerPoint: [30.1431749158, 121.9380381277],
        // 初始缩放级别
        zoom: 7,
        // zoomSnap: 0.1, // 缩放递度
        // 最小缩放级别
        minZoom: 4,
        // 最大缩放级别
        maxZoom: 18,
        mapTypes: ['MT_SEA'],
        // 栅格
        gratings: { isShow: false },
        // 公司版权信息( 支持html )，默认Elane Inc.
        attribution: {
          isShow: false,
          emptyString:
            '&copy;2019 &nbsp;<a >兴通海运股份有限公司 保留所有版权 闽ICP备15001600号-3</a>'
        },
        measureCtrl: {
          // 是否开启测距控件，默认：true
          isShow: false,
          // 是否显示测距按钮，默认：true
          showMeasurementsMeasureControl: true,
          // 是否显示删除按钮，默认：true
          showMeasurementsClearControl: true,
          // 是否显示切换单位按钮，默认：true
          showUnitControl: true
        },
        //鼠标移动悬浮经纬度控件
        mousePostionCtrl: { isShow: false, position: 'bottomright' },
        //缩放工具控件的显示隐藏
        zoomControlElane: { isShow: true, position: 'topright' },
        // 缩放级别显示控件
        zoomviewControl: { isShow: false, position: 'topleft' },
        //地图切换控件的位置
        basemapsControl: { isShow: false, position: 'topright' },
        // 比例尺，控件
        scaleCtrl: { isShow: true, position: "bottomleft" },
      },
      statistic: {
        chartGoodsNum: 0,
        chartTurnoverNum: 0,
        chartVolumeNum: 0,
        chartWaitingTime: 0
      },
      fullscreen: false
    }
  },
  async created() {
    const token = getToken()
    const baseUrl = process.env.NODE_ENV === 'development' ? config.baseUrl.dev : config.baseUrl.pro
    this.worker = new Worker('/worker.js')
    await queryMapBaseList({ base_type: 1 }).then(res => {
      if (res.data.Code === 10000) {
        this.searchPortList = res.data.Result
      }
    })
    this.loading = true
    await this.getList()
    await this.getCurVoyage()
    await this.getPlanVoyage()

    // await this.getWarnTime()
  },
  async mounted() {

    this.startClock();
    this.startAutoHighlight();
    this.startNotificationRotation();
    this.$nextTick(() => {
      this.calculateTextWidths();
    });
    this.initMap()
    this.renameShip()
    this.getStatistic()

  },
  beforeDestroy() {
    clearInterval(this.clockInterval);
    clearInterval(this.highlightInterval);
    clearInterval(this.notificationInterval);
  },
  watch: {
    async currentShipIndex(val, oldVal) {
      this.getWeather(val)
      this.getPortDetail(val)
      this.locShip(val)
      if (val === 0) {
        this.scrollTable('top')
      } else if (val >= 12) {
        this.scrollTable('bottom')
      }
      if (val === 0 && oldVal === this.dataList.length - 1) {
        this.loading = true
        await this.getList()
        await this.getCurVoyage()
        await this.getPlanVoyage()
      }
      this.startAutoHighlight()
    }
  },
  methods: {
    scrollTable(val) {
      let container = this.$refs.tbodyRef
      if (container) {
        if (val === 'top') {
          container.scrollTop = 0
        } else {
          container.scrollTop = container.scrollHeight
        }
      }
    },
    getStatistic() {
      queryShipService({ start_month: dayjs().subtract(1, 'month').format('YYYY-MM'), end_month: dayjs().format('YYYY-MM') }).then(res => {
        if (res.data.Code === 10000) {
          this.statistic.chartGoodsNum = parseFloat(res.data.amount_sum) // 货运量
          this.statistic.chartTurnoverNum = parseFloat(res.data.turnover_sum) // 周转量
          this.statistic.chartVolumeNum = parseInt(res.data.voyage_sum) // 航次数
          // this.chartVoyageNum = parseFloat(res.data.mile_sum) // 航程
          this.statistic.chartWaitingTime = parseFloat(res.data.wait_berth_sum) // 待泊时长
          // this.chartTotalLoss = parseFloat(res.data.goods_loss_sum) // 总损耗
          // this.chartAverageLossRate = parseFloat(res.data.goods_loss_average) // 平均损耗

        }
      })
    },
    // 绘制船舶
    renameShip() {
      if (!localStorage.shipNameList) return
      let shipList = JSON.parse(localStorage.shipNameList).filter(item => item.business_model === '1' && !this.shipIgnoreList.includes(item.mmsi))
      let fleetships = []
      shipList.forEach(item => {
        var ship = item;
        var c_ship = new CanvasShip();
        c_ship.mmsi = ship.mmsi;
        c_ship.name = ship.ship_name;
        c_ship.custom_name = ship.ship_name;
        c_ship.istop = true;
        // 获取船位
        var _s_d = this.map.shipsService.getShipByMmsi(c_ship.mmsi, true);
        if (_s_d != null) {
          c_ship.lat = _s_d.lat;
          c_ship.lng = _s_d.lng;
          c_ship.shipid = _s_d.shipid;
        }
        //
        fleetships.push(c_ship);
      })
      this.map.shipsService.addFleetShips(fleetships);
    },
    // 定位船舶
    locShip(val) {

      this.map.shipsService.locationShip(this.dataList[val].mmsi, true)
      this.map.setZoom(7)

    },
    // 初始化地图
    initMap() {
      // 创建地图示例
      this.map = new ShipxyAPI.Map("map", this.options);
      // 默认 MT_SATELLITE 卫星图 MT_GOOGLE 谷歌地图 MT_SEA 海图
      this.map.basemapsControl.changeMap('MT_SEA')
      // 开启区域船服务
      const canvasShips = ShipxyAPI.ShipService(this.map, {
        enableAreaShip: false, // 区域船
        enableFleetShip: false, // 船队船
        // enableDShip: true, // D+船
        lableFont: ['600 12px Arial', '600 12px 宋体'], // 船舶名称，文字字体，默认值：["600 12px Arial", "500 12px Arial"]
        lableTxtColor: ['#000', '#eee'], // 船舶名称，文字颜色，默认值：["#000","#fff"]
        lableLineColor: ['rgba(1, 30, 62, 1)', 'rgba(1, 30, 62, 1)'], //  边框颜色，默认值：["#000","#000"]
        lableLinefillColor: ['rgba(255, 255, 255, 0.7)', 'rgba(1, 30, 62, 0.3)'], // 框内填充颜色，默认值：[null,null]
        obliqueLineColor: ['#000', '#000'], // 船舶名称，斜线颜色，默认值：[null,null]
        dShipColor: '#FF6437' // D+船颜色，默认：#ff6347
      })
      canvasShips.addSelectedListener(function (ship) {
        // 选中船监听
      });
    },
    // 获取港口详情
    async getPortDetail(index) {
      if (index === undefined) return
      if (this.portTrafficList[index]) return
      let ship = this.dataList[index]
      let portTrafficNum = {
        estimate: 0,
        wait: 0,
        work: 0
      }
      this.portTrafficList[index] = portTrafficNum
      if (!ship || !ship.unloadPortNames) return
      let portName = ship.unloadPortNames
      if (portName === '福清江阴') {
        portName = '江阴（福建）'
      }
      let portDetail = this.searchPortList.find(item => item.name === portName)

      if (portDetail) {
        this.portLoading = true
        let _param = {
          mapbase_ports_id: portDetail.id
        }
        let [estimate, wait, work] = await Promise.all([
          queryShipFormulaInfo({
            url: 'https://mobile.shipformula.com/v1/api/node/map/MapBaseControl/getPortTrafficEstimate',
            paramMap: JSON.stringify(_param)
          }),
          queryShipFormulaInfo({
            url: 'https://mobile.shipformula.com/v1/api/node/map/MapBaseControl/getPortTrafficWait',
            paramMap: JSON.stringify(_param)
          }),
          queryShipFormulaInfo({
            url: 'https://mobile.shipformula.com/v1/api/node/map/MapBaseControl/getPortTrafficWork',
            paramMap: JSON.stringify(_param)
          })
        ])
        if (estimate.data.Code === 10000 && estimate.data.Result.info) {
          portTrafficNum.estimate = estimate.data.Result.info.total || 0
        }
        if (wait.data.Code === 10000 && wait.data.Result.info) {
          portTrafficNum.wait = wait.data.Result.info.total || 0
        }
        if (work.data.Code === 10000 && work.data.Result.info) {
          portTrafficNum.work = work.data.Result.info.total || 0
        }
        this.portTrafficList[index] = portTrafficNum
        this.portLoading = false
      }
    },
    // 获取天气
    async getWeather(index) {
      if (index === undefined) return
      if (this.weatherInfo[index]) return
      let ship = this.dataList[index]

      if (!ship || !ship.unloadPortNames) return
      let portName = ship.unloadPortNames
      if (portName === '福清江阴') {
        portName = '江阴（福建）'
      }
      let portDetail = this.searchPortList.find(item => item.name === portName)
      let res = null
      if (portDetail) {
        res = await axios.get(`http://api.openweathermap.org/data/2.5/forecast?lat=${portDetail.latitude}&lon=${portDetail.longitude}&appid=c26c6e6d37b96522d668ae55da072c71&lang=zh_cn`)
      } else {
        res = await axios.get(`http://api.openweathermap.org/data/2.5/forecast?q=${portName}&appid=c26c6e6d37b96522d668ae55da072c71&lang=zh_cn`)
      }
      // OpenWeather 
      // axios.get(`http://api.openweathermap.org/data/2.5/forecast?q=${res.data.geocodes[0].city}&appid=c26c6e6d37b96522d668ae55da072c71&lang=zh_cn`).then(res => {
      if (res.data.cod === '200') {
        const weatherData = res.data.list
        // 按天分组数据
        const groupedData = {};
        weatherData.forEach((item) => {
          const date = new Date(item.dt * 1000).toISOString().split('T')[0];
          if (!groupedData[date]) {
            groupedData[date] = [];
          }
          // 温度转换为摄氏度
          const tempCelsius = item.main.temp - 273.15;
          item.main.temp = tempCelsius.toFixed(0);
          item.main.feels_like = (item.main.feels_like - 273.15).toFixed(0);
          item.main.temp_min = (item.main.temp_min - 273.15).toFixed(0);
          item.main.temp_max = (item.main.temp_max - 273.15).toFixed(0);

          // 风向转换为东西南北风
          const deg = item.wind.deg;
          let windDirection;
          if ((deg >= 337.5) || (deg < 22.5)) {
            windDirection = '北风';
          } else if (deg < 67.5) {
            windDirection = '东北风';
          } else if (deg < 112.5) {
            windDirection = '东风';
          } else if (deg < 157.5) {
            windDirection = '东南风';
          } else if (deg < 202.5) {
            windDirection = '南风';
          } else if (deg < 247.5) {
            windDirection = '西南风';
          } else if (deg < 292.5) {
            windDirection = '西风';
          } else {
            windDirection = '西北风';
          }
          item.wind.deg = windDirection;

          groupedData[date].push(item);
        });

        // 处理分组后的数据，添加一天内的最高和最低温度
        const result = Object.entries(groupedData).map(([date, data]) => {
          let minTemp = Infinity;
          let maxTemp = -Infinity;
          data.forEach((item) => {
            const temp = parseFloat(item.main.temp);
            minTemp = Math.min(minTemp, temp);
            maxTemp = Math.max(maxTemp, temp);
          });
          return {
            date,
            data,
            day: dayjs(date).day(),
            minTemperature: minTemp.toFixed(0),
            maxTemperature: maxTemp.toFixed(0)
          };
        });
        this.weatherInfo[index] = result
      }
      // axios.get(`https://restapi.amap.com/v3/weather/weatherInfo?city=${adcode}&key=2fe81a10ede49cfcafbbb0ec2104f06c&extensions=all`).then(weatherRes => {
      //   if (weatherRes.data.infocode === '10000') {
      //     this.weatherInfo = weatherRes.data.forecasts[0].casts.map((item, idx) => {
      //       return {
      //         ...item,
      //         power: isDaytime ? item.daypower : item.nightpower,
      //         temp: isDaytime ? item.daytemp : item.nighttemp,
      //         weather: isDaytime ? item.dayweather : item.nightweather,
      //         wind: isDaytime ? item.daywind : item.nightwind,
      //         day: dayjs(item.date).day()
      //       }
      //     })
      //   }
      // })
    },
    // 获取船舶列表
    async getList(val = 0) {
      let that = this
      if (!localStorage.shipNameList) return
      this.selfShipList = JSON.parse(localStorage.shipNameList)
      this.selfShipList = this.selfShipList.filter(item => item.business_model === '1' && !this.shipIgnoreList.includes(item.mmsi))
      this.dataList = []
      this.anchorMmsiList = []
      this.anchorIdx = []
      let totalShipList = []
      let mmsiList = []
      this.selfShipList.forEach((item, idx) => { // 剔除万邦船舶
        if (val === 1) { // 针对爱兰账号处理万邦船舶展示
          totalShipList.push(item)
          mmsiList.push(item.mmsi)
        } else {
          if (this.isWanBang) {
            totalShipList.push(item)
            mmsiList.push(item.mmsi)
          } else {
            if (!this.shipIgnoreList.includes(item.mmsi)) {
              totalShipList.push(item)
              mmsiList.push(item.mmsi)
            }
          }
        }
      })
      let queryUrl = 'https://api.shipxy.com/apicall/GetManyShip?v=2&k=a5bb8f37140d428391e1546d7b704413&enc=1&id=' + mmsiList.join(',')
      await axios.get(queryUrl).then(res => {
        that.shipDetailList = res.data.data

        totalShipList.forEach((item, idx) => {
          that.dataList.push({
            business_model: item.business_model,
            business_name: item.business_name,
            mmsi: item.mmsi,
            ship_name: item.ship_name,
            status_code: that.shipDetailList[idx].navistat,
            port_name: that.shipDetailList[idx].dest,
            ata: that.shipDetailList[idx].eta_std,
            status: CanvasShipUtils.getDisValue(that.shipDetailList[idx].navistat, 'naviStatus', 'zh_CN'),
            delayTime: 0,
            delayHour: '--',
            delayTimeStr: '--',
            length: that.shipDetailList[idx].length,
            width: that.shipDetailList[idx].width,
            draught: that.shipDetailList[idx].draught,
            sog: that.shipDetailList[idx].sog,
            lasttime: dayjs(that.shipDetailList[idx].lasttime).format('YYYY-MM-DD HH:mm:ss'),
            lat: that.shipDetailList[idx].lat,
            lng: that.shipDetailList[idx].lon,
            cog: that.shipDetailList[idx].cog,
            hdg: that.shipDetailList[idx].hdg,
            rot: that.shipDetailList[idx].rot,
            shiptype: that.shipDetailList[idx].shiptype,

          })

          if (that.dataList[idx] && that.dataList[idx].status_code === 1) { // 锚泊状态
            that.anchorIdx.push(idx)
            that.getLastVoyageListShipXy(item.mmsi, idx, totalShipList.length)
          }

        })



      })
    },
    // 获取预警时间
    async getWarnTime() {
      let that = this
      this.dataList.forEach((item, idx) => {

        if (item.status_code === 1) { // 锚泊状态
          that.anchorIdx.push(idx)
          that.getLastVoyageListShipXy(item.mmsi, idx, totalShipList.length)
        }
      })
    },
    // 获取历史航次列表 shipXy
    async getLastVoyageListShipXy(mmsi, idx, len) {
      const that = this
      let dateTime = new Date()
      let currentYear = dateTime.getFullYear()
      let currentMonth = dateTime.getMonth()
      let currentDate = dateTime.getDate()
      let startDay = new Date(currentYear, currentMonth - 1, currentDate).getTime().toString().substring(0, 10)
      let endDay = dateTime.getTime().toString().substring(0, 10)
      // 获取时间段内航次 船舶靠港记录
      let url1 = 'http://api.shipxy.com/apicall/GetPortOfCallByShip?k=a5bb8f37140d428391e1546d7b704413' + '&v=2&mmsi=' + mmsi + '&timetype=2&begin=' + startDay + '&end=' + endDay
      // 获取港口及到港时间
      await jQuery.getJSON(url1 + '&jsf=?', function (result) {
        if (result.records.length > 0) {
          let startDate = result.records[result.records.length - 1].atd || result.records[result.records.length - 1].ata // 先拿离港，如果没有离港就拿到港时间
          that.getCurWarnList(mmsi, idx, len, startDate)
        }
      })
    },
    // 获取预警时间 hifleet
    async getCurWarnList(mmsi, idx, len, startDate) {
      const that = this
      const date = new Date()
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')

      let currentTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
      let _param = {
        mmsis: mmsi,
        zoom: 1,
        startdates: startDate,
        endates: currentTime
      }
      await getShipsTrajectory(_param).then(res => {
        if (res.data.status !== '1') {
          // this.pushMsg()
        }

        if (res.data.data.length > 0) {
          let delayHour = that.getDelayHours(mmsi, res.data.data[0].offers)
          Object.assign(that.dataList[idx], {
            anchorHours: parseFloat(delayHour) === 0 ? 0.1 : delayHour,
          })
        } else {
          Object.assign(that.dataList[idx], {
            port_name: '--',
            ata: '--'
          })
        }
        this.trackId += 1
        if (this.trackId === this.anchorIdx.length) {
          setTimeout(() => {
            that.dataList.sort((a, b) => {
              if (parseFloat(a.delayTime) > parseFloat(b.delayTime)) {
                return -1
              } else if (parseFloat(a.delayTime) < parseFloat(b.delayTime)) {
                return 1
              } else {
                if (parseInt(a.status_code) > parseInt(b.status_code)) {
                  return -1
                } else if (parseInt(a.status_code) < parseInt(b.status_code)) {
                  return 1
                } else {
                  return 0
                }
              }
            })
            that.$nextTick(() => {
              that.modelSort()
            })
            that.loading = false
          }, 2000)
        }


      }).catch(err => {
        // this.pushMsg()
      })
    },
    // 获取天气图标
    getWeatherIcon(weather) {
      for (let key in this.iconWeatherMap) {
        if (this.iconWeatherMap[key].includes(weather)) {
          return {
            icon: key,
            color: this.getWeatherColor(key)
          }
        }
      }
      return {
        icon: 'fa-sun',
        color: 'weather-sunny'
      }
    },
    // 获取天气颜色
    getWeatherColor(iconType) {
      const colorMap = {
        'fa-wind': 'weather-wind',
        'fa-cloud-sun': 'weather-cloudy',
        'fa-snowflake': 'weather-snow',
        'fa-smog': 'weather-fog',
        'fa-sun': 'weather-sunny',
        'fa-cloud-rain': 'weather-rain',
        'fa-water': 'weather-haze'
      }
      return colorMap[iconType] || 'weather-sunny'
    },
    // 获取预警时间
    getDelayHours(mmsi, list) {
      let totalMinutes = 0
      let backHour = 0
      if (list.length > 0) {
        list.forEach(item => {
          if (item.accumulatetime && item.accumulatetime !== '-') {
            const timeStr = item.accumulatetime.replace('h', ' ').replace('m', '')
            const [hours, minutes] = timeStr.split(' ')
            totalMinutes += parseInt(hours) * 60 + parseInt(minutes)
          }
        })
        backHour = (parseFloat(totalMinutes / 60)).toFixed(1)
      } else {
        backHour = '-'
      }
      return backHour
    },
    // 获取当前航次
    async getCurVoyage() {
      queryVoyageMonthPlanListAndVoyagesByShipGroup().then(res => {
        if (res.data.Code === 10000) {
          let shipList = res.data.Result
          shipList.forEach(item => {
            item.currentList = item.planList.filter(item => item.from_flag === '0' && item.status === '2')
            item.planList = item.planList.filter(item => item.from_flag === '0' && item.status === '1')
          })
          this.dataList.forEach(item => {
            let targetItem = shipList.find(row => {
              return row.mmsi === item.mmsi
            })
            if (targetItem && targetItem.currentList.length > 0) {
              item.unloadPortNames = targetItem.currentList[0].unload_port_name
              item.currentRoute = `${targetItem.currentList[0].load_port_name}→${targetItem.currentList[0].unload_port_name}`
              item.cargo = targetItem.currentList[0].goods_name
              item.amounts = targetItem.currentList[0].amounts
            }else {
              item.currentRoute = '待下发'
              item.unloadPortNames = ''
              item.cargo = ''
              item.amounts = ''
            }
            item.planList = targetItem.planList
          })

          // 定义排序顺序
          const order = ["锚泊", "靠泊", "在航(主机推动)"];

          this.dataList.sort((a, b) => {
            const indexA = order.indexOf(a.status);
            const indexB = order.indexOf(b.status);
            if (indexA === indexB) {
              // 如果状态相同，并且是锚泊状态，按照时长降序排列
              if (a.status === "锚泊" && b.status === "锚泊") {
                return b.anchorHours - a.anchorHours;
              }
              return 0;
            }
            return indexA - indexB;
          });
          this.loading = false
          this.getWeather(0)
          this.getPortDetail(0)
          this.locShip(0)
        }
      })
    },
    // 获取计划航次
    async getPlanVoyage() {
      API.queryVoyagePlanConsolePage({
        pageSize: 1000,
        pageIndex: 1
      }).then(res => {
        if (res.data.Code === 10000) {
          this.dataList.forEach(item => {
            let targetItem = res.data.Result.find(row => {
              return row.mmsi === item.mmsi
            })
            if (targetItem) {
              item.planCargo = targetItem.cargoResult.map(item => {
                return `(${item.goods_name}:${item.amounts})`
              }).join(',')
              item.plannedRoute = `${targetItem.portResult[0].port_name}→${targetItem.portResult[1].port_name}`

            }
          })
        }
      })
    },
    // 计算文本宽度
    calculateTextWidths() {
      const container = document.querySelector('.alert-text-container');
      if (!container) return;

      const containerWidth = container.offsetWidth;
      this.notifications.forEach((notice, index) => {
        const tempSpan = document.createElement('span');
        tempSpan.style.visibility = 'hidden';
        tempSpan.style.position = 'absolute';
        tempSpan.style.whiteSpace = 'nowrap';
        tempSpan.style.font = window.getComputedStyle(container).font;
        tempSpan.textContent = notice.text;
        document.body.appendChild(tempSpan);
        this.textWidths[index] = tempSpan.offsetWidth;
        document.body.removeChild(tempSpan);
      });
    },
    isTextOverflow(text) {
      const index = this.notifications.findIndex(n => n.text === text);
      if (index === -1) return false;

      const container = document.querySelector('.alert-text-container');
      if (!container) return false;

      // 增加安全边距，确保有足够空间才不滚动
      return (this.textWidths[index] || 0) > (container.offsetWidth - 20);
    },
    getNoticeColor(type) {
      switch (type) {
        case 'warning': return '#ffb73f';
        case 'danger': return '#ff5a5a';
        case 'info': return '#38b0ff';
        default: return '#ff5a5a';
      }
    },
    startNotificationRotation() {
      this.notificationInterval = setInterval(() => {
        // 完成横向滚动后再切换通知
        const currentNotice = this.notifications[this.currentNotificationIndex];
        if (this.isTextOverflow(currentNotice.text)) {
          if (this.horizontalScrollActive) return;

          this.horizontalScrollActive = true;
          // 根据文本长度设置延迟，给足够时间完成横向滚动
          const textLength = currentNotice.text.length;
          // 最少6秒，每个字符150毫秒，确保长文本有足够时间展示
          const scrollTime = Math.max(6000, textLength * 150);

          setTimeout(() => {
            this.horizontalScrollActive = false;
            this.currentNotificationIndex = (this.currentNotificationIndex + 1) % this.notifications.length;
          }, scrollTime);
        } else {
          // 如果文本不需要横向滚动，直接切换
          this.currentNotificationIndex = (this.currentNotificationIndex + 1) % this.notifications.length;
        }
      }, 5000); // 增加基础切换间隔，让用户有更多时间阅读
    },
    toggleAutoPlay() {
      if (this.autoPlay) {
        this.startAutoHighlight();
      } else {
        clearInterval(this.highlightInterval);
        this.highlightInterval = null;
      }
    },
    startAutoHighlight() {
      if (this.highlightInterval) {
        clearInterval(this.highlightInterval);
      }
      if (this.autoPlay) {
        this.highlightInterval = setInterval(() => {
          if (!this.isHighlightPaused) {
            this.currentShipIndex = (this.currentShipIndex + 1) % this.dataList.length;
          }
        }, 10000); // 每10秒切换一次高亮船舶
      }
    },
    pauseAutoHighlight() {
      this.isHighlightPaused = true;
    },
    resumeAutoHighlight() {
      this.isHighlightPaused = false;
    },
    getRowClass(status, anchorHours) {
      if (status === '锚泊') {
        if (anchorHours >= 72) return 'row-danger';
        if (anchorHours >= 48) return 'row-warning';
        if (anchorHours >= 24) return 'row-caution';
      }
      return '';
    },
    getStatusClass(status) {
      if (status === '在航') return 'sailing';
      if (status === '在航(主机推动)') return 'sailing';
      if (status === '靠泊') return 'docked';
      if (status === '锚泊') return 'anchored';
      return '';
    },
    getStatusIcon(status) {
      if (status === '在航') return 'fas fa-ship';
      if (status === '在航(主机推动)') return 'fas fa-ship';
      if (status === '靠泊') return 'fas fa-anchor';
      if (status === '锚泊') return 'fas fa-life-ring';
      return 'fas fa-circle';
    },
    startClock() {
      this.updateDateTime();
      this.clockInterval = setInterval(this.updateDateTime, 1000);
    },
    updateDateTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');
      this.currentDateTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    openShipDetail(ship, index) {
      this.selectedShip = { ...ship }; // 克隆对象，避免直接引用
      this.selectedShipIndex = index;
      this.showShipDetail = true;

      // 暂停自动高亮
      if (this.autoPlay) {
        this.pauseAutoHighlight();
      }

      // 添加禁止滚动类到body
      document.body.classList.add('modal-open');
    },
    closeShipDetail() {
      this.showShipDetail = false;

      // 恢复自动高亮
      if (this.autoPlay) {
        this.resumeAutoHighlight();
      }

      // 移除禁止滚动类
      document.body.classList.remove('modal-open');
    },
    // 经纬度转换
    toLnglat(data) {
      let newData = data + ''
      if (data < 1) {
        newData = data * 1000000 + ''
      }
      if (newData.indexOf('.') < 0) {
        let _curIntData = data / 1000000
        newData = _curIntData + ''
      }
      let newArr = newData.split('.')
      let newStr = newArr[0] + '-' + (parseInt(newArr[1]) / 16666.36).toFixed(3)
      return newStr
    },
    // 船舶类型转换
    shipTypeTrans(typein) {
      let curShipType = ''
      let type = typein
      switch (true) {
        case (type >= 20 && type <= 29):
          curShipType = '地效应船'
          break
        case (type === 30):
          curShipType = '捕捞'
          break
        case (type === 31):
          curShipType = '拖引'
          break
        case (type === 32):
          curShipType = '拖引并且船长>200m 或船宽>25m'
          break
        case (type === 33):
          curShipType = '疏浚或水下作业'
          break
        case (type === 34):
          curShipType = '潜水作业'
          break
        case (type === 35):
          curShipType = '参与军事行动'
          break
        case (type === 36):
          curShipType = '帆船航行'
          break
        case (type === 37):
          curShipType = '娱乐船'
          break
        case (type >= 40 && type <= 49):
          curShipType = '高速船'
          break
        case (type === 50):
          curShipType = '引航船'
          break
        case (type === 51):
          curShipType = '搜救船'
          break
        case (type === 52):
          curShipType = '拖轮'
          break
        case (type === 53):
          curShipType = '港口供应船'
          break
        case (type === 54):
          curShipType = '载有防污染装置和设备的船舶'
          break
        case (type === 55):
          curShipType = '执法艇'
          break
        case (type === 56):
          curShipType = '备用-用于当地船舶的任务分配'
          break
        case (type === 57):
          curShipType = '备用-用于当地船舶的任务分配'
          break
        case (type === 58):
          curShipType = '医疗船（如 1949 年日内瓦公约及附加条款所规定）'
          break
        case (type === 59):
          curShipType = '符合 18 号决议（Mob-83）的船舶'
          break
        case (type >= 60 && type <= 69):
          curShipType = '客船'
          break
        case (type >= 60 && type <= 79):
          curShipType = '货船'
          break
        case (type >= 80 && type <= 89):
          curShipType = '油轮'
          break
        case (type >= 90 && type <= 99):
          curShipType = '其他类型的船舶'
          break
        case (type === 100):
          curShipType = '集装箱'
          break
        default:
          curShipType = '未知类型'
      }
      return curShipType
    },
    // 全屏显示
    toggleFullScreen() {
      this.$refs['fullscreen'].toggle()
    },
    fullscreenChange(fullscreen) {
      this.fullscreen = fullscreen
      // fullscreen ? this.map.basemapsControl.hide() : this.map.basemapsControl.show()
    },
  }
};
</script>

<style scoped>
/* 导入Font Awesome */
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css');

/* 基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.fleet-monitor {
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Microsoft YaHei", sans-serif;
  background-color: #011628;
  color: #e6f7ff;
  min-height: 100vh;
  padding: 0;
  display: flex;
  flex-direction: column;
}

/* 头部样式 */
.header {
  position: relative;
  overflow: hidden;
  height: 50px;
  background-color: #001529;
  border-bottom: 1px solid #1a3d6a;
  display: flex;
  align-items: center;
  padding: 0 20px;
}

.logo {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #072040, #0a345e);
  padding: 0 20px;
  height: 35px;
  border-radius: 4px;
  box-shadow: 0 0 15px rgba(30, 192, 187, 0.3);
  border: 1px solid #1a3d6a;
}

.logo-icon {
  font-size: 24px;
  color: #1ec0bb;
  margin-right: 10px;
}

.logo-text {
  color: #1ec0bb;
  font-size: 22px;
  font-weight: 500;
  text-shadow: 0 0 8px rgba(30, 192, 187, 0.5);
}

.datetime {
  font-weight: 600;
  padding: 8px 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  /* animation: pulse 2s infinite; */
}

.datetime::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: shimmer 3s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 15px rgba(30, 192, 187, 0.3);
  }

  50% {
    box-shadow: 0 0 20px rgba(30, 192, 187, 0.5);
  }

  100% {
    box-shadow: 0 0 15px rgba(30, 192, 187, 0.3);
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }

  100% {
    left: 100%;
  }
}

.notification-area {
  display: flex;
  align-items: center;
  height: 35px;
  background: linear-gradient(135deg, #072040, #0a345e);
  padding: 0 15px;
  border-radius: 4px;
  box-shadow: 0 0 15px rgba(30, 192, 187, 0.3);
  border: 1px solid #1a3d6a;
  position: absolute;
  right: 60px;
  overflow: hidden;
}

.notification-area::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(56, 176, 255, 0.4), transparent);
  animation: glow-line 3s infinite;
}

@keyframes glow-line {

  0%,
  100% {
    opacity: 0.3;
  }

  50% {
    opacity: 0.8;
  }
}

.notification-container {
  width: 580px;
  height: 100%;
  overflow: hidden;
  position: relative;
}

.notification-scroll {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  position: relative;
}

.alert {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  padding-right: 10px;
}

.alert i {
  font-size: 16px;
  margin-right: 8px;
  flex-shrink: 0;
}

.alert-text-container {
  overflow: hidden;
  width: 100%;
}

.alert-text {
  white-space: nowrap;
  display: inline-block;
  padding: 4px 0;
  transition: all 0.3s ease;
}

.scrolling-text {
  display: inline-block;
  padding-right: 50px;
  /* 确保滚动文本末尾有足够空白 */
  animation: scroll-text 15s linear infinite;
  animation-delay: 1s;
  /* 给用户时间先阅读开头 */
}

@keyframes scroll-text {

  0%,
  10% {
    transform: translateX(0);
  }

  90%,
  100% {
    transform: translateX(calc(-100% - 50px));
  }
}

.notification-transition-enter-active {
  transition: all 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: absolute;
  width: 100%;
}

.notification-transition-leave-active {
  transition: all 0.8s cubic-bezier(0.36, 0, 0.66, -0.56);
  position: absolute;
  width: 100%;
}

.notification-transition-enter-from {
  opacity: 0;
  transform: translateY(20px) scale(0.95);
  filter: blur(2px);
}

.notification-transition-leave-to {
  opacity: 0;
  transform: translateY(-20px) scale(0.95);
  filter: blur(2px);
}

.bell {
  position: relative;
  margin-left: 20px;
}

.bell i {
  font-size: 16px;
  color: #ffb73f;
}

.badge {
  position: absolute;
  top: -7px;
  right: -7px;
  width: 14px;
  height: 14px;
  background-color: #ff4d4f;
  color: white;
  border-radius: 50%;
  font-size: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 统计卡片区域样式 */
.stat-cards {
  display: flex;
  padding: 10px;
  gap: 10px;
}

.stat-card {
  flex: 1;
  background: linear-gradient(160deg, #0c294b, #072040);
  border-radius: 6px;
  padding: 8px 15px;
  display: flex;
  align-items: center;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
  border: 1px solid #0e3461;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15px;
}

.blue {
  background-color: rgba(24, 144, 255, 0.15);
  color: #38b0ff;
}

.purple {
  background-color: rgba(114, 46, 209, 0.15);
  color: #a37feb;
}

.green {
  background-color: rgba(19, 194, 194, 0.15);
  color: #22e1e1;
}

.orange {
  background-color: rgba(250, 140, 22, 0.15);
  color: #ffa246;
}

.stat-icon i {
  font-size: 20px;
}

.icon-chart {
  color: #38b0ff;
}

.icon-ship {
  color: #a37feb;
}

.icon-cycle {
  color: #22e1e1;
}

.icon-time {
  color: #ffa246;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 20px;
  font-weight: 500;
  line-height: 1.2;
  color: #fff;
  display: flex;
  align-items: end;
}

.unit {
  font-size: 14px;
  color: #8eb0d1;
  margin-left: 4px;
}

.stat-label {
  font-size: 14px;
  color: #8eb0d1;
}

/* 主内容区域样式 */
.main-content {
  display: flex;
  padding: 0 10px 10px;
  gap: 10px;
  /* height: calc(100vh - 181px); */
}

.left-panel {
  flex: 7;
  /* height: 810px; */
}

.right-panel {
  flex: 3;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.fleet-table-card,
.map-card,
.port-info-card {
  background: linear-gradient(140deg, #0c294b, #072040);
  border-radius: 6px;
  height: 100%;
  padding: 15px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
  border: 1px solid #0e3461;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #1ec0bb;
  border-bottom: 1px solid #1a3d6a;
  padding-bottom: 10px;
  display: flex;
  align-items: center;
  min-height: 24px;
}

.title-text {
  display: flex;
  align-items: center;
  height: 100%;
}

.card-title i {
  margin-right: 6px;
  color: #1ec0bb;
}

/* 自动播放开关 */
.autoplay-toggle {
  display: flex;
  align-items: center;
  margin-left: auto;
  height: 100%;
}

.autoplay-toggle input {
  opacity: 0;
  width: 0;
  height: 0;
  position: absolute;
}

.autoplay-toggle label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.toggle-track {
  width: 40px;
  height: 20px;
  background-color: rgba(0, 20, 40, 0.7);
  border: 1px solid #1a3d6a;
  border-radius: 20px;
  position: relative;
  transition: all 0.3s ease;
  cursor: pointer;
  display: inline-block;
  margin-right: 8px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.4);
  flex-shrink: 0;
}

.toggle-indicator {
  width: 16px;
  height: 16px;
  background-color: #555;
  border-radius: 50%;
  position: absolute;
  top: 1px;
  left: 2px;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
}

input:checked+label .toggle-track {
  background-color: rgba(56, 176, 255, 0.3);
  border-color: #38b0ff;
}

input:checked+label .toggle-indicator {
  transform: translateX(20px);
  background-color: #38b0ff;
}

.toggle-label {
  font-size: 13px;
  color: #8eb0d1;
  cursor: pointer;
  line-height: 20px;
  position: relative;
  top: 0;
}

input:checked+label .toggle-label {
  color: #e6f7ff;
}

/* 表格样式 */
.fleet-table {
  width: 100%;
  height: calc(100% - 50px);
  overflow-x: hidden;
  position: relative;
}

table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  table-layout: fixed;
  /* 固定表格布局 */
}

table caption {
  font-weight: bold;
  font-size: 24px;
  line-height: 50px;
}

table tbody {
  display: block;
  width: calc(100% + 8px);
  /*这里的8px是滚动条的宽度*/
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
}

table thead tr,
table tbody tr,
table tfoot tr {
  box-sizing: border-box;
  table-layout: fixed;
  display: table;
  width: 100%;
}

thead {
  background-color: #081f3a;
  position: sticky;
  top: 0;
  z-index: 10;
}

th {
  padding: 12px 8px;
  text-align: left;
  color: #8eb0d1;
  font-weight: 500;
  font-size: 14px;
  border-bottom: 1px solid #1a3d6a;
}

td {
  padding: 0px 8px;
  border-bottom: 1px solid #1a3d6a;
  font-size: 14px;
  color: #e6f7ff;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.status-tag {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  text-align: center;
}

.status-tag i {
  margin-right: 4px;
  font-size: 10px;
}

.anchor-hours {
  margin-left: 4px;
  font-size: 10px;
  opacity: 0.9;
}

.sailing {
  background-color: #22c55e;
  color: white;
}

.docked {
  background-color: #0b84e5;
  color: white;
}

.anchored {
  background-color: #f59e0b;
  color: white;
}

/* 整行预警样式 */
.row-caution {
  background-color: rgba(234, 179, 8, 0.1);
  animation: row-pulse-slow 2s infinite;
}

.row-warning {
  background-color: rgba(249, 115, 22, 0.15);
  animation: row-pulse-medium 1.5s infinite;
}

.row-danger {
  background-color: rgba(239, 68, 68, 0.2);
  animation: row-pulse-fast 1s infinite;
}

/* 预警行动画 */
@keyframes row-pulse-slow {

  0%,
  100% {
    background-color: rgba(234, 179, 8, 0.1);
  }

  50% {
    background-color: rgba(234, 179, 8, 0.2);
  }
}

@keyframes row-pulse-medium {

  0%,
  100% {
    background-color: rgba(249, 115, 22, 0.15);
  }

  50% {
    background-color: rgba(249, 115, 22, 0.25);
  }
}

@keyframes row-pulse-fast {

  0%,
  100% {
    background-color: rgba(239, 68, 68, 0.2);
  }

  50% {
    background-color: rgba(239, 68, 68, 0.3);
  }
}

/* 确保表格交替行的颜色与预警不冲突 */
tr:nth-child(even) {
  background-color: rgba(9, 30, 53, 0.3);
}

tr:hover {
  background-color: rgba(26, 61, 106, 0.3);
}

/* 预警行的悬停效果 */
.row-caution:hover {
  background-color: rgba(234, 179, 8, 0.25) !important;
}

.row-warning:hover {
  background-color: rgba(249, 115, 22, 0.3) !important;
}

.row-danger:hover {
  background-color: rgba(239, 68, 68, 0.35) !important;
}

/* 船舶高亮浮动效果 */
.highlighted-ship {
  position: relative;
  overflow: hidden;
  /* z-index: 20; */
  /* box-shadow: 0 6px 16px rgba(56, 176, 255, 0.5); */
  background: linear-gradient(90deg, rgba(16, 52, 97, 0.4), rgba(56, 176, 255, 0.15), rgba(16, 52, 97, 0.4)) !important;
  border-radius: 4px;
  transition: all 0.3s ease;
  /* transform: translateY(-2px); */
  height: 50px;
  /* 增加行高 */
}

.highlighted-ship td {
  color: #ffffff !important;
  /* text-shadow: 0 0 8px rgba(56, 176, 255, 0.8); */
  border-bottom: 1px solid #1a3d6a;
  font-weight: 600;
  letter-spacing: 0.3px;
  font-size: 15.5px;
  /* 增加字体大小 */
  padding: 0px 8px;
  /* 增加上下内边距 */
}

.highlighted-ship::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-left: 4px solid #38b0ff;
  border-right: 4px solid #38b0ff;
  pointer-events: none;
  animation: border-pulse 1.5s infinite;
}

@keyframes border-pulse {

  0%,
  100% {
    border-left-color: rgba(56, 176, 255, 0.8);
    border-right-color: rgba(56, 176, 255, 0.8);
  }

  50% {
    border-left-color: rgba(56, 176, 255, 1);
    border-right-color: rgba(56, 176, 255, 1);
  }
}

.highlighted-ship::after {
  content: '';
  position: absolute;
  top: 0;
  left: -150%;
  width: 35%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(56, 176, 255, 0.2), transparent);
  animation: scan-effect 1.5s linear infinite;
  pointer-events: none;
}

.highlighted-ship .status-tag {
  box-shadow: 0 0 10px rgba(56, 176, 255, 0.7);
  transform: scale(1.05);
  transition: all 0.3s ease;
}

@keyframes scan-effect {
  0% {
    left: -40%;
  }

  100% {
    left: 100%;
  }
}

/* 确保预警样式与高亮船舶浮动效果兼容 */
.row-caution.highlighted-ship {
  background: linear-gradient(90deg, rgba(234, 179, 8, 0.2), rgba(234, 179, 8, 0.3), rgba(234, 179, 8, 0.2)) !important;
  /* box-shadow: 0 5px 15px rgba(234, 179, 8, 0.3); */
}

.row-warning.highlighted-ship {
  background: linear-gradient(90deg, rgba(249, 115, 22, 0.2), rgba(249, 115, 22, 0.3), rgba(249, 115, 22, 0.2)) !important;
  /* box-shadow: 0 5px 15px rgba(249, 115, 22, 0.3); */
}

.row-danger.highlighted-ship {
  background: linear-gradient(90deg, rgba(239, 68, 68, 0.2), rgba(239, 68, 68, 0.35), rgba(239, 68, 68, 0.2)) !important;
  /* box-shadow: 0 5px 15px rgba(239, 68, 68, 0.3); */
}


tbody tr {
  position: relative;
  height: 42px;
  /* 固定行高，确保列表长度一致 */
  transition: all 0.3s ease;
}

/* 设置表格列宽 */
table th:nth-child(1),
table td:nth-child(1) {
  width: 10%;
}

/* 船名 */
table th:nth-child(2),
table td:nth-child(2) {
  width: 15%;
}

/* 状态 */
table th:nth-child(3),
table td:nth-child(3) {
  width: 25%;
}

/* 当前航次 */
table th:nth-child(4),
table td:nth-child(4) {
  width: 25%;
}

/* 装货/卸货 */
table th:nth-child(5),
table td:nth-child(5) {
  width: 25%;
}

/* 计划航次 */

/* 地图区域样式 */
.map-card {
  flex: 6;
  position: relative;
}

.map-container {
  height: calc(100% - 40px);
  position: relative;
  background-size: cover;
  background-position: center;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #1a3d6a;
}

.map-content {
  width: 100%;
  height: 100%;
  position: relative;
}

.ship-marker {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.ship-marker i {
  color: #38b0ff;
  font-size: 18px;
  filter: drop-shadow(0 0 3px rgba(0, 0, 0, 0.7));
}

.marker-highlight {
  color: #ffa246 !important;
  text-shadow: 0 0 10px rgba(255, 162, 70, 0.5);
}

.marker-label {
  background-color: rgba(0, 10, 20, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 2px;
  font-size: 12px;
  margin-top: 2px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.ship-marker-1 {
  top: 40%;
  left: 30%;
}

.ship-marker-2 {
  top: 30%;
  left: 60%;
}

.ship-marker-3 {
  top: 50%;
  left: 45%;
}

.map-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.map-control-btn {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  background-color: white;
  border: none;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.map-control-btn i {
  color: #555;
  font-size: 14px;
}

.map-zoom-controls {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

/* 港口信息区域样式 */
.port-info-card {
  flex: 4;
}

.section-title {
  font-weight: 500;
  margin-bottom: 8px;
  color: #b7c5d6;
}

.section-title i {
  margin-right: 6px;
  color: #1ec0bb;
}

.weather-section {
  padding: 0 10px;
  height: 130px;
}

.weather-current {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.weather-icon {
  font-size: 32px;
  margin-right: 15px;
}

.weather-info {
  flex: 1;
}

.city-name {
  font-weight: 500;
  margin-bottom: 5px;
  color: #e6f7ff;
}

.temperature {
  color: #8eb0d1;
  font-size: 12px;
}

.weather-update {
  color: #8eb0d1;
  font-size: 12px;
}

.weather-forecast {
  display: flex;
  justify-content: space-between;
}

.forecast-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.day {
  font-size: 12px;
  color: #8eb0d1;
  margin-bottom: 4px;
}

.weather-icon-small {
  font-size: 12px;
  margin-bottom: 4px;
}

.sunny {
  color: #ffa246;
}

.wind {
  color: #ffa246;
}

.partly-cloudy {
  color: #38b0ff;
}

.rainy {
  color: #8eb0d1;
}

.temp {
  font-size: 12px;
  color: #8eb0d1;
}

.divider {
  height: 1px;
  background-color: #1a3d6a;
  margin: 8px 0;
}

.ship-stats-section {
  padding: 0 10px;
}

.ship-stats {
  display: flex;
  justify-content: space-between;
  gap: 10px;
  margin-top: 15px;
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: rgba(8, 29, 49, 0.7);
  border-radius: 6px;
  /* padding: 15px; */
  border: 1px solid #1a3d6a;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.stat-item-icon {
  font-size: 24px;
  margin-top: 10px;
}

.ship-blue {
  color: #38b0ff;
}

.departure-blue {
  color: #38b0ff;
}

.dock-green {
  color: #22c55e;
}

.stat-number {
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 5px;
  color: #fff;
}

.stat-item-label {
  font-size: 14px;
  color: #8eb0d1;
  margin-bottom: 5px;
}

/* 船舶详情弹窗样式 */
.ship-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 13, 26, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.ship-detail-container {
  width: 700px;
  max-height: 85vh;
  background: linear-gradient(135deg, #07213f, #0a3055);
  border-radius: 8px;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.5);
  border: 1px solid #1a3d6a;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: modal-in 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
}

@keyframes modal-in {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }

  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: rgba(13, 41, 71, 0.7);
  border-bottom: 1px solid #1a3d6a;
}

.ship-title {
  display: flex;
  align-items: center;
  color: #38b0ff;
  font-size: 20px;
  font-weight: 600;
}

.ship-title i {
  margin-right: 10px;
}

.close-button {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: rgba(56, 176, 255, 0.1);
  border: 1px solid rgba(56, 176, 255, 0.3);
  color: #8eb0d1;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-button:hover {
  background-color: rgba(56, 176, 255, 0.2);
  color: #fff;
}

.modal-content {
  padding: 20px;
  overflow-y: auto;
  max-height: calc(85vh - 60px);
}

.info-section {
  margin-bottom: 20px;
  background-color: rgba(9, 30, 54, 0.5);
  border-radius: 6px;
  padding: 15px;
  border: 1px solid rgba(26, 61, 106, 0.6);
}

.section-header {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 15px;
  color: #e6f7ff;
  display: flex;
  align-items: center;
  border-bottom: 1px solid rgba(26, 61, 106, 0.6);
  padding-bottom: 10px;
}

.section-header i {
  margin-right: 8px;
  color: #38b0ff;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 13px;
  color: #8eb0d1;
  margin-bottom: 5px;
}

.info-value {
  font-size: 15px;
  color: #e6f7ff;
  font-weight: 500;
}

.status-tag-small {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  text-align: center;
}

.efficiency-good {
  color: #22c55e;
}

.route-list,
.route-current,
.route-planned {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.route-item {
  display: flex;
  align-items: center;
  color: #e6f7ff;
  padding: 8px 12px;
  background-color: rgba(13, 41, 71, 0.5);
  border-radius: 4px;
  border: 1px solid rgba(26, 61, 106, 0.4);
}

.route-item i {
  margin-right: 10px;
  color: #38b0ff;
}

.route-item.planned {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #e6f7ff;
}

.cargo-info {
  color: #8eb0d1;
  margin-left: auto;
  margin-right: 15px;
}

.plan-button {
  background-color: rgba(56, 176, 255, 0.2);
  color: #38b0ff;
  border: 1px solid rgba(56, 176, 255, 0.4);
  padding: 4px 10px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
}

/* 表格行光标样式 */
tbody tr {
  /* cursor: pointer; */
}

/* 禁止滚动body */
:global(.modal-open) {
  overflow: hidden;
}

/* 天气图标颜色 */
.weather-sunny {
  color: #ffa246;
}

.weather-cloudy {
  color: #eedc8d;
}

.weather-rain {
  color: #38b0ff;
}

.weather-snow {
  color: #a5d8ff;
}

.weather-fog {
  color: #b7c5d6;
}

.weather-wind {
  color: #22e1e1;
}

.weather-haze {
  color: #d4b483;
}

/* 修改天气图标样式 */
.weather-icon {
  font-size: 32px;
  margin-right: 15px;
}


.ivu-spin-fix {
  background-color: #09244590
}

.ivu-spin-fix.table-loading {
  background-color: #092546
}

#map {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: #a3ccff;
  z-index: 10;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.text-danger {
  color: #ffc400;
}
</style>