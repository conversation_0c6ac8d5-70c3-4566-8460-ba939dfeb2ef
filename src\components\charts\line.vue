<template>
  <div ref="dom" class="charts chart-bar"></div>
</template>

<script>
import echarts from 'echarts'
import tdTheme from './theme.json'
import { on, off } from '@/libs/tools'
echarts.registerTheme('tdTheme', tdTheme)
export default {
  name: 'ChartLine',
  props: {
    value: Object,
    unit: String,
    xAxisUnit: String,
    text: String,
    subtext: String,
    clickable: {
      type: Boolean,
      default: false
    },
    legendShow: {
      type: Boolean,
      default: true
    },
    color: {
      type: Array,
      default: () => {
        return ['#ff7f7f', '#ff7fbf', '#ff7fff', '#bf7fff', '#7f7fff', '#7fbfff', '#7fffff', '#7fffbf', '#7fff7f', '#bfff7f', '#DFDFDF']
      }
    },
    rotate: {
      type: String,
      default: '0'
    },
    formatter: {
      type: Boolean,
      default: false
    },
    showOnemarkLine: {
      type: Boolean,
      default: false
    },
    gridRight: {
      type: Number,
      default: 30
    }
  },
  data () {
    return {
      dom: null
    }
  },
  methods: {
    resize () {
      this.dom.resize()
    },
    chartInit () {
      let _that = this
      this.$nextTick(() => {
        let xAxisData = this.value.xAxis
        let option = {
          title: {
            text: this.text,
            subtext: this.subtext,
            x: 'left',
            textStyle: {
              color: '#2B304C',
              fontSize: '16'
            }
          },
          grid: {
            left: 0,
            right: this.gridRight,
            top: 60,
            bottom: 25,
            containLabel: true
          },
          legend: {
            show: this.legendShow,
            icon: 'roundRect',
            data: this.value.legend,
            top: 25
          },
          // toolbox: {
          //   feature: {
          //     // dataView: { show: true, readOnly: false },
          //     magicType: { show: true, type: ['line', 'bar'] },
          //     restore: { show: true }
          //     // saveAsImage: { show: true },
          //   }
          // },
          color: this.color,
          tooltip: {
            trigger: 'axis',
            formatter: item => {
              let str = ''
              if (_that.formatter) {
                str = item.length > 1 ? item[0].name + '<br/>'  : '<br/>'
                item.forEach(list => {
                  str += list.marker + list.value + '<br/>'
                })
              } else {
                if (item.length > 1) {
                  str = item[0].name + (this.xAxisUnit ? this.xAxisUnit : '') + '<br/>'
                  item.forEach(list => {
                    str += list.marker + list.seriesName + ':<br/>' + list.value + this.unit + '<br/>'
                  })
                } else {
                  str = item[0].name + '<br/>'
                  str += item[0].marker + item[0].value
                }
              }
              return str
            }
          },
          xAxis: {
            name: this.xAxisUnit,
            type: 'category',
            splitLine: {
              show: false
            },
            axisLabel: {
              show: true,
              overflow: 'none',
              ellipsis: 'truncate',
              interval: '0', // 强制显示文本
              rotate: this.rotate
            },
            data: xAxisData
          },
          yAxis: [],
          series: []
        }
        if (this.value.legend && this.value.legend.length > 1) {
          this.value.data.forEach((item, idx) => {
            option.series.push({
              name: this.value.legend[idx],
              data: item,
              type: 'line',
              showSymbol: this.value.symbol && this.value.symbol.length > 1,
              symbol: this.value.symbol && this.value.symbol.length > 1 ? this.value.symbol[idx] : 'emptyCircle',
              smooth: this.value.smooth !== undefined ? this.value.smooth : true,
              yAxisIndex: this.value.yAxis !== undefined && this.value.yAxis.length > 1 && idx > 0 ? 1 : 0,
              lineStyle: {
                width: 3
              }
            })
            if (this.showOnemarkLine && idx === 0) {
              option.series[idx] = Object.assign(option.series[idx], {
                markLine: {
                  label: {
                    formatter: '{b}: {c}'
                  },
                  data: [{ type: 'average', name: '均值' }]
                }
              })
            }
          })
        } else {
          option.series.push({
            name: this.value.legend[0],
            data: this.value.data,
            type: 'line',
            showSymbol: true,
            symbol: this.value.symbol > 1 ? this.value.symbol : 'emptyCircle',
            smooth: this.value.smooth !== undefined ? this.value.smooth : true,
            lineStyle: {
              width: 3
            }
          })
        }
        if (this.value.yAxis !== undefined && this.value.yAxis.length > 1) {
          this.value.yAxis.forEach((item, idx) => {
            option.yAxis.push({
              name: item.name,
              type: 'value',
              splitNumber: 4,
              max: item.max,
              axisLine: {
                show: false
              },
              axisTick: {
                show: false
              },
              nameTextStyle: {
                color: '#4A4A4A',
                fontWeight: '600',
                align: idx > 0 ? 'right' : ''
              }
            })
          })
        } else {
          option.yAxis.push({
            name: this.unit,
            type: 'value',
            splitNumber: 4,
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            nameTextStyle: {
              color: '#4A4A4A',
              fontWeight: '600',
              align: 'left'
            }
          })
        }
        this.dom = echarts.init(this.$refs.dom, 'tdTheme')
        this.dom.setOption(option)
        this.dom.off('click')
        this.dom.on('click', (params) => {
          if (this.clickable) {
            _that.$emit('clickBack', params)
          }
        })
        on(window, 'resize', this.resize)
      })
    }
  },
  mounted () {
    this.chartInit()
  },
  beforeDestroy () {
    if (this.dom) {
      this.dom.off('click')
    }
    off(window, 'resize', this.resize)
  },
  watch: {
    value: {
      handler (n, o) {
        this.chartInit()
      },
      deep: true,
      immediate: true
    }
  }
}
</script>
