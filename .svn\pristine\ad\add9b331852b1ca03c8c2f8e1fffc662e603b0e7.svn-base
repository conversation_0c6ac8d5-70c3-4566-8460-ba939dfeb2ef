<template>
  <div>
    <div class="base-ship-info">
      <span>{{ baseObj.ship_name }}</span>
      <span class="base-ship-info-voyageno">{{ baseObj.voyage_no }}</span>
      <span class="base-ship-info-goodsno" v-for="(item, idx) in baseObj.cargoResult" :key="idx">
        <Tooltip :content="item.cargo_company_name" placement="top">
          <span>{{ item.cargo_no }}</span>
        </Tooltip>
        <button :data-clipboard-text="item.cargo_no" @click="copyText" class="copy_btn">复制</button>
      </span>
    </div>
    <Row justify="center" align="middle" class-name="base-area">
      <Col span="2" align="middle">
        <img :src="logoPath" alt="">
      </Col>
      <Col span="6">
        <div v-for="(item, idx) in baseObj.portResult" :key="idx">
          <div>
            <Icon :type="item.port_type === '1' ? 'md-disc' : 'md-pin'" color="#007DFF"/>
            <span>{{ item.port_name }} / {{ item.wharf_name }}</span>
          </div>
          <Icon v-if="idx !== (baseObj.portResult.length - 1)" type="ios-arrow-round-down" color="#D9D9D9"/>
        </div>
      </Col>
      <Col span="6" class-name="goods-area">
        <div>
          <Icon type="md-cube" color="#007DFF" class="goods-icon"/>
          <span class="goods-type">货品</span>
          <span class="goods-name">{{ baseObj.cargoResult.map(item => item.goods_name).join()}}</span>
        </div>
        <div>
          <Icon type="ios-albums" color="#007DFF" class="goods-icon"/>
          <span class="goods-type">货量</span>
          <span class="goods-name">{{ baseObj.cargoResult.map(item => item.amounts).join()}}</span>
        </div>
        <div>
          <Icon type="md-speedometer" color="#007DFF" class="goods-icon"/>
          <span class="goods-type">里程</span>
          <span class="goods-name">{{ baseObj.mile }}</span>
        </div>
      </Col>
      <Col span="10" class-name="goods-area">
        <div>
          <Icon type="md-volume-up" color="#007DFF" class="goods-icon"/>
          <span class="goods-type">最新动态</span>
          <nodeList :nodeObj="baseObj" :fontSize="14" :nameColor="'#007DFF'" :textColor="'#333'" :dateShow="true"></nodeList>
        </div>
        <div v-if="this.$route.name === 'voyageDetail'">
          <Icon type="md-volume-up" color="#007DFF" class="goods-icon"/>
          <span class="goods-type">靠泊计划</span>
          <span class="goods-name">{{ berthPlanStr === '' ? '暂无靠泊计划' : berthPlanStr }}</span>
        </div>
      </Col>
    </Row>
    <Divider style="margin: 4px 0 20px;"/>
    <div class="step-area">
      <div class="case-status">
        <div :class="baseInfoList.includes('AA') ? 'cur-box' : 'dis-box'">
          <Icon custom="voyagefont iconvoyage-sail" size="23" :color="baseInfoList.includes('AA') ? checkColor : disableColor"/>
        </div>
        <div class="case-status-text">起航</div>
        <div class="case-status-time">{{progressDate('AA')}}</div>
      </div>
      <div class="process-step">
        <Tooltip v-if="detailType !== 'planVoyage' && toPortName !== ''" always :content="toPortName" placement="top" :transfer="false">
          <div class="process-bar-point-wrap">
            <div v-for="(item, idx) in [1,2,3]" :key="idx" :class="baseInfoList.includes('AC') ? 'process-bar-point' : 'process-bar-point-dis'"></div>
          </div>
        </Tooltip>
        <div v-else class="process-bar-point-wrap">
          <div v-for="(item, idx) in [1,2,3]" :key="idx" :class="baseInfoList.includes('AC') ? 'process-bar-point' : 'process-bar-point-dis'"></div>
        </div>
      </div>
      <div class="case-status">
        <div :class="baseInfoList.includes('AC') ? 'cur-box' : 'dis-box'">
          <Icon custom="voyagefont iconvoyage-arrival" size="23" :color="baseInfoList.includes('AC') ? checkColor : disableColor"/>
        </div>
        <div class="case-status-text">到港</div>
        <div class="case-status-time">{{progressDate('AC')}}</div>
      </div>
      <div class="process-step">
        <div class="process-bar-point-wrap">
          <div v-for="(item, idx) in [1,2,3]" :key="idx" :class="baseInfoList.includes('AJ') ? 'process-bar-point' : 'process-bar-point-dis'"></div>
        </div>
      </div>
      <div class="case-status">
        <div :class="baseInfoList.includes('AJ') ? 'cur-box' : 'dis-box'">
          <Icon custom="voyagefont iconvoyage-landing" size="23" :color="baseInfoList.includes('AJ') ? checkColor : disableColor"/>
        </div>
        <div class="case-status-text">缆绳上岸</div>
        <div class="case-status-time">{{progressDate('AJ')}}</div>
      </div>
      <div class="process-step">
        <div class="process-bar-point-wrap">
          <div v-for="(item, idx) in [1,2,3]" :key="idx" :class="baseInfoList.includes('AK') ? 'process-bar-point' : 'process-bar-point-dis'"></div>
        </div>
      </div>
      <div class="case-status">
        <div :class="baseInfoList.includes('AK') ? 'cur-box' : 'dis-box'">
          <Icon custom="voyagefont iconvoyage-mooring" size="23" :color="baseInfoList.includes('AK') ? checkColor : disableColor"/>
        </div>
        <div class="case-status-text">船舶系泊</div>
        <div class="case-status-time">{{progressDate('AK')}}</div>
      </div>
      <div class="process-step">
        <div class="process-bar-point-wrap">
          <div v-for="(item, idx) in [1,2,3]" :key="idx" :class="baseInfoList.includes('AP') || baseInfoList.includes('AQ') ? 'process-bar-point' : 'process-bar-point-dis'"></div>
        </div>
      </div>
      <div class="case-status" v-if="portType === '1'">
        <div :class="baseInfoList.includes('AP') ? 'cur-box' : 'dis-box'">
          <Icon custom="voyagefont iconvoyage-loads" size="23" :color="baseInfoList.includes('AP') ? checkColor : disableColor"/>
        </div>
        <div class="case-status-text">开始装货</div>
        <div class="case-status-time">{{progressDate('AP')}}</div>
      </div>
      <div class="process-step" v-if="portType === '1'">
        <div class="process-bar-point-wrap">
          <div v-for="(item, idx) in [1,2,3]" :key="idx" :class="baseInfoList.includes('AV') ? 'process-bar-point' : 'process-bar-point-dis'"></div>
        </div>
      </div>
      <div class="case-status" v-if="portType === '1'">
        <div :class="baseInfoList.includes('AV') ? 'cur-box' : 'dis-box'">
          <Icon custom="voyagefont iconvoyage-loade" size="23" :color="baseInfoList.includes('AV') ? checkColor : disableColor"/>
        </div>
        <div class="case-status-text">结束装货</div>
        <div class="case-status-time">{{progressDate('AV')}}</div>
      </div>
      <div class="process-step" v-if="portType === '1'">
        <div class="process-bar-point-wrap">
          <div v-for="(item, idx) in [1,2,3]" :key="idx" :class="baseInfoList.includes('BG') ? 'process-bar-point' : 'process-bar-point-dis'"></div>
        </div>
      </div>
      <div class="case-status" v-if="portType === '2'">
        <div :class="baseInfoList.includes('AQ') ? 'cur-box' : 'dis-box'">
          <Icon custom="voyagefont iconvoyage-loads" size="23" :color="baseInfoList.includes('AQ') ? checkColor : disableColor"/>
        </div>
        <div class="case-status-text">开始卸货</div>
        <div class="case-status-time">{{progressDate('AQ')}}</div>
      </div>
      <div class="process-step" v-if="portType === '2'">
        <div class="process-bar-point-wrap">
          <div v-for="(item, idx) in [1,2,3]" :key="idx" :class="baseInfoList.includes('AW') ? 'process-bar-point' : 'process-bar-point-dis'"></div>
        </div>
      </div>
      <div class="case-status" v-if="portType === '2'">
        <div :class="baseInfoList.includes('AW') ? 'cur-box' : 'dis-box'">
          <Icon custom="voyagefont iconvoyage-loade" size="23" :color="baseInfoList.includes('AW') ? checkColor : disableColor"/>
        </div>
        <div class="case-status-text">结束卸货</div>
        <div class="case-status-time">{{progressDate('AW')}}</div>
      </div>
      <div class="process-step" v-if="portType === '2'">
        <div class="process-bar-point-wrap">
          <div v-for="(item, idx) in [1,2,3]" :key="idx" :class="baseInfoList.includes('BG') ? 'process-bar-point' : 'process-bar-point-dis'"></div>
        </div>
      </div>
      <div class="case-status">
        <div :class="baseInfoList.includes('BG') ? 'cur-box' : 'dis-box'">
          <Icon custom="voyagefont iconvoyage-untie" size="23" :color="baseInfoList.includes('BG') ? checkColor : disableColor"/>
        </div>
        <div class="case-status-text">开始解缆</div>
        <div class="case-status-time">{{progressDate('BG')}}</div>
      </div>
      <div class="process-step">
        <div class="process-bar-point-wrap">
          <div v-for="(item, idx) in [1,2,3]" :key="idx" :class="baseInfoList.includes('BH') ? 'process-bar-point' : 'process-bar-point-dis'"></div>
        </div>
      </div>
      <div class="case-status">
        <div :class="baseInfoList.includes('BH') ? 'cur-box' : 'dis-box'">
          <Icon custom="voyagefont iconvoyage-offshore" size="23" :color="baseInfoList.includes('BH') ? checkColor : disableColor"/>
        </div>
        <div class="case-status-text">缆绳离岸</div>
        <div class="case-status-time">{{progressDate('BH')}}</div>
      </div>
      <div class="process-step">
        <div class="process-bar-point-wrap">
          <div v-for="(item, idx) in [1,2,3]" :key="idx" :class="baseInfoList.includes('BK') ? 'process-bar-point' : 'process-bar-point-dis'"></div>
        </div>
      </div>
      <div class="case-status">
        <div :class="baseInfoList.includes('BK') ? 'cur-box' : 'dis-box'">
          <Icon style="transform: scale(0.75,0.75);*font-size: 9px;" custom="voyagefont iconvoyage-depart" size="12" :color="baseInfoList.includes('BK') ? checkColor : disableColor"/>
        </div>
        <div class="case-status-text">离港</div>
        <div class="case-status-time">{{progressDate('BK')}}</div>
      </div>
    </div>
  </div>
</template>
<script>
import logoPath from '@/assets/images/logo-boat.png'
import nodeList from '@/components/nodeList/nodeNList'
import API from '@/api/voyageManage/voyageDetail'
import SAPI from '@/api/search'
import Clipboard from 'clipboard'

export default {
  props: {
    baseObj: Object
  },
  components: {
    nodeList
  },
  data () {
    return {
      logoPath,
      detailType: localStorage.detailType,
      goodsIndex: 0, // 默认第一个货品
      toPortName: '', // 前往港口名称
      portType: '1', // 装卸货类型（1：转 2：卸）
      baseInfoList: [], // 动态节点数据列表
      progressList: [], // 进度节点数据列表
      disableColor: '#D3D3D3',
      checkColor: '#007DFF',
      berthPlanStr: '--' // 最新靠泊计划信息
    }
  },
  created () {
    this.getBaseInfo()
  },
  methods: {
    // 复制文本
    copyText () {
      let clipboard = new Clipboard('.copy_btn')
      clipboard.on('success', e => {
        this.$Message.success('复制单号成功！')
        clipboard.destroy()
      })
      clipboard.on('error', e => {
        this.$Message.error('该浏览器不支持自动复制')
        clipboard.destroy()
      })
    },
    // 获取当前节点时间
    progressDate (node_str) {
      if (this.progressList.length === 0) {
        return ''
      } else {
        let curObj = this.progressList.filter(item => item.node_code === node_str)
        if (curObj.length === 0) return ''
        let curDate = curObj[0].node_date
        let preStr = curDate.split(' ')[0] // 年-月-日
        let backStr = curDate.split(' ')[1] // 时：分
        let mdStr = preStr.split('-')[1] + '.' + preStr.split('-')[2]
        return mdStr + ' ' + backStr
      }
    },
    getBaseInfo () {
      // 查询页调用的进度信息
      if (this.$route.name === 'searchDetail') {
        let curParam = {
          voyage_id: this.baseObj.id,
          number_no: this.$route.params.id,
          goods_id: this.baseObj.cargoResult[this.goodsIndex].goods_id
        }
        SAPI.queryWaybillNumberByProgress(curParam).then(res => {
          if (res.data.Code === 10000) {
            this.portType = res.data.portTypeResult ? res.data.portTypeResult.port_type : '1'
            this.progressList = res.data.Result
            this.baseInfoList = res.data.Result.map(item => item.node_code)
            this.toPortName = '前往' + res.data.portTypeResult.wharf_name
          }
        })
      } else {
        // 获取当前航次最新一条靠泊计划
        API.getCurrentBerthPlanByVoyageId({ voyage_id: this.baseObj.id }).then(res => {
          if (res.data.Code === 10000) {
            let curObj = res.data.Result
            if (JSON.stringify(curObj) === '{}') return
            this.berthPlanStr = '预计' + curObj.plan_date + '靠泊' + curObj.port_name + '港' + curObj.wharf_name + '码头' + curObj.berth_name + '泊位'
          }
        })
        // 获取动态进度条数据
        API.getVoyageProgressById({ voyage_id: this.baseObj.id }).then(res => {
          if (res.data.Code === 10000) {
            this.portType = res.data.portTypeResult ? res.data.portTypeResult.port_type : '1'
            this.progressList = res.data.Result
            this.baseInfoList = res.data.Result.map(item => item.node_code)
            this.toPortName = '前往' + res.data.portTypeResult.wharf_name
          }
        })
      }
    }
  },
  watch: {
    goodsIndex () {
      this.getBaseInfo()
    }
  }
}
</script>
<style lang="less">
  .base-ship-info {
    color: #333;
    font-size: 16px;
    font-weight: 600;
    &-voyageno {
      margin-left: 20px;
    }
    &-goodsno {
      margin-left: 40px;
      .copy_btn {
        padding: 0 8px;
        margin-left: 12px;
        font-size: 12px;
        transform: scale(0.833,0.833);
        *font-size: 10px;
        color: #007DFF;
        background:#fff;
        border-radius:12px;
        border:1px solid #007DFF;
        cursor: pointer;
      }
    }
  }
</style>
