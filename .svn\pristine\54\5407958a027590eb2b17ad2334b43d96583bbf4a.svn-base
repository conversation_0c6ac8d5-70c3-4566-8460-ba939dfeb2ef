<template>
  <div class="ship-detail-view">
    <div class="ship-header">
      <div class="ship-title">
        <h2>{{ ship.name }}</h2>
        <Tag :color="getShipTypeColor(ship.type)">{{ getShipTypeText(ship.type) }}</Tag>
      </div>
      <div class="ship-contact">
        <Button type="primary" size="small" icon="md-call">联系船舶</Button>
      </div>
    </div>
    
    <Divider />
    
    <div class="detail-content">
      <Tabs>
        <TabPane label="当前航次信息" name="current-voyage">
          <div class="voyage-info">
            <Row :gutter="16">
              <Col span="12">
                <Card title="航次基本信息">
                  <p><strong>航次号:</strong> {{ ship.currentVoyage.voyageNumber }}</p>
                  <p><strong>装货港:</strong> {{ ship.currentVoyage.loadingPort }}</p>
                  <p><strong>卸货港:</strong> {{ ship.currentVoyage.dischargingPort }}</p>
                  <p><strong>货品:</strong> {{ ship.currentVoyage.cargo.type }}</p>
                  <p><strong>货量:</strong> {{ ship.currentVoyage.cargo.volume }}吨</p>
                  <p><strong>受载日期:</strong> {{ ship.currentVoyage.laycanDate }}</p>
                </Card>
              </Col>
              <Col span="12">
                <Card title="货物信息">
                  <p><strong>货主:</strong> {{ ship.currentVoyage.cargo.owner }}</p>
                  <p><strong>装港计量方式:</strong> {{ ship.currentVoyage.loadingMeasurement }}</p>
                  <p><strong>卸港计量方式:</strong> {{ ship.currentVoyage.dischargingMeasurement }}</p>
                  <p><strong>允许损耗率:</strong> {{ ship.currentVoyage.allowableLossRate }}%</p>
                  <p><strong>洗舱要求:</strong> {{ ship.currentVoyage.tankCleaningRequirements }}</p>
                </Card>
              </Col>
            </Row>
            
            <Card title="联系信息" style="margin-top: 16px;">
              <Row :gutter="16">
                <Col span="6">
                  <h4>操作员</h4>
                  <p>{{ ship.currentVoyage.operator.name }}</p>
                  <p>{{ ship.currentVoyage.operator.contact }}</p>
                </Col>
                <Col span="6">
                  <h4>港口调度</h4>
                  <p>{{ ship.currentVoyage.portDispatcher.name }}</p>
                  <p>{{ ship.currentVoyage.portDispatcher.contact }}</p>
                </Col>
                <Col span="6">
                  <h4>发货人</h4>
                  <p>{{ ship.currentVoyage.shipper.name }}</p>
                  <p>{{ ship.currentVoyage.shipper.contact }}</p>
                </Col>
                <Col span="6">
                  <h4>收货人</h4>
                  <p>{{ ship.currentVoyage.consignee.name }}</p>
                  <p>{{ ship.currentVoyage.consignee.contact }}</p>
                </Col>
              </Row>
            </Card>
            
            <Card title="合同相关事宜" style="margin-top: 16px;">
              <Table :columns="contractColumns" :data="ship.currentVoyage.contractDetails" />
            </Card>
          </div>
        </TabPane>
        
        <TabPane label="下一航次计划" name="next-voyage" v-if="ship.nextVoyage">
          <div class="voyage-info">
            <Card>
              <p><strong>航次号:</strong> {{ ship.nextVoyage.voyageNumber }}</p>
              <p><strong>装货港:</strong> {{ ship.nextVoyage.loadingPort }}</p>
              <p><strong>卸货港:</strong> {{ ship.nextVoyage.dischargingPort }}</p>
              <p><strong>货品:</strong> {{ ship.nextVoyage.cargo.type }}</p>
              <p><strong>货量:</strong> {{ ship.nextVoyage.cargo.volume }}吨</p>
              <p><strong>受载日期:</strong> {{ ship.nextVoyage.laycanDate }}</p>
            </Card>
          </div>
        </TabPane>
        
        <TabPane label="船舶状态" name="ship-status">
          <div class="status-info">
            <Row :gutter="16">
              <Col span="12">
                <Card title="当前状态">
                  <p><strong>航行状态:</strong> {{ getStatusText(ship.status) }}</p>
                  <p><strong>当前航速:</strong> {{ ship.speed }}节</p>
                  <p><strong>当前位置:</strong> {{ ship.currentPosition }}</p>
                  <p v-if="ship.status === 'anchored'">
                    <strong>抛锚时长:</strong> 
                    <span :class="getAnchorageWarningClass(ship.anchorageWarning)">
                      {{ ship.anchorageDuration }}小时
                    </span>
                  </p>
                </Card>
              </Col>
              <Col span="12">
                <Card title="预计信息">
                  <p><strong>预计到港时间:</strong> {{ ship.eta }}</p>
                  <p><strong>预计航行时间:</strong> {{ ship.estimatedTravelTime }}</p>
                  <p><strong>预计剩余距离:</strong> {{ ship.remainingDistance }}海里</p>
                </Card>
              </Col>
            </Row>
          </div>
        </TabPane>
      </Tabs>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ShipDetailView',
  props: {
    ship: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      contractColumns: [
        { title: '项目', key: 'item' },
        { title: '内容', key: 'content' },
        { title: '备注', key: 'remarks' }
      ]
    };
  },
  methods: {
    getShipTypeText(type) {
      const typeMap = {
        'self-owned': '自营船舶',
        'chartered': '期租船舶',
        'international': '国际船舶'
      };
      return typeMap[type] || '未知类型';
    },
    getShipTypeColor(type) {
      const colorMap = {
        'self-owned': 'success',
        'chartered': 'primary',
        'international': 'warning'
      };
      return colorMap[type] || 'default';
    },
    getStatusText(status) {
      const statusMap = {
        sailing: '航行中',
        loading: '装货中',
        unloading: '卸货中',
        anchored: '抛锚中',
        docked: '靠泊中'
      };
      return statusMap[status] || '未知';
    },
    getAnchorageWarningClass(warning) {
      if (!warning) return '';
      return `warning-${warning}`;
    }
  }
};
</script>

<style lang="less" scoped>
.ship-detail-view {
  .ship-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .ship-title {
      h2 {
        margin: 0;
        display: inline-block;
        margin-right: 10px;
      }
    }
  }
  
  .detail-content {
    margin-top: 20px;
  }
  
  .warning-24h {
    color: #FF9800;
    font-weight: bold;
  }
  
  .warning-36h {
    color: #FF5722;
    font-weight: bold;
  }
  
  .warning-48h {
    color: #F44336;
    font-weight: bold;
    animation: blink 1s infinite;
  }
}

@keyframes blink {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}
</style> 