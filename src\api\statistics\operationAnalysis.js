import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 获取营运效率总览
export function queryShipsAmounts (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/head/queryShipsServiceOverall', 
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 损耗率统计
export function queryShipsGoodsLoss (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/head/queryShipsServiceGoodsLoss', 
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 船舶抛锚时长
export function queryShipsAnchorInfo (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/head/queryShipsServiceAnchorInfo', 
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 港口抛锚时长分析
export function querySumOrderByPorts (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/head/queryStatLineSumOrderByPorts', 
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 航次数
export function queryVoyageLineSum (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/head/queryStatVoyageLineSum', 
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 损耗率详情
export function queryShipsGoodsLossDetail (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/head/queryShipsServiceGoodsLossDetail', 
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 导出
export function exportTeplateAll (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/stat/template/voyageCbyyzlReportTeplateAll', 
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  queryShipsAmounts,
  queryShipsGoodsLoss,
  queryShipsAnchorInfo,
  querySumOrderByPorts,
  queryVoyageLineSum,
  queryShipsGoodsLossDetail,
  exportTeplateAll
}