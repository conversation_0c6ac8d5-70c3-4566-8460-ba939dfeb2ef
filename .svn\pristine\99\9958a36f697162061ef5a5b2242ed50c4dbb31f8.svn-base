import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 详情航次数据，直接从列表页面带回来
export function getVoyageProgressById (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/record/execute/getVoyageProgressById',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取港口详情数据
export function getVoyageById (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/record/execute/getVoyageById',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 动态列表（分港口）
export function getVoyageDynamicById (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/record/dynamic/getVoyageDynamicById',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 当前最新一条的靠泊计划
export function getCurrentBerthPlanByVoyageId (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/record/execute/getCurrentBerthPlanByVoyageId',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  getVoyageProgressById,
  getVoyageById,
  getVoyageDynamicById,
  getCurrentBerthPlanByVoyageId
}
