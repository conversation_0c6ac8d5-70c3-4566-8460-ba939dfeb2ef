<template>
  <div class="monthly-schedule">
    <div class="schedule-header">
      <h2>月度排期计划</h2>
      <div class="month-controls">
        <Button type="primary" icon="ios-arrow-back" @click="prevMonth"></Button>
        <span class="current-month">{{ currentYearMonth }}</span>
        <Button type="primary" icon="ios-arrow-forward" @click="nextMonth"></Button>
      </div>
    </div>
    
    <div class="schedule-container" ref="scheduleContainer">
      <div class="schedule-content" :style="{ transform: `translateX(-${scrollPosition}px)` }">
        <!-- 固定的船舶列 -->
        <div class="ships-column">
          <div class="header-cell ship-header">船舶</div>
          <div 
            v-for="ship in ships" 
            :key="ship.id" 
            class="ship-cell"
          >
            <div class="ship-name">{{ ship.name }}</div>
            <div class="ship-capacity">{{ ship.capacity }}</div>
          </div>
        </div>
        
        <!-- 可滚动的日期和航次部分 -->
        <div class="scrollable-area" @wheel.prevent="handleWheel">
          <!-- 日期头部 -->
          <div class="date-headers">
            <div 
              v-for="day in daysInMonth" 
              :key="day.date" 
              class="header-cell date-cell"
              :class="{ 'today': isTodayDate(day.date) }"
            >
              {{ day.dayOfMonth }}
            </div>
          </div>
          
          <!-- 船舶行 -->
          <div 
            v-for="ship in ships" 
            :key="ship.id" 
            class="ship-row"
          >
            <div 
              v-for="day in daysInMonth" 
              :key="`${ship.id}-${day.date}`" 
              class="day-cell"
              :class="{ 'today': isTodayDate(day.date) }"
            ></div>
            
            <!-- 航次条 -->
            <div 
              v-for="voyage in getShipVoyages(ship.id)" 
              :key="voyage.id"
              class="voyage-bar"
              :style="getVoyageStyle(voyage)"
              :class="getVoyageClass(voyage)"
              @click="showVoyageDetails(voyage)"
            >
              <div class="voyage-content">
                <div class="voyage-title">{{ voyage.title }}</div>
                <div class="voyage-route">{{ voyage.departurePort }} - {{ voyage.arrivalPort }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 航次详情弹窗 -->
    <Modal v-model="showVoyageModal" title="航次详情" width="600">
      <div v-if="selectedVoyage">
        <h3>{{ getShipName(selectedVoyage.shipId) }} - {{ selectedVoyage.voyageNo }}</h3>
        <Row>
          <Col span="12">
            <FormItem label="起始港口">{{ selectedVoyage.departurePort }}</FormItem>
          </Col>
          <Col span="12">
            <FormItem label="目的港口">{{ selectedVoyage.arrivalPort }}</FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="12">
            <FormItem label="开始时间">{{ formatDateTime(selectedVoyage.startTime) }}</FormItem>
          </Col>
          <Col span="12">
            <FormItem label="结束时间">{{ formatDateTime(selectedVoyage.endTime) }}</FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="24">
            <FormItem label="货物">{{ selectedVoyage.cargo }}</FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="24">
            <FormItem label="状态">{{ selectedVoyage.status }}</FormItem>
          </Col>
        </Row>
      </div>
    </Modal>
  </div>
</template>

<script>
import { addMonths, subMonths, format, startOfMonth, endOfMonth, eachDayOfInterval, isSameDay, isWithinInterval, parseISO } from 'date-fns';

export default {
  name: 'MonthlySchedule',
  props: {
    voyages: {
      type: Array,
      default: () => []
    },
    ships: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      currentDate: new Date(),
      scrollPosition: 0,
      cellWidth: 60, // 每个日期单元格的宽度
      dayHeight: 50, // 每行的高度
      selectedVoyage: null,
      showVoyageModal: false
    };
  },
  computed: {
    currentYearMonth() {
      const year = this.currentDate.getFullYear();
      const month = this.currentDate.getMonth() + 1;
      return `${year}年${month}月`;
    },
    daysInMonth() {
      const year = this.currentDate.getFullYear();
      const month = this.currentDate.getMonth();
      const daysCount = new Date(year, month + 1, 0).getDate();
      
      const days = [];
      for (let i = 1; i <= daysCount; i++) {
        const date = new Date(year, month, i);
        days.push({
          date: date.toISOString().split('T')[0],
          dayOfMonth: i,
          dayOfWeek: date.getDay()
        });
      }
      
      return days;
    },
    maxScrollPosition() {
      return this.daysInMonth.length * this.cellWidth - (this.$refs.scheduleContainer ? this.$refs.scheduleContainer.clientWidth - 150 : 0);
    }
  },
  methods: {
    prevMonth() {
      const newDate = new Date(this.currentDate);
      newDate.setMonth(newDate.getMonth() - 1);
      this.currentDate = newDate;
      this.scrollPosition = 0;
    },
    nextMonth() {
      const newDate = new Date(this.currentDate);
      newDate.setMonth(newDate.getMonth() + 1);
      this.currentDate = newDate;
      this.scrollPosition = 0;
    },
    handleWheel(event) {
      // 处理水平滚动
      const delta = event.deltaY || event.deltaX;
      let newPosition = this.scrollPosition + (delta > 0 ? 50 : -50);
      
      // 限制滚动范围
      newPosition = Math.max(0, Math.min(newPosition, this.maxScrollPosition));
      this.scrollPosition = newPosition;
    },
    isTodayDate(dateStr) {
      const today = new Date();
      const todayStr = today.toISOString().split('T')[0];
      return dateStr === todayStr;
    },
    getShipVoyages(shipId) {
      return this.voyages.filter(voyage => voyage.shipId === shipId);
    },
    getShipName(shipId) {
      const ship = this.ships.find(s => s.id === shipId);
      return ship ? ship.name : '';
    },
    getVoyageStyle(voyage) {
      const startDate = new Date(voyage.startTime);
      const endDate = new Date(voyage.endTime);
      
      // 检查是否在当前月份内
      const currentYear = this.currentDate.getFullYear();
      const currentMonth = this.currentDate.getMonth();
      const monthStart = new Date(currentYear, currentMonth, 1);
      const monthEnd = new Date(currentYear, currentMonth + 1, 0);
      
      // 如果完全在当前月份之外，不显示
      if (endDate < monthStart || startDate > monthEnd) {
        return { display: 'none' };
      }
      
      // 计算在当前月的开始日期
      const visibleStartDate = startDate < monthStart ? monthStart : startDate;
      const visibleEndDate = endDate > monthEnd ? monthEnd : endDate;
      
      // 计算位置和宽度
      const startDay = visibleStartDate.getDate();
      const daysDuration = Math.ceil((visibleEndDate - visibleStartDate) / (24 * 60 * 60 * 1000)) + 1;
      
      return {
        left: `${(startDay - 1) * this.cellWidth}px`,
        width: `${daysDuration * this.cellWidth - 10}px`,
        top: '10px'
      };
    },
    getVoyageClass(voyage) {
      const statusMap = {
        'loading': 'voyage-loading',
        'sailing': 'voyage-sailing',
        'unloading': 'voyage-unloading',
        'completed': 'voyage-completed',
        'planned': 'voyage-planned'
      };
      
      return statusMap[voyage.status] || 'voyage-default';
    },
    showVoyageDetails(voyage) {
      this.selectedVoyage = voyage;
      this.showVoyageModal = true;
    },
    formatDateTime(dateStr) {
      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    },
    handleKeyDown(event) {
      const step = 100;
      if (event.key === 'ArrowLeft') {
        this.scrollPosition = Math.max(0, this.scrollPosition - step);
      } else if (event.key === 'ArrowRight') {
        const containerWidth = this.$refs.scheduleContainer ? this.$refs.scheduleContainer.clientWidth : 0;
        const maxScroll = this.daysInMonth.length * this.cellWidth - containerWidth + 100;
        this.scrollPosition = Math.min(this.scrollPosition + step, maxScroll);
      }
    },
    scrollToToday() {
      const today = new Date();
      if (today.getMonth() === this.currentDate.getMonth() && 
          today.getFullYear() === this.currentDate.getFullYear()) {
        const dayOfMonth = today.getDate();
        this.scrollPosition = (dayOfMonth - 3) * this.cellWidth;
      }
    }
  },
  mounted() {
    // 计算单元格宽度
    if (this.$refs.scheduleContainer) {
      const containerWidth = this.$refs.scheduleContainer.clientWidth - 150; // 减去船舶列宽度
      const daysCount = this.daysInMonth.length;
      // 如果天数较多，保持最小宽度
      this.cellWidth = Math.max(60, containerWidth / daysCount);
    }
    
    // 添加键盘左右箭头控制滚动
    window.addEventListener('keydown', this.handleKeyDown);
    
    // 初始化滚动位置
    this.scrollToToday();
  },
  beforeDestroy() {
    window.removeEventListener('keydown', this.handleKeyDown);
  }
}
</script>

<style scoped>
.monthly-schedule {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border: 1px solid #dcdee2;
  border-radius: 4px;
  overflow: hidden;
}

.schedule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  background-color: #f8f8f9;
  border-bottom: 1px solid #dcdee2;
}

.month-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.current-month {
  font-size: 16px;
  font-weight: bold;
  min-width: 100px;
  text-align: center;
}

.schedule-container {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.schedule-content {
  display: flex;
  height: 100%;
  transition: transform 0.3s ease;
}

.ships-column {
  width: 150px;
  flex-shrink: 0;
  border-right: 1px solid #dcdee2;
  background-color: #f8f8f9;
  z-index: 10;
}

.scrollable-area {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.header-cell {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #dcdee2;
  font-weight: bold;
}

.ship-header {
  background-color: #f0f0f0;
}

.date-headers {
  display: flex;
  position: sticky;
  top: 0;
  background-color: #f0f0f0;
  z-index: 5;
}

.date-cell {
  min-width: 40px;
  width: 40px;
  flex-shrink: 0;
  border-right: 1px solid #dcdee2;
}

.date-cell.today {
  background-color: #e6f7ff;
  color: #1890ff;
}

.ship-cell {
  height: 60px;
  padding: 5px;
  border-bottom: 1px solid #dcdee2;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.ship-name {
  font-weight: bold;
  margin-bottom: 4px;
}

.ship-capacity {
  font-size: 12px;
  color: #999;
}

.ship-row {
  display: flex;
  position: relative;
  height: 50px;
}

.day-cell {
  min-width: 60px;
  height: 50px;
  border-right: 1px solid #eee;
  border-bottom: 1px solid #eee;
}

.day-cell.today {
  background-color: rgba(255, 248, 230, 0.5);
}

.voyage-bar {
  position: absolute;
  height: 40px;
  top: 10px;
  border-radius: 4px;
  color: #fff;
  cursor: pointer;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s;
  z-index: 2;
}

.voyage-bar:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.voyage-content {
  padding: 4px 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.voyage-title {
  font-weight: bold;
  font-size: 12px;
}

.voyage-route {
  font-size: 11px;
  opacity: 0.9;
}

/* 不同状态的航次颜色 */
.voyage-loading {
  background-color: #2d8cf0;
}

.voyage-sailing {
  background-color: #19be6b;
}

.voyage-unloading {
  background-color: #ff9900;
}

.voyage-completed {
  background-color: #909399;
}

.voyage-planned {
  background-color: #5cadff;
}

.voyage-default {
  background-color: #2d8cf0;
}
</style>