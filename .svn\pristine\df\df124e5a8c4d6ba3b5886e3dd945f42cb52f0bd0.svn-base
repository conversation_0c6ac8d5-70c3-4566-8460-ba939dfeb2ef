<template>
  <div>
    <Card style="padding-bottom: 60px;">
      <span v-if="voyageId">船名：{{ voyage_ship }}</span>
      <search v-else @searchResults='searchResults' @selectOnChanged='selectOnChanged' :setSearch='setSearchData' @keydown.enter.native='searchResults' @resetResults='resetResults'></search>
      <Table style="margin-top: 20px;" border :columns="columns" :data="checkList"></Table>
      <div v-if="!isFromErp" class="check_btn">
        <span class="check-txt">{{ this.check_person === '' ? '该月份没有核对' : `该月份已由${this.check_person}核对` }}</span>
        <Button type="primary" @click="checkComplete">核对完成</Button>
      </div>
    </Card>
    <CheckDrawer :modalData="drawData" @nodeDataBack="getList"></CheckDrawer>
    <BakModal :modalData="bakData" @bakDataBack="getList"></BakModal>
  </div>
</template>
<script>
import search from '_c/search' // 查询组件
import API from '@/api/voyageManage/voyageCheck'
import CheckDrawer from './drawer/checkDrawer.vue'
import BakModal from './drawer/bakModal.vue'

export default {
  components: {
    search,
    CheckDrawer,
    BakModal
  },
  data () {
    return {
      voyageId: undefined,
      voyage_ship: '--',
      disabledExtra: true,
      drawData: {
        modal: false,
        title: '数据核对',
        data: null,
        type: ''
      }, // 弹窗数据
      bakData: { // 备注弹窗参数
        modal: false,
        title: '备注信息',
        id: '',
        stat_line_id: '',
        bak: '',
        operate_exception_bak: '',
        delayed_time_bak: '', // 滞期备注
        isOperate: '' // 是否作业状态
      },
      voyage_over_month: '', // 月份
      queryParam: {
        ship_id: ''
      },
      check_person: '', // 核对人员信息
      checkList: [], // 核对列表数据
      setSearchData: {// 查询设置，对象key值为回调参数
        ship_id: {
          type: 'select',
          label: '船名',
          selectData: [],
          selected: '',
          placeholder: '请选择船名',
          flag: 'ship_id',
          selectName: '',
          width: 130,
          value: ''
        },
        voyage_over_month: {
          type: 'month',
          label: '月份',
          selected: '',
          width: 130,
          flag: 'date',
          value: '',
          isdisable: false
        }
      },
      columns: [
        {
          title: '月份',
          key: 'voyage_over_date',
          align: 'center',
          width: 90,
          fixed: 'left'
        },
        {
          title: '航次',
          key: 'voyage_no',
          align: 'center',
          width: 75,
          fixed: 'left'
        },
        {
          title: '航线',
          align: 'center',
          fixed: 'left',
          children: [
            {
              title: '港口',
              key: 'port_name',
              align: 'center',
              width: 85,
              fixed: 'left'
            }
          ]
        },
        {
          title: '货物',
          align: 'center',
          children: [
            {
              title: '货名',
              key: 'goods_name',
              align: 'center',
              width: 85
            },
            {
              title: '货运量(合同约定)',
              key: 'goods_amount',
              align: 'center',
              width: 95,
              render: (h, params) => {
                return h('div', {
                  style: {
                    cursor: 'pointer'
                  },
                  on: {
                    click: () => {
                      this.showNodeDraw(params, 'mount', 'BA')
                    }
                  }
                }, params.row.goods_amount || '--')
              }
            },
            {
              title: '货运量(船板)',
              key: 'ship_amount',
              align: 'center',
              width: 85
            }
          ]
        },
        {
          title: '船板交接损耗‰',
          key: 'ship_loss_rate',
          align: 'center',
          width: 90
        },
        {
          title: '合同实际损耗‰',
          key: 'loss_rate',
          align: 'center',
          width: 90
        },
        {
          title: '合同允许损耗‰',
          key: 'allowable_loss',
          align: 'center',
          width: 90
        },
        {
          title: '周转量',
          align: 'center',
          children: [
            {
              title: '万吨公里',
              key: 'turnover_value',
              align: 'center',
              width: 95
            }
          ]
        },
        {
          title: '航程',
          align: 'center',
          children: [
            {
              title: '海里',
              key: 'sea_mile',
              align: 'center',
              width: 95,
              render: (h, params) => {
                return h('div', {
                  style: {
                    cursor: 'pointer'
                  },
                  on: {
                    click: () => {
                      this.showNodeDraw(params, 'mate', '')
                    }
                  }
                }, params.row.sea_mile || '--')
              }
            },
            {
              title: '公里',
              key: 'kilometre_value',
              align: 'center',
              width: 95
            }
          ]
        },
        {
          title: '作业时间/日期',
          align: 'center',
          children: [
            {
              title: '抵锚',
              key: 'dm_date_fmt',
              align: 'center',
              width: 85,
              render: (h, params) => {
                return h('div', {
                  style: {
                    cursor: 'pointer'
                  },
                  on: {
                    click: () => {
                      this.showNodeDraw(params, 'common', 'AG,AI')
                    }
                  }
                }, params.row.dm_date_fmt || '--')
              }
            },
            {
              title: '起锚',
              key: 'qm_date_fmt',
              align: 'center',
              width: 85,
              render: (h, params) => {
                return h('div', {
                  style: {
                    cursor: 'pointer'
                  },
                  on: {
                    click: () => {
                      this.showNodeDraw(params, 'common', 'AG,AI')
                    }
                  }
                }, params.row.qm_date_fmt || '--')
              }
            },
            {
              title: '缆绳上岸',
              key: 'lssa_date_fmt',
              align: 'center',
              width: 85,
              render: (h, params) => {
                return h('div', {
                  style: {
                    cursor: 'pointer'
                  },
                  on: {
                    click: () => {
                      this.showNodeDraw(params, 'common', 'AJ')
                    }
                  }
                }, params.row.lssa_date_fmt || '--')
              }
            },
            {
              title: '靠泊',
              key: 'kb_date_fmt',
              align: 'center',
              width: 85,
              render: (h, params) => {
                return h('div', {
                  style: {
                    cursor: 'pointer'
                  },
                  on: {
                    click: () => {
                      this.showNodeDraw(params, 'common', 'AK')
                    }
                  }
                }, params.row.kb_date_fmt || '--')
              }
            },
            {
              title: '开(装/卸)',
              key: 'kzx_date_fmt',
              align: 'center',
              width: 95,
              render: (h, params) => {
                let portStr = ''
                if (params.row.port_type === '1') {
                  portStr = 'AP,AV,AR,AT'
                } else {
                  portStr = 'AQ,AW,AS,AU'
                }
                return h('div', {
                  style: {
                    cursor: 'pointer'
                  },
                  on: {
                    click: () => {
                      this.showNodeDraw(params, 'common', portStr)
                    }
                  }
                }, params.row.kzx_date_fmt || '--')
              }
            },
            {
              title: '完(装/卸)',
              key: 'wzx_date_fmt',
              align: 'center',
              width: 95,
              render: (h, params) => {
                let portStr = ''
                if (params.row.port_type === '1') {
                  portStr = 'AP,AV,AR,AT'
                } else {
                  portStr = 'AQ,AW,AS,AU'
                }
                return h('div', {
                  style: {
                    cursor: 'pointer'
                  },
                  on: {
                    click: () => {
                      this.showNodeDraw(params, 'common', portStr)
                    }
                  }
                }, params.row.wzx_date_fmt || '--')
              }
            },
            {
              title: '拆管',
              key: 'cg_date_fmt',
              align: 'center',
              width: 85,
              render: (h, params) => {
                return h('div', {
                  style: {
                    cursor: 'pointer'
                  },
                  on: {
                    click: () => {
                      this.showNodeDraw(params, 'common', 'BD')
                    }
                  }
                }, params.row.cg_date_fmt || '--')
              }
            },
            {
              title: '离泊',
              key: 'lb_date_fmt',
              align: 'center',
              width: 85,
              render: (h, params) => {
                return h('div', {
                  style: {
                    cursor: 'pointer'
                  },
                  on: {
                    click: () => {
                      this.showNodeDraw(params, 'common', 'BH')
                    }
                  }
                }, params.row.lb_date_fmt || '--')
              }
            }
          ]
        },
        {
          title: '抛锚>72小时',
          align: 'center',
          children: [
            {
              title: '小时',
              key: 'pm_hour',
              align: 'center',
              width: 85
            },
            {
              title: '分钟',
              key: 'pm_minute',
              align: 'center',
              width: 85
            },
            {
              title: '原因',
              key: 'drop_anchor_reason_option',
              align: 'center',
              width: 160,
              render: (h, params) => {
                return h('Select', {
                  props: {
                    value: parseInt(this.checkList[params.index].drop_anchor_reason_option),
                    placeholder: '选择原因',
                    transfer: true
                  },
                  style: {
                    display: (params.row.pm_hour > 0 || params.row.pm_minute > 0) ? 'block' : 'none',
                    width: '90%'
                  },
                  on: {
                    'on-select': e => {
                      this.checkList[params.index].drop_anchor_reason_option = e.value
                      let _param = {
                        id: params.row.stat_cargo_id,
                        stat_line_id: params.row.stat_line_id,
                        bak: params.row.bak,
                        operate_exception_bak: params.row.operate_exception_bak,
                        delayed_time_bak: params.row.delayed_time_bak,
                        drop_anchor_reason_option: e.value
                      }
                      API.updateStatCargo(_param).then(res => {
                        if (res.data.Code !== 10000) {
                          this.$Message.error(res.data.Message)
                        }
                      })
                    }
                  }
                },
                [
                  h('Option', {
                    props: { value: 0 }
                  }, '计划问题'),
                  h('Option', {
                    props: { value: 1 }
                  }, '天气影响'),
                  h('Option', {
                    props: { value: 2 }
                  }, '船舶排队'),
                  h('Option', {
                    props: { value: 3 }
                  }, '库容不足'),
                  h('Option', {
                    props: { value: 4 }
                  }, '货量不足'),
                  h('Option', {
                    props: { value: 5 }
                  }, '航行限制'),
                  h('Option', {
                    props: { value: 6 }
                  }, '船舶因素'),
                  h('Option', {
                    props: { value: 7 }
                  }, '其他影响')
                ])
              }
            },
            {
              title: '备注(>24小时)',
              key: 'bak',
              align: 'center',
              width: 85,
              render: (h, params) => {
                return h('div', {
                  style: {
                    cursor: 'pointer',
                    background: (parseFloat(params.row.pm_hour) >= 24 && params.row.bak === '') ? 'red' : ''
                  },
                  on: {
                    click: () => {
                      this.bakData.modal = true
                      this.bakData.id = params.row.stat_cargo_id
                      this.bakData.stat_line_id = params.row.stat_line_id
                      this.bakData.operate_exception_bak = params.row.operate_exception_bak
                      this.bakData.bak = params.row.bak
                      this.bakData.delayed_time_bak = params.row.delayed_time_bak // 滞期备注
                      this.bakData.isOperate = 'cargo'
                    }
                  }
                }, params.row.bak || '--')
              }
            }
          ]
        },
        {
          title: '装卸货时间',
          align: 'center',
          children: [
            {
              title: '小时',
              key: 'zxh_hour',
              align: 'center',
              width: 85
            },
            {
              title: '分钟',
              key: 'zxh_minute',
              align: 'center',
              width: 85
            },
            {
              title: '作业异常情况',
              key: 'operate_exception_bak',
              align: 'center',
              width: 85,
              render: (h, params) => {
                return h('div', {
                  style: {
                    cursor: 'pointer'
                  },
                  on: {
                    click: () => {
                      this.bakData.modal = true
                      this.bakData.id = params.row.stat_cargo_id
                      this.bakData.stat_line_id = params.row.stat_line_id
                      this.bakData.operate_exception_bak = params.row.operate_exception_bak
                      this.bakData.bak = params.row.bak
                      this.bakData.delayed_time_bak = params.row.delayed_time_bak // 滞期备注
                      this.bakData.isOperate = 'operate'
                    }
                  }
                }, params.row.operate_exception_bak || '--')
              }
            }
          ]
        },
        {
          title: '码头/泊位',
          key: 'wharf_berth_name',
          align: 'center',
          width: 95
        },
        {
          title: '装卸率',
          align: 'center',
          children: [
            {
              title: '吨/小时',
              key: 'zx_rate',
              align: 'center',
              width: 85
            }
          ]
        },
        {
          title: '滞期时间（小时）',
          key: 'delayed_time',
          align: 'center',
          children: [
            {
              title: '约定',
              key: 'promise_delayed_time',
              align: 'center',
              width: 85
            },
            {
              title: '实际',
              key: 'delayed_time',
              align: 'center',
              width: 85,
              render: (h, params) => {
                return h('div', {
                  style: {
                    cursor: 'pointer',
                    color: params.row.delayed_time_exception === '1' ? 'red' : ''
                  },
                  on: {
                    click: () => {
                      this.showNodeDraw(params, 'common', 'AD,BD,AG,AI')
                    }
                  }
                }, params.row.delayed_time || '--')
              }
            },
            {
              title: '备注',
              key: 'delayed_time_bak',
              align: 'center',
              width: 85,
              render: (h, params) => {
                return h('div', {
                  style: {
                    cursor: 'pointer'
                  },
                  on: {
                    click: () => {
                      this.bakData.modal = true
                      this.bakData.id = params.row.stat_cargo_id
                      this.bakData.stat_line_id = params.row.stat_line_id
                      this.bakData.operate_exception_bak = params.row.operate_exception_bak
                      this.bakData.bak = params.row.bak
                      this.bakData.delayed_time_bak = params.row.delayed_time_bak // 滞期备注
                      this.bakData.isOperate = 'delay'
                    }
                  }
                }, params.row.delayed_time_bak || '--')
              }
            }
          ]
        }
      ]
    }
  },
  computed: {
    isFromErp () {
      return (this.$route.params.id && this.$route.params.id !== '')
    }
  },
  methods: {
    // 获取列表数据
    getList () {
      if ((!this.queryParam.ship_id || this.queryParam.ship_id === '') && !this.voyageId) {
        this.$Message.warning('请选择一艘船舶！')
        return
      }
      API.voyageOperationCheckInfo(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          this.checkList = res.data.Result
          this.check_person = res.data.check_person
          if (!this.checkList || this.checkList.length === 0) {
            this.$Message.warning('暂无相关数据！')
            this.disabledExtra = true
          } else {
            this.voyage_ship = this.checkList[0].ship_name
          }
        }
      })
    },
    // 苍穹嵌套专用，地址栏引入航次id，直接获取数据
    getParamList () {
      API.voyageOperationCheckInfo(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          this.checkList = res.data.Result
          this.check_person = res.data.check_person
          if (!this.checkList || this.checkList.length === 0) {
            this.$Message.warning('暂无相关数据！')
            this.disabledExtra = true
          }
        }
      })
    },
    showNodeDraw (list, type, nodeStr) {
      // let titleStr = list.row.ship_name + '  ' + list.row.voyage_no + '  ' + list.row.port_name + '  ' + (list.row.port_type === '1' ? '装' : '卸')
      this.drawData = {
        title: '',
        modal: true,
        data: list.row,
        type: type,
        node_code_in: nodeStr
      }
    },
    // 核对完成
    checkComplete () {
      API.addVoyageCheckInfo({
        ship_id: this.queryParam.ship_id,
        check_month: this.queryParam.voyage_over_month
      }).then(res => {
        if (res.data.Code === 10000) {
          this.$Message.success(res.data.Message)
          this.getList()
        } else {
          this.$Message.warning(res.data.Message)
        }
      })
    },
    // 船舶切换
    selectOnChanged (e) {
      if (e.flag === 'month_start') {
        this.voyage_over_month = e.key
      } else {
        this.queryParam.ship_id = e.target.ship_id.selected
      }
    },
    getUrlKey (name) {
      // eslint-disable-next-line no-sparse-arrays
      return decodeURIComponent((new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(location.href) || [, ''])[1].replace(/\+/g, '%20')) || null
    },
    // 查询
    searchResults (e) {
      if (this.queryParam.voyage_id) {
        delete this.queryParam.voyage_id
      }
      if (e.target) delete e.target
      if (e.ship_id === '') {
        this.$Message.warning('请选择一艘船舶！')
        return
      }
      this.queryParam.ship_id = e.ship_id
      this.queryParam.voyage_over_month = this.voyage_over_month
      this.disabledExtra = false
      this.getList()
    },
    // 重置
    resetResults () {
      this.queryParam = Object.assign(this.queryParam, { // 列表请求参数
        ship_id: '',
        voyage_over_month: ''
      })
      if (this.queryParam.voyage_id) {
        delete this.queryParam.voyage_id
      }
      this.check_person = ''
      this.checkList = []
      this.setSearchData.ship_id.selected = ''
      this.setSearchData.voyage_over_month.selected = ''
      this.getList()
    }
  },
  created () {
    this.voyageId = this.$route.params.id
    if (this.voyageId) {
      Object.assign(this.queryParam, {
        voyage_id: this.voyageId
      })
      this.getList()
    }
    if (localStorage.bussiShipList) {
      let shipList = []
      if (this.$store.state.user.access.includes('companyAdmin')) {
        shipList = JSON.parse(window.localStorage.shipNameList)
      } else {
        shipList = JSON.parse(window.localStorage.bussiShipList)
      }
      // let shipList = JSON.parse(window.localStorage.bussiShipList)
      shipList.map(item => {
        // if ((item.business_model === '1' || item.business_model === '2') && !item.ship_name.includes('善')) { // 只要自营和船期船舶且不需要万邦船舶  2024/09/09调整
        this.setSearchData.ship_id.selectData.push({
          value: item.ship_id,
          label: item.ship_name
        })
        // }
      })
    } else {
      this.setSearchData.ship_id.selectData = []
    }
  }
}
</script>
<style scoped>
  .check-txt {
    color: #007AFF;
    margin-right: 20px;
  }
  .check_btn {
    position: absolute;
    right: 20px;
    margin-top: 20px;
  }
</style>
