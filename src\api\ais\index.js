import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 获取当前航次信息
export function queryCurVoyageMmsi (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/ais/record/queryAisVoyageGetShipMMis',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取历史航次
export function queryHistoryVoyageMmsi (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/ais/record/queryAisVoyageHistoryGetMMSIList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询计划航次
export function queryVoyagePlanConsolePage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/console/home/<USER>/queryVoyagePlanConsolePage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export function queryAisShipMsg (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/ship/info/spider/queryAisShipMsg',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取船舶类型字典
export function getCustomerDictEntryList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/smsconfig/node/config/getCustomerDictEntryList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询港口码头组合
export function queryInnerPortAndWharfList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/piisp/inner/port/queryInnerPortAndWharfList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询ais搜索记录
export function queryAisSearchRecordGroupList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/ais/search/record/queryAisSearchRecordGroupList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 添加ais搜索记录
export function addAisSearchRecord (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/ais/search/record/addAisSearchRecord',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 删除ais搜索记录
export function delAisSearchRecord (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/ais/search/record/delAisSearchRecord',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取所有台风信息
export function queryTyphoonInfoAll (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/typhoon/info/queryTyphoonInfoAll',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取单个台风信息
export function queryTyphoonInfo (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/typhoon/info/queryTyphoonInfo',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}
