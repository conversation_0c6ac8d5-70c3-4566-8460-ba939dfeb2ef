<template>
  <div>
    <Drawer
      v-model="berthingFormModal"
      :data="modalData"
      :title="berthingPlanFormTitle"
      :dialogType="dialogType"
      :width="800"
      :mask-closable="dialogType==='detail'"
      @on-visible-change="visibleChange">
      <Form ref="berthingValidate" v-if="berthingFormModal" :model="berthingValidate" :rules="berthingPlanRuleValidate" :label-width="90" inline>
        <div>
          <h3 class="bold-font title-font">基础信息</h3>
        </div>
        <Row>
          <Col span="8">
            <FormItem label="船名" style="width:100%" prop="ship_id">
              <Select v-model="berthingValidate.ship_id" :disabled="dialogType!=='create'" @on-change="getVoyageList" filterable>
                <Option v-for="(item, index) in shipNameList" :key="index" :value="item.value">{{ item.label }}</Option>
              </Select>
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="航次" prop="voyage_id" style="width:100%">
              <Select v-model="berthingValidate.voyage_id" :disabled="!disabled" filterable>
                <Option v-for="(item, index) in voyageNameList" :key="index" :value="item.value">{{ item.label }}</Option>
              </Select>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="8">
            <FormItem label="港口" prop="port_id" style="width:100%">
              <Select v-model="berthingValidate.port_id" :disabled="dialogType==='detail'" filterable @on-change="portSelector">
                <Option v-for="(item, index) in portNameList" :key="index" :value="item.value">{{ item.label }}</Option>
              </Select>
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="码头" prop="wharf_id" style="width:100%">
              <Select v-model="berthingValidate.wharf_id" :disabled="dialogType==='detail' || berthingValidate.port_id === ''" filterable @on-change="wharfSelector">
                <Option v-for="(item, index) in wharfNameList" :key="index" :value="item.value">{{ item.label }}</Option>
              </Select>
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="泊位" prop="berth_id" style="width:100%">
              <Select v-model="berthingValidate.berth_id" :disabled="dialogType==='detail' || berthingValidate.wharf_id === ''" filterable>
                <Option v-for="(item, index) in berthNameList" :key="index" :value="item.value">{{ item.label }}</Option>
              </Select>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="8">
            <FormItem label="计划时间" prop="plan_date" style="width:100%">
              <DatePicker type="datetime" format="yyyy-MM-dd HH:mm" :value="berthingValidate.plan_date" @on-change="(datetime) =>{ changePlanDate(datetime)}" @on-ok="handleok" :disabled="dialogType==='detail'"></DatePicker>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="24">
            <FormItem label="备注" prop="remarks" style="width: 100%">
              <Input type="textarea" :autosize="true" v-model="berthingValidate.remark" :rows="4" :disabled="dialogType==='detail'"></Input>
            </FormItem>
          </Col>
        </Row>
      </Form>
      <div class="demo-drawer-footer" v-if="dialogType!=='detail'">
        <Button v-if="dialogType==='update'" @click="berthingFormModal = false" style="margin-right:10px;">取消</Button>
        <Button v-if="dialogType==='create'" type="primary" @click="createData('1')" style="margin-right: 10px;">发送</Button>
        <Button v-if="dialogType==='create'" type="primary" @click="createData('2')">不发送</Button>
        <Button v-if="dialogType==='update'" type="primary" @click="updateData">保存</Button>
      </div>
    </Drawer>
  </div>
</template>
<script>
import API from '@/api/berthingPlan/berthingPlan'
import { queryVoyageList, queryPortList, queryWharfList, queryBerthList } from '@/api/basicData'
export default {
  data () {
    return {
      disabled: false, // 航次默认不用编辑
      loadWharfListQuery: {
        port_id: '',
        pageSize: 10000,
        pageIndex: 1
      },
      loadBerthListQuery: {
        terminal_id: '',
        pageSize: 10000,
        pageIndex: 1
      },
      shipNameList: [], // 船舶列表
      voyageNameList: [], // 航次列表
      portNameList: [], // 存储港口
      wharfNameList: [], // 存储码头
      berthNameList: [], // 存储泊位
      berthingFormModal: false, // 模态框显示状态
      berthingPlanFormTitle: '', // 模态框标题
      modalData: {},
      dialogType: null,
      berthingValidate: {
        voyage_id: '',
        send_type: '',
        port_id: '',
        wharf_id: '',
        berth_id: '',
        plan_date: '',
        remark: ''
      },
      berthingPlanRuleValidate: {
        ship_id: [{ required: true, message: '此处不能为空', trigger: 'change' }],
        voyage_id: [{ required: true, message: '此处不能为空', trigger: 'change' }],
        port_id: [{ required: true, message: '此处不能为空', trigger: 'change' }],
        wharf_id: [{ required: true, message: '此处不能为空', trigger: 'change' }],
        berth_id: [{ required: true, message: '此处不能为空', trigger: 'change' }],
        plan_date: [{ required: true, message: '此处不能为空', trigger: 'change' }]
      }
    }
  },
  created () {
    // 获取船名
    if (window.localStorage.shipNameList) {
      let shipList = JSON.parse(window.localStorage.shipNameList)
      shipList.map(item => {
        if ((item.business_model === '1' || item.business_model === '2') && !item.ship_name.includes('善')) { // 只要自营和船期船舶且不需要万邦船舶  2024/09/09调整
          this.shipNameList.push({
            value: item.ship_id,
            label: item.ship_name
          })
        }
      })
    } else {
      this.shipNameList = []
    }
  },
  methods: {
    // 开启模态框
    visibleChange (d) {
      if (d === true) {
        this.getPortDataList()
        if (this.dialogType !== 'create') {
          this.berthingValidate = Object.assign({}, this.modalData)
          this.getVoyageList()
          this.getWharf()
          this.getBerth()
        }
      } else {
        this.resetFiled()
      }
    },
    // 获取航次
    getVoyageList () {
      let params = {
        pageIndex: 1,
        pageSize: 10000,
        ship_id: this.berthingValidate.ship_id
      }
      this.disabled = true
      queryVoyageList(params).then(res => {
        if (res.data.Code === 10000) {
          this.voyageNameList = []
          if (res.data.Result.length === 0 && this.dialogType === 'create') this.berthingValidate.voyage_id = ''
          res.data.Result.map(item => {
            this.voyageNameList.push({
              value: item.id,
              label: item.voyage_no
            })
          })
        } else {
          this.$Message.warning(res.data.Message)
        }
      }).catch()
    },
    getvoyageNameList () {
      this.$Message.warning('船名不能为空！')
    },
    // 获取港口
    getPortDataList () {
      queryPortList().then(response => {
        if (response.data.Code === 10000) {
          response.data.Result.map(item => {
            this.portNameList.push({
              value: item.id,
              label: item.port_name
            })
          })
        }
      })
    },
    // 获取码头
    getWharf () {
      queryWharfList(this.loadWharfListQuery).then(response => {
        if (response.data.Code === 10000) {
          response.data.Result.map(item => {
            this.wharfNameList.push({
              value: item.id,
              label: item.terminal_name
            })
          })
        }
      })
    },
    // 获取泊位
    getBerth () {
      queryBerthList(this.loadBerthListQuery).then(response => {
        if (response.data.Code === 10000) {
          response.data.Result.map(item => {
            this.berthNameList.push({
              value: item.id,
              label: item.berth_name
            })
          })
        }
      })
    },
    changePlanDate (dateTime) {
      this.berthingValidate.plan_date = dateTime
    },
    handleok () {
      let d = new Date()
      let month = d.getMonth() + 1
      let date = d.getDate()
      let hours = d.getHours()
      let minutes = d.getMinutes()
      month = month < 10 ? '0' + month : month
      date = date < 10 ? '0' + date : date
      hours = hours < 10 ? '0' + hours : hours
      minutes = minutes < 10 ? '0' + minutes : minutes
      if (this.berthingValidate.plan_date === '') {
        this.berthingValidate.plan_date = d.getFullYear() + '-' + month + '-' + date + ' ' + hours + ':' + minutes
      }
    },
    // 获取港口选择器
    portSelector () {
      this.loadWharfListQuery.port_id = this.berthingValidate.port_id
      this.wharfNameList = []
      this.berthNameList = []
      this.getWharf()
    },
    // 获取码头选择器
    wharfSelector () {
      this.loadBerthListQuery.terminal_id = this.berthingValidate.wharf_id
      this.berthNameList = []
      this.getBerth()
    },
    // 新增保存
    createData (d) {
      this.$refs['berthingValidate'].validate((valid) => {
        if (valid) {
          this.$Modal.confirm({
            title: '提示',
            content: d === '1' ? '<p>是否发送给船端？</p>' : '<p>是否新增靠泊计划？</p>',
            loading: true,
            onOk: () => { // 1.发送 2.不发送
              if (d === '1') {
                this.berthingValidate.send_type = 1
              } else {
                this.berthingValidate.send_type = 2
              }
              let data = Object.assign({}, this.berthingValidate)
              API.berthplanAdd(data).then((response) => {
                if (response.data.Code === 10000) {
                  this.$Message.success(response.data.Message)
                  this.$Modal.remove()
                  this.$emit('addSuccess')
                  this.closeModal()
                } else {
                  this.berthingFormModal = true
                  this.$Message.error(response.data.Message)
                  this.$Modal.remove()
                }
              }).catch()
            }
          })
        }
      })
    },
    // 编辑保存
    updateData () {
      this.$refs['berthingValidate'].validate((valid) => {
        if (valid) {
          this.$Modal.confirm({
            title: '提示',
            content: '<p>是否修改靠泊计划？</p>',
            loading: true,
            onOk: () => {
              let data = Object.assign({}, this.berthingValidate)
              API.berthplantUpdate(data).then((response) => {
                if (response.data.Code === 10000) {
                  this.$Message.success(response.data.Message)
                  this.$Modal.remove()
                  this.$emit('addSuccess')
                  this.closeModal()
                } else {
                  this.berthingFormModal = true
                  this.$Message.error(response.data.Message)
                  this.$Modal.remove()
                }
              }).catch()
            }
          })
        }
      })
    },
    // 参数重置
    resetFiled () {
      this.voyageNameList = []
      this.berthingValidate = {
        voyage_id: '',
        send_type: '',
        port_id: '',
        wharf_id: '',
        berth_id: '',
        plan_date: '',
        remark: ''
      }
    },
    // 关闭模态框
    closeModal () {
      this.berthingFormModal = false
      this.resetFiled()
    }
  }
}
</script>
<style scoped>
.demo-drawer-footer {
  width: 100%;
  bottom: 0;
  left: 0;
  position: absolute;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  background: #fff;
}
.formModalTitle {
  margin: 0 0 9px;
}
</style>
