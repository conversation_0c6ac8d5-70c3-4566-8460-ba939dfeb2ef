<template>
  <div class="cur-area">
    <Tabs type="card" v-model="curTab" :animated="false">
        <TabPane label="当前航次" name="curVoyage">
          <cur-voyage ref="curVoyageRefs" :reset="curReset" @planModify="handleModify"></cur-voyage>
        </TabPane>
        <TabPane label="计划航次" name="planVoyage">
          <plan-voyage ref="planVoyageRefs" :reset="planReset" @planModify="handleModify"></plan-voyage>
        </TabPane>
        <span slot="extra">
          <Button v-if="getCompanyAdmin" type="primary" size="small" icon="md-add" @click="addPlanVoyage">新增</Button>
          <Button type="primary" size="small" icon="md-sync" @click="resetVoyage" style="margin-left: 10px;">刷新</Button>
        </span>
    </Tabs>
    <voyageAdd ref="voyageAddDrawer" @addBack="addBackFun"></voyageAdd>
  </div>
</template>
<script>
import curVoyage from './curVoyage'
import planVoyage from './planVoyage'
import voyageAdd from './drawer/voyageAddDraw'

export default {
  components: {
    curVoyage,
    planVoyage,
    voyageAdd
  },
  data () {
    return {
      curTab: 'curVoyage', // 默认执行航次
      planReset: false, // 计划航次是否刷新
      curReset: false, // 当前航次是否刷新
      setFormAction: {
        operation: ['create']
      }
    }
  },
  computed: {
    getCompanyAdmin () {
      return this.$store.state.user.access.includes('companyAdmin')
    }
  },
  methods: {
    // 新增计划航次
    addPlanVoyage () {
      this.planReset = false
      this.curReset = false
      this.$refs.voyageAddDrawer.formModal = true
      this.$refs.voyageAddDrawer.title = '新增航次'
      this.$refs.voyageAddDrawer.showType = 'create'
    },
    resetVoyage () {
      if (this.curTab === 'curVoyage') {
        this.$refs.curVoyageRefs.getList()
      }
      if (this.curTab === 'planVoyage') {
        this.$refs.planVoyageRefs.getList()
      }
    },
    // 新增成功回调
    addBackFun (type) {
      this.curTab = type
      if (type === 'curVoyage') {
        this.curReset = true
      }
      if (type === 'planVoyage') {
        this.planReset = true
      }
    },
    // 计划航次修改、变更
    handleModify (d) {
      this.$refs.voyageAddDrawer.formModal = true
      this.$refs.voyageAddDrawer.title = d.type === 'modify' ? '修改航次' : '航次变更'
      this.$refs.voyageAddDrawer.showType = d.type
      this.$refs.voyageAddDrawer.detailId = d.id
    }
  }
}
</script>

<style lang="less">
  .cur-area {
    .ivu-tabs-bar {
      margin-bottom: 0;
    }
  }
</style>
