<template>
  <div class="sanction_area">
    <div class="top_sticky">
      <h3 class="sanction_title">制裁名单查询</h3>
      <Input v-model="company_name" size="large" placeholder="请输入公司名称" @on-enter="getList">
        <Button class="search_btn" slot="append" icon="ios-search" @click="getList"></Button>
      </Input>
    </div>
    <Spin v-if="isLoading" fix>
      <Icon type="ios-loading" size=30 class="demo-spin-icon-load"></Icon>
      <div>正在全网筛查中……</div>
    </Spin>
    <!-- 美国财政部海外资产控制办公室受制裁清单 开始 -->
    <div v-if="company_name !== ''" class="sanction_list_area">
      <Card class="sanction_card" title="美国财政部海外资产控制办公室受制裁清单">
        <div v-if="!sanctionObj.sanctionsSearch || sanctionObj.sanctionsSearch.length === 0">暂无数据</div>
        <div v-else v-for="(item, idx) in sanctionObj.sanctionsSearch" :key="'first' + idx">
          <Poptip transfer trigger="hover" placement="top-start">
            <span class="first_title">{{ idx + 1}}、{{ item.name }}</span>
            <div slot="content" style="width: 800px; max-width: 100%;">
              <Row class="first_list_title">
                <Col span="4">Name</Col>
                <Col span="4">Address</Col>
                <Col span="4">Type</Col>
                <Col span="4">Program(s)</Col>
                <Col span="4">List</Col>
                <Col span="4">Score</Col>
              </Row>
              <Row class="first_list_content">
                <Col span="4" style="white-space: break-spaces;">{{ item.name || '-' }}</Col>
                <Col span="4" style="white-space: break-spaces;">{{ item.address || '-' }}</Col>
                <Col span="4" style="white-space: break-spaces;">{{ item.type || '-' }}</Col>
                <Col span="4" style="white-space: break-spaces;">{{ item.program || '-' }}</Col>
                <Col span="4" style="white-space: break-spaces;">{{ item.list || '-' }}</Col>
                <Col span="4" style="white-space: break-spaces;">{{ item.score || '-' }}</Col>
              </Row>
            </div>
          </Poptip>
        </div>
      </Card>
      <!-- 美国财政部海外资产控制办公室受制裁清单 结束 -->

      <!-- 美国商务部工业和安全局受制裁清单 开始 -->
      <Card class="sanction_card" title="美国商务部工业和安全局受制裁清单">
        <div v-if="!sanctionObj.consolidatedSearch || sanctionObj.consolidatedSearch.length === 0">暂无数据</div>
        <div v-else v-for="(item, idx) in sanctionObj.consolidatedSearch" :key="'first' + idx">
          <Poptip transfer trigger="hover" placement="top-start">
            <span class="first_title"> {{ idx + 1}}、{{ item.name }}</span>
            <div slot="content" style="width: 300px; max-width: 100%;">
              <div class="first_list_title">Alt_names</div>
              <div class="first_list_content" v-for="(list, index) in item.alt_names" :key="'alt' + index">
                {{ list }}
              </div>
            </div>
          </Poptip>
        </div>
      </Card>
      <!-- 美国商务部工业和安全局受制裁清单 结束 -->

      <!-- 美国商务部工业和安全局拒绝往来名单 开始 -->
      <Card class="sanction_card" title="美国商务部工业和安全局受制裁清单">
        <div v-if="!sanctionObj.deniedSearch || sanctionObj.deniedSearch.length === 0">暂无数据</div>
        <div style="max-width: 300px;" v-else v-for="(item, idx) in sanctionObj.deniedSearch" :key="'first' + idx" @click="deniedClick(item)">
          <span class="first_title">{{ idx + 1}}、{{ item.text }}</span>
        </div>
      </Card>
      <!-- 美国商务部工业和安全局拒绝往来名单 结束 -->

      <!-- 欧洲联盟委员会金融稳定、金融服务和资本市场联盟，金融制裁清单 开始 -->
      <Card class="sanction_card" title="欧洲联盟委员会金融制裁清单">
        <div v-if="!sanctionObj.europaSearch || sanctionObj.europaSearch.length === 0">暂无数据</div>
        <div style="max-width: 300px;" v-else v-for="(item, idx) in sanctionObj.europaSearch" :key="'first' + idx">
          <span class="first_title_none">{{ idx + 1}}、{{ item }}</span>
        </div>
      </Card>
      <!-- 欧洲联盟委员会金融稳定、金融服务和资本市场联盟，金融制裁清单 结束 -->

      <!-- 英国官网，查询受制裁主体清单检 开始 -->
      <Card class="sanction_card" title="英国官网受制裁主体清单">
        <div v-if="!sanctionObj.sanctionsListSearch || sanctionObj.sanctionsListSearch.length === 0">暂无数据</div>
        <div style="width: 300px;" v-else v-for="(item, idx) in sanctionObj.sanctionsListSearch" :key="'first' + idx">
          <Poptip transfer trigger="hover" placement="top-end">
            <span class="first_title">{{ idx + 1}}、{{ item.fullName }}</span>
            <div slot="content" style="width: 800px; max-width: 100%;">
              <Row class="first_list_title">
                <Col span="3">Group ID</Col>
                <Col span="4">Name</Col>
                <Col span="4">Regime</Col>
                <Col span="4">Status</Col>
                <Col span="3">Type</Col>
                <Col span="6">Other Information</Col>
              </Row>
              <Row class="first_list_content">
                <Col span="3" style="white-space: break-spaces;">{{ item.groupID || '-' }}</Col>
                <Col span="4" style="white-space: break-spaces;">{{ item.fullName || '-' }}</Col>
                <Col span="4" style="white-space: break-spaces;">{{ item.regimeName || '-' }}</Col>
                <Col span="4" style="white-space: break-spaces;">{{ item.groupStatus || '-' }}</Col>
                <Col span="3" style="white-space: break-spaces;">{{ item.groupTypeDescription || '-' }}</Col>
                <Col span="6" style="white-space: break-spaces;">{{ item.otherInformation || '-' }}</Col>
              </Row>
            </div>
          </Poptip>
        </div>
      </Card>
      <!-- 英国官网，查询受制裁主体清单检 结束 -->
    </div>
  </div>
</template>

<script>
import API from '@/api/unLoginPage/sanction.js'

export default {
  data() {
    return {
      isLoading: false,
      company_name: '',
      sanctionObj: {}
    }
  },
  methods: {
    getList() {
      if (!this.company_name || this.company_name === '') {
        this.$Message.warning('请先输入查询名单！')
        return
      }
      this.sanctionObj = {}
      this.isLoading = true
      API.queryCompanies({ company_name: this.company_name }).then(res => {
        this.isLoading = false
        if (res.data.Code === 10000) {
          this.sanctionObj = res.data
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    deniedClick (item) { // 美国商务部工业和安全局拒绝往来名单 点击跳转
      let _targetUrl = 'https://www.bis.doc.gov' + item.href
      window.open(_targetUrl, '_blank')
    }
  }
}
</script>
<style scoped>
  .top_sticky {
    position: sticky;
    background: #fff;
    z-index: 999;
    top: 0;
  }
  .sanction_area {
    height: 100%;
    overflow-y: scroll;
    padding: 0 50px;
    margin-top: 30px;
  }
  .sanction_title {
    text-align: center;
    margin: 15px;
  }
  .search_btn {
    background: #57a3f3;
    color: #fff;
  }
  .sanction_list_area {
    display: flex;
    justify-content: flex-start;
    margin-top: 20px;
    overflow: auto;
    flex-wrap: wrap;
  }
  .sanction_card {
    /* max-height: 800px; */
    max-height: calc(100vh - 145px);
    overflow: auto;
    margin-right: 20px;
    margin-bottom: 20px;
  }
  .first_title {
    color: #57a3f3;
    cursor: pointer;
    border-bottom: 1px solid #57a3f3;
  }
  .first_title_none {
    color: #57a3f3;
  }
  .first_list_title {
    display: flex;
    background: #57a3f3;
    font-size: 14px;
    font-weight: bold;
    color: #fff;
    padding: 5px;
  }
  .first_list_content {
    display: flex;
    padding: 5px;
  }
  .demo-spin-icon-load {
    animation: ani-demo-spin 1s linear infinite;
  }
  @keyframes ani-demo-spin {
    from { transform: rotate(0deg);}
    50%  { transform: rotate(180deg);}
    to   { transform: rotate(360deg);}
  }
</style>