<template>
  <div> <!-- 装货作业 -->
    <Col align="right" style="position: absolute; right: 0; z-index: 1000;">
      <Button type="primary" size="small" v-show="dialogType==='update'" style="margin-right: 8px" @click="closeModal()">返回</Button>
      <!-- <Button type="primary" size="small" icon="md-brush" @click="handleModify" :disabled="disabled">编辑</Button> &nbsp; -->
      <Button type="primary" size="small" icon="md-eye" @click="IsUpdateAll ? updateData() : exportExcel()">预览</Button>
    </Col>
    <Tabs @on-click="tab_click" value="tab1-1" type="card">
      <TabPane v-for="(tab, index) in tabList" :key="index" :label="tab.tabListInfo" :name="`tab1-${index + 1}`">
        <!-- 基本信息 -->
        <Form ref="formInline" :model="formInline" inline :label-width="80" label-colon class="formstyle">
          <Row v-for="(item, index) in tableHeaderData" :key="index">
            <Col span="7">
              <FormItem label="装货港"><b>{{ item.port_name }}</b></FormItem>
            </Col>
            <!-- <Col style="float:right;">
              <FormItem label="状态"><b>{{ item.is_update }}</b></FormItem>
            </Col> -->
          </Row>
        </Form>
        <Col :span="dialogType === 'update' ? '11' : '18'">
          <Table border :loading="listLoading" :columns="columns" :data="list" class="tableclass" :row-class-name="rowClassName"></Table>
        </Col>
        <!-- 编辑查看右侧列表内容 -->
        <!-- <Col span="13" v-show="dialogType === 'update'">
          <Col span="4" style="margin-top: 50%;"><Icon type="ios-arrow-forward" size="100" /></Col>
          <Table border :columns="editColumns" :data="editList" class="tableclass"></Table>
        </Col> -->
      </TabPane>
    </Tabs>
    <!-- <div slot="footer" v-show="dialogType==='update'" class="footer-btn">
      <Button type="primary" @click="updateData">保存</Button>
    </div> -->
  </div>
</template>
<script>
import API from '@/api/voyageManage/voyageSof'

export default {
  data () {
    return {
      row_class_value: '',
      disabled: false, // 是否可编辑
      tabList: [], // 港口tabName
      tabListInfoPortId: '', // 存储港口id
      tabListInfoPortName: '', // 存储港口名称
      tabListInfoPort: '', // 存储港口名称
      tabListInfoWharfId: '', // 码头id
      tabListInfoWharfName: '', // 存储码头名称
      formInline: {}, // 表单获取港口基本信息
      tableHeaderData: [], // 存储港口数据
      tableList: [], // 存储全局码头信息
      tHeaderData: [], // 存储全局头部港口信息
      tabIndex: 0, // 默认显示的tab
      dialogType: null,
      listLoading: false, // 表单列表loding状态
      getDataInfoList: [], // table抬头title
      columnsFirstData: {}, // 存储columns第一列是否可视操作
      columns: [
        {
          title: '',
          align: 'center',
          width: '45',
          render: (h, params) => {
            return h('div', [
              h('Icon', {
                props: {
                  type: params.row.is_read === '0' ? 'md-eye-off' : 'md-eye',
                  size: '16'
                },
                style: {
                  color: params.row.is_read === '0' ? '#999' : ''
                },
                on: {
                  click: () => {
                    Object.assign(params.row, {
                      is_read: params.row.is_read === '1' ? '0' : '1'
                    })
                    this.list[params.index].is_read = params.row.is_read
                    this.changeOnForOff(params.index, params.row)
                  }
                }
              })
            ])
          }
        },
        {
          title: '港口',
          align: 'center',
          children: [
            {
              title: '节点',
              key: 'node_name',
              align: 'center'
            }
          ]
        },
        {
          align: 'center',
          renderHeader: (h, params) => {
            return h('span', this.getDataInfo)
          },
          children: [
            {
              title: '日期',
              key: 'node_date',
              align: 'center'
            },
            {
              title: '时间',
              key: 'node_time',
              align: 'center'
            },
            {
              title: '备注',
              key: 'remark',
              align: 'center'
            }
          ]
        }
      ],
      list: [], // 表单列表数据
      listDataArr: [], // 存储未编辑保存的list表单列表数据
      editColumns: [ // 右侧表单列表
        {
          title: '港口',
          align: 'center',
          children: [
            {
              title: '节点',
              key: 'node_name',
              align: 'center'
            }
          ]
        },
        {
          align: 'center',
          renderHeader: (h, params) => {
            return h('span', this.getDataInfo)
          },
          children: [
            {
              title: '日期',
              key: 'node_date',
              align: 'center'
            },
            {
              title: '时间',
              key: 'node_time',
              align: 'center'
            },
            {
              title: '备注',
              key: 'remark',
              align: 'center'
            }
          ]
        }
      ],
      editList: [], // 右侧列表数据
      resultArr: [], // 存储回参数据
      IsUpdateAll: '', // 存储是否已编辑
      subMenuDisabled: [], // 按钮权限
      voyageDetailId: '', // 航线id
      messList: [], // 存储缺失关键节点数据
      mainNodeData: [], // 存储所有节点
      mainNodeItemValue: [], // 关键节点value
      deleCurList: [] // 存储移除的非关键节点
    }
  },
  created () {
    // 按钮权限
    if (localStorage.menuSort && JSON.parse(localStorage.menuSort).length !== 0) {
      JSON.parse(localStorage.menuSort).map(item => {
        this.subMenuDisabled.push(item.menu_sort)
      })
      this.subMenuDisabled = this.subMenuDisabled.join(',')
    }
  },
  mounted () {
    this.columnsFirstData = this.columns[0]
    this.columns.splice(0, 1)
  },
  methods: {
    // 列表装货信息
    getLoadingOperationList () {
      this.listLoading = false
      this.tabList = []
      this.tHeaderData = []
      API.queryVoyageLoadPortSof({ voyage_id: localStorage.getItem('voyageId') }).then(res => {
        if (res.data.Code === 10000) {
          if (res.data.Result.length > 0) {
            if (res.data.Result[0].is_update === 0) {
              this.IsUpdateAll = true
            } else {
              this.IsUpdateAll = false
            }
          }
          res.data.Result.map(item => {
            this.tabListInfoPort = item.port_name ? item.port_name : '' // 港口
            let tabListInfoWharf = item.wharf_name && item.wharf_name !== '' ? '(' + item.wharf_name + ')' : ''
            this.tabList.push({ // 港口(码头)tab切换
              tabListInfo: this.tabListInfoPort + tabListInfoWharf
            })
            this.tHeaderData.push({ // 获取头部港口信息
              port_name: this.tabListInfoPort,
              is_update: item.is_update === 0 ? '未编辑' : '已编辑'
            })
            this.getDataInfoList.push(this.tabListInfoPort + tabListInfoWharf) // columns.title
          })
          if (res.data.Result.length > 0) {
            this.disabled = false
            this.tableList = res.data.Result
            this.changeListData(this.tabIndex)
            this.tabListInfoPortId = this.tableList[this.tabIndex].port_id
            this.tabListInfoPortName = this.tableList[this.tabIndex].port_name
            this.tabListInfoWharfId = this.tableList[this.tabIndex].wharf_id
            this.tabListInfoWharfName = this.tableList[this.tabIndex].wharf_name
            this.voyageDetailId = this.tableList[this.tabIndex].voyage_id
          }
        }
        setTimeout(() => {
          this.listLoading = false
        }, 1 * 500)
      }).catch(
        setTimeout(() => {
          this.listLoading = false
        }, 1 * 500)
      )
    },
    // 获取table动态信息
    changeListData (tabIndex) {
      this.tableHeaderData = []
      this.nullData(tabIndex)
      this.resultArr = this.tableList[tabIndex].dynamicResult
      this.resultArr.map((item, index) => {
        if (item.is_read === '0') {
          this.$set(this.list, index, {
            id: item.id,
            is_read: item.is_read,
            node_name: item.node_name,
            node_time: '—',
            node_date: '—',
            remark: '—'
          })
        } else {
          this.$set(this.list, index, {
            id: item.id,
            is_read: item.is_read,
            node_name: item.node_name,
            node_time: item.node_time ? item.node_time : '-',
            node_date: item.node_date ? item.node_date : '-',
            remark: item.remark ? item.remark : '-'
          })
        }
      })
      this.disabled = false
      this.listDataArr = this.list // 存储list列表初始数据
    },
    // tab点击切换
    tab_click (index) {
      this.tabIndex = parseInt(index.split('-')[1]) - 1
      this.tabListInfoPortId = this.tableList[this.tabIndex].port_id
      this.tabListInfoPortName = this.tableList[this.tabIndex].port_name
      this.tabListInfoWharfId = this.tableList[this.tabIndex].wharf_id
      this.tabListInfoWharfName = this.tableList[this.tabIndex].wharf_name
      this.voyageDetailId = this.tableList[this.tabIndex].detail_id
      if (this.tableList.length !== 0) {
        this.changeListData(this.tabIndex)
      }
      if (this.tableList[this.tabIndex].is_update === 0) {
        this.IsUpdateAll = true
      } else {
        this.IsUpdateAll = false
      }
      this.editList = [...this.list]
    },
    // 重置table信息
    nullData (tabIndex) {
      this.dialogType = null // 切换tab时重置编辑弹窗
      if (this.columns.length > 2) {
        this.columns.splice(0, 1)
      }
      this.list = []
      this.editList = []
      this.tableHeaderData.push(this.tHeaderData[tabIndex]) // 列表前港口信息
      this.getDataInfo = this.getDataInfoList[tabIndex] // columns.title
    },
    rowClassName (row) {
      if (this.dialogType === 'update' && row.is_read === '0') {
        return 'demo-table-isnoread-column'
      } else {
        return ''
      }
    },
    // 开启编辑组件
    handleModify () {
      this.dialogType = 'update'
      this.disabled = true
      this.columns.splice(0, 0, this.columnsFirstData)
      this.list = []
      let _tempList = this.listDataArr.map(item => {
        return item.id
      })
      if (this.IsUpdateAll) { // 未编辑
        this.resultArr.map(e => {
          this.list.push({
            node_name: e.node_name,
            node_date: e.node_date ? e.node_date : '—', // 日期
            node_time: e.node_time ? e.node_time : '—', // 时间
            remark: e.remark ? e.remark : '—', // 备注
            id: e.id, // 动态id
            port_name: e.port_name ? e.port_name : '—',
            is_read: _tempList.includes(e.id) ? '1' : '0'
          })
        })
      } else { // 已编辑
        this.resultArr.map(e => {
          this.list.push({
            node_name: e.node_name,
            node_date: e.node_date ? e.node_date : '—', // 日期
            node_time: e.node_time ? e.node_time : '—', // 时间
            remark: e.remark ? e.remark : '—', // 备注
            id: e.id, // 动态id
            port_name: e.port_name ? e.port_name : '—',
            is_read: e.is_read
          })
        })
      }
      this.editList = [...this.list]
      this.list.map((leftList, index_e) => {
        if (leftList.is_read === '0') {
          this.editList.map((e, index1) => {
            if (e.id === leftList.id) {
              this.$set(this.editList, index1, {
                id: e.id,
                is_read: e.is_read,
                node_name: e.node_name,
                node_time: '—',
                node_date: '—',
                remark: '—'
              })
            }
          })
        }
      })
    },
    // 点击可见改为不可见
    changeOnForOff (index, row) {
      if (row.is_read === '1') { // 1:可见
        this.editList.map((e, index1) => {
          if (e.id === row.id) {
            this.$set(this.editList, index1, row)
            this.editList[index1].is_read = row.is_read
          }
        })
        this.deleCurList = [...this.editList]
      } else { // 0:不可见
        this.editList.map((e, index1) => {
          if (e.id === row.id) {
            this.$set(this.editList, index1, {
              id: e.id,
              is_read: row.is_read,
              node_name: e.node_name,
              node_time: '—',
              node_date: '—',
              remark: '—'
            })
          }
        })
        this.deleCurList = [...this.editList]
      }
    },
    // 编辑保存
    updateData () {
      // this.$Modal.confirm({
      //   title: '提示',
      //   content: '<p>是否保存修改？</p>',
      //   loading: true,
      //   onOk: () => {
      let dynamic_data = []
      // this.deleCurList = this.list
      this.list.map(item => {
        if (item.id) {
          dynamic_data.push({ // 装货动态数组
            dynamic_id: item.id, // 动态id
            is_read: '1' // item.is_read // 1：可见 0：不可见
          })
        }
      })
      let data = {
        port_id: this.tabListInfoPortId,
        wharf_id: this.tabListInfoWharfId,
        voyage_id: localStorage.getItem('voyageId'),
        detail_id: this.voyageDetailId,
        dynamic_data: JSON.stringify(dynamic_data)
      }
      API.updateLoadPortSof(data).then(res => {
        if (res.data.Code === 10000) {
          //   this.dialogType = null
          //   this.columns.splice(0, 1)
          //   this.$Modal.remove()
          //   this.disabled = false
          //   this.$Message.success(res.data.Message)
          //   this.getLoadingOperationList()
          this.exportExcel()
        } else {
          this.$Message.error(res.data.Message)
          //   this.$Modal.remove()
        }
      }).catch()
      //   }
      // })
    },
    // 关闭编辑
    closeModal () {
      this.dialogType = null
      this.disabled = false
      this.columns.splice(0, 1) // 移除是否可视操作table列
      this.list = this.listDataArr
    },
    // 导出装货作业
    exportExcel () {
      let voyageObj = JSON.parse(localStorage.getItem('voyageObj'))
      let param = {
        voyage_id: localStorage.getItem('voyageId'),
        port_id: this.tabListInfoPortId,
        port_name: this.tabListInfoPortName,
        wharf_id: this.tabListInfoWharfId,
        wharf_name: this.tabListInfoWharfName,
        ship_name: voyageObj.ship_name,
        voyage_date: voyageObj.voyage_date,
        voyage_no: voyageObj.voyage_no
      }
      API.previewVoyageLoadPort(param).then(res => {
        if (res.data.Code === 10000) {
          sessionStorage.setItem('wpsUrl', res.data.wpsUrl)
          sessionStorage.setItem('token', res.data.token)
          const jump = this.$router.resolve({ name: 'viewFile' })
          window.open(jump.href, '_blank')
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.formstyle {
  width: 75%;
  b {
    font-size: 14px;
  }
  .ivu-form-item-label {
    font-size: 14px;
  }
}
.footer-btn {
  text-align: right;
  margin-top: 30px;
}
.tableclass .ivu-table-tip table {
  min-height: 350px;
}
</style>
<style>
.demo-table-isnoread-column td {
  background-color: #dcdee2;
}
</style>
