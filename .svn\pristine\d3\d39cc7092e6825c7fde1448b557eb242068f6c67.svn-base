import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 获取船东公司权限配置列表
export function spComConfigAuthPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/auth/company/config/querySpComConfigAuthPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 公司查询
export function spComConfigAuthList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/auth/company/config/querySpComConfigAuthList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 权限开启关闭
export function changeSpComConfig (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/auth/company/config/changeSpComConfigAuth',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  spComConfigAuthPage,
  spComConfigAuthList,
  changeSpComConfig
}
