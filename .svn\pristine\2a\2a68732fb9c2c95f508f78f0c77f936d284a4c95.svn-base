import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 货运总览》整体对比
export function queryShipsGoodsAmountOverall (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/head/queryShipsGoodsAmountOverall',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 货运总览》整体对比》船舶货运量 & 船舶航次 详情中对应的船舶月度报表
export function queryShipsGoodsAmountOverallReport (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/head/queryShipsGoodsAmountOverallReport',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 货运总览》整体对比》周转量详情 报表部分
export function queryShipsTurnoverOverallReport (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/head/queryShipsTurnoverOverallReport',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 货运总览》单船对比 按月分组
export function queryStatShipMonthInfo (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/head/queryStatShipMonthInfo',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 货运总览》港口货流量
export function queryStatPortsGoodsAmount (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/head/queryStatPortsGoodsAmount',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  queryShipsGoodsAmountOverall,
  queryShipsGoodsAmountOverallReport,
  queryShipsTurnoverOverallReport,
  queryStatShipMonthInfo,
  queryStatPortsGoodsAmount
}
