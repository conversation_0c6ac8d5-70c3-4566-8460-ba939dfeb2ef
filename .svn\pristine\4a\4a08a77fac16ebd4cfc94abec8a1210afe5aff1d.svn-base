<template>
  <Drawer
    :title="modalData.title"
    v-model="modalData.modal"
    width="520"
    @on-visible-change="visibleChange"
    :mask-closable="false"
    :styles="drawerStyles"
    >
    <Form :model="formData" :rules="formRules" :label-width="100" ref="cargoForm">
      <FormItem label="船舶" prop="ship_id" :rules="{ type: 'array', required: true, message: '请选择船舶', trigger: 'change' }">
        <Select filterable multiple clearable v-model="formData.ship_id" placeholder="请选择船舶">
          <Option v-for="(item, n) in shipList" :value="item.ship_id" :key="n">{{ item.ship_name }}</Option>
        </Select>
      </FormItem>

      <FormItem v-if="modalData.modal" label="归属月份" prop="belong_month">
        <DatePicker type="month" :value="formData.belong_month" placeholder="选择归属月份" style="width: 300px" @on-change="data => formData.belong_month = data"></DatePicker>
      </FormItem>
      
      <FormItem label="货品" prop="goods_id">
        <Select filterable v-model="formData.goods_id" placeholder="请选择货品">
          <Option v-for="(item, n) in goodsList" :value="item.id" :key="n">{{ item.cargo_name }}</Option>
        </Select>
      </FormItem>
      
      <FormItem label="货量" prop="amounts" @on-change="getFee">
        <InputNumber v-model="formData.amounts" :min="1"  style="width: 150px"></InputNumber>
        <span style="margin-left: 8px">吨</span>
      </FormItem>

      <FormItem label="运价" prop="freight_rate">
        <InputNumber v-model="formData.freight_rate" :min="1" style="width: 150px" @on-change="getFee"></InputNumber>
        <span style="margin-left: 8px">元/吨</span>
      </FormItem>

      <FormItem label="运费" prop="shipping_fee">
        <InputNumber disabled v-model="formData.shipping_fee" :min="1" style="width: 150px"></InputNumber>
        <span style="margin-left: 8px">元</span>
      </FormItem>
      
      <FormItem label="装货港" prop="load_port_id">
        <Select filterable v-model="formData.load_port_id" placeholder="请选择装货港">
          <Option v-for="(item, n) in portNameList" :value="item.id" :key="n">{{ item.port_name }}</Option>
        </Select>
      </FormItem>
      
      <FormItem label="卸货港" prop="unload_port_id">
        <Select filterable v-model="formData.unload_port_id" placeholder="请选择卸货港">
          <Option v-for="(item, n) in portNameList" :value="item.id" :key="n">{{ item.port_name }}</Option>
        </Select>
      </FormItem>
      
      <FormItem label="托运方">
        <Select filterable v-model="formData.shipper" placeholder="请输入联系人姓名">
          <Option v-for="(item, n) in agentList" :value="item.customer_company_id" :key="n">{{ item.customer_company_name }}</Option>
        </Select>
      </FormItem>
      
      <FormItem label="收货方">
        <Select filterable v-model="formData.recipient" placeholder="请输入联系电话">
          <Option v-for="(item, n) in agentList" :value="item.customer_company_id" :key="n">{{ item.customer_company_name }}</Option>
        </Select>
      </FormItem>
      
      <FormItem label="备注说明">
        <Input v-model="formData.remarks" type="textarea" :rows="4" placeholder="请输入备注说明" />
      </FormItem>
      
      <div class="drawer-footer">
        <Button @click="handleCancel" style="margin-right: 8px">取消</Button>
        <Button type="primary" @click="handleSubmit" :loading="submitting">提交</Button>
      </div>
    </Form>
  </Drawer>
</template>
<script>
import API from '@/api/shipSchedule'
import { queryCustomerList, queryPortList, queryBasicCargoList } from '@/api/basicData'

export default ({
  props: {
    modalData: Object
  },
  data() {
    return {
      belong_month: null,
      submitting: false,
      shipList: [], // 船舶列表
      goodsList: [], // 货品列表
      portNameList: [], // 港口列表
      agentList: [], // 货主列表
      formData: {
        ship_id: '',
        goods_id: '',
        amounts: 0,
        freight_rate: 0,
        shipping_fee: 0,
        load_port_id: '',
        unload_port_id: '',
        shipper: '',
        recipient: '',
        remarks: ''
      },
      drawerStyles: {
        height: 'calc(100% - 55px)',
        overflow: 'auto',
        paddingBottom: '53px',
        position: 'static'
      },
      formRules: {
        goods_id: [
          { required: true, message: '请选择货物类型', trigger: 'change' }
        ],
        belong_month: [
          { required: true, message: '请选择归属月份', trigger: 'change' }
        ],
        amounts: [
          { 
            required: true, 
            message: '请输入货物数量', 
            trigger: 'blur',
            type: 'number',
            validator: (rule, value, callback) => {
              if (value === 0 || value) {
                callback();
              } else {
                callback(new Error('请输入货物数量'));
              }
            }
          }
        ],
        freight_rate: [
        { 
            required: true, 
            message: '请输入运价', 
            trigger: 'blur',
            type: 'number',
            validator: (rule, value, callback) => {
              if (value === 0 || value) {
                callback();
              } else {
                callback(new Error('请输入运价'));
              }
            }
          }
        ],
        load_port_id: [
          { required: true, message: '请输入装货港', trigger: 'blur' }
        ],
        unload_port_id: [
          { required: true, message: '请输入卸货港', trigger: 'blur' }
        ]
      },
    }
  },
  methods: {
    getBaseData() {
      this.shipList = JSON.parse(localStorage.getItem('shipNameList')).filter(list => list.business_model === '1').filter(t => !t.ship_name.includes('善')) || [];
      // 获取港口
      queryPortList(this.loadPortListQuery).then(res => {
        if (res.data.Code === 10000) {
          this.portNameList = res.data.Result
        }
      })
      // 获取货物
      queryBasicCargoList({ cargo_name: '' }).then(res => {
        if (res.data.Code === 10000) {
          this.goodsList = res.data.Result
        }
      })
      // 获取代理公司 公司类型（1、船东；2、货主；3、代理）
      queryCustomerList({ company_type: 2 }).then(res => {
        this.agentList = []
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.agentList.push(item)
          })
        }
      })
    },
    handleCancel() {
      this.modalData.modal = false
      this.$emit('editClose')
      this.resetForm()
    },
    handleSubmit() {
      this.$refs.cargoForm.validate((valid) => {
        if (valid) {
          let _param = {...{}, ...this.formData}
          if(_param.ship_id.length > 0) {
            _param.ship_ids = _param.ship_id.join()
            _param.belong_month = this.formatMonth(_param.belong_month)
            delete _param.ship_id
          }
          if(this.modalData.type === 'modify') {
            API.updateVoyageGuaranteePlan(_param).then(res => {
              if(res.data.Code === 10000) {
                this.$Message.success(res.data.Message)
                this.$emit('editClose')
              } else {
                this.$Message.error(res.data.Message)
              }
            })
          }
          if(this.modalData.type === 'add') {
            API.addVoyageGuaranteePlan(_param).then(res => {
              if(res.data.Code === 10000) {
                this.$Message.success(res.data.Message)
                this.$emit('editClose')
              } else {
                this.$Message.error(res.data.Message)
              }
            })
          }
        }
      })
    },
    formatMonth(dateInput) {
      const dateObj = new Date(dateInput) // 保证为 Date 类型
      if (isNaN(dateObj)) return '' // 避免非法时间格式

      const y = dateObj.getFullYear()
      const m = dateObj.getMonth() + 1
      return `${y}-${m < 10 ? '0' + m : m}`
    },
    visibleChange(val) {
      this.resetForm()
      if (!val) {
        this.$emit('editClose')
      } else {
        this.getBaseData()
        if(this.modalData.type === 'modify') {
          // 确保数据格式正确
          const data = {...this.modalData.data}
          // 处理归属月份：确保是 Date 对象
          // if (data.belong_month) {
          //   data.belong_month = new Date(data.belong_month)
          // }
          // 处理其他数值类型字段
          data.amounts = parseFloat(data.amounts)
          data.shipping_fee = parseFloat(data.shipping_fee)
          data.freight_rate = parseFloat(data.freight_rate)
          // 处理船舶ID数组
          if (data.ship_ids) {
            data.ship_id = data.ship_ids.split(',')
            delete data.ship_ids
          }
          this.formData = data
        }
      }
    },
    resetForm() {
      this.formData = {
        ship_id: '',
        belong_month: null,
        goods_id: '',
        amounts: 0,
        freight_rate: 0,
        shipping_fee: 0,
        load_port_id: '',
        unload_port_id: '',
        shipper: '',
        recipient: '',
        remarks: ''
      };
      if (this.$refs.cargoForm) {
        this.$refs.cargoForm.resetFields();
      }
    },
    // 计算运费
    getFee() {
      this.formData.shipping_fee = parseFloat(this.formData.amounts) * parseFloat(this.formData.freight_rate)
    }
  }
})
</script>
<style>
  .drawer-footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    border-top: 1px solid #e8e8e8;
    padding: 10px 16px;
    text-align: right;
    left: 0;
    background: #fff;
  }
</style>
