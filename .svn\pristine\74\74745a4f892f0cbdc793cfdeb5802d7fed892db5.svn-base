// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from 'vue'
import App from './App'
import router from './router'
import store from './store'
import ViewUI from 'view-design'
import Vant from 'vant'
import config from '@/config'
import md5 from './plugin/md5'
import installPlugin from '@/plugin'
import * as WPS from './libs/jwps.es6'
import './index.less'
import 'vant/lib/index.css'
import '@/assets/icons/iconfont.css'
import '@/assets/iconfont/iconfont.css'
import 'v-org-tree/dist/v-org-tree.css'
import fullscreen from 'vue-fullscreen' // 全屏显示

Vue.prototype.wps = WPS
Vue.use(Vant)
Vue.use(ViewUI, {
  transfer: true
})

Vue.use(md5)
Vue.use(fullscreen)
// 下载指令
Vue.directive('down', {
  inserted: (el, binding) => {
    el.addEventListener('click', () => {
      let link = document.createElement('a')
      let url = binding.value
      fetch(url).then(res => res.blob()).then(blob => {
        link.href = URL.createObjectURL(blob)
        link.download = ''
        document.body.appendChild(link)
        link.click()
      })
    })
  }
})
Vue.directive('dblclick', {
  bind(el, binding) {
    const handler = () => {
      if (typeof binding.value === 'function') {
        binding.value()
      }
    }
    el.__dblclick__ = handler
    el.addEventListener('dblclick', handler)
  },
  unbind(el) {
    el.removeEventListener('dblclick', el.__dblclick__)
    delete el.__dblclick__
  }
})
/**
 * @description 注册admin内置插件
 */
installPlugin(Vue)
/**
 * @description 生产环境关掉提示
 */
Vue.config.productionTip = false
/**
 * @description 全局注册应用配置
 */
Vue.prototype.$config = config

/* eslint-disable no-new */
new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
