import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 获取码头管理列表,分页
export function queryWharfPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/wharf/record/queryBasicWharfManagePage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取码头管理列表，无分页
export function queryWharfList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/wharf/record/queryBasicWharfManageList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 删除
export function queryDeleteWharf (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/wharf/record/delBasicWharf',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 新增
export function addWharf (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/wharf/record/addBasicWharf',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 修改
export function updateWharf (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/wharf/record/updateBasicWharf',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}
