<template>
  <Modal
    v-model="modalData.modal" class="wharf_area" :styles="{position: 'absolute', top: '110px', left: '215px'}" :mask="false"
    :transfer="false" width="500px" draggable footer-hide @on-visible-change="tideShow">
    <p slot="header">
      <div class="wharf_title">
        <span>潮汐观察点：{{ modalData.data.cncountry }} ({{ modalData.data.cnname }})</span>
        <span style="margin-left: 20px;">时区：UTC+{{ modalData.data.timezone }}</span>
      </div>
    </p>
    <div>
      <Row>
        <Col span="12" v-for="(item, idx) in tideViewList" :key="'view' + idx">
          <div class="tide-view-title">{{ transDate(item.date) }}</div>
          <div class="tide-view-item">
            <span>{{ item.tide_lowhigh1 }}：{{ item.tide_time1 }}</span>
            <span style="margin-left: 20px;">潮高：{{ item.tide_height1 }}m</span>
          </div>
          <div class="tide-view-item">
            <span>{{ item.tide_lowhigh2 }}：{{ item.tide_time2 }}</span>
            <span style="margin-left: 20px;">潮高：{{ item.tide_height2 }}m</span>
          </div>
          <div class="tide-view-item">
            <span>{{ item.tide_lowhigh3 }}：{{ item.tide_time3 }}</span>
            <span style="margin-left: 20px;">潮高：{{ item.tide_height3 }}m</span>
          </div>
          <div class="tide-view-item">
            <span>{{ item.tide_lowhigh4 }}：{{ item.tide_time4 }}</span>
            <span style="margin-left: 20px;">潮高：{{ item.tide_height4 }}m</span>
          </div>
        </Col>
      </Row>
      <div class="tide-btn-area">
        <Button class="tide-btn" type="primary" size="small" @click="preBtnHandle">前一天</Button>
        <Button class="tide-btn" type="primary" size="small" @click="aftBtnHandle">后一天</Button>
      </div>
      <div>
        <TideLine style="width: 500px; height: 200px;" unit="潮高(米)" :value="tideNumData" text=""></TideLine>
      </div>
    </div>
  </Modal>
</template>
<script>
import { querySysDate, queryTideList } from '@/api/basicData'
import { TideLine } from '_c/charts'

export default {
  props: {
    modalData: Object
  },
  components: {
    TideLine
  },
  data () {
    return {
      sysDate: '', // 系统时间
      tidePreDate: '', // 潮汐当前日期
      tideAftDate: '', // 潮汐后一天日期
      tideDetailList: [], // 潮汐数据表
      tideViewList: [], // 潮汐总览表
      tideXList: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
      tideNumData: { // 线性图数据
        xAxis: [],
        legend: [],
        data: {
          name: '',
          data: [],
          time: []
        }
      }
    }
  },
  methods: {
    // 获取潮汐详情
    getTideDetail () {
      queryTideList({
        pid: this.modalData.id,
        sdate: this.tidePreDate,
        edate: this.tideAftDate
      }).then(res => {
        if (res.data.Code === 10000) {
          this.tideViewList = res.data.Result.data.overview
          this.tideDetailList = res.data.Result.data.detail
          this.tideNumData.xAxis = []
          this.tideNumData.data.data = []
          this.tideNumData.data.time = []
          this.tideDetailList.forEach(item => {
            this.tideXList.forEach((list, idx) => {
              this.tideNumData.xAxis.push(idx)
              this.tideNumData.data.name = this.modalData.data.cnname
              this.tideNumData.data.time.push(idx > 10 ? (idx + ':00') : ('0' + idx + ':00'))
              this.tideNumData.data.data.push(item['h' + idx])
            })
          })
        }
      })
    },
    // 前一天点击事件
    preBtnHandle () {
      this.tidePreDate = this.getPreDate(this.tidePreDate)
      this.tideAftDate = this.getPreDate(this.tideAftDate)
      this.getTideDetail()
    },
    // 后一天点击事件
    aftBtnHandle () {
      this.tidePreDate = this.getAftDate(this.tidePreDate)
      this.tideAftDate = this.getAftDate(this.tideAftDate)
      this.getTideDetail()
    },
    getPreDate (date) {
      let curDate = new Date(date)
      let preDayDate = curDate.getTime() - 24 * 60 * 60 * 1000
      let preDay = new Date(preDayDate)
      let returnDate = preDay.getFullYear() + '-' + (preDay.getMonth() + 1) + '-' + preDay.getDate()
      return returnDate
    },
    getAftDate (date) {
      let curDate = new Date(date)
      let preDayDate = curDate.getTime() + 24 * 60 * 60 * 1000
      let preDay = new Date(preDayDate)
      let returnDate = preDay.getFullYear() + '-' + (preDay.getMonth() + 1) + '-' + preDay.getDate()
      return returnDate
    },
    // 日期格式转换
    transDate (date) {
      let dateArr = date.split('-')
      let backStr = dateArr[0] + '年' + dateArr[1] + '月' + dateArr[2] + '日'
      return backStr
    },
    tideShow (val) {
      if (val) {
        querySysDate().then(res => {
          if (res.data.Code === 10000) {
            this.sysDate = res.data.systemDate.split(' ')[0]
            this.tidePreDate = this.sysDate
            this.tideAftDate = this.getAftDate(this.sysDate)
            this.getTideDetail()
          }
        })
      }
    }
  },
  watch: {
    modalData (n, o) {
      if (n) {
        this.tideShow(true)
      }
    }
  }
}
</script>
<style scoped>
  .tide-view-title {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 10px;
  }
  .tide-view-item {
    font-size: 12px;
    margin: 5px 0;
  }
  .tide-btn-area {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .tide-btn {
    margin: 10px;
  }
</style>
