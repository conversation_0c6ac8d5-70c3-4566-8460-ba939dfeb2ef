import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 查询配置信息列表
export function queryAisUserConfigList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/ais/user/config/queryAisUserConfigList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 配置信息列表 新增 单条
export function addAisUserConfig (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/ais/user/config/addAisUserConfig',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 配置信息列表 修改 单条
export function updateAisUserConfig (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/ais/user/config/updateAisUserConfig',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询配置信息列表 删除 单条
export function delAisUserConfig (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/ais/user/config/delAisUserConfig',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  queryAisUserConfigList,
  addAisUserConfig,
  updateAisUserConfig,
  delAisUserConfig
}
