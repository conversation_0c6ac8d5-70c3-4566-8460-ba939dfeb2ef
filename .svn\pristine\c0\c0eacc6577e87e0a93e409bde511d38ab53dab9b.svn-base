<template>
  <div>
    <Spin size="large" fix v-if="totalSpinShow">
      <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
      <div>Loading</div>
    </Spin>
    <Card>
      <div class="btn-area">
        <Button size="small" type="primary" icon="ios-arrow-back" @click="$emit('callback')">返回</Button>
        <month-select @on-change="dateSelect"></month-select>
        <Button size="small" type="primary" icon="md-eye" @click="handleReport('view')">预览报表</Button>
        <Button size="small" type="primary" icon="md-download" @click="handleReport('down')">下载报表</Button>
      </div>
      <div class="total-num">
        <Row>
          <Col span="6">
            <div class="num-title">总货运量</div>
            <!-- <div class="num-date">{{ nowDate }} | 截止本月</div> -->
            <count-to class="num-data" :end="totalNum" unitText="万吨" unitClass="num-unit" :decimals="decimals3" usegroup/>
            <!-- <div class="num-data">800,000<span class="num-unit">吨</span></div> -->
          </Col>
          <Col span="6">
            <div class="num-title">化学品货运量</div>
            <count-to class="num-data" :end="chemicalNum" unitText="万吨" unitClass="num-unit" :decimals="decimals3" usegroup/>
            <!-- <div class="num-data">550,000<span class="num-unit">吨</span></div> -->
          </Col>
          <Col span="6">
            <div class="num-title">油品货运量</div>
            <count-to class="num-data" :end="oilNum" unitText="万吨" unitClass="num-unit" :decimals="decimals3" usegroup/>
            <!-- <div class="num-data">60,000<span class="num-unit">吨</span></div> -->
          </Col>
          <Col span="6">
            <div class="num-title">总航次</div>
            <count-to class="num-data" :end="voyageNum" unitText="次" unitClass="num-unit" usegroup/>
            <!-- <div class="num-data">56<span class="num-unit">次</span></div> -->
          </Col>
        </Row>
      </div>
      <div class="line-area">
        <Row>
          <Col span="8">
            <chart-line style="height: 400px;" unit="万吨" :value="totalNumData" text="总货运量"/>
          </Col>
          <Col span="8">
            <chart-line style="height: 400px;" unit="万吨" :value="chemicalNumData" text="化学品货运量"/>
          </Col>
          <Col span="8">
            <chart-line style="height: 400px;" unit="万吨" :value="oilNumData" text="油品货运量"/>
          </Col>
        </Row>
      </div>
      <div>
        <chart-line style="height: 400px;" unit="次" :value="voyageNumData" text="总航次"/>
      </div>
    </Card>
  </div>
</template>
<script>
import { ChartLine } from '_c/charts'
import CountTo from '_c/count-to'
import MonthSelect from '@/components/monthSelect'
import API from '@/api/statistics'

export default {
  components: {
    ChartLine,
    MonthSelect,
    CountTo
  },
  data () {
    return {
      decimals3: 3, // 小数点保留位数
      totalSpinShow: false,
      totalNum: 0, // 货运总量
      chemicalNum: 0, // 化学品量
      oilNum: 0, // 油品量
      voyageNum: 0, // 总航次
      queryParam: {
        start_month: '',
        end_month: ''
      },
      totalNumData: {
        xAxis: [],
        legend: [],
        data: []
      },
      chemicalNumData: { // 化学品货运量
        xAxis: [],
        legend: [],
        data: []
      },
      oilNumData: { // 油品货运量
        xAxis: [],
        legend: [],
        data: []
      },
      voyageNumData: {
        xAxis: [],
        legend: [],
        data: []
      }
    }
  },
  methods: {
    getList () {
      this.resetData()
      API.queryStatisticGoodsInnerReport(this.queryParam).then(res => {
        this.totalSpinShow = false
        if (res.data.Code === 10000) {
          this.totalNum = parseFloat(res.data.amount_sum)
          this.chemicalNum = parseFloat(res.data.chemical_amount_sum)
          this.oilNum = parseFloat(res.data.oils_amount_sum)
          this.voyageNum = parseFloat(res.data.voyage_total)
          // 总货运量
          res.data.monthTotalArr.forEach((item, idx) => {
            this.totalNumData.xAxis.push(item.voyage_over_month)
            this.totalNumData.data.push(parseFloat(item.month_amount_sum))
          })
          // 化学品货运量
          res.data.monthChemicalsArr.forEach(item => {
            this.chemicalNumData.xAxis.push(item.voyage_over_month)
            this.chemicalNumData.data.push(parseFloat(item.month_amount_sum))
          })
          // 油品货运量
          res.data.monthOilsArr.forEach(item => {
            this.oilNumData.xAxis.push(item.voyage_over_month)
            this.oilNumData.data.push(parseFloat(item.month_amount_sum))
          })
          // 总航次
          res.data.monthVoyageNumberArr.forEach(item => {
            this.voyageNumData.xAxis.push(item.voyage_over_month)
            this.voyageNumData.data.push(parseFloat(item.month_voyage_sum))
          })
        }
      })
    },
    // 数据清空 重置
    resetData () {
      this.totalSpinShow = true
      this.totalNum = 0 // 货运总量
      this.chemicalNum = 0 // 化学品量
      this.oilNum = 0 // 油品量
      this.voyageNum = 0 // 总航次
      this.totalNumData.xAxis = []
      this.totalNumData.data = []
      this.chemicalNumData.xAxis = []
      this.chemicalNumData.data = []
      this.oilNumData.xAxis = []
      this.oilNumData.data = []
      this.voyageNumData.xAxis = []
      this.voyageNumData.data = []
    },
    dateSelect (dateObj) {
      this.queryParam.start_month = dateObj[0] ? dateObj[0] : ''
      this.queryParam.end_month = dateObj[1] ? dateObj[1] : ''
      this.getList()
    },
    // 报表预览 下载
    handleReport (str) {
      API.voyageHylztxqReportTemplate(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          if (str === 'view') { // 预览
            sessionStorage.setItem('wpsUrl', res.data.wpsUrl)
            sessionStorage.setItem('token', res.data.token)
            const jump = this.$router.resolve({ name: 'viewFile' })
            window.open(jump.href, '_blank')
          } else { // 下载
            window.open(res.data.fileUrl, '_blank')
          }
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    }
  },
  created () {
    this.getList()
  }
}
</script>

<style lang="less" scoped>
  .btn-area {
    text-align: right;
    margin-bottom: 10px;
    button {
      margin-left: 12px;
    }
  }
  .total-num {
    position: relative;
    background: #DAE8FF;
    padding: 32px 25px;
    .num-title {
      font-size: 14px;
      font-weight: bold;
    }
    .num-date {
      font-size: 10px;
      color: #7C8093;
    }
    .num-data {
      margin-top: 13px;
      font-size: 24px;
      font-weight: bold;
    }
  }
  .line-area {
    margin-top: 40px;
  }
</style>
<style>
  .num-unit {
    font-size: 10px;
    font-weight: 100;
    margin-left: 6px;
  }
</style>
