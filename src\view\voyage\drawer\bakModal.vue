<template>
  <Modal v-model="modalData.modal"
    :title="modalData.title"
    width="300"
    :closable="false"
    :mask-closable="false"
    @on-ok="handleSave">
    <Input v-if="modalData.isOperate === 'cargo'" v-model="modalData.bak" />
    <Input v-if="modalData.isOperate === 'operate'" v-model="modalData.operate_exception_bak" />
    <Input v-if="modalData.isOperate === 'delay'" v-model="modalData.delayed_time_bak" />
  </Modal>
</template>
<script>
import API from '@/api/voyageManage/voyageCheck'

export default {
  props: {
    modalData: Object
  },
  data () {
    return {

    }
  },
  methods: {
    handleSave () {
      let _param = {
        id: this.modalData.id,
        stat_line_id: this.modalData.stat_line_id,
        bak: this.modalData.bak,
        operate_exception_bak: this.modalData.operate_exception_bak,
        delayed_time_bak: this.modalData.delayed_time_bak
      }
      API.updateStatCargo(_param).then(res => {
        if (res.data.Code === 10000) {
          this.$emit('bakDataBack')
        } else {
          this.$Message.warning(res.data.Message)
        }
      })
    }
  }
}
</script>
