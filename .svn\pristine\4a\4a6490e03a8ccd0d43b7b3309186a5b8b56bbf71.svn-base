import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'
// const baseUrl = process.env.NODE_ENV === 'development' ? config.baseUrl.dev : config.baseUrl.pro

// 获取报表模板数据
export function exportTemplateList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/template/dynamic/queryDynamicTeplateReportList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData,
    cancelToken: true
  })
}

// 导出
export function exportTemplateReport (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/template/dynamic/previewDynamicTeplateReport',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取船东公司下的客户公司信息
export function customerCompanyList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/customer/company/queryDistinctBasicCustomerCompanyList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  exportTemplateList,
  exportTemplateReport,
  customerCompanyList
}
