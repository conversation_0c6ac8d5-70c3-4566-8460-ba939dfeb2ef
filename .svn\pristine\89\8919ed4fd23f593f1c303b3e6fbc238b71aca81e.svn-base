<!-- 货运量整体对比 -->
<template>
  <div>
    <Card>
      <div>
        时间：<month-select class="month-select" @on-change="sdateSelect" :placeMent="startPlace" v-model="curDate"></month-select>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        对比时间：<month-select class="month-select" @on-change="cdateSelect" v-model="checkDate"></month-select>
        <Button style="margin-left: 20px;" type="primary" size="small" @click="searchDataUp">查询</Button>
        <Button style="margin-left: 10px;" size="small" @click="searchResetUp">重置</Button>
      </div>
      <div>
        <div class="morebtn" @click="amountDrawerShow">详情 &gt;</div>
        <!-- <ChartLine class="box-chart" :value="lineData1" unit="万吨" :formatter="true" :color="lineColor" text="船舶货运量" /> -->
        <ChartBar class="box-chart" :value="lineData1" unit="万吨" :color="lineColor" text="船舶货运量" rotate="45"></ChartBar>
        <Table border :columns="columns1" :data="data1"></Table>
        <div class="morebtn" @click="voyageDrawerShow">详情 &gt;</div>
        <!-- <ChartLine class="box-chart" :value="lineData2" unit="次" :formatter="true" :color="lineColor" text="船舶航次" /> -->
        <ChartBar class="box-chart" :value="lineData2" unit="次" :color="lineColor" text="船舶航次" rotate="45"></ChartBar>
        <Table border :columns="columns2" :data="data2"></Table>
        <div class="morebtn" @click="turnDrawerShow">详情 &gt;</div>
        <!-- <ChartLine class="box-chart" :value="lineData3" unit="万吨公里" :formatter="true" :color="lineColor" text="船舶周转量" /> -->
        <ChartBar class="box-chart" :value="lineData3" unit="万吨公里" :color="lineColor" text="船舶周转量" rotate="45"></ChartBar>
        <Table border :columns="columns3" :data="data3"></Table>
      </div>
    </Card>
    <AmountDrawer :modalData="amountModal" :dateObj="queryParam" :defaultStartDate="start_default_date" :defaultEndDate="end_default_date"></AmountDrawer>
    <VoyageDrawer :modalData="voyageModal" :dateObj="queryParam" :defaultStartDate="start_default_date" :defaultEndDate="end_default_date"></VoyageDrawer>
    <TurnDrawer :modalData="turnModal" :dateObj="queryParam" :defaultStartDate="start_default_date" :defaultEndDate="end_default_date"></TurnDrawer>
  </div>
</template>
<script>
import API from '@/api/statistics/transPortView'
import { queryStatTime } from '@/api/basicData'
import MonthSelect from '@/components/monthSelect'
import { ChartLine, ChartBar } from '_c/charts'
import AmountDrawer from '../drawer/amountDrawer.vue'
import VoyageDrawer from '../drawer/voyageDrawer.vue'
import TurnDrawer from '../drawer/turnDrawer.vue'

export default {
  components: {
    MonthSelect,
    ChartLine,
    ChartBar,
    AmountDrawer,
    VoyageDrawer,
    TurnDrawer
  },
  props: {
    startDate: String,
    endDate: String
  },
  data () {
    return {
      curCheck: 0, // 当前对比图
      startPlace: 'bottom-start',
      shipList: [], // 公司船舶列表
      shipNameList: [], // 公司船舶名称列表
      start_default_date: '', // 默认开始时间
      end_default_date: '', // 默认结束时间
      queryParam: {
        start_month: '',
        end_month: '',
        start_contrast_month: '',
        end_contrast_month: ''
      },
      amountModal: {
        modal: false,
        title: '船舶货运量',
        data: {}
      },
      voyageModal: {
        modal: false,
        title: '船舶航次',
        data: {}
      },
      turnModal: {
        modal: false,
        title: '船舶周转量',
        data: {}
      },
      lineColor: ['#5B8FF9', '#73DEB3'],
      lineData1: {
        xAxis: [],
        legend: ['时间', '对比时间'],
        data: [[], []],
        symbol: ['circle', 'triangle']
      },
      lineData2: {
        xAxis: [],
        legend: ['时间', '对比时间'],
        data: [[], []],
        symbol: ['circle', 'triangle']
      },
      lineData3: {
        xAxis: [],
        legend: ['时间', '对比时间'],
        data: [[], []],
        symbol: ['circle', 'triangle']
      },
      checkDataList: {
        xAxis: [],
        yAxis: [
          { name: '货运量(万吨)' }
        ],
        legend: ['2019年', '2020年'],
        seriesData: [
          {
            name: '2019年',
            type: 'bar',
            smooth: 0.2,
            symbol: 'circle',
            data: []
          },
          {
            name: '2020年',
            type: 'bar',
            smooth: 0.2,
            symbol: 'circle',
            data: []
          }
        ]
      },
      columns1: [], // 船舶货运量表头
      data1: [], // 船舶货运量数据
      columns2: [], // 船舶航次表头
      data2: [], // 船舶航次数据
      columns3: [], // 船舶周转量表头
      data3: [] // 船舶周转量数据
    }
  },
  created () {
    this.getSysDate()
    // this.getUpList()
  },
  computed: {
    curDate () {
      if (this.queryParam.start_month !== '' && this.queryParam.end_month !== '') {
        return this.queryParam.start_month + '~' + this.queryParam.end_month
      }
      if (this.queryParam.start_month !== '' && this.queryParam.end_month === '') {
        return this.queryParam.start_month
      }
      return ''
    },
    checkDate () {
      if (this.queryParam.start_contrast_month !== '' && this.queryParam.end_contrast_month !== '') {
        return this.queryParam.start_contrast_month + '~' + this.queryParam.end_contrast_month
      }
      if (this.queryParam.start_contrast_month !== '' && this.queryParam.end_contrast_month === '') {
        return this.queryParam.start_contrast_month
      }
      return ''
    }
  },
  methods: {
    getUpList () { // 获取船舶货运量 航次 周转量数据 拆线图
      this.resetLineData()
      this.lineData1.legend = [this.curDate, this.checkDate]
      this.lineData2.legend = [this.curDate, this.checkDate]
      this.lineData3.legend = [this.curDate, this.checkDate]
      this.shipList = JSON.parse(window.localStorage.shipNameList)
      this.columns1.push({
        title: '日期',
        key: 'valueDate',
        align: 'center',
        width: 135
      })
      this.shipList.forEach((item, idx) => {
          if ((item.business_model === '1' || item.business_model === '2') && !item.ship_name.includes('善')) { // 只要自营和船期船舶且不需要万邦船舶  2024/09/09调整
          this.shipNameList.push(item.ship_name)
          this.columns1.push({
            title: item.ship_name,
            key: 'value' + idx,
            align: 'center',
            width: 100
          })
        }
      })
      this.columns1 = this.columns2 = this.columns3
      let curDataArr = []
      let compareDataArr = []
      API.queryShipsGoodsAmountOverall(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          if (res.data.shipNumAndAmountArray.length > 0) { // 剔除货运量 航次数据为0船舶(暂时以货运量为标准数据计算)
            res.data.shipNumAndAmountArray.forEach((item, idx) => {
              if (res.data.shipNumAndAmountArray[idx].amount_sum !== '0' || res.data.contrastShipNumAndAmountArray[idx].amount_sum !== '0') {
                curDataArr.push(res.data.shipNumAndAmountArray[idx])
                compareDataArr.push(res.data.contrastShipNumAndAmountArray[idx])
              } else { // 剔除表头内容
                this.columns1.splice(idx)
                this.columns2.splice(idx)
              }
            })
          }
          // 船舶货运量 & 船舶航次对比数据
          this.resetShipAmountVoyage(curDataArr, 0)
          this.resetShipAmountVoyage(compareDataArr, 1)
          // 船舶周转量数据对比
          let curTurnData = []
          let compareTurnData = []
          if (res.data.shipTurnoverArray.length > 0) { // 剔除货运量 航次数据为0船舶(暂时以货运量为标准数据计算)
            res.data.shipTurnoverArray.forEach((item, idx) => {
              if (res.data.shipTurnoverArray[idx].turnover_volume_sum !== '0' || res.data.contrastShipTurnoverArray[idx].turnover_volume_sum !== '0') {
                curTurnData.push(res.data.shipTurnoverArray[idx])
                compareTurnData.push(res.data.contrastShipTurnoverArray[idx])
              } else { // 剔除表头内容
                this.columns3.splice(idx)
              }
            })
          }
          this.resetShipTurnOver(curTurnData, 0)
          this.resetShipTurnOver(compareTurnData, 1)

          // 货运量数据重组
          this.resetTableData(curDataArr, this.data1, 0, 'amount_sum')
          this.resetTableData(compareDataArr, this.data1, 1, 'amount_sum')
          // 航次数据重组
          this.resetTableData(curDataArr, this.data2, 0, 'voyage_total')
          this.resetTableData(compareDataArr, this.data2, 1, 'voyage_total')
          // 航次数据重组
          this.resetTableData(curTurnData, this.data3, 0, 'turnover_volume_sum')
          this.resetTableData(compareTurnData, this.data3, 1, 'turnover_volume_sum')
        }
      })
    },
    getSysDate () {
      queryStatTime().then(res => {
        if (res.data.Code === 10000) {
          if (res.data.start_month && res.data.end_month) {
            this.queryParam.start_month = res.data.start_month
            this.queryParam.end_month = res.data.end_month
            this.queryParam.start_contrast_month = res.data.start_month.substring(0, 4) - 1 + res.data.start_month.substring(4, 7)
            this.queryParam.end_contrast_month = res.data.end_month.substring(0, 4) - 1 + res.data.end_month.substring(4, 7)
            this.getUpList()
          }
        }
      })
      // querySysDate().then(res => {
      //   if (res.data.Code === 10000) {
      //     let _year = res.data.systemDate.substring(0, 4)
      //     this.start_default_date = _year + '-01'
      //     this.end_default_date = res.data.systemDate.substring(0, 7)
      //     this.queryParam.start_month = this.queryParam.start_contrast_month = _year + '-01'
      //     this.queryParam.end_month = this.queryParam.end_contrast_month = res.data.systemDate.substring(0, 7)
      //     this.getUpList()
      //   }
      // })
    },
    // 船舶货运量详情展示
    amountDrawerShow () {
      this.amountModal.modal = true
    },
    voyageDrawerShow () {
      this.voyageModal.modal = true
    },
    turnDrawerShow () {
      this.turnModal.modal = true
    },
    resetTableData (arr, data, index, str) {
      let startDate = this.curDate.indexOf('~') > 0 ? this.curDate : '本年度'
      let endDate = this.checkDate.indexOf('~') > 0 ? this.checkDate : '本年度'
      arr.forEach((item, idx) => {
        if (idx === 0) {
          if (index === 0) {
            data.push({
              'valueDate': startDate
            })
          }
          if (index === 1) {
            data.push({
              'valueDate': endDate
            })
          }
          if (this.shipNameList.includes(item.ship_name)) {
            Object.assign(data[index], {
              ['value' + idx]: item[str]
            })
          } else {
            // data.push({
            //   ['value' + idx]: 0
            // })
            Object.assign(data[index], {
              ['value' + idx]: 0
            })
          }
        } else {
          if (this.shipNameList.includes(item.ship_name)) {
            Object.assign(data[index], {
              ['value' + idx]: item[str]
            })
          } else {
            Object.assign(data[index], {
              ['value' + idx]: 0
            })
          }
        }
      })
    },
    // 船舶货运量 & 船舶航次对比数据
    resetShipAmountVoyage (data, backIdx) {
      data.forEach(item => {
        if (!item.ship_name.includes('万华')) { // 剔除万华8
          if (this.lineData1.xAxis.includes(item.ship_name)) {
            let _curIdx = this.lineData1.xAxis.findIndex(list => { return item.ship_name === list.ship_name })
            if (_curIdx > 0 && this.lineData1.xAxis.length > 1) { // 判断是否有数据  无数据补0
              this.lineData1.xAxis.forEach((item, idx) => {
                if (idx < _curIdx) {
                  this.lineData1.data[backIdx][idx] = 0
                  this.lineData2.data[backIdx][idx] = 0
                } else {
                  this.lineData1.data[_curIdx] = item.amount_sum
                  this.lineData2.data[_curIdx] = item.voyage_total
                }
              })
            } else {
              this.lineData1.data[backIdx].push(item.amount_sum)
              this.lineData2.data[backIdx].push(item.voyage_total)
            }
          } else {
            this.lineData1.xAxis.push(item.ship_name)
            this.lineData1.data[backIdx].push(item.amount_sum)

            this.lineData2.xAxis.push(item.ship_name)
            this.lineData2.data[backIdx].push(item.voyage_total)
          }
        }
      })
    },
    // 船舶周转量数据对比
    resetShipTurnOver (data, backIdx) {
      data.forEach(item => {
        if (!item.ship_name.includes('万华')) { // 剔除万华8
          if (this.lineData3.xAxis.includes(item.ship_name)) {
            let _curIdx = this.lineData3.xAxis.findIndex(list => { return item.ship_name === list.ship_name })
            if (_curIdx > 0 && this.lineData3.xAxis.length > 1) { // 判断是否有数据  无数据补0
              this.lineData3.xAxis.forEach((item, idx) => {
                if (idx < _curIdx) {
                  this.lineData3.data[backIdx][idx] = 0
                } else {
                  this.lineData3.data[_curIdx] = item.turnover_volume_sum
                }
              })
            } else {
              this.lineData3.data[backIdx].push(item.turnover_volume_sum)
            }
          } else {
            this.lineData3.xAxis.push(item.ship_name)
            this.lineData3.data[backIdx].push(item.turnover_volume_sum)
          }
        }
      })
    },
    // 开始时间变化触发
    sdateSelect (dateObj) {
      this.queryParam.start_month = dateObj[0] ? dateObj[0] : ''
      this.queryParam.end_month = dateObj[1] ? dateObj[1] : ''
    },
    // 对比时间变化触发
    cdateSelect (dateObj) {
      this.queryParam.start_contrast_month = dateObj[0] ? dateObj[0] : ''
      this.queryParam.end_contrast_month = dateObj[1] ? dateObj[1] : ''
    },
    // 重置拆线数据
    resetLineData () {
      this.lineData1.xAxis = []
      this.lineData1.data = [[], []]

      this.lineData2.xAxis = []
      this.lineData2.data = [[], []]

      this.lineData3.xAxis = []
      this.lineData3.data = [[], []]

      this.columns1 = this.columns2 = this.columns3 = []
      this.data1 = []
      this.data2 = []
      this.data3 = []
    },
    searchDataUp () {
      this.getUpList()
    },
    searchResetUp () {
      // this.queryParam.start_month = this.queryParam.start_contrast_month = this.start_default_date
      // this.queryParam.end_month = this.queryParam.end_contrast_month = this.end_default_date
      this.getSysDate()
    }
  }
}
</script>
<style scoped>
  .box-chart {
    height: 250px;
    margin-top: 20px;
  }
  .morebtn {
    right: 25px;
    margin-top: 20px;
    position: absolute;
    font-size: 12px;
    color: #666;
    cursor: pointer;
    font-family: Arial, Helvetica, sans-serif;
    z-index: 1000;
  }
  .morebtn:hover {
    color: #57a3f3;
  }
</style>
