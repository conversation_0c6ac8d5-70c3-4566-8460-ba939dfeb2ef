<template>
  <Modal
    v-model="modalData.modal" class="port_area" :styles="{position: 'absolute', top: '110px', left: '215px'}" :mask="false"
    :transfer="false" width="400px" footer-hide>
    <p slot="header">
      <!-- <img src="@/assets/images/port_back.png" alt=""/> -->
      <div class="port_title">{{ modalData.name }}</div>
    </p>
    <div>
      <div v-for="(item,idx) in modalData.detail" :key="idx">
        <div class="port_inner_title">{{ item.portTitle }}</div>
        <div class="port_inner_detail" v-html="item.portDetail"></div>
      </div>
    </div>
  </Modal>
</template>
<script>
export default {
  props: {
    modalData: Object
  },
  data () {
    return {
      isTra: false
    }
  }
}
</script>
<style>
  .port_area .ivu-modal-header {
    height: 50px;
    padding: 0 !important;
    border-bottom: none;
  }
  .port_area .ivu-modal {
    top: 50px;
  }
  .port_area .ivu-modal-header p {
    background: #007dff;
    height: 50px;
  }
  .port_area .ivu-modal-header p img {
    width: 100%;
    height: 100%;
    pointer-events: none;
  }
  .port_title {
    position: absolute;
    /* background: #0770e799; */
    padding: 5px 15px 0 5px;
    top: 5px;
    left: 15px;
    color: #fff;
    font-size: 18px;
  }
  .port_inner_title {
    text-align: left;
    color: #027DB4;
    font-weight: bold;
  }
  .port_inner_detail {
    white-space: pre-wrap;
    text-align: left;
    color: #333;
    margin-bottom: 20px;
  }
</style>
