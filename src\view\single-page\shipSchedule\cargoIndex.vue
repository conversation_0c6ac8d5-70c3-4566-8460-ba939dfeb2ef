<template>
  <div>
    <Card>
      <search @searchResults='searchResults' @selectOnChanged='selectOnChanged' :setSearch='setSearchData' @keydown.enter.native='searchResults' @resetResults='resetResults'></search>
    </Card>
    <Card class="cargo-main-card">
      <!-- 待排期列表 -->
      <div class="section-title">
        <h3>待排期列表</h3>
        <Tag color="blue">{{ total }}</Tag>
        <div class="header-btns">
          <Button class="add_btns" type="primary" icon="md-add" @click="addCargo">添加货源</Button>
          <Upload action="" :before-upload="addCargoFile" style="margin-top: 8px;">
            <Button class="add_btns" type="primary" icon="md-cloud-upload">导入货盘</Button>
          </Upload>
          <!-- <Button class="add_btns" type="primary" icon="md-cog" @click="cargoSet">包运管理</Button> -->
        </div>
      </div>

      <Table border :columns="pendingColumns" :data="pendingData" class="mb-20">
        <template slot-scope="{ row }" slot="action">
          <ButtonGroup size="small">
            <Button type="primary" icon="md-create" @click="editCargo(row)">编辑</Button>
            <Button style="margin-left: 10px;" type="error" icon="md-trash" @click="deleteCargo(row)">删除</Button>
          </ButtonGroup>
        </template>
      </Table>
      <Page show-sizer :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="queryParam.pageSize" :current.sync="queryParam.pageIndex"
          :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
    </Card>
    <!-- 添加货源抽屉组件 -->
    <CargoDrawer
      v-model="cargoDrawerVisible"
      :cargo-data="currentCargo"
      :is-edit="isEditingCargo"
      @submit="handleCargoSubmit"
      @cancel="handleCargoCancel"
    />
  </div>
</template>

<script>
import search from '_c/search' // 查询组件
import CargoDrawer from './CargoDrawer.vue'
import API from '@/api/shipSchedule'
import { querySysDate, queryBasicCargoList, queryCustomerList } from '@/api/basicData'

export default ({
  components: {
    search,
    CargoDrawer
  },
  data () {
    return {
      defaultMonth: null,
      total: 0,
      belong_month: '',
      cargoDrawerVisible: false,
      currentCargo: {},
      cargoSetModal: {
        modal: false,
        title: '包运管理',
        data: {}
      },
      isEditingCargo: false,
      pendingColumns: [
        { title: '月份', key: 'belong_month', align: 'center' },
        { title: '船舶',
          key: 'ship_name',
          align: 'center',
          render: (h, params) => {
            return h('div', {}, params.row.ship_name || '-')
          }
        },
        { title: '受载期',
          key: '',
          align: 'center',
          render: (h, params) => {
            let _start_date = this.monthDay(params.row.start_plan_date)
            let _end_date = this.monthDay(params.row.end_plan_date)
            let limit_date = _start_date + ' - ' + _end_date
            return h('div', {}, limit_date)
          } },
        { title: '航次',
          key: 'voyage_no',
          align: 'center',
          render: (h, params) => {
            return h('div', {}, params.row.voyage_no || '-')
          }
        },
        { title: '航线',
          key: '',
          align: 'center',
          render: (h, params) => {
            let _load_port_name = params.row.load_port_name || '-'
            let _unload_port_name = params.row.unload_port_name
            let _line = _load_port_name + ' - ' + _unload_port_name
            return h('div', {}, _line)
          }
        },
        { title: '航次期间',
          key: '',
          align: 'center',
          render: (h, params) => {
            let _start_date = this.monthDay(params.row.empty_sail_start_day)
            let _end_date = this.monthDay(params.row.estimated_over_day)
            let limit_date = _start_date + ' - ' + _end_date
            return h('div', {}, limit_date)
          } },
        { title: '货品', key: 'goods_name', align: 'center', sortable: true },
        { title: '货量(吨)',
          key: 'amounts',
          width: 120,
          align: 'center',
          sortable: true,
          sortMethod: (a, b, type) => {
            if (type === 'asc') {
              return parseInt(a) > parseInt(b) ? -1 : 1
            } else {
              return parseInt(a) > parseInt(b) ? 1 : -1
            }
          } },
        { title: '运价(元/吨)',
          key: 'freight_rate',
          align: 'center',
          render: (h, params) => {
            return h('div', {}, params.row.freight_rate || '-')
          }
        },
        { title: '运费(元)',
          key: 'shipping_fee',
          align: 'center',
          render: (h, params) => {
            return h('div', {}, params.row.shipping_fee || '-')
          }
        },
        { title: '货主',
          key: 'shipper_name',
          align: 'center',
          render: (h, params) => {
            return h('div', {}, params.row.shipper_name || '-')
          }
        },
        { title: '状态',
          key: 'status',
          align: 'center',
          render: (h, params) => {
            // 状态（0未分配，1人工指定，2系统分配）
            // 人工指定状态为蓝色，系统分配状态为绿色，未分配状态为灰色
            let statusStr = ''
            let statusColor = ''
            if (params.row.status === '0') {
              statusStr = '未分配'
              statusColor = 'gray'
            }
            if (params.row.status === '1') {
              statusStr = '人工指定'
              statusColor = 'blue'
            }
            if (params.row.status === '2') {
              statusStr = '系统分配'
              statusColor = 'green'
            }
            return h('Tag', {
              props: {
                color: statusColor
              }
            }, statusStr)
          }
        }, { title: '操作', slot: 'action', width: 180, align: 'center' }
      ],
      portNameList: [],
      pendingData: [],
      setSearchData: {
        ship_id_real: {
          type: 'select',
          label: '船舶',
          filterable: true,
          selectData: [],
          selected: '',
          placeholder: '请选择船舶',
          flag: 'ship_id',
          selectName: '',
          width: 130,
          value: ''
        },
        belong_month: {
          type: 'month',
          label: '归属月份',
          selected: '',
          width: 130,
          value: '',
          isdisable: false
        },
        // load_port_id: {
        //   type: 'select',
        //   label: '装货港',
        //   filterable: true,
        //   selectData: [],
        //   selected: '',
        //   placeholder: '请选择装货港口',
        //   flag: 'load_port',
        //   selectName: '',
        //   width: 130,
        //   value: ''
        // },
        // unload_port_id: {
        //   type: 'select',
        //   label: '卸货港',
        //   filterable: true,
        //   selectData: [],
        //   selected: '',
        //   placeholder: '请选择卸货港口',
        //   flag: 'unload_port',
        //   selectName: '',
        //   width: 130,
        //   value: ''
        // },
        shipper: {
          type: 'select',
          label: '货主',
          filterable: true,
          selectData: [],
          selected: '',
          placeholder: '请选择货主',
          flag: 'shipper',
          selectName: '',
          width: 130,
          value: ''
        },
        goods_id: {
          type: 'select',
          label: '货品',
          filterable: true,
          selectData: [],
          selected: '',
          placeholder: '请选择货品',
          flag: 'goods_id',
          selectName: '',
          width: 130,
          value: ''
        },
        source: {
          type: 'select',
          label: '来源',
          filterable: true,
          selectData: [
            { value: 0, label: '人工录入' },
            { value: 1, label: '包运拆分' }
          ],
          selected: '',
          placeholder: '请选择货盘来源',
          flag: 'status',
          selectName: '',
          width: 130,
          value: ''
        },
        status: {
          type: 'select',
          label: '状态',
          filterable: true,
          selectData: [
            { value: 0, label: '未分配' },
            { value: 1, label: '人工指定' },
            { value: 2, label: '系统分配' }
          ],
          selected: '',
          placeholder: '请选择货盘状态',
          flag: 'status',
          selectName: '',
          width: 130,
          value: ''
        }
      },
      queryParam: {
        pageSize: 10,
        pageIndex: 1
      }
    }
  },
  created () {
    this.getSysDate()
    this.getBaseData()
  },
  methods: {
    getGoodsList () {
      // 获取货源信息
      API.queryVoyageMonthPlanPage(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          this.pendingData = res.data.Result
          this.total = res.data.Total
        }
      })
    },
    // 获取基础信息
    getBaseData () {
      let shipList = JSON.parse(localStorage.getItem('shipNameList')).filter(list => list.business_model === '1').filter(t => !t.ship_name.includes('善')) || []
      this.setSearchData.ship_id_real.selectData = shipList.map(item => {
        return {
          label: item.ship_name,
          value: item.ship_id
        }
      })
      // 获取港口
      // queryPortList(this.loadPortListQuery).then(res => {
      //   if (res.data.Code === 10000) {
      //     this.portNameList = res.data.Result.map(item => {
      //       return {
      //         label: item.port_name,
      //         value: item.id
      //       }
      //     })
      //     this.setSearchData.load_port_id.selectData = this.portNameList
      //     this.setSearchData.unload_port_id.selectData = this.portNameList
      //   }
      // })
      // 获取货物
      queryBasicCargoList({ cargo_name: '' }).then(res => {
        if (res.data.Code === 10000) {
          let goodsList = res.data.Result
          this.setSearchData.goods_id.selectData = goodsList.map(item => {
            return {
              label: item.cargo_name,
              value: item.id
            }
          })
        }
      })
      // 获取代理公司 公司类型（1、船东；2、货主；3、代理）
      queryCustomerList({ company_type: 2 }).then(res => {
        let agentList = res.data.Result
        this.setSearchData.shipper.selectData = agentList.map(item => {
          return {
            label: item.customer_company_name,
            value: item.customer_company_id
          }
        })
      })
    },
    monthDay (dateStr) {
      if (!dateStr) return ''
      const [, month, day] = dateStr.split('-')
      return `${parseInt(month)}/${parseInt(day)}`
    },
    handleCargoSubmit () {
      this.getGoodsList()
    },
    handleCargoCancel () {
      this.$Message.info('已取消操作')
    },
    getSysDate () {
      querySysDate().then(res => {
        if (res.data.Code === 10000) {
          let sysDate = res.data.systemDate.split(' ')[0]
          this.belong_month = this.defaultMonth = this.formatMonth(new Date(sysDate))
          this.setSearchData.belong_month.selected = this.defaultMonth
          Object.assign(this.queryParam, {
            belong_month: this.defaultMonth
          })
          this.getGoodsList()
        }
      })
    },
    // 格式化日期为 YYYY-MM
    formatMonth (date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      return `${year}-${month}`
    },
    // 包运配置
    cargoSet () {
      this.cargoSetModal.modal = true
    },
    // 添加货源
    addCargo () {
      this.isEditingCargo = false
      this.currentCargo = {}
      this.cargoDrawerVisible = true
    },
    addCargoFile (file) {
      let formDataList = []
      formDataList.push(file)
      let formData = new FormData()
      let through = false // 判断是否需要上传文件
      formDataList = formDataList.filter(item => {
        if (item.type !== undefined) {
          through = true
          formData.append('file', item)
        }
        return item.type === undefined
      })
      if (through) {
        API.importMonthPlan(formData).then(res => {
          if (res.data.Code === 10000) {
            this.$Message.success(res.data.Message)
            this.getGoodsList()
          } else {
            this.$Modal.error({
              title: '导入出错',
              content: res.data.Message
            })
          }
        })
      }
    },
    editCargo (cargo) {
      this.isEditingCargo = true
      this.currentCargo = { ...cargo }
      this.cargoDrawerVisible = true
    },
    deleteCargo (cargo) {
      this.$Modal.confirm({
        title: '确认删除',
        content: `确定要删除 ${cargo.goods_name} 为 ${cargo.amounts}吨 货源吗？`,
        onOk: () => {
          API.delVoyageMonthPlan({ voyage_month_plan_id: cargo.voyage_month_plan_id }).then(res => {
            if (res.data.Code === 10000) {
              this.$Message.success(res.data.Message)
              this.getGoodsList()
            } else {
              this.$Message.error(res.data.Message)
            }
          }
          )
        }
      })
    },
    // 查询
    searchResults (e) {
      if (e.target) delete e.target
      this.queryParam.pageIndex = 1
      Object.assign(this.queryParam, e)
      this.queryParam.belong_month = this.belong_month
      this.getGoodsList()
    },
    selectOnChanged (e) {
      if (e.flag === 'month_start') {
        this.belong_month = e.key
      }
    },
    // 重置查询条件
    resetResults () {
      this.setSearchData.belong_month.selected = ''
      // this.setSearchData.load_port_id.selected = ''
      // this.setSearchData.unload_port_id.selected = ''
      this.setSearchData.source.selected = ''
      this.setSearchData.status.selected = ''
      this.setSearchData.ship_id_real.selected = ''
      this.setSearchData.shipper.selected = ''
      this.setSearchData.goods_id.selected = ''
      this.belong_month = this.defaultMonth
      this.setSearchData.belong_month.selected = this.defaultMonth
      this.queryParam = { // 列表请求参数
        belong_month: this.defaultMonth,
        pageSize: 10,
        pageIndex: 1
      }
      this.getGoodsList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.queryParam.pageSize = val
      this.getGoodsList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.queryParam.pageIndex = val
      this.getGoodsList()
    }
  }
})
</script>
<style>
.cargo-main-card {
  margin-top: 20px;
  margin-bottom: 20px;
}
.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  font-weight: bold;
  font-size: 16px;
}

.section-title .ivu-icon {
  margin-left: 5px;
  color: #2d8cf0;
}
.section-title h3 {
  margin-left: 10px;
  margin-right: 10px;
}
.header-btns {
  display: flex;
  align-items: center;
}
.header-btns button {
  margin-left: 10px;
}
.add_btns .ivu-icon {
  color: #fff;
}
.mb-20 {
  margin-bottom: 20px;
}
</style>
