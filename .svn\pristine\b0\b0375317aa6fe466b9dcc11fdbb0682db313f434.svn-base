<template>
  <div style="padding: 20px; background: #fff;">
    <search @searchResults='searchResults' :setSearch='setSearchData' @keydown.enter.native='searchResults' @resetResults='resetResults'></search>
    <Row class="ctrl-plan-list">
        <Col span="4" v-for="(item, idx) in list" :key="idx" class="ctrl-plan-list-box" @click="handleDetail(item)">
          <Tooltip transfer placement="right" max-width="200">
            <span class="box-title">{{ item.voyage_date }} {{ item.voyage_date_week }}</span>
            <div class="box-voyage">
              <span class="box-voyage-name">
                <Icon type="md-boat" color="#fff" />
                {{ item.ship_name }}
              </span>
              <span class="box-voyage-no">{{ item.voyage_no }}</span>
            </div>
            <span class="box-voyage" v-for="(item, idx) in getGoods(item.cargoResult)" :key="idx">
              <span class="box-voyage-goods">
                <Icon type="md-cube" color="#fff" />
                {{ item }}
              </span>
            </span>
              <div slot="content" class="tip-content">
                <p class="tip-content-name">{{ item.ship_name }} {{ item.voyage_no }}</p>
                <p class="tip-content-title">货品 货量<p>
                <div v-for="(list, idx) in getGoods(item.cargoResult)" :key="idx">
                  <p> {{ list }}</p>
                </div>
                <div>
                  <p class="tip-content-title">装港:</p>
                  <p> {{ getPort(item.portResult, '1') }}</p>
                </div>
                <div>
                  <p class="tip-content-title">卸港:</p>
                  <p> {{ getPort(item.portResult, '2') }}</p>
                </div>
              </div>
            </Tooltip>
        </Col>
    </Row>
    <Spin fix v-if="spinShow">
      <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
      <div>Loading</div>
    </Spin>
  </div>
</template>
<script>
import search from '_c/search' // 查询组件
import API from '@/api/control'
export default {
  components: {
    search
  },
  data () {
    return {
      spinShow: false, // loading
      pageCur: 1, // 当前页
      pageSize: 1000, // 每页个数
      total: 0, // 总页数
      queryParam: {
        pageSize: 1000,
        pageIndex: 1
      },
      setSearchData: {// 查询设置，对象key值为回调参数
        ship_id: {
          type: 'select',
          label: '船名',
          selectData: [],
          selected: '',
          placeholder: '请选择船名',
          flag: 'ship_id',
          selectName: '',
          width: 210,
          value: ''
        }
      },
      list: []
    }
  },
  computed: {
    getGoods () { // 货品,货量解析
      return function (list) {
        if (!list || list.length <= 0) return ''
        // return list[0].goods_name + ' - ' + list[0].amounts
        let curList = list.map(item => {
          return item.goods_name + ' ' + item.amounts + '吨'
        })
        return curList
      }
    },
    getPort () { // 港口,码头解析
      return function (list, type) {
        if (!list || list.length <= 0) return ''
        // 按类型提取需要的港头数据列表,type = 1为装港, type = 2为卸港
        let curList = list.filter(item => item.port_type === type)
        let portList = curList.map(d => {
          return d.wharf_name // === '' ? d.port_name : d.port_name + '/' + d.wharf_name
        })
        return portList.join(' ')
      }
    }
  },
  created () {
    if (window.localStorage.shipNameList) {
      let shipList = JSON.parse(window.localStorage.shipNameList)
      shipList.map(item => {
        if ((item.business_model === '1' || item.business_model === '2') && !item.ship_name.includes('善')) { // 只要自营和船期船舶且不需要万邦船舶  2024/09/09调整
          this.setSearchData.ship_id.selectData.push({
            value: item.ship_id,
            label: item.ship_name
          })
        }
      })
    } else {
      this.setSearchData.ship_id.selectData = []
    }
    this.getList()
  },
  methods: {
    // 获取列表
    getList () {
      this.spinShow = true
      API.queryVoyagePlanConsolePage(this.queryParam).then(res => {
        this.spinShow = false
        if (res.data.Code === 10000) {
          this.list = res.data.Result
          this.total = res.data.Total
        }
      })
    },
    // 航次详情
    handleDetail (row) {
      localStorage.setItem('detailType', 'planVoyage')
      localStorage.setItem('voyageObj', JSON.stringify(row))
      this.$router.push({
        name: 'voyageDetail'
      })
    },
    // 查询
    searchResults (e) {
      if (e.target) delete e.target
      this.pageCur = 1
      this.queryParam.pageIndex = 1
      Object.assign(this.queryParam, e)
      this.getList()
    },
    // 重置
    resetResults () {
      this.pageCur = 1
      this.queryParam = Object.assign(this.queryParam, { // 列表请求参数
        pageIndex: 1,
        ship_id: ''
      })
      this.setSearchData.ship_id.selected = ''
      this.getList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.queryParam.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.queryParam.pageIndex = val
      this.getList()
    }
  }
}
</script>

<style lang="less">
  .ctrl-plan-list {
    margin: 30px 0;
    &-box {
      width: 160px;
      height: 110px;
      background: #198AFF;
      border-radius: 6px;
      opacity: 0.9;
      margin-left: 40px;
      margin-bottom: 60px;
      cursor: pointer;
      .box-title {
        display: inline-block;
        width: 160px;
        height: 37px;
        line-height: 37px;
        background: #167BE5;
        text-align: center;
        color: #fff;
        font-size: 16px;
        font-weight: 500;
        border-top-left-radius: 6px;
        border-top-right-radius: 6px;
      }
      .box-voyage {
        padding: 13px 10px 10px 13px;
        color: #fff;
        font-size: 14px;
        font-weight: 500;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        &-no {
          margin-left: 5px;
        }
        &-goods {
          display: inline-block;
          height: 30px;
        }
      }
    }
  }
  .tip-content {
    &-name {
      height: 40px;
      line-height: 40px;
      text-align: center;
      font-size: 16px;
      font-weight: 600;
    }
    &-title {
      height: 30px;
      line-height: 30px;
      font-size: 14px;
      font-weight: 600;
    }
  }
</style>
