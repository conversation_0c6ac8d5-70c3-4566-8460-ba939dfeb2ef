<template>
  <div>
    <!-- 营运分析各船损耗率详情页 -->
    <Card>
      <div class="btn-area">
        <span>时间：</span>
        <DatePicker type="month" style="width: 120px" :value="queryParam.base_month" @on-change="(val) =>{changeContrastDate(val, 'base_month')}"></DatePicker>
        <span> &nbsp; 对比时间：</span>
        <DatePicker type="month" style="width: 120px" :value="queryParam.contrast_month" @on-change="(val) =>{changeContrastDate(val, 'contrast_month')}"></DatePicker>
        <Button size="small" type="primary" @click="searchResults" style="padding: 3px 7px">查询</Button>
        <Button size="small" type="primary" @click="resetData" style="padding: 3px 7px">重置</Button>
        <Button size="small" type="primary" icon="ios-arrow-back" @click="$emit('callback')" style="float: right;">返回</Button>
      </div>
      <Row>
        <Col span="13">
          <div class="line-area">
            <!-- <ChartLine class="box-chart" :legendShow="true" :value="lineData1" xAxisUnit="月" unit="吨" :color="lineColor" :showOnemarkLine="true" :gridRight="100" :text="`${ship_name}货运量`" /> -->

            <chart-line style="height: 300px;" unit="损耗率(‰)" :value="contractLossData" :formatter="true" :color="lineColor" :rotate="rotate" text="合同损耗"/>
          </div>
          <div class="line-area">
            <chart-line style="height: 300px;" unit="损耗率(‰)" :value="turnoverNumData" :formatter="true" :color="lineColor" :rotate="rotate" text="船板损耗"/>
          </div>
        </Col>
        <Col span="11">
          <Table :data="listData" border :loading="loading" :columns="columns" show-summary :summary-method="handleSummary" :height="tableHeight" class="list_table"></Table> <!--   -->
        </Col>
      </Row>
      <Row class="loss_list">
        <Table border :columns="lossListcolumns" :data="lossListData"></Table>
      </Row>
    </Card>
    <Spin size="large" fix v-if="spinShow">
      <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
      <div>Loading</div>
    </Spin>
  </div>
</template>
<script>
import { ChartLine } from '_c/charts'
import { querySysDate } from '@/api/basicData'
import { queryShipsGoodsLossDetail } from '@/api/statistics/operationAnalysis'

export default {
  props: {
    modalData: Object
  },
  components: {
    ChartLine
  },
  data () {
    return {
      tableHeight: '',
      spinShow: false,
      rotate: '45',
      queryParam: {
        ship_id: '',
        base_month: '',
        contrast_month: ''
      },
      contractLossData: {
        xAxis: [],
        legend: ['时间', '对比时间'],
        data: [[], []],
        symbol: ['emptyCircle', 'circle']
      },
      turnoverNumData: {
        xAxis: [],
        legend: ['时间', '对比时间'],
        data: [[], []],
        symbol: ['emptyCircle', 'circle']
      },
      loading: false,
      listData: [],
      lossListData: [],
      lossListcolumns: [],
      columns: [
        {
          title: '船舶',
          key: 'ship_name',
          align: 'center'
        },
        {
          title: '',
          align: 'center',
          children: [
            {
              title: '板对板(‰)',
              key: 'ship_loss_rate_avg',
              align: 'center'
            },
            {
              title: '合同约定(‰)',
              key: 'contract_loss_rate_avg',
              align: 'center'
            },
            {
              title: '奖励对照',
              key: '',
              width: 70,
              align: 'center',
              render: (h, params) => { // （第一档）＜0.15‰ A 深绿  （第二档）0.15‰-0.35‰ B 浅绿 （第三档）0.36‰-0.5‰ C 黄
                let rateLevel = '-'
                let reteLevelNum = parseFloat(params.row.contract_loss_rate_avg)
                if (reteLevelNum > 0 && reteLevelNum < 0.15) rateLevel = 'A'
                if (reteLevelNum >= 0.15 && reteLevelNum <= 0.35) rateLevel = 'B'
                if (reteLevelNum > 0.35 && reteLevelNum <= 0.5) rateLevel = 'C'
                return h('div', {}, rateLevel)
              }
            }
          ]
        }
      ],
      contract_sum_avg: '', // 合同损耗平均值
      ship_sum_avg: '',
      lineColor: ['#6699FF', '#E74823']
    }
  },
  created () {
    this.getCurDate()
  },
  methods: {
    getList () {
      this.clearData()
      this.loading = true
      this.spinShow = true
      this.contractLossData.legend = [this.queryParam.base_month, this.queryParam.contrast_month]
      this.turnoverNumData.legend = [this.queryParam.base_month, this.queryParam.contrast_month]
      queryShipsGoodsLossDetail(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          this.loading = false
          this.spinShow = false
          this.contract_sum_avg = res.data.contract_sum_avg
          this.ship_sum_avg = res.data.ship_sum_avg
          this.listData = res.data.shipsGoodsLossRateArray.filter(item => { return (item.ship_name.indexOf('万华') < 0 && item.ship_loss_rate_avg !== '0') })
          this.tableHeight = res.data.shipsGoodsLossRateArray.length > 10 ? '560' : ''
          this.lossListcolumns.push({
            title: '船名',
            key: 'title',
            width: 155,
            align: 'center',
            fixed: 'left'
          })
          res.data.shipsGoodsLossRateArray.forEach((item, idx) => {
            if (!item.ship_name.includes('万华') && item.ship_loss_rate_avg !== '0') { // 剔除万华8 剔除数据为0船舶
              this.contractLossData.xAxis.push(item.ship_name)
              this.turnoverNumData.xAxis.push(item.ship_name)
              this.contractLossData.data[0].push(item.contract_loss_rate_avg) // 合同损耗
              this.turnoverNumData.data[0].push(item.ship_loss_rate_avg) // 船板损耗
              this.lossListcolumns.push({
                title: item.ship_name,
                key: 'value' + idx,
                // width: res.data.shipsGoodsLossRateArray.length > 7 ? 90 : '',
                align: 'center'
              })
            }
          })
          res.data.lastYearLossArray.forEach(item => {
            this.contractLossData.data[1].push(item.contract_loss_rate_avg)
            this.turnoverNumData.data[1].push(item.ship_loss_rate_avg)
          })
          
          // this.lossListData = res.data.shipsGoodsLossRateArray
          this.resetTableData(res.data.shipsGoodsLossRateArray, this.lossListData, 0, 'voyage_num')
          this.resetTableData(res.data.shipsGoodsLossRateArray, this.lossListData, 1, 'contract_loss_rate_avg')
          this.resetTableData(res.data.shipsGoodsLossRateArray, this.lossListData, 2, 'ship_loss_rate_avg')
          this.resetTableData(res.data.shipsGoodsLossRateArray, this.lossListData, 3, 'contract_loss_max')
          this.resetTableData(res.data.shipsGoodsLossRateArray, this.lossListData, 4, 'contract_loss_min')
          this.resetTableData(res.data.shipsGoodsLossRateArray, this.lossListData, 5, '')
        } else {
          this.loading = false
          this.$Message.error(res.data.Message)
        }
      })
    },
    resetTableData (arr, data, index, str) {
      switch (index) {
        case 0:
          data.push({
            title: '航次数',
            voyage_num: data[str]
          })
          break
        case 1:
          data.push({
            title: '平均合同损耗率(‰)'
          })
          break
        case 2:
          data.push({
            title: '平均船板损耗率(‰)'
          })
          break
        case 3:
          data.push({
            title: '最大损耗率(‰)'
          })
          break
        case 4:
          data.push({
            title: '最小损耗率(‰)'
          })
          break
        case 5:
          data.push({
            title: '奖励对照'
          })
          break
        default:
          break
      }
      arr.forEach((item, idx) => {
        if (str === '') { // 奖励对照 特殊处理
          let rateLevel = '-'
          let reteLevelNum = parseFloat(item.contract_loss_rate_avg)
          if (reteLevelNum > 0 && reteLevelNum < 0.15) rateLevel = 'A'
          if (reteLevelNum >= 0.15 && reteLevelNum <= 0.35) rateLevel = 'B'
          if (reteLevelNum > 0.35 && reteLevelNum <= 0.5) rateLevel = 'C'
          Object.assign(data[index], {
            ['value' + idx]: rateLevel
          })
        } else {
          Object.assign(data[index], {
            ['value' + idx]: item[str]
          })
        }
      })
    },
    // 数据清空
    clearData () {
      this.lossListData = []
      this.lossListcolumns = []
      this.contractLossData.xAxis = []
      this.turnoverNumData.xAxis = []
      this.contractLossData.data = [[], []]
      this.turnoverNumData.data = [[], []]
    },
    // 重置
    resetData () {
      this.clearData()
      this.getCurDate()
    },
    // 选择时间
    changeContrastDate (val, d) {
      if (d === 'base_month') {
        this.queryParam.base_month = val
      } else if (d === 'contrast_month') {
        this.queryParam.contrast_month = val
      }
    },
    // 查询
    searchResults () {
      this.getList()
      let curMonth = this.queryParam.base_month.substring(5, 7) > 9 ? this.queryParam.base_month.substring(5, 7) : this.queryParam.base_month.substring(6, 7)
      this.columns[1].title = curMonth + '月份航次平均损耗'
    },
    // 系统年月
    getCurDate () {
      querySysDate().then(res => {
        if (res.data.Code === 10000 && res.data.systemDate !== '') {
          let _year = res.data.systemDate.substring(0, 4)
          this.queryParam.base_month = res.data.systemDate.substring(0, 7)
          this.queryParam.contrast_month = _year - 1 + res.data.systemDate.substring(4, 7)
          let curMonth = res.data.systemDate.substring(5, 7) > 9 ? res.data.systemDate.substring(5, 7) : res.data.systemDate.substring(6, 7)
          this.columns[1].title = curMonth + '月份航次平均损耗'
          this.getList()
        }
      })
    },
    handleSummary ({ columns, data }) {
      const curAvg = {}
      columns.forEach((column, index) => {
        const key = column.key
        if (index === 0) {
          curAvg[key] = {
            value: '平均值'
          }
        } else if (index === 1) {
          curAvg[key] = {
            key,
            value: this.ship_sum_avg
          }
        } else if (index === 2) {
          curAvg[key] = {
            key,
            value: this.contract_sum_avg
          }
        } else {
          curAvg[key] = {
            key,
            value: ''
          }
        }
      })
      return curAvg
    }
  }
}
</script>

<style lang="less" scoped>
.btn-area {
  margin-bottom: 10px;
  button {
    margin-left: 12px;
  }
}
.line-area {
  margin-top: 40px;
}
.list_table {
  margin-top: 50px;
}
</style>
<style>
.list_table .ivu-table table.ivu-table-summary td {
  background: #f8f8f9;
}
</style>
