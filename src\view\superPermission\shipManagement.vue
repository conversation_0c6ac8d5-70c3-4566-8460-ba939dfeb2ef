<template>
  <div>
    <Drawer
      v-model="shipModal"
      :data="modalData"
      title="船舶管理"
      :width="800"
      :mask-closable="false"
      @on-visible-change="visibleChange">
      <Card>
        <search @searchResults='searchResults' :setSearch='setSearchData' @keydown.enter.native='searchResults' @resetResults='resetResults' class="searchdiv"></search>
        <Button type="primary" @click="handleCreate">添加船舶</Button>
      </Card>
      <Card style="margin-top:10px;padding-top:6px;">
        <Table border :loading="shipLoading" ref="selection" :columns="shipColumns" :data="shipList"></Table>
        <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
          :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
      </Card>
      <Modal
        v-model="addShipModal"
        title="添加船舶"
        @on-cancel="clearData"
        dialogType="create"
        width="800">
        <Form ref="formInline" :model="formInline" :label-width="65" class="formdiv">
          <div v-for="(item, index) in formInline.detailJson" :key="index">
            <Row>
              <Col span="6">
                <FormItem label="船舶" :prop="'detailJson.' + index + '.ship_id'" :rules="{required: true, message: '此处不能为空', trigger: 'change'}">
                  <Select v-model="item.ship_id" filterable @on-open-change="shipIdOpen(index)" @on-change="shipIdSelected">
                    <Option v-for="(item1, idx) in shipListData" :key="idx" :value="item1.ship_id" v-show="!shipIdList.includes(item1.ship_id)">{{ item1.ship_name }}</Option>
                  </Select>
                </FormItem>
              </Col>
              <Col span="6">
                <FormItem label="商务" :prop="'detailJson.' + index + '.business_id'" :rules="{required: true, message: '此处不能为空', trigger: 'change'}">
                  <Select v-model="item.business_id" filterable>
                    <Option v-for="(item1, idx) in businessAffairsData" :key="idx" :value="item1.business_id">{{ item1.business_name }}</Option>
                  </Select>
                </FormItem>
              </Col>
              <Col span="10">
                <FormItem label="船端用户" :label-width="90" :prop="'detailJson.' + index + '.ship_user_id'" :rules="{required: true, message: '此处不能为空', trigger: 'change', type: 'array'}">
                  <Select v-model="item.ship_user_id" multiple filterable @on-open-change="shipUserOpen(index)" @on-change="shipUserSelected(index, item)">
                    <Option v-for="(item2, idx) in shipUserData" :key="idx" :value="item2.ship_user_id" v-show="!shipUserList.includes(item2.ship_user_id)">{{ item2.ship_user_name }}</Option>
                  </Select>
                </FormItem>
              </Col>
              <Col span="2">
                <Button type="text" @click="handleRemove(index)" icon="md-remove-circle" class="formbtn"></Button>
              </Col>
            </Row>
          </div>
          <Button type="text" @click="handleAdd()" icon="md-add-circle" class="formbtn addbtn"></Button>
        </Form>
        <div slot="footer">
          <Button @click="clearData" style="margin-right:10px;">取消</Button>
          <Button type="primary" @click="saveShipBtn">保存</Button>
        </div>
      </Modal>
      <Modal
        v-model="updateShipModal"
        title="船舶修改"
        dialogType="update"
        @on-cancel="clearUpdateData">
        <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="90">
          <FormItem label="船舶" prop="ship_id">
            <Select v-model="formValidate.ship_id" filterable>
              <Option v-for="(item1, idx) in shipListData" :key="idx" :value="item1.ship_id">{{ item1.ship_name }}</Option>
            </Select>
          </FormItem>
          <FormItem label="商务" prop="business_id">
            <Select v-model="formValidate.business_id" filterable>
              <Option v-for="(item1, idx) in businessAffairsData" :key="idx" :value="item1.business_id">{{ item1.business_name }}</Option>
            </Select>
          </FormItem>
          <FormItem label="船端用户" prop="ship_user_id">
            <Select v-model="formValidate.ship_user_id" multiple filterable>
              <Option v-for="(item2, idx) in shipUserData" :key="idx" :value="item2.ship_user_id">{{ item2.ship_user_name }}</Option>
            </Select>
          </FormItem>
        </Form>
        <div slot="footer">
          <Button @click="clearUpdateData" style="margin-right:10px;">取消</Button>
          <Button type="primary" @click="updateData">保存</Button>
        </div>
      </Modal>
    </Drawer>
  </div>
</template>

<script>
import search from '_c/search' // 查询组件
import API from '@/api/superPermission/shipManagement'

export default {
  components: {
    search
  },
  data () {
    return {
      shipModal: false,
      modalData: {},
      curShipId: '', // 当前船舶id
      curShipIndex: '', //
      curShipUser: [], // 当前船端用户
      shipIdList: [],
      shipUserList: [],
      setSearchData: {// 查询设置，对象key值为回调参数
        ship_name: {
          type: 'text',
          label: '船名',
          value: '',
          width: 150
        },
        business_name: {
          type: 'text',
          label: '商务',
          value: '',
          width: 150
        }
      },
      shipLoading: false, // 表单列表loding状态
      shipColumns: [
        {
          title: '船舶',
          key: 'ship_name',
          align: 'center'
        },
        {
          title: '商务',
          key: 'business_name',
          align: 'center'
        },
        {
          title: '船端用户',
          key: 'ship_user_info',
          align: 'center'
        },
        {
          title: '操作',
          key: 'operation',
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                style: {
                  margin: '4px'
                },
                props: {
                  icon: 'md-brush',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleUpdate(params.row)
                  }
                }
              }, '修改'),
              h('Button', {
                style: {
                  margin: '4px'
                },
                props: {
                  icon: 'md-trash',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleDelete(params.row)
                  }
                }
              }, '删除')
            ])
          }
        }
      ],
      shipList: [], // 列表数据
      total: null, // 列表数据条数
      listQuery: {// 列表请求参数
        pageSize: 10,
        pageIndex: 1,
        ship_company_id: '',
        ship_id: '',
        ship_name: '',
        business_id: '',
        business_name: ''
      },
      listCurrent: 1, // 当前页码
      addShipModal: false, // 添加模态框默认状态
      formInline: {
        ship_company_config_auth_id: '',
        ship_company_id: '',
        detailJson: [{
          ship_id: '',
          business_id: '',
          ship_user_id: []
        }]
      },
      ship_user: [], // 已选中的船端用户
      shipListData: [], // 存储船舶数据
      businessAffairsData: [], // 存储商务数据
      shipUserData: [], // 存储船端用户数据
      resetShipUserData: [], // 存储剔除后的船端用户数据
      updateShipModal: false, // 船舶修改模态框默认状态
      dialogType: null,
      formValidate: {
        ship_id: '',
        business_id: '',
        ship_user_id: [],
        ship_user_ids: '',
        ship_config_ids: ''
      },
      ruleValidate: {
        ship_id: [{ required: true, message: '此处不能为空', trigger: 'change' }],
        business_id: [{ required: true, message: '此处不能为空', trigger: 'change' }],
        ship_user_id: [{ required: true, message: '此处不能为空', trigger: 'change', type: 'array' }]
      }
    }
  },
  methods: {
    // 开启模态框
    visibleChange (d) {
      if (d === true) {
        this.getShipList()
      }
    },
    // 查询
    searchResults (e) {
      if (e.target) delete e.target
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      Object.assign(this.listQuery, e)
      this.getShipList()
    },
    // 重置
    resetResults () {
      this.listQuery.pageIndex = 1
      this.setSearchData.ship_name.value = ''
      this.setSearchData.business_name.value = ''
      this.listQuery.ship_company_id = ''
      this.listQuery.ship_id = ''
      this.listQuery.ship_name = ''
      this.listQuery.business_id = ''
      this.listQuery.business_name = ''
      this.listCurrent = 1
      this.getShipList()
    },
    // 获取船舶管理列表
    getShipList () {
      this.shipLoading = true
      this.listQuery.ship_company_id = this.modalData.ship_company_id
      API.shipConfigPage(this.listQuery).then(response => {
        if (response.data.Code === 10000) {
          this.shipList = response.data.Result
          this.total = response.data.Total
        } else {
          this.$Message.error(response.data.Message)
        }
        setTimeout(() => {
          this.shipLoading = false
        }, 1 * 800)
      }).catch(
        setTimeout(() => {
          this.shipLoading = false
        }, 1 * 800)
      )
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getShipList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getShipList()
    },
    // 添加船舶
    handleCreate () {
      this.dialogType = 'create'
      this.addShipModal = true
      this.getAllshipList()
      this.getSpUserList()
    },
    // 获取所有船舶（下拉不带分页）
    getAllshipList (row) {
      let data = {
        ship_id: '',
        ship_name: '',
        exclude_ship_config_flag: '1',
        include_ship_id: this.dialogType === 'create' ? '' : row.ship_id
      }
      API.shipListByCondition(data).then(response => {
        if (response.data.Code === 10000) {
          response.data.Result.map(item => {
            this.shipListData.push({
              ship_id: item.ship_id,
              ship_name: item.ship_name
            })
          })
        } else {
          this.$Message.error(response.data.Message)
        }
      })
    },
    // 获取商务/船端用户
    getSpUserList (row) {
      let shiperUserData = {
        ship_company_id: this.modalData.ship_company_id,
        ship_user_name: '',
        ship_user_id: '',
        mobile: '',
        shore_auth: '',
        ship_auth: '0',
        include_ship_user_ids: this.dialogType === 'create' ? [] : row.ship_user_ids
      }
      API.spComConfigUserList({ ship_company_id: this.modalData.ship_company_id, shore_auth: '1' }).then(response => { // 获取商务
        if (response.data.Code === 10000) {
          response.data.Result.map(item => {
            this.businessAffairsData.push({
              business_id: item.ship_user_id,
              business_name: item.ship_user_name
            })
          })
        }
      })
      API.spComConfigUserList(shiperUserData).then(response => { // 获取船端
        if (response.data.Code === 10000) {
          response.data.Result.map(item => {
            this.shipUserData.push({
              ship_user_id: item.ship_user_id,
              ship_user_name: item.ship_user_name
            })
          })
        }
      })
    },
    // 保存船舶添加
    saveShipBtn () {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          this.$Modal.confirm({
            title: '提示',
            content: '<p>是否新增船舶信息？</p>',
            loading: true,
            onOk: () => {
              this.formInline.detailJson.map(item => {
                Object.assign(item, { ship_user_ids: item.ship_user_id.join() })
                // delete item.ship_user_id
              })
              let data = {
                ship_company_config_auth_id: this.modalData.ship_company_config_auth_id,
                ship_company_id: this.modalData.ship_company_id,
                detailJson: JSON.stringify(this.formInline.detailJson)
              }
              API.addBatchSpConfig(data).then((response) => {
                if (response.data.Code === 10000) {
                  this.$Message.success(response.data.Message)
                  this.$Modal.remove()
                  this.getShipList()
                  this.clearData()
                } else {
                  this.addShipModal = true
                  this.$Message.error(response.data.Message)
                  this.$Modal.remove()
                }
              }).catch()
            }
          })
        }
      })
    },
    // 重置新增船舶信息
    clearData () {
      this.addShipModal = false
      this.shipListData = []
      this.businessAffairsData = []
      this.shipUserData = []
      this.curShipId = ''
      this.curShipIndex = ''
      this.curShipUser = []
      this.shipIdList = []
      this.shipUserList = []
      this.formInline = {
        ship_company_config_auth_id: '',
        ship_company_id: '',
        detailJson: [
          {
            ship_id: '',
            business_id: '',
            ship_user_id: []
          }
        ]
      }
      this.$refs['formInline'].resetFields()
    },
    // 添加船舶信息内容
    handleAdd () {
      this.formInline.detailJson.push({
        ship_id: '',
        business_id: '',
        ship_user_id: []
      })
    },
    // 修改列表船舶信息
    handleUpdate (row) {
      this.updateShipModal = true
      this.dialogType = 'update'
      this.getAllshipList(row)
      this.getSpUserList(row)
      this.formValidate = {
        business_id: row.business_id,
        ship_id: row.ship_id,
        ship_user_id: row.ship_user_ids.split(','),
        ship_company_config_auth_id: row.ship_company_config_auth_id,
        ship_company_id: row.ship_company_id,
        ship_config_ids: row.ship_config_ids
      }
    },
    // 保存修改列表船舶信息
    updateData () {
      this.$refs['formValidate'].validate((valid) => {
        if (valid) {
          this.$Modal.confirm({
            title: '提示',
            content: '<p>是否修改船舶信息？</p>',
            loading: true,
            onOk: () => {
              this.formValidate.ship_user_ids = this.formValidate.ship_user_id.join()
              delete this.formValidate.ship_user_id
              let data = Object.assign({}, this.formValidate)
              API.updateSpConfig(data).then((response) => {
                if (response.data.Code === 10000) {
                  this.$Message.success(response.data.Message)
                  this.$Modal.remove()
                  this.getShipList()
                  this.clearUpdateData()
                } else {
                  this.updateShipModal = true
                  this.$Message.error(response.data.Message)
                  this.$Modal.remove()
                }
              }).catch()
            }
          })
        }
      })
    },
    // 重置船舶修改弹窗内容
    clearUpdateData () {
      this.shipListData = []
      this.businessAffairsData = []
      this.shipUserData = []
      this.updateShipModal = false
      this.$refs['formValidate'].resetFields()
    },
    // 删除列表船舶信息
    handleDelete (d) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>是否删除该成员？</p>',
        loading: true,
        onOk: () => {
          let data = {
            ship_company_config_auth_id: d.ship_company_config_auth_id,
            ship_company_id: d.ship_company_id,
            ship_id: d.ship_id,
            business_id: d.business_id,
            ship_user_id: d.ship_user_ids
          }
          API.deleteSpConfig(data).then((response) => {
            if (response.data.Code === 10000) {
              this.$Message.success(response.data.Message)
              this.$Modal.remove()
              this.loading = false
              this.getShipList()
            } else {
              this.$Message.error(response.data.Message)
              this.loading = false
            }
          })
        }
      })
    },
    shipIdOpen (index) {
      this.curShipId = this.formInline.detailJson[index].ship_id
    },
    // 存储已选择的数据
    shipIdSelected (d) {
      if (this.shipIdList.includes(this.curShipId)) {
        let idIndex = this.shipIdList.indexOf(this.curShipId)
        this.shipIdList.splice(idIndex, 1)
      }
      this.shipIdList.push(d)
    },
    // 移除船舶信息内容
    handleRemove (index) {
      if (this.formInline.detailJson.length > 1) {
        this.shipIdList.splice(index, 1)
        this.shipUserList.splice(index, 1)
        this.formInline.detailJson.splice(index, 1)
      } else {
        this.$Message.warning('请至少保留一条船舶数据！')
      }
    },
    shipUserOpen (index) {
      this.formInline.detailJson.map(item => {
        if (!item.ship_user_id) return
        item.ship_user_id.map(list => {
          if (!this.shipUserList.includes(list)) {
            this.shipUserList.push(list)
          }
        })
      })
    },
    // 存储已选择的船端用户
    shipUserSelected (idx, d) {
      let curList = []
      this.formInline.detailJson.map(item => {
        if (!item.ship_user_id) return
        item.ship_user_id.map(list => {
          curList.push(list)
        })
      })
      this.shipUserList = curList
    }
  }
}
</script>
<style scoped>
  .formdiv {
    position: relative;
  }
  .searchdiv {
    display: inline-block;
    vertical-align: middle;
    margin-right: -5px;
  }
  .formbtn {
    font-size: 24px;
    padding: 0 3px;
  }
  .formbtn.addbtn {
    right: 0;
    bottom: 18px;
    position: absolute;
  }
</style>
