<template>
  <div class="loading-module">
    <div class="loading_header">
      <div class="loading_title">
        <span class="title-icon">{{ titleIcon }}</span>
        <span>{{ title }}</span>
      </div>
      <div>
        <span>{{ subtitle }}</span>
      </div>
    </div>
    
    <div class="progress-bar">
      <div class="progress-fill" :style="{ width: progressPercent + '%' }"></div>
    </div>
    
    <div class="content">
      <div class="main-steps">
        <div
          v-for="(step, index) in steps"
          :key="index"
          class="main-step"
          :class="{ 
            'active': currentStepIndex === index, 
            'completed': currentStepIndex > index
          }"
        >
          <div class="step-header">
            <div class="step-icon" :class="getMainIconClass(index)">
              <span v-if="currentStepIndex <= index">{{ index + 1 }}</span>
              <span v-else>✓</span>
            </div>
            <div class="step-title">{{ step.title }}</div>
          </div>
          <div class="step-desc">{{ step.description }}</div>
        </div>
      </div>
      
      <div class="details">
        <div class="detail-section" v-if="steps.length > 0">
          <div class="detail-header">
            <div class="detail-title">{{ currentStep.title }}</div>
            <div class="detail-status">{{ getStepStatus(currentStepIndex) }}</div>
          </div>
          
          <div class="sub-tasks">
            <div
              v-for="(subTask, subIndex) in currentStep.subTasks"
              :key="subIndex"
              class="sub-task"
            >
              <div class="sub-task-header">
                <div class="sub-task-icon" :class="getSubTaskIconClass(subIndex)">
                  <span v-if="currentSubTaskIndex <= subIndex">{{ subIndex + 1 }}</span>
                  <span v-else>✓</span>
                </div>
                <div class="sub-task-title">{{ subTask.title }}</div>
              </div>
              <div class="sub-task-desc">{{ subTask.description }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LoadingModule',
  props: {
    // 主标题图标
    titleIcon: {
      type: String,
      default: '🚢'
    },
    // 主标题
    title: {
      type: String,
      default: '智能数据处理系统'
    },
    // 副标题
    subtitle: {
      type: String,
      default: '🔄 多模块协同计算'
    },
    // 步骤数据
    steps: {
      type: Array,
      required: true,
      // 期望格式: 
      /*
      [
        {
          title: '数据收集',
          description: '收集系统数据、用户信息和历史操作记录',
          subTasks: [
            { title: '船舶基础数据收集', description: '收集船舶标识、吨位、航速等基础信息' },
            { title: '港口信息收集', description: '获取港口坐标、停靠时间、装卸能力数据' },
            { title: '历史航行记录', description: '分析历史航线和航行时间数据' }
          ]
        },
        // 更多步骤...
      ]
      */
    },
    // 是否自动开始加载
    autoStart: {
      type: Boolean,
      default: true
    },
    // 子任务加载时间（毫秒）
    subTaskDuration: {
      type: Number,
      default: 300
    },
    // 主任务之间的间隔时间（毫秒）
    mainTaskInterval: {
      type: Number,
      default: 200
    }
  },
  data() {
    return {
      isProcessing: false,
      currentStepIndex: 0,
      currentSubTaskIndex: -1,
      completed: false
    }
  },
  computed: {
    currentStep() {
      return this.steps[this.currentStepIndex] || { subTasks: [] };
    },
    progressPercent() {
      if (this.steps.length === 0) return 0;
      
      const stepProgress = (this.currentStepIndex / this.steps.length) * 100;
      
      if (this.currentSubTaskIndex >= 0 && this.currentStep.subTasks.length > 0) {
        const subTaskProgressPerStep = 100 / this.steps.length;
        const subTaskProgressValue = (this.currentSubTaskIndex / this.currentStep.subTasks.length) * subTaskProgressPerStep;
        return Math.min(stepProgress + subTaskProgressValue, 100);
      }
      
      return stepProgress;
    }
  },
  methods: {
    showStep(index) {
      if (!this.isProcessing && index <= this.currentStepIndex) {
        this.currentStepIndex = index;
        this.currentSubTaskIndex = this.steps[index].subTasks.length - 1;
      }
    },

    setStep(index) {
      this.currentStepIndex = index;
      this.currentSubTaskIndex = -1
    },
    
    getStepStatus(index) {
      if (this.currentStepIndex > index) {
        return '已完成';
      } else if (this.currentStepIndex === index) {
        if (this.currentSubTaskIndex >= this.currentStep.subTasks.length - 1) {
          return '已完成';
        } else {
          return '处理中...';
        }
      } else {
        return '等待中...';
      }
    },
    
    getMainIconClass(index) {
      if (this.currentStepIndex > index) {
        return 'step-icon status-success';
      } else if (this.currentStepIndex === index) {
        return 'step-icon status-active';
      } else {
        return 'step-icon';
      }
    },
    
    getSubTaskIconClass(subIndex) {
      if (this.currentSubTaskIndex > subIndex) {
        return 'sub-task-icon status-success';
      } else if (this.currentSubTaskIndex === subIndex) {
        return 'sub-task-icon status-loading';
      } else {
        return 'sub-task-icon status-pending';
      }
    },
    
    async startProcess() {
      if (this.isProcessing || this.completed || this.steps.length === 0) return;
      
      this.isProcessing = true;
      this.currentStepIndex = 0;
      this.currentSubTaskIndex = -1;
      
      try {
        for (let i = 0; i < this.steps.length; i++) {
          this.currentStepIndex = i;
          const step = this.steps[i];
          
          // 处理子任务
          for (let j = 0; j < step.subTasks.length; j++) {
            this.currentSubTaskIndex = j;
            // 模拟处理时间
            await this.wait(this.subTaskDuration);
          }
          
          // 标记当前主步骤完成
          this.$emit('step-completed', i);
          
          // 如果不是最后一个主步骤，等待一段时间
          if (i < this.steps.length - 1) {
            await this.wait(this.mainTaskInterval);
          }
        }
        
        this.completed = true;
        this.$emit('completed');
      } finally {
        this.isProcessing = false;
      }
    },
    
    reset() {
      this.isProcessing = false;
      this.currentStepIndex = 0;
      this.currentSubTaskIndex = -1;
      this.completed = false;
    },
    
    wait(ms) {
      return new Promise(resolve => setTimeout(resolve, ms));
    }
  },
  mounted() {
    if (this.autoStart) {
      this.$nextTick(() => {
        this.startProcess();
      });
    }
  }
}
</script>

<style scoped>
.loading-module {
  width: 800px;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  background-color: #161b22;
  color: #e6edf3;
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.loading_header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.loading_title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  font-weight: bold;
}

.title-icon {
  color: #3981c5;
  font-size: 28px;
}

.progress-bar {
  height: 8px;
  background-color: #21262d;
  border-radius: 4px;
  margin: 20px 0 30px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #3981c5;
  width: 0;
  transition: width 0.5s ease-in-out;
}

.content {
  display: flex;
  gap: 30px;
}

.main-steps {
  flex: 0 0 300px;
}

.details {
  flex: 1;
  background-color: #21262d;
  border-radius: 8px;
  padding: 20px;
  min-height: 450px;
  max-height: 600px;
  overflow-y: auto;
}

.main-step {
  padding: 16px;
  border-radius: 8px;
  background-color: #21262d;
  margin-bottom: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: 0.7;
  position: relative;
}

.main-step.active {
  background-color: #2d3239;
  opacity: 1;
  border-left: 4px solid #3981c5;
}

.main-step.completed {
  opacity: 1;
}

.step-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.step-icon {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #30363d;
  transition: all 0.3s ease;
}

.step-icon.status-success {
  background-color: #3981c5;
  color: #0d1117;
}

.step-icon.status-active {
  background-color: #3981c5;
  color: #0d1117;
}

.step-title {
  font-weight: bold;
  font-size: 16px;
}

.step-desc {
  margin-top: 8px;
  font-size: 14px;
  color: #8b949e;
  text-align: left;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.detail-title {
  font-size: 20px;
  font-weight: bold;
}

.detail-status {
  font-size: 14px;
  color: #3981c5;
}

.sub-tasks {
  margin-top: 20px;
}

.sub-task {
  padding: 16px;
  border-radius: 8px;
  background-color: #30363d;
  margin-bottom: 12px;
  position: relative;
  transition: all 0.3s ease;
  overflow: hidden;
}

.sub-task-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.sub-task-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.status-pending {
  border: 2px solid #8b949e;
  color: #8b949e;
}

.status-loading {
  border: 2px solid #3981c5;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

.status-success {
  background-color: #3981c5;
  color: #0d1117;
}

.sub-task-title {
  font-weight: bold;
  font-size: 15px;
}

.sub-task-desc {
  margin-top: 8px;
  font-size: 14px;
  color: #8b949e;
  text-align: left;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>