<template>
  <div class="pushstyle">
    <p slot='title' class="bold-font">权限管理</p>
    <Card class="divcard">
      <div style="width: 100%;max-width: 800px;margin: 0 auto;">
        <search @searchResults='searchResults' :setSearch='setSearchData' @keydown.enter.native='searchResults' @resetResults='resetResults'></search>
        <div class="extra" slot="extra">
          <formAction :setFormAction='setFormAction' @handleUpdateTable="getList"></formAction>
        </div>
        <Table border :loading="loading" :columns="columns" :data="list" width="800"></Table>
        <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
          :total="total" prev-text="< 上一页" next-text="下一页 >"
          @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
      </div>
    </Card>
    <memberManagement ref="memberManagement"></memberManagement>
    <shipManagement ref="shipManagement"></shipManagement>
  </div>
</template>

<script>
import search from '_c/search' // 查询组件
import formAction from '_c/formAction' // 表单操作组件
import API from '@/api/superPermission/superPermission'
import memberManagement from './memberManagement'
import shipManagement from './shipManagement'

export default {
  components: {
    search,
    formAction,
    memberManagement,
    shipManagement
  },
  data () {
    return {
      setSearchData: {// 查询设置，对象key值为回调参数
        ship_company_name: {
          type: 'select',
          label: '公司名称',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 150,
          value: '',
          filterable: true
        }
      },
      setFormAction: {
        operation: ['updateTable']
      },
      loading: false, // 表单列表loding状态
      columns: [
        {
          title: '公司名称',
          key: 'ship_company_name',
          align: 'center'
        },
        {
          title: '是否开启航次动态关联',
          align: 'center',
          render: (h, params) => {
            return h('i-switch', {
              props: {
                value: params.row.is_open === '1'
              },
              on: {
                'on-change': () => {
                  params.row.is_open = params.row.is_open === '0' ? '1' : '0' // 1:可用 0：关闭
                  this.changeSpComConfigAuth(params.row)
                }
              }
            })
          }
        },
        {
          title: '操作',
          key: 'operation',
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                style: {
                  margin: '4px'
                },
                props: {
                  icon: 'md-person',
                  size: 'small',
                  disabled: params.row.is_open === '0'
                },
                on: {
                  click: () => {
                    this.formModalState('member', params.row)
                  }
                }
              }, '成员管理'),
              h('Button', {
                style: {
                  margin: '4px'
                },
                props: {
                  icon: 'md-boat',
                  size: 'small',
                  disabled: params.row.is_open === '0'
                },
                on: {
                  click: () => {
                    this.formModalState('ship', params.row)
                  }
                }
              }, '船舶管理')
            ])
          }
        }
      ],
      list: [], // 表单列表数据
      total: null, // 列表数据条数
      listQuery: {// 列表请求参数
        pageSize: 10,
        pageIndex: 1,
        ship_company_id: '',
        ship_company_name: ''
      },
      listCurrent: 1 // 当前页码
    }
  },
  created () {
    // 获取公司
    API.spComConfigAuthList({ ship_company_id: '', ship_company_name: '' }).then(response => {
      if (response.data.Code === 10000) {
        response.data.Result.map(item => {
          this.setSearchData.ship_company_name.selectData.push({
            value: item.ship_company_name,
            label: item.ship_company_name
          })
        })
      } else {
        this.setSearchData.name.selectData = []
      }
    })
    this.getList()
  },
  methods: {
    // 查询
    searchResults (e) {
      if (e.target) delete e.target
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      Object.assign(this.listQuery, e)
      this.getList()
    },
    // 重置
    resetResults () {
      this.listQuery.pageIndex = 1
      this.listQuery.ship_company_id = ''
      this.listQuery.ship_company_name = ''
      this.listCurrent = 1
      this.setSearchData.ship_company_name.selected = ''
      this.getList()
    },
    // 获取公司列表
    getList () {
      this.loading = true
      API.spComConfigAuthPage(this.listQuery).then(response => {
        if (response.data.Code === 10000) {
          this.list = response.data.Result
          this.total = response.data.Total
        } else {
          this.$Message.error(response.data.Message)
        }
        setTimeout(() => {
          this.loading = false
        }, 1 * 800)
      }).catch(
        setTimeout(() => {
          this.loading = false
        }, 1 * 800)
      )
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    },
    // 开启组件
    formModalState (d, row) {
      if (d === 'member') {
        this.$refs.memberManagement.modalData = Object.assign({}, row)
        this.$refs.memberManagement.memberModal = true
      } else if (d === 'ship') {
        this.$refs.shipManagement.modalData = Object.assign({}, row)
        this.$refs.shipManagement.shipModal = true
      }
    },
    // 权限开启关闭
    changeSpComConfigAuth (row) {
      let data = {
        is_open: row.is_open,
        ship_company_id: row.ship_company_id
      }
      API.changeSpComConfig(data).then(response => {
        if (response.data.Code === 10000) {
          this.getList()
          this.$Message.success(response.data.Message)
        } else {
          this.$Message.error(response.data.Message)
        }
      })
    }
  }
}
</script>
<style lang="less">
  .pushstyle {
    .filter-container {
      float: left;
      margin-bottom: 10px;
    }
    .divcard {
      padding: 35px 0;
    }
    .extra {
      float: right;
      margin-top: 5px;
    }
    .ivu-table-wrapper {
      clear: both;
    }
  }
</style>
