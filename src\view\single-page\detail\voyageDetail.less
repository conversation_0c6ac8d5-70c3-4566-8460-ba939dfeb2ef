.search-area {
  width: 100%;
  height: ~"calc(100% - 140px)";
  overflow: auto;
  left: 50%;
  position: absolute;
  margin-left: -550px;
  margin-top: 50px;
  .indetailCon {
    display: inline-block;
    width: ~"calc(100% - 220px)";
    margin: 0 auto;
    .ivu-tabs-bar {
      margin-bottom: 0;
    }
  }
  .detailCon {
    display: inline-block;
    width: 1100px;
    margin: 0 auto;
    .ivu-tabs-bar {
      margin-bottom: 0;
    }
  }
}
.search-in-box {
  height: ~"calc(100% - 90px)";
  margin-top: 0;
  left: auto;
  margin-left: auto;
}
.detailCon {
  // width: 1100px;
  margin: 0 auto;
  .ivu-tabs-bar {
    margin-bottom: 0;
  }
}
.base-box {
  .ivu-card-body {
    padding: 16px 43px;
  }
  .base-area {
    display: flex;
    justify-items: center;
    align-items: center;
    margin-top: 13px;
    color: #333;
    font-weight: 600;
    .goods-area {
      div {
        line-height: 30px;
        .goods-icon {
          margin-right: 8px;
        }
        .goods-type {
          color: #969FB6;
          margin-right: 16px;
        }
      }
    }
  }
}
.search-con {
  margin: 20px auto;
  input {
      font-size: 18px;
      height: 50px;
      line-height: 50px;
  }
  .search-type {
      width: 120px;
      font-size: 20px;
      padding: 10px;
      color: #57a3f3;
  }
  .ivu-input-group {
      font-size: 16px;
  }
  .ivu-input-search {
    width: 150px;
  }
  .ivu-select-selected-value {
      font-size: 16px;
  }
}
.step-area {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  .case-status {
    width: 90px;
    height: 128px;
    border-radius: 8px;
    padding-bottom: 6px;
    padding-top: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative;
    z-index: 3;
    &:hover {
      box-sizing: border-box;
      top: -2px;
      box-shadow: 0px 2px 6px 2px rgba(0, 0, 0, 0.1);
    }
    .dis-box {
      width: 56px;
      height: 56px;
      background: #f0f0f0;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .cur-box {
      width: 56px;
      height: 56px;
      background: #EFF8FF;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .case-status-text {
      text-align: center;
      font-size: 14px;
      font-weight: 600;
      color: #333333;
    }
    .case-status-time {
      font-size: 12px;
      font-weight: 400;
      color: #666666;
      line-height: 17px;
      display: flex;
    }
  }
  .process-step {
    height: 124px;
    display: flex;
    padding-top: 26px;
    .ivu-tooltip-inner {
      background-color: #007DFF;
    }
    .ivu-tooltip-arrow {
      border-top-color: #007DFF;
    }
    .process-bar-point-wrap {
      display: flex;
      flex-wrap: nowrap;
      justify-content: space-around;
      width: 20px;
      margin-top: 10px;
      .process-bar-point {
        width: 4px;
        height: 4px;
        background: #0081FF;
        border-radius: 50%;
      }
      .process-bar-point-dis {
        width: 4px;
        height: 4px;
        background: #D3D3D3;
        border-radius: 50%;
      }
    }
  }
}
.detail-con {
  color: #333;
  background: #fff;
  padding: 20px 20px 35px;
  .detail-block {
    margin-bottom: 20px;
    .detail-other-block {
      font-size: 12px;
      font-weight: 500;
      color: #4A4A4A;
      margin-bottom: 10px;
    }
  }
  .ivu-form {
    border-left: 1px solid #DAE1ED;
    border-bottom: 1px solid #DAE1ED;
  }
  .detail-port-type {
    font-size: 16px;
    color: #333;
    font-weight: 600;
  }
  .detail-name {
    font-size: 16px;
    color: #333;
    font-weight: 600;
    margin-left: 20px;
  }
  .detail-time {
    font-size: 16px;
    color: #333;
    font-weight: 400;
    margin-left: 20px;
  }
  .ivu-form-item {
    margin-bottom: 0;
    height: 40px;
    line-height: 40px;
  }
  .ivu-form-item-label {
    padding: 0 10px;
    text-align: left;
    font-weight: 600;
    border: 1px solid #DAE1ED;
    border-bottom: none;
    border-left: none;
    background-color: #EFF8FF;
    height: 40px;
    line-height: 40px;
  }
  .ivu-form-item-content {
    border: 1px solid #DAE1ED;
    border-left: none;
    border-bottom: none;
    padding: 0 10px;
    height: 40px;
    line-height: 40px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}