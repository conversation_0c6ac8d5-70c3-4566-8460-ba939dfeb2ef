<template>
  <div>
    <Drawer
      v-model="modalSMS"
      :data="modalSMSData"
      :title="title"
      :dialogType="dialogType"
      :width="800"
      :mask-closable="dialogType === 'detail'"
      @on-visible-change="visibleChange">
      <div class="divtitleclass">
        <label>公司名称： </label>
        <Select v-model="cargoowner_id" style="width: 235px" :disabled="dialogType !== 'create'" @on-change="changeCargoowner">
          <Option v-for="(item, idx) in companyList" :value="item.customer_company_id" :key="idx">{{item.customer_company_name}}</Option>
        </Select>
      </div>
      <!-- 成员 -->
      <h3 class="divtitleclass" v-if="dialogType === 'detail' && this.companyUserSelected.length > 0">发送成功列表</h3>
      <div :class="companyUserSelected.length !== 0 ? 'companyuserdiv' : ''">
        <div class="companyuserspan" v-for="(item, index) in companyUserSelected" :key="index">
          <Button v-if="!item.is_success || item.is_success === '1'">{{ item.customer_member_name }}</Button>
        </div>
      </div>
      <div v-if="dialogType === 'detail' && pushFailData.length !== 0">
        <h3 class="divtitleclass">发送失败列表 <Button type="error" @click="pushAgainUser('allUser')" style="float: right; margin-top: -10px;">全部发送</Button></h3>
        <Table border max-height="500" :columns="pushFailColumns" :data="pushFailData" class="nodecolumnstable"></Table>
      </div>
      <div v-if="dialogType === 'detail'" class="sms-content">
        <h3 class="divtitleclass">短信内容</h3>
        <div class="sms-area">{{ modalSMSData.sms_content}}</div>
      </div>
    </Drawer>
  </div>
</template>

<script>
import API from '@/api/setting/SMSPush'
import { pushAgainSMS } from '@/api/setting/historicalSMS'

export default {
  data () {
    return {
      // checkAll: false, // 全选当前打勾
      // checkAllClick: false, // 所有已经全选的数据
      isFirstSave: true, // 第一次保存成员列表
      tableShow: false, // 节点列表渲染
      modalSMS: false, // 模态框弹窗
      title: '', // 模态框title
      modalSMSData: '',
      dialogType: null, // 弹窗类型
      cargoowner_id: '', // 公司id
      companyList: [], // 公司列表
      companyUserSelected: [], // 已选成员
      company_name: '', // 选中公司
      companyUserList: [], // 成员列表
      total: null,
      listQuery: {
        cargoowner_id: '', // 货主公司id
        pageSize: 100000,
        pageIndex: 1
      },
      columns: [ // 成员列表
        {
          title: '公司成员',
          key: 'customer_member_name',
          align: 'center'
        },
        {
          title: '是否开启推送',
          key: 'is_push',
          align: 'center',
          render: (h, params) => {
            return h('Checkbox', {
              props: {
                value: params.row.is_push === '1'
              },
              on: {
                'on-change': () => {
                  this.companyUserList[params.index].is_push = this.companyUserList[params.index].is_push === '0' ? '1' : '0'
                  if (this.companyUserList[params.index].is_push === '1') {
                    this.companyUserSelected.push(this.companyUserList[params.index])
                  } else {
                    this.companyUserSelected.splice(this.companyUserSelected.findIndex(zx => zx.user_id === params.row.user_id), 1)
                  }
                }
              }
            }, '开启')
          }
        }
      ],
      // nodeColumns: [ // 节点列表
      //   {
      //     title: '节点名称',
      //     key: 'template_node_name',
      //     align: 'center'
      //   },
      //   {
      //     title: '是否开启推送',
      //     key: 'is_push',
      //     align: 'center',
      //     renderHeader: (h, params) => {
      //       return h('Checkbox', {
      //         props: {
      //           disabled: this.cargoowner_id === '' || this.companyUserSelected.length === 0,
      //           value: this.checkAll
      //         },
      //         on: {
      //           'on-change': () => {
      //             this.checkAllClick = !this.checkAll
      //             // 强制更新
      //             this.checkAllSelect()
      //           }
      //         }
      //       }, '是否开启推送')
      //     },
      //     render: (h, params) => {
      //       return h('Checkbox', {
      //         props: {
      //           disabled: this.cargoowner_id === '' || this.companyUserSelected.length === 0,
      //           value: params.row.is_push === '1'
      //         },
      //         on: {
      //           'on-change': () => {
      //             if (!this.nodeData[params.index].is_push) {
      //               Object.assign(this.nodeData[params.index], {
      //                 is_push: '1'
      //               })
      //             } else {
      //               this.nodeData[params.index].is_push = this.nodeData[params.index].is_push === '1' ? '0' : '1'
      //             }
      //             this.isAllCheck() // 判断是否已经全选
      //           }
      //         }
      //       }, '开启')
      //     }
      //   }
      // ],
      // nodeData: [], // 节点列表
      pushFailColumns: [ // 发送失败列表
        {
          title: '名称',
          key: 'customer_member_name',
          align: 'center'
        },
        {
          title: '操作',
          key: 'is_success',
          align: 'center',
          render: (h, params) => {
            return h('Button', {
              props: {
                type: 'error'
              },
              on: {
                click: () => {
                  this.pushAgainUser('oneUser', params)
                }
              }
            }, '重新发送')
          }
        }
      ],
      pushFailData: []
    }
  },
  methods: {
    // 开启模态框
    visibleChange (d) {
      this.cargoowner_id = ''
      this.tableShow = d
      if (d === true) {
        if (this.dialogType === 'create') {
          // 获取公司名称
          API.companyOutOfSelected().then(response => {
            if (response.data.Code === 10000) {
              this.companyList = response.data.Result
            }
          })
        } else if (this.dialogType === 'update') {
          this.cargoowner_id = this.modalSMSData.customer_company_id
          this.companyList = [{
            customer_company_id: this.modalSMSData.customer_company_id,
            customer_company_name: this.modalSMSData.company_name
          }]
        } else { // 查看详情
          this.cargoowner_id = this.modalSMSData.customer_company_id
          this.companyList = [{
            customer_company_id: this.modalSMSData.customer_company_id,
            customer_company_name: this.modalSMSData.customer_company_name
          }]
          this.modalSMSData.userInfo.map((lit, indx) => {
            if (lit.is_success === '0') {
              this.pushFailData.push(this.modalSMSData.userInfo[indx])
            } else {
              this.companyUserSelected.push({
                user_id: lit.user_id,
                customer_member_name: lit.customer_member_name,
                is_success: lit.is_success
              })
            }
          })
        }
        if (this.dialogType !== 'detail') {
          // this.getNodeList() // 查询节点列表
          if (this.cargoowner_id !== '') {
            this.getCompanyUserConfigList() // 回显成员列表
          }
        }
      } else {
        this.companyUserSelected = []
        this.pushFailData = []
        // this.checkAll = false
        // this.checkAllClick = false
        this.isFirstSave = true
      }
    },
    // 切换公司
    changeCargoowner () {
      this.companyUserSelected = []
    },
    // 获取节点列表
    // getNodeList () {
    //   API.msgNodeConfigList().then(response => {
    //     if (response.data.Code === 10000) {
    //       this.nodeData = response.data.Result
    //       if (this.dialogType !== 'create') {
    //         let nodeListQuery = {
    //           cargoowner_id: this.cargoowner_id, // 货主公司id
    //           // shipowner_id: this.$store.state.user.conpanyId,
    //           is_push: '' // 是否开启推送（1开启，0未开启）
    //         }
    //         API.updateMsgNodeConfigList(nodeListQuery).then(response => {
    //           if (response.data.Code === 10000) {
    //             response.data.Result.map(item => {
    //               this.nodeData.map((list, idx) => {
    //                 if (list.template_node === item.template_node) {
    //                   list.is_push = item.is_push
    //                 }
    //               })
    //             })
    //             this.$nextTick(() => {
    //               // 获取数据后判断全选状态
    //               this.isAllCheck()
    //             })
    //           }
    //         })
    //       }
    //     }
    //   })
    // },
    // 判断是否全部选中
    // isAllCheck () {
    //   this.checkAll = this.nodeData.every(lis => { return lis.is_push === '1' })
    // },
    // // 全部选中与取消
    // checkAllSelect () {
    //   this.nodeData.map(lis => { lis.is_push = this.checkAllClick ? '1' : '0' })
    //   this.checkAll = this.checkAllClick
    // },
    // 选中成员列表回显
    getCompanyUserConfigList () {
      let listQueryData = {
        cargoowner_id: this.cargoowner_id,
        // shipowner_id: this.$store.state.user.conpanyId,
        is_push: '1', // 选中成员为1.开启
        pageSize: 10000,
        pageIndex: 1
      }
      API.companyUserConfigList(listQueryData).then(response => {
        if (response.data.Code === 10000) {
          this.companyUserSelected = response.data.Result
        } else {
          this.$Message.error(response.data.Message)
        }
      })
    },
    // 保存模态框
    okDrawerBtn () {
      if (this.companyUserSelected.length === 0) {
        this.$Message.error('成员不能为空！')
      } else {
        this.saveMsgNodeConfig()
      }
    },
    // 取消模态框
    cancelDrawerBtn () {
      this.modalSMS = false
    },
    // 修改/保存公司成员及节点列表
    // saveMsgNodeConfig () {
    //   let userId = []
    //   let nodeId = []
    //   this.companyUserSelected.map(d => {
    //     userId.push(d.user_id)
    //   })
    //   this.nodeData.forEach(item => item.is_push && item.is_push === '1' ? nodeId.push(item.id) : '')
    //   let listData = {
    //     user_ids: userId.join(','), // 用户ids，多个用英文逗号隔开（必填）
    //     msg_template_ids: nodeId.join(','), // 节点模板ids，多个用英文逗号隔开（必填）
    //     cargoowner_id: this.cargoowner_id
    //     // shipowner_id: this.$store.state.user.conpanyId
    //   }
    //   API.saveMsgNodeConfigList(listData).then(response => {
    //     if (response.data.Code === 10000) {
    //       if (this.dialogType === 'create') {
    //         this.modalSMS = false
    //         this.$Message.success('新增货主节点短信推送配置成功')
    //       } else {
    //         this.modalSMS = false
    //         this.$Message.success('修改货主节点短信推送配置成功')
    //       }
    //       this.$emit('addSuccess')
    //     } else {
    //       this.$Message.error(response.data.Message)
    //     }
    //   })
    // },
    // 重新发送
    pushAgainUser (d, data) {
      if (d === 'allUser') {
        let listUserId = []
        let listUsermobile = []
        this.pushFailData.map(item => {
          listUserId.push(item.id)
          listUsermobile.push(item.customer_member_tel)
        })
        let listQueryData = {
          ids: listUserId.join(','),
          mobiles: listUsermobile.join(','),
          sms_content: this.pushFailData[0].sms_content
        }
        pushAgainSMS(listQueryData).then(response => {
          if (response.data.Code === 10000) {
            this.$Message.success(response.data.Message)
            this.$emit('addUpdateList')
            this.pushFailData.forEach(item => {
              this.companyUserSelected.push({
                user_id: item.user_id,
                customer_member_name: item.customer_member_name,
                is_success: '1'
              })
            })
            this.pushFailData.splice(0, this.pushFailData.length)
          } else {
            this.$Message.error(response.data.Message)
          }
        })
      } else {
        let listQueryData = {
          ids: data.row.id,
          mobiles: data.row.customer_member_tel,
          sms_content: data.row.sms_content
        }
        pushAgainSMS(listQueryData).then(response => {
          if (response.data.Code === 10000) {
            this.$Message.success(response.data.Message)
            this.$emit('addUpdateList')
            this.pushFailData.splice(data.index, 1)
            this.companyUserSelected.push({
              user_id: data.row.user_id,
              customer_member_name: data.row.customer_member_name,
              is_success: '1'
            })
          } else {
            this.$Message.error(response.data.Message)
          }
        })
      }
    }
  }
  // watch: {
  //   checkAllClick: {
  //     handler () {
  //       this.checkAllSelect()
  //     }
  //   }
  // }
}
</script>
<style scoped>
.companyname {
  text-align: center;
  line-height: 40px;
  height: 40px;
  margin: 0 auto 5px;
  font-size: 16px;
  color: #152935;
  font-weight: 600;
}
.companyuserdiv {
  max-height: 177px;
  overflow-y: auto;
  border: 1px solid #ccc;
  margin: 10px 0 20px;
  padding: 5px 10px;
}
.companyuserspan {
  color: #333;
  display: inline-block;
  position: relative;
  margin: 5px 10px 5px 0;
}
.companyuserspan .ivu-btn {
  padding: 5px 20px 6px 10px;
}
.companyuserspan .ivu-icon {
  font-size: 22px;
  right: 0;
  position: absolute;
  top: 3px;
  color: #777;
  font-weight: bold;
}
.sms-content {
  margin-top: 20px;
}
.sms-area {
  min-height: 50px;
  border: 1px solid #ccc;
  padding: 5px 10px;
}
.divtitleclass {
  margin: 10px 0;
  color: #152935;
  font-size: 13px;
  font-weight: normal;
}
.demo-drawer-footer {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  background: #fff;
}
.nodecolumnstable {
  width: 100%;
}
</style>
