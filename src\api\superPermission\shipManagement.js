import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 获取船舶管理列表
export function shipConfigPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/auth/ship/config/querySpConfigPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取所有船舶（下拉不带分页）
export function shipListByCondition (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/sys/ship/querySysShipListByCondition',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取商务/船端用户
export function spComConfigUserList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/auth/company/user/querySpComConfigUserList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 保存船舶数据
export function addBatchSpConfig (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/auth/ship/config/addBatchSpConfig',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 修改船舶数据
export function updateSpConfig (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/auth/ship/config/updateSpConfig',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 删除单个船舶数据
export function deleteSpConfig (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/auth/ship/config/delSpConfig',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 批量删除船舶数据
export function deleteAllSpConfig (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/auth/ship/config/delSpConfigBatch',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  shipConfigPage,
  shipListByCondition,
  spComConfigUserList,
  addBatchSpConfig,
  updateSpConfig,
  deleteSpConfig,
  deleteAllSpConfig
}
