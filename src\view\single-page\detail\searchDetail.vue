<template>
  <div>
    <headerLogin></headerLogin>
    <div class="search-area">
      <div class="detailCon">
        <div class="search-con">
          <Input search enter-button="查询" placeholder="请输入货运单号" @on-search="searchDetail">
            <Select v-model="selectType" slot="prepend" class="search-type">
                <Option value="1">货运单号</Option>
            </Select>
          </Input>
        </div>
        <!-- 多个货品展示 -->
        <Tabs type="card" :animated="false" @on-click="tabClick" v-if="voyageBaseInfo.cargoResult.length > 1">
          <TabPane v-for="(item, index) in voyageBaseInfo.cargoResult" :key="index" :label="item.goods_name" :name="index">
            <Card class="base-box">
              <baseInfo ref="baseRefs" :baseObj="voyageBaseInfo"></baseInfo>
            </Card>
            <div class="bold-font dynamic-title">实时动态</div>
            <Card>
              <Row>
                <Col span="11">
                  <dynamicInfo ref="dynamicRefs" :baseObj="voyageBaseInfo"></dynamicInfo>
                </Col>
                <Col span="12" offset="1">
                  <aisInfo :mmsi="mmsi"></aisInfo>
                </Col>
              </Row>
            </Card>
          </TabPane>
        </Tabs>
        <!-- 不显示货品名称 -->
        <div v-if="voyageBaseInfo.cargoResult.length === 1">
          <Card class="base-box">
              <baseInfo :baseObj="voyageBaseInfo"></baseInfo>
            </Card>
            <div class="bold-font dynamic-title">实时动态</div>
            <Card>
              <Row>
                <Col span="11">
                  <dynamicInfo :baseObj="voyageBaseInfo"></dynamicInfo>
                </Col>
                <Col span="12" offset="1">
                  <aisInfo :mmsi="mmsi"></aisInfo>
                </Col>
              </Row>
            </Card>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import API from '@/api/search'
import './voyageDetail.less'
import headerLogin from '../../login/login'
import portDetail from './components/portDetail' // 装卸港详情
import baseInfo from './components/baseInfo' // 顶部基础信息
import dynamicInfo from './components/dynamicInfo' // 动态信息
import aisInfo from './components/aisInfo' // 船舶AIS信息
export default {
  components: {
    portDetail,
    baseInfo,
    dynamicInfo,
    aisInfo,
    headerLogin
  },
  data () {
    return {
      selectType: '1', // 单号类型
      searchId: '', // 货运单号
      mmsi: '', // 船舶mmsi信息
      voyage_id: '', // 航次id
      goods_id: '', // 货品id
      voyageBaseInfo: localStorage.voyageObj ? JSON.parse(localStorage.voyageObj) : {}, // 航次基础信息
      param: {}
    }
  },
  created () {
    this.searchId = this.$route.params.id
    this.setBaseInfo()
  },
  beforeDestroy () {
    // localStorage.setItem('voyageObj', {})
  },
  methods: {
    // 查询详情
    searchDetail (val) {
      if (val === '') return
      API.queryWaybillNumber({ number_no: val }).then(res => {
        if (res.data.Code === 10000) {
          if (res.data.Result.length === 0) {
            this.$Notice.warning({
              title: '无此单号',
              desc: '查无此单号信息,请确认单号信息是否正确!'
            })
          } else {
            localStorage.setItem('voyageObj', JSON.stringify(res.data.Result[0]))
            this.setBaseInfo()
          }
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    setBaseInfo () {
      let curVoyageObj = localStorage.voyageObj ? JSON.parse(localStorage.voyageObj) : {}
      this.mmsi = curVoyageObj.mmsi
      this.voyage_id = curVoyageObj.id
      this.goods_id = curVoyageObj.cargoResult[0].goods_id
    },
    tabClick (val) {
      this.$refs.baseRefs.goodsIndex = val
      this.$refs.dynamicRefs.goodsIndex = val
    }
  }
}
</script>
<style lang="less">
  .dynamic-title {
    color: #4A4A4A;
    font-size: 16px;
    font-weight: 500;
    height: 22px;
    line-height: 22px;
    margin-top: 20px;
  }
</style>
