<template>
  <div>
    <div class="detail-block">
      <div v-for="(item, idx) in portInfoList" :key="idx">
        <div class="bold-font">
          <span class="detail-port-type">{{ item.port_type === '1' ? '装' : '卸' }}</span>
          <span class="detail-name">{{ item.wharf_name }}</span>
        </div>
        <Form :label-width="105" style="margin-bottom: 20px;" v-for="(list, index) in item.cargoParam" :key="index">
          <Row>
            <Col span="8">
              <FormItem label="货主:">
                <span>{{ list.cargo_company_name === '' ? '--' : list.cargo_company_name }}</span>
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="货品:">
                <span>{{ list.goods_name === '' ? '--' : list.goods_name }}</span>
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="货量:">
                <span>{{ list.amounts === '' ? '--' : list.amounts + 'T'}}</span>
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="8">
              <FormItem label="代理公司:">
                <span>{{ list.agency_company_name === '' ? '--' : list.agency_company_name }}</span>
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="代理联系人:">
                <span>{{ list.agency_contact_name === '' ? '--' : list.agency_contact_name }}</span>
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="电话:">
                <span>{{ list.agency_contact_tel === '' ? '--' : list.agency_contact_tel }}</span>
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="8">
              <FormItem label="计量方式:">
                <span>{{ list.unit_name === '' ? '--' : list.unit_name }}</span>
              </FormItem>
            </Col>
            <Col span="16">
              <FormItem label="允许损耗率:">
                <span>{{ list.allowable_loss === '' ? '' : list.allowable_loss + '‰' }}</span>
              </FormItem>
            </Col>
          </Row>
        </Form>
      </div>
    </div>
    <div class="detail-block">
      <div class="bold-font">
        <span class="detail-name">其他信息</span>
      </div>
      <Form :label-width="105" style="margin-bottom: 20px;">
        <Row>
          <Col span="8">
            <FormItem label="录入日期:">
              <span>{{ voyageBaseInfo.insert_time }}</span>
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="受载日期:">
              <span>{{ voyageBaseInfo.start_plan_dates }}</span>
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="操作员:">
              <span>{{ voyageBaseInfo.business_name }}</span>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="8">
            <FormItem label="船上联系方式:">
              <span>{{ voyageBaseInfo.shipper_phone }}</span>
            </FormItem>
          </Col>
          <Col span="16">
            <FormItem label="发票抬头:">
              <span>{{ voyageBaseInfo.invoice }}</span>
            </FormItem>
          </Col>
        </Row>
      </Form>
      <div>
        <div class="detail-other-block">
          <div>洗舱的具体要求</div>
          <Input v-model="voyageBaseInfo.cabin_wash_context" type="textarea" :autosize="true" readonly></Input>
        </div>
        <div class="detail-other-block">
          <div>港口调度/发货人/收货人/代理人的相关资料、联系方式</div>
          <Input v-model="voyageBaseInfo.port_context" :autosize="true" type="textarea" readonly></Input>
        </div>
        <div class="detail-other-block">
          <div>航次港口、货物等应注意的问题</div>
          <Input v-model="voyageBaseInfo.voyage_context" :autosize="true" type="textarea" readonly></Input>
        </div>
        <div class="detail-other-block">
          <div>航次合同相关事宜</div>
          <Input v-model="voyageBaseInfo.contract_context" :autosize="true" type="textarea" readonly></Input>
        </div>
      </div>
    </div>
    <Spin fix v-if="spinShow">
      <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
      <div>Loading</div>
    </Spin>
  </div>
</template>
<script>
import API from '@/api/voyageManage/voyageDetail'
export default {
  props: {
    voyageId: String
  },
  data () {
    return {
      spinShow: false, // loading
      portInfoList: [], // 港口信息接口
      voyageBaseInfo: localStorage.voyageObj ? JSON.parse(localStorage.voyageObj) : {} // 航次基础信息
    }
  },
  created () {
    this.voyageBaseInfo.contract_context = this.voyageBaseInfo.contract_context !== '' ? this.voyageBaseInfo.contract_context : '装货港以流量计，卸货港以商检船板量为准。 \n 为了避免交接疑异，请大副在装港商检船板量与流量计的数量、装港商检船板量与卸港商检船板量的数量均控制在0.1%以内，若超出0.1%，请跟商检做好沟通重新计量并告知公司负责人后再签单离泊。'
    this.getPortInfo()
  },
  methods: {
    getPortInfo () {
      if (this.$route.name === 'searchDetail') {

      } else {
        this.spinShow = true
        API.getVoyageById({ voyage_id: this.voyageId }).then(res => {
          this.spinShow = false
          if (res.data.Code === 10000) {
            this.portInfoList = res.data.Result
          }
        })
      }
    }
  }
}
</script>
