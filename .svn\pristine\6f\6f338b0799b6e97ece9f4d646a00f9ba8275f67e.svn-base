import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 查询装货sof
export function queryVoyageLoadPortSof (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/load/sof/queryVoyageLoadPortSof',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 编辑保存装货sof
export function updateLoadPortSof (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/load/sof/updateLoadPortSof',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询卸货sof
export function queryVoyageUnLoadPortSof (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/unload/sof/queryVoyageUnLoadPortSof',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 编辑保存卸货sof
export function updateUnLoadPortSof (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/unload/sof/updateUnLoadPortSof',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 装货sof导出预览功能
export function previewVoyageLoadPort (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/load/sof/previewVoyageLoadPort',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 卸货sof导出预览功能
export function previewVoyageUnLoadPort (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/unload/sof/previewVoyageUnLoadPort',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  queryVoyageLoadPortSof,
  updateLoadPortSof,
  queryVoyageUnLoadPortSof,
  updateUnLoadPortSof,
  previewVoyageLoadPort,
  previewVoyageUnLoadPort
}
