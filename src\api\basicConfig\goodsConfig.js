import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 修改货品列表
export function updateBasicGoods (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/cargo/record/updateBasicCargo',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取货品价格列表
export function queryBasicGoodsPricePage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/cargo/price/queryAllBasicCargoPricePage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取单货品价格详情
export function queryBasicGoodsPriceDetail (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/cargo/price/querySingleBasicCargoPricePage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询上批次货品价格数据
export function queryBasicGoodsLatelyPriceList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/cargo/price/queryLatelyBasicCargoPriceList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 批量新增单货品价格
export function addBasicGoodsPrice (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/cargo/price/addBatchBasicCargoPrice',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 货品详情修改单货品价格
export function updateBasicGoodsPrice (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/cargo/price/updateBasicCargoPrice',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 货品详情删除单货品价格
export function delBasicGoodsPrice (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/cargo/price/delBasicCargoPrice',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  updateBasicGoods,
  queryBasicGoodsPricePage,
  queryBasicGoodsPriceDetail,
  queryBasicGoodsLatelyPriceList,
  addBasicGoodsPrice,
  updateBasicGoodsPrice,
  delBasicGoodsPrice
}
