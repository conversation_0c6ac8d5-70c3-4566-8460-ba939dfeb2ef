<template>
  <div>
    <Drawer
      v-model="portLineData.modal"
      :data="portLineData.data"
      width="650"
      :title="title"
      :mask-closable="false"
      @on-visible-change="visibleChange">
      <Col>
        <h3 class="bold-font">步骤数</h3>
        <div v-for="(item, idx) in portLineList" :key="idx">
          <div class="port_info">
            <span class="con_text con_load" v-if="item.port_type === '1'">装</span>
            <span class="con_text con_unload" v-if="item.port_type === '2'">卸</span>
            <span style="font-weight: bold;">{{ item.port_name }} - {{ item.wharf_name }}</span>
          </div>
          <span>对应步骤数 &nbsp;&nbsp;&nbsp;&nbsp; {{ item.line_step }}</span>
        </div>
      </Col>
      <h3 class="bold-font" style="margin-top: 80px">步骤数新增</h3>
      <Col>
        <span class="con_text con_load" v-if="curPortType === '1'">装</span>
        <span class="con_text con_unload" v-if="curPortType === '2'">卸</span>
        <label>港口码头</label>
        <Cascader :data="portResultList" trigger="hover" v-model="portResultData" :render-format="format" @on-change="changePortWharf" class="cascade_div"></Cascader>
        <label><span style="color: red;">*</span> 对应步骤数</label>
        <InputNumber :min="1" v-model="line_step"></InputNumber>
      </Col>
      <div class="demo-drawer-footer">
        <Button @click="portLineData.modal = false" style="margin-right: 10px;">取消</Button>
        <Button type="primary" @click="updateData">保存</Button>
      </div>
    </Drawer>
  </div>
</template>

<script>
import { queryVoPortLineList, changeVoPortLine } from '@/api/voyageHistory'
export default {
  props: {
    portLineData: Object
  },
  data () {
    return {
      title: '',
      port_id: '',
      wharf_id: '',
      port_type: '',
      line_step: null,
      curPortType: '',
      portLineList: [],
      portResultList: [],
      portResultData: []
    }
  },
  methods: {
    // 开启模态框
    visibleChange (d) {
      if (d === true) {
        let curList = []
        this.title = this.portLineData.data.ship_name + ' ' + this.portLineData.data.voyage_no
        this.portLineData.data.portResult.map(item => {
          if (curList.includes(item.port_id)) {
            let _curIdx = this.portResultList.findIndex(list => list.value === item.port_id)
            this.portResultList[_curIdx].children.push({
              label: item.wharf_name,
              value: item.wharf_id + item.port_type,
              portType: item.port_type
            })
          } else {
            this.portResultList.push({
              label: item.port_name,
              value: item.port_id,
              children: [{
                label: item.wharf_name,
                value: item.wharf_id + item.port_type,
                portType: item.port_type
              }]
            })
          }
          curList.push(item.port_id)
        })
        queryVoPortLineList({ voyage_id: this.portLineData.data.id }).then(res => {
          if (res.data.Code === 10000) {
            this.portLineList = res.data.Result
          }
        })
      } else {
        this.port_id = ''
        this.wharf_id = ''
        this.port_type = ''
        this.line_step = null
        this.curPortType = ''
        this.portLineList = []
        this.portResultList = []
        this.portResultData = []
      }
    },
    // 选择归属港口码头
    changePortWharf (value, selectedData) {
      this.port_id = value[0]
      this.wharf_id = value[1].substr(0, value[1].length - 1)
      this.portResultData = [value[0], value[1]]
      this.curPortType = selectedData[1].portType
    },
    // 级联格式
    format (labels, selectedData) {
      if (labels.length === 0) return
      return labels[labels.length - 2] + ' - ' + labels[labels.length - 1]
    },
    updateData () {
      if (this.port_id === '' || this.wharf_id === '') {
        this.$Message.error('港口码头不能为空！')
      } else if (this.line_step === null) {
        this.$Message.error('步骤数不能为空！')
      } else {
        this.$Modal.confirm({
          title: '提示',
          content: '<p>是否保存步骤数？</p>',
          loading: true,
          onOk: () => {
            let data = {
              voyage_id: this.portLineData.data.id,
              line_step: this.line_step,
              port_id: this.port_id,
              wharf_id: this.wharf_id,
              port_type: this.curPortType
            }
            changeVoPortLine(data).then(res => {
              if (res.data.Code === 10000) {
                this.$Message.success(res.data.Message)
                this.portLineData.modal = false
                this.$Modal.remove()
                this.$emit('addSuccess')
              } else {
                this.$Modal.remove()
                this.$Message.error(res.data.Message)
              }
            })
          }
        })
      }
    }
  }
}
</script>
<style lang="less" scoped>
.port_info {
  width: 30%;
  line-height: 40px;
  display: inline-block;
}
.con_text {
  width: 22px;
  height: 22px;
  line-height: 22px;
  text-align: center;
  display: inline-block;
  margin-right: 5px;
  border-radius: 50%;
  -moz-border-radius: 50%;
  -webkit-border-radius: 50%;
}
.con_load {
  color: #017db4;
  background-color: #facd91;
}
.con_unload {
  color: #facd91;
  background-color: #017db4;
}
label {
  margin-right: 10px;
}
.cascade_div {
  width: 35%;
  margin-right: 20px;
  display: inline-block;
}
</style>
