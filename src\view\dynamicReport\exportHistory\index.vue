<template>
  <div>
    <Card>
      <search @searchResults='searchResults' @selectOnChanged='selectOnChanged' :setSearch='setSearchData' @keydown.enter.native='searchResults' @resetResults='resetResults'></search>
    </Card>
    <Card style="margin-top:10px;padding-top:6px;">
      <p slot='title' class="bold-font">导出历史</p>
      <div class="extra" slot="extra" >
        <formAction :setFormAction='setFormAction' @handleUpdateTable="handleUpdateTable"></formAction>
      </div>
      <Table border :loading="listLoading" :columns="columns" :data="list"
        ref="selection"
        @on-select-all="tableSelectAll"
        @on-select-all-cancel="tableSelectCancel"
        @on-select="tableSelectAll"
        @on-select-cancel="tableSelectCancel"></Table>
      <div class="select_all">
        <Checkbox v-model="selectAll" @on-change="handleSelectAll(selectAll)">全选</Checkbox>
        <Button @click="handleDown">全部下载</Button>
      </div>
      <Page :total="total" :current.sync="listCurrent" :page-size-opts='[5, 10, 15, 20]' show-sizer :styles="{margin:'16px -10px 0 0',textAlign: 'right'}" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
    </Card>
  </div>
</template>

<script>
import Cookies from 'js-cookie'
import search from '_c/search' // 查询组件
import formAction from '_c/formAction' // 表单操作组件
import API from '@/api/dynamicReport/exportHistory'

export default {
  components: {
    search,
    formAction
  },
  data () {
    return {
      setSearchData: {// 查询设置，对象key值为回调参数
        cargo_company_name: {
          type: 'text',
          label: '公司名称',
          width: 150,
          value: ''
        },
        template_date: {
          type: 'daterange',
          label: '导出时间',
          selected: '',
          width: 200,
          value: '',
          isdisable: false
        },
        template_key: {
          type: 'select',
          label: '模板',
          selectData: [],
          selected: '',
          selectName: '',
          value: '',
          filterable: true
          // change: this.changeTemplate
        }
      },
      setFormAction: {
        operation: ['updateTable']
      },
      listLoading: true, // 表单列表loding状态
      columns: [
        {
          type: 'selection',
          width: 60,
          align: 'center'
        },
        {
          title: '公司',
          key: 'cargo_company_name',
          align: 'center',
          render: (h, params) => {
            return h('div', {}, params.row.cargo_company_name !== '' ? params.row.cargo_company_name : Cookies.get('company_name'))
          }
        },
        {
          title: '报表名称',
          key: 'template_name',
          align: 'center'
        },
        {
          title: '时间',
          key: 'template_date',
          align: 'center'
        },
        {
          title: '操作',
          key: 'operation',
          align: 'center',
          width: 260,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: {
                  icon: 'md-paper',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handlePreview(params.row)
                  }
                }
              }, '预览'),
              h('Button', {
                style: {
                  margin: '0 5px'
                },
                props: {
                  icon: 'ios-trash',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleDelete(params.row)
                  }
                }
              }, '删除')
            ])
          }
        }
      ],
      list: [], // 表单列表数据
      total: null, // 列表数据条数
      listQuery: {// 列表请求参数
        template_key: '',
        template_date_st: '',
        template_date_ed: '',
        cargo_company_name: '',
        pageSize: 10,
        pageIndex: 1
      },
      listCurrent: 1, // 当前页码
      template_url: '', // 导出地址
      selectAll: false
    }
  },
  created () {
    this.setSearchData.template_key.selectData = [
      {
        label: '每日动态',
        value: '每日动态'
      },
      {
        label: '船舶动态',
        value: '船舶动态'
      },
      {
        label: '中海油船舶动态模板',
        value: '中海油船舶动态模板'
      },
      {
        label: '浙石化动态模板',
        value: '浙石化动态模板'
      },
      {
        label: '万兴船舶动态模板',
        value: '万兴船舶动态模板'
      },
      {
        label: '福炼船舶动态模板',
        value: '福炼船舶动态模板'
      }
    ]
    this.getList()
  },
  methods: {
    changeTemplate (d) {
    },
    // 全选
    handleSelectAll (status) {
      this.$refs.selection.selectAll(status)
    },
    // 取消勾选
    tableSelectCancel (selection, row) {
      this.selectAll = false
      this.selectionData = selection
    },
    // 选中勾选触发
    tableSelectAll (selection, row) {
      if (selection.length === this.list.length) this.selectAll = true
      this.selectionData = selection
    },
    // 批量下载
    handleDown () {
      this.selectionData.map(item => {
        // window.open(item.template_url, '_blank')
        const iframe = document.createElement('iframe')
        iframe.style.display = 'none' // 防止影响页面
        iframe.style.height = 0 // 防止影响页面
        iframe.src = item.template_url
        document.body.appendChild(iframe) // 这一行必须，iframe挂在到dom树上才会发请求
        setTimeout(() => {
          iframe.remove()
        }, 5 * 60 * 1000)
      })
    },
    // 查询
    searchResults (e) {
      if (e.target) delete e.target
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      Object.assign(this.listQuery, e)
      this.listQuery.template_date_st = this.template_date_st
      this.listQuery.template_date_ed = this.template_date_ed
      let templateKeyValue = this.setSearchData.template_key.selected
      if (templateKeyValue === '每日动态') {
        this.listQuery.template_key = '0'
      } else if (templateKeyValue === '船舶动态') {
        this.listQuery.template_key = '5'
      } else if (templateKeyValue === '中海油船舶动态模板') {
        this.listQuery.template_key = '1'
      } else if (templateKeyValue === '浙石化动态模板') {
        this.listQuery.template_key = '2'
      } else if (templateKeyValue === '万兴船舶动态模板') {
        this.listQuery.template_key = '3'
      } else if (templateKeyValue === '福炼船舶动态模板') {
        this.listQuery.template_key = '4'
      }
      this.getList()
    },
    // 时间格式
    selectOnChanged (e) {
      if (e.flag === 'daterange') {
        this.template_date_st = e.key[0]
        this.template_date_ed = e.key[1]
      }
    },
    // 重置
    resetResults () {
      this.listCurrent = 1
      this.listQuery = {
        pageSize: 10,
        pageIndex: 1
      }
      this.setSearchData.template_date.selected = ''
      this.setSearchData.cargo_company_name.value = ''
      this.getList()
    },
    // 获取历史记录列表
    getList () {
      this.listLoading = false
      API.exportHistoryList(this.listQuery).then(response => {
        if (response.data.Code === 10000) {
          this.list = response.data.Result
          this.total = response.data.Total
          this.list.map(item => {
            this.template_url = item.template_url
          })
        } else {
          this.$Message.error(response.data.Message)
        }
        setTimeout(() => {
          this.listLoading = false
        }, 1 * 800)
      }).catch(
        setTimeout(() => {
          this.listLoading = false
        }, 1 * 800)
      )
    },
    // 预览
    handlePreview (row) {
      API.getDownFile({ wps_id: row.wps_id }).then(res => {
        if (res.data.Code === 10000) {
          sessionStorage.setItem('wpsUrl', res.data.wpsUrl)
          sessionStorage.setItem('token', res.data.token)
          const jump = this.$router.resolve({ name: 'viewFile' })
          window.open(jump.href, '_blank')
        } else {
          this.$Message.error(res.data.Message)
        }
      }).catch(error => {
        console.log(error)
      })
    },
    // 删除
    handleDelete (row) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确定删除该条记录？</p>',
        loading: true,
        onOk: () => {
          API.exportHistoryDelete({ id: row.id, template_url: row.template_url }).then(res => {
            this.$Modal.remove()
            res.data.Code === 10000 ? this.$Message.success(res.data.Message) : this.$Message.warning(res.data.Message)
            this.getList()
          })
        }
      })
    },
    // 手动更新列表
    handleUpdateTable () {
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      this.getList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    }
  }
}
</script>
<style scoped>
  .select_all {
    margin-top: 10px;
  }
</style>
