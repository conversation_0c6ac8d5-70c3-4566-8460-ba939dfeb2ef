<template>
  <div>
    <Row v-if="showHomePage">
      <card>
        <div class="btn-area">
          <!-- <Select size="small" class="select-ship-content" :transfer="false" v-model="queryParam.ship_id" placeholder="请选择船舶"
                  clearable @on-change="shipSelect">
            <Option v-for="(item, idx) in companyShip" :key="idx" :value="item.ship_id">{{ item.ship_name }}</Option>
          </Select> -->
          <month-select class="month-select" v-model="curDate" @on-change="dateSelect"></month-select>
          <div v-if="!operateShow" style="display: inline-block;">
            <Button size="small" type="primary" :disabled="isView" icon="md-eye" @click="handleReport('view')">{{ isView ? '生成中…' : '预览报表' }}</Button>
            <Button size="small" type="primary" :disabled="isExport" icon="md-download" @click="handleReport('down')">{{ isExport ? '生成中…' : '导出报表' }}</Button>
          </div>
        </div>
        <Row style="margin-top: 13px;">
          <Col span="16">
            <chart-bar style="height: 300px;" unit="万吨" :value="analysisData" text="船舶营运总览"/>
          </Col>
          <Col span="8">
            <chart-pie style="height: 260px;" :value="transData" text="运输货品占比" :color="transColor" :center="pieCenter" unit="万吨"></chart-pie>
          </Col>
        </Row>
        <Row style="margin-top: 30px;">
          <Col span="15" style="padding: 15px;background-color: #f1f5ff">
            <Row>
              <Col span="5">
                <div class="num-title">执行航次数</div>
                <count-to class="num-chart" :end="chartVolumeNum" unitClass="num-unit"/>
                <span>次</span>
              </Col>
              <Col span="6">
                <div class="num-title">货运量</div>
                <count-to class="num-chart" :end="chartGoodsNum" unitClass="num-unit" :decimals="decimals3"/>
                <span>万吨</span>
              </Col>
              <Col span="5">
                <div class="num-title">化学品</div>
                <count-to class="num-chart" :end="chemicalTon" unitClass="num-unit" :decimals="decimals3"/>
                <span>万吨</span>
              </Col>
              <Col span="5">
                <div class="num-title">油品</div>
                <count-to class="num-chart" :end="oilTon" unitClass="num-unit" :decimals="decimals3"/>
                <span>万吨</span>
              </Col>
              <Col span="3">
                <div class="num-title">液化气</div>
                <count-to class="num-chart" :end="liquidTon" unitClass="num-unit" :decimals="decimals3"/>
                <span>万吨</span>
              </Col>
            </Row>
            <Row style="margin-top: 30px;">
              <Col span="5">
                <div class="num-title">航程</div>
                <count-to class="num-chart" :end="chartVoyageNum" unitClass="num-unit" :decimals="decimals3"/>
                <span>万海里</span>
              </Col>
              <Col span="6">
                <div class="num-title">周转量</div>
                <count-to class="num-chart" :end="chartTurnoverNum" unitClass="num-unit" :decimals="decimals3"/>
                <span>万吨公里</span>
              </Col>
              <Col span="5">
                <div class="num-title">待泊时长</div>
                <count-to class="num-chart" :end="chartWaitingTime" unitClass="num-unit" :decimals="decimals2"/>
                <span>小时</span>
              </Col>
              <Col span="5">
                <div class="num-title">总损耗</div>
                <count-to class="num-chart" :end="chartTotalLoss" unitClass="num-unit" :decimals="decimals3"/>
                <span>万吨</span>
              </Col>
              <Col span="3">
                <div class="num-title">总损耗率</div>
                <count-to class="num-chart" :end="chartAverageLossRate" unitClass="num-unit" :decimals="decimals2"/>
                <span>‰</span>
              </Col>
            </Row>
          </Col>
          <Col span="8" offset="1" style="margin-top: -50px">
            <h4 style="margin-bottom: 10px; text-align: center;">{{ queryParam.start_month.substring(0, 4) }}年运输货品组成</h4>
            <chart-pie style="height: 220px;" :value="yearPieData" text="" :color="transColor" :radius="70" :center="pieCenter" unit="万吨"></chart-pie>
          </Col>
        </Row>
      </card>
      <card style="margin-top: 10px;">
        <Row>
          <Col span="10">
            <!-- <chart-line style="height: 300px;" unit="损耗率(‰)" :value="lossLineData" :color="lineColor" :legendShow="false" rotate="45" text="各船损耗率统计"/> -->
            <chart-bar style="height: 300px;" unit="‰" :value="lossLineData" rotate="45" text="各船损耗率统计"/>
          </Col>
          <Col span="14" style="overflow: hidden">
            <div @click="showModelBtn('goodsLoss')" class="morebtn">详情 &gt;</div>
            <Table border :columns="columns" :data="lossList"></Table>
          </Col>
        </Row>
      </card>
      <card style="margin-top: 10px;">
        <Row>
          <Col span="10">
            <!-- <chart-line style="height: 300px;" unit="" :value="shipAuchorData" :color="lineColor" rotate="45" text="各船抛锚时长分析"/> -->
            <chart-bar style="height: 300px;" :value="shipAuchorData" :color="lineColor" rotate="45" unit="" legendPosition="30" text="各船抛锚时长分析" unit="小时,次"/>
          </Col>
          <Col span="9">
            <!-- <chart-line style="height: 300px;" unit="" :value="portAuchorData" :color="lineColor" text="港口抛锚时长分析"/> -->
            <chart-bar style="height: 300px;" :value="portAuchorData" :color="lineColor" rotate="45" unit="小时,次" legendPosition="30" text="港口抛锚时长分析"/>
          </Col>
          <Col span="5">
            <card>
              <h4 style="margin-bottom: 10px;font-weight: bold;">航次数</h4>
              <Row class="voyage_num">
                <div v-for="(item, idx) in voyageNum" :key="idx">
                  <label>{{ idx + 1 }}</label>
                  {{ item.port_line }}
                  <span>{{ item.voyage_total }}次</span>
                </div>
              </Row>
            </card>
          </Col>
        </Row>
      </card>
    </Row>
    <goodsLossChart v-if="showModelLoss" :modalData="modalData" @callback="goBackHome('goodsLoss')"></goodsLossChart>
  </div>
</template>
<script>
import { ChartPie, ChartLine, ChartBar } from '_c/charts'
import MonthSelect from '@/components/monthSelect'
import CountTo from '_c/count-to'
import API from '@/api/statistics/operationAnalysis'
import { queryStatTime } from '@/api/basicData'
import goodsLossChart from './moreChart/goodsLossChart'

export default {
  components: {
    ChartPie,
    ChartLine,
    ChartBar,
    MonthSelect,
    CountTo,
    goodsLossChart
  },
  data () {
    return {
      operateShow: false,
      isView: false, // 是否预览
      isExport: false, // 是否导出
      showHomePage: true,
      showModelLoss: false,
      decimals2: 2, // 保留两位小数
      decimals3: 3, // 保留四位小数
      queryParam: {
        ship_id: '',
        start_month: '',
        end_month: ''
      },
      companyShip: [],
      analysisData: {
        xAxis: [],
        data: []
      },
      lineColor: ['#6699FF', '#E74823'],
      transColor: ['#5B8FF9', '#5AD8A6', '#5D7092', '#F6BD16', '#E8684A', '#6DC8EC', '#9270CA', '#FF9D4D', '#269A99', '#FF99C3'],
      transData: [],
      yearPieData: [],
      lossLineData: {
        xAxis: [],
        data: []
      },
      lossList: [],
      shipAuchorData: {
        xAxis: [],
        legend: ['时长', '次数'],
        data: [[], []],
        yAxis: [
          { name: '抛锚时长/小时' },
          { name: '抛锚72h+次数' }
        ]
      },
      portAuchorData: {
        xAxis: [],
        legend: ['时长', '次数'],
        data: [[], []],
        yAxis: [
          { name: '抛锚时长/小时' },
          { name: '抛锚72h+次数' }
        ]
      },
      pieCenter: ['50%', '50%'],
      chartGoodsNum: 0, // 货运量
      chartTurnoverNum: 0, // 周转量
      chartVolumeNum: 0, // 航次数
      chartVoyageNum: 0, // 航程
      chartWaitingTime: 0, // 待泊时长
      chartTotalLoss: 0, // 总损耗
      chartAverageLossRate: 0, // 平均损耗率
      oilTon: 0, // 油品
      chemicalTon: 0, // 化学品
      liquidTon: 0,
      voyageNum: [],
      modalData: {},
      columns: []
    }
  },
  computed: {
    curDate () {
      if (this.queryParam.start_month !== '' && this.queryParam.end_month !== '') {
        return this.queryParam.start_month + '~' + this.queryParam.end_month
      }
      if (this.queryParam.start_month !== '' && this.queryParam.end_month === '') {
        return this.queryParam.start_month
      }
      return ''
    }
  },
  created () {
    JSON.parse(window.localStorage.bussiShipList).forEach(e => {
      if (!e.ship_name.includes('万华')) { // 剔除万华8
        this.companyShip.push({
          ship_name: e.ship_name,
          ship_id: e.ship_id
        })
      }
    })
    if (window.localStorage.userDataId) {
      this.operateShow = window.localStorage.userDataId === '9'
    }
    this.getCurDate()
  },
  methods: {
    // 获取信息
    getList () {
      this.clearData()
      API.queryShipsAmounts(this.queryParam).then(res => { // 营运总览
        if (res.data.Code === 10000) {
          this.chartGoodsNum = parseFloat(res.data.amount_sum)
          this.chartTurnoverNum = parseFloat(res.data.turnover_sum)
          this.chartVolumeNum = parseInt(res.data.voyage_sum)
          this.chartVoyageNum = parseFloat(res.data.mile_sum)
          this.chartWaitingTime = parseFloat(res.data.wait_berth_sum)
          this.chartTotalLoss = parseFloat(res.data.goods_loss_sum)
          this.chartAverageLossRate = parseFloat(res.data.goods_loss_average)
          this.oilTon = res.data.oils_amount_sum
          this.chemicalTon = res.data.chemical_amount_sum
          this.liquidTon = res.data.liquid_gas_amount_sum
          if (res.data.chemical_amount_rate > 0) {
            this.yearPieData.push({
              value: res.data.chemical_amount_sum,
              name: '化学品'
            })
          }
          if (res.data.oils_amount_rate > 0) {
            this.yearPieData.push({
              value: res.data.oils_amount_sum,
              name: '油品'
            })
          }
          if (res.data.liquid_gas_amount_rate > 0) {
            this.yearPieData.push({
              value: res.data.liquid_gas_amount_sum,
              name: '液化气'
            })
          }
          res.data.shipAmountSummary.forEach(e => {
            if (!e.ship_name.includes('万华') && e.amount_sum !== '0') { // 剔除万华8 剔除数据为0船舶
              this.analysisData.xAxis.push(e.ship_name)
              this.analysisData.data.push(e.amount_sum)
            }
          })
          res.data.goodsAmountArray.forEach(e => {
            this.transData.push({
              value: e.goods_amount_sum,
              name: e.goods_name
            })
          })
        }
      })
      API.queryShipsAnchorInfo(this.queryParam).then(res => { // 船舶抛锚时长
        if (res.data.Code === 10000) {
          res.data.shipAnchorTimeSumArray.forEach((e, idx) => {
            if (!e.ship_name.includes('万华')) { // 剔除万华8 剔除数据为0船舶
              if (parseInt(e.anchor_time_sum) !== 0 || parseInt(res.data.shipAnchorTimeRedLineNumArray[idx].count_num) !== 0) {
                this.shipAuchorData.xAxis.push(e.ship_name)
                this.shipAuchorData.data[0].push(e.anchor_time_sum)
                this.shipAuchorData.data[1].push(res.data.shipAnchorTimeRedLineNumArray[idx].count_num)
              }
            }
          })
          // res.data.shipAnchorTimeRedLineNumArray.forEach((e, idx) => {
          //   if (!e.ship_name.includes('万华')) { // 剔除万华8 剔除数据为0船舶
          //     this.shipAuchorData.data[1].push(e.count_num)
          //   }
          //   if (parseInt(e.count_num) === 0 && parseInt(this.shipAuchorData.data[0][idx]) === 0) {
          //     this.shipAuchorData.xAxis.splice(idx, 1)
          //     this.shipAuchorData.data[0].splice(idx, 1)
          //     this.shipAuchorData.data[1].splice(idx, 1)
          //   }
          // })
        }
      })
      API.querySumOrderByPorts(this.queryParam).then(res => { // 港口抛锚时长
        if (res.data.Code === 10000) {
          res.data.portAnchorTimeSumArray.forEach(e => {
            this.portAuchorData.xAxis.push(e.port_name)
            this.portAuchorData.data[0].push(e.anchor_time_sum)
          })
          res.data.portAnchorTimeRedLineNumArray.forEach(e => {
            this.portAuchorData.data[1].push(e.port_num)
          })
        }
      })
      API.queryVoyageLineSum(this.queryParam).then(res => { // 航次数
        if (res.data.Code === 10000) {
          res.data.Result.forEach(e => {
            this.voyageNum.push({
              port_line: e.port_line,
              voyage_total: e.voyage_total
            })
          })
        }
      })
      API.queryShipsGoodsLoss(this.queryParam).then(res => { // 损耗率
        if (res.data.Code === 10000) {
          this.columns.push({
            title: '船名',
            key: 'title',
            width: 130,
            align: 'center',
            fixed: 'left'
          })
          res.data.Result.forEach((e, idx) => {
            if (!e.ship_name.includes('万华') && e.goods_loss_rate_average !== '0') { // 剔除万华8 剔除数据为0船舶
              this.lossLineData.xAxis.push(e.ship_name)
              this.lossLineData.data.push(e.goods_loss_rate_average)
              this.columns.push({
                title: e.ship_name,
                key: 'value' + idx,
                width: res.data.Result.length > 6 ? 90 : '',
                align: 'center'
              })
            }
          })
          this.resetTableData(res.data.Result, this.lossList, 0, 'voyage_total')
          this.resetTableData(res.data.Result, this.lossList, 1, 'goods_loss_rate_average')
          this.resetTableData(res.data.Result, this.lossList, 2, 'goods_loss_rate_max')
          this.resetTableData(res.data.Result, this.lossList, 3, 'goods_loss_rate_min')
        }
      })
    },
    resetTableData (arr, data, index, str) {
      switch (index) {
        case 0:
          data.push({
            title: '航次数'
          })
          break
        case 1:
          data.push({
            title: '平均损耗率(‰)'
          })
          break
        case 2:
          data.push({
            title: '最大损耗率(‰)'
          })
          break
        case 3:
          data.push({
            title: '最小损耗率(‰)'
          })
          break
        default:
          break
      }
      arr.forEach((item, idx) => {
        if (idx === 0) {
          Object.assign(data[index], {
            ['value' + idx]: item[str]
          })
        } else {
          Object.assign(data[index], {
            ['value' + idx]: item[str]
          })
        }
      })
    },
    // 清除数据
    clearData () {
      this.analysisData.xAxis = []
      this.analysisData.data = []
      this.lossLineData.xAxis = []
      this.lossLineData.data = []
      this.transData = []
      this.chartGoodsNum = 0
      this.chartTurnoverNum = 0
      this.chartVolumeNum = 0
      this.chartVoyageNum = 0
      this.chartWaitingTime = 0
      this.chartTotalLoss = 0
      this.chartAverageLossRate = 0
      this.oilTon = 0
      this.chemicalTon = 0
      this.liquidTon = 0
      this.yearPieData = []
      this.lossList = []
      this.shipAuchorData.xAxis = []
      this.shipAuchorData.data = [[], []]
      this.portAuchorData.xAxis = []
      this.portAuchorData.data = [[], []]
      this.voyageNum = []
      this.columns = []
    },
    // 获取时间区间
    getCurDate () {
      queryStatTime().then(res => {
        if (res.data.Code === 10000) {
          if (res.data.start_month && res.data.end_month) {
            this.queryParam.start_month = res.data.start_month
            this.queryParam.end_month = res.data.end_month
            this.getList()
          }
        }
      })
      // querySysDate().then(res => {
      //   if (res.data.Code === 10000 && res.data.systemDate !== '') {
      //     let _year = res.data.systemDate.substring(0, 4)
      //     this.queryParam.start_month = _year + '-01'
      //     this.queryParam.end_month = res.data.systemDate.substring(0, 7)
      //     this.getList()
      //   }
      // })
    },
    // 日期变化触发
    dateSelect (dateObj) {
      if (dateObj.length === 0) {
        this.getCurDate()
      } else {
        this.queryParam.start_month = dateObj[0] ? dateObj[0] : ''
        this.queryParam.end_month = dateObj[1] ? dateObj[1] : ''
        this.getList()
      }
    },
    // 船舶选择触发
    shipSelect () {
      this.getList()
    },
    // 报表预览
    handleReport (str) {
      this.isView = str === 'view'
      this.isExport = str === 'down'
      API.exportTeplateAll(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          if (str === 'view') { // 预览
            sessionStorage.setItem('wpsUrl', res.data.wpsUrl)
            sessionStorage.setItem('token', res.data.token)
            const jump = this.$router.resolve({ name: 'viewFile' })
            this.isView = false
            window.open(jump.href, '_blank')
          } else { // 导出
            this.isExport = false
            window.open(res.data.fileUrl, '_blank')
          }
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 详情
    showModelBtn (e) {
      if (e === 'goodsLoss') {
        this.showModelLoss = true
        this.modalData.base_month = this.queryParam.end_month
      }
      this.showHomePage = false
    },
    // 返回首页
    goBackHome (d) {
      if (d === 'goodsLoss') {
        this.showModelLoss = false
      }
      this.showHomePage = true
    }
  }
}
</script>
<style lang="less" scoped>
  .voyage_num {
    height: 230px;
    overflow-y: scroll;
    font-weight: bold;
    line-height: 35px;
    label {
      font-size: 14px;
      color: #fff;
      width: 20px;
      height: 20px;
      text-align: center;
      border-radius: 50%;
      margin-right: 5px;
      display: inline-block;
      font-weight: normal;
      vertical-align: middle;
      line-height: 20px;
      background-color: #4680ff;
    }
    span {
      float: right;
      font-weight: normal;
    }
  }
  .btn-area {
    text-align: right;
    margin-bottom: 15px;
    button {
      margin-left: 12px;
    }
    .morebtn {
      right: 25px;
      top: 20px;
      position: absolute;
      font-size: 12px;
      color: #666;
      cursor: pointer;
      font-family: Arial, Helvetica, sans-serif;
    }
  }
  .btn-area .select-ship-content {
    width: 110px;
    margin-left:12px !important;
    height: 32px !important;
  }
  .btn-area .select-ship-content .ivu-select-selection {
    height: 32px !important;
    background: #007DFF;
  }
  .btn-area .select-ship-content .ivu-select-selection .ivu-select-selected-value {
    color: #fff;
  }
  .btn-area .select-ship-content .ivu-select-selection .ivu-select-arrow {
    color: #fff;
  }
  .num-chart {
    display: inline-block;
    font-size: 16px;
    font-weight: bold;
    margin-right: 5px;
  }
  .loss_list {
    text-align: center;
    display: table;
    width: 100%;
    overflow: hidden;
    line-height: 35px;
    div {
      outline: 1px solid #f3f3f3;
      outline-offset: -1px;
    }
    label {
      display: block;
      outline: 1px solid #f3f3f3;
      outline-offset: -1px;
      background-color: #f8f8f9;
    }
    .lost_datacol {
      display: flex;
      overflow-x: auto;
      border-right: 1px solid #f3f3f3;
      .lost_data {
        flex: 1;
      }
      .ivu-col {
        height: 35px;
        width: 70px;
        white-space: nowrap;
      }
    }
  }
  .morebtn {
    color: #666;
    cursor: pointer;
    text-align: right;
    margin-bottom: 10px;
  }
</style>
