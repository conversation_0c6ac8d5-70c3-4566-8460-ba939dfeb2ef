import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 查询核对记录列表
export function queryVoyageCheckHisList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/check/info/queryStatList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 导出
export function exportTemplateInfo (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/check/info/templateInfo',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}
