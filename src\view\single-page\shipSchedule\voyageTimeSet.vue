<template>
  <div>
    <Tabs type="card" v-model="curTab" @on-click="tabChange">
      <TabPane v-for="(tab, index) in tabList" :key="index" :label="tab.label" :name="tab.name">
        <Card>
          <search @searchResults='searchResults' @selectOnChanged='selectOnChanged' :setSearch='setSearchData' @keydown.enter.native='searchResults' @resetResults='resetResults'></search>
        </Card>
        <Card style="margin-top: 20px;">
          <Table border :loading="listLoading" :columns="curTab === 'empty' ? emptyColumns : fullColumns" :data="list">
            <template slot-scope="{ row }" slot="action">
              <ButtonGroup size="small">
                <!-- <Button type="primary" icon="md-create" @click="editData(row)">编辑</Button> -->
                <Button style="margin-left: 10px;" type="error" icon="md-trash" @click="deleteData(row)">删除</Button>
              </ButtonGroup>
            </template>
          </Table>
          <Page show-sizer :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="queryParam.pageSize" :current.sync="queryParam.pageIndex"
              :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
        </Card>
      </TabPane>
      <div slot="extra">
        <Button @click="handlePortAdd" size="small" icon="md-add" type="primary">新增</Button>
        <Button style="margin-left: 10px;" size="small" icon="md-refresh" type="info" @click="refreshList">
          同步
          <Spin fix v-if="isRefresh">
            <Icon type="ios-loading" size='28' class="spin-icon-load"></Icon>
            <div>同步中...</div>
          </Spin>
        </Button>
      </div>
    </Tabs>
    <Modal v-model="isAdd" :title="addTitle" width="400" :mask-closable="false">
      <Form ref="addForm" :model="formData" :label-width="70">
        <FormItem label="船舶" prop="ship_id" :rules="curTab === 'empty' ? {} : { required: true, message: '请选择船舶', trigger: 'change' }">
          <Select filterable clearable v-model="formData.ship_id" placeholder="请选择船舶">
            <Option v-for="(item, n) in shipList" :value="item.ship_id" :key="n">{{ item.ship_name }}</Option>
          </Select>
        </FormItem>

        <FormItem label="起始港" prop="load_port_id" :rules="{ required: true, message: '请选择起始港', trigger: 'change' }">
          <Select filterable v-model="formData.load_port_id" placeholder="请选择起始港">
            <Option v-for="(item, n) in portNameList" :value="item.id" :key="n">{{ item.port_name }}</Option>
          </Select>
        </FormItem>

        <FormItem label="终到港" prop="unload_port_id" :rules="{ required: true, message: '请选择终到港', trigger: 'change' }">
          <Select filterable v-model="formData.unload_port_id" placeholder="请选择终到港">
            <Option v-for="(item, n) in portNameList" :value="item.id" :key="n">{{ item.port_name }}</Option>
          </Select>
        </FormItem>

        <FormItem label="更正耗时" prop="avg_sail_hours">
          <InputNumber v-model="formData.avg_sail_hours" style="width: 260px"></InputNumber>
          <span style="margin-left: 8px">小时</span>
        </FormItem>
      </Form>
      <div class="drawer-footer" slot="footer">
        <Button @click="handleCancel" style="margin-right: 8px">取消</Button>
        <Button type="primary" @click="handleSave">提交</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import search from '_c/search' // 查询组件
import { queryPortList } from '@/api/basicData'
import API from '@/api/shipSchedule/timeSet'

export default({
  components: {
    search
  },
  data () {
    return {
      isAdd: false,
      isRefresh: false,
      listLoading: false,
      addTitle: '',
      formData: {

      },
      shipList: [],
      portNameList: [],
      curTab: 'empty',
      editingRow: null, // 当前编辑行的ID
      originalValue: null, // 原始值，用于取消编辑时恢复
      tabList: [
        { label: '空载航时', name: 'empty' },
        { label: '重载耗时', name: 'full' }
      ],
      queryParam: {
        pageSize: 10,
        pageIndex: 1,
        port_id_positive: '',
        port_id_negative: ''
      },
      total: 0,
      setSearchData: {
        ship_id: {
          type: 'select',
          label: '船名',
          filterable: true,
          selectData: [],
          selected: '',
          placeholder: '请选择船舶',
          selectName: '',
          width: 130,
          value: ''
        },
        port_id_positive: {
          type: 'select',
          label: '起始港',
          filterable: true,
          selectData: [],
          selected: '',
          placeholder: '请选择起始港',
          selectName: '',
          width: 130,
          value: ''
        },
        port_id_negative: {
          type: 'select',
          label: '终到港',
          filterable: true,
          selectData: [],
          selected: '',
          placeholder: '请选择终到港',
          selectName: '',
          width: 130,
          value: ''
        }
      },
      fullColumns: [
        { title: '船舶', key: 'ship_name', align: 'center' },
        { title: '起始港', key: 'port_name_from', align: 'center' },
        { title: '终到港', key: 'port_name_to', align: 'center' },
        { title: '船队平均时长',
          key: 'avg_carrier_hours',
          align: 'center',
          render: (h, params) => {
            return h('div', {}, parseFloat(params.row.avg_carrier_hours).toFixed(2))
          } },
        { title: '单船平均时长',
          key: 'ship_avg_carrier_hours',
          align: 'center',
          render: (h, params) => {
            return h('div', {}, parseFloat(params.row.ship_avg_carrier_hours).toFixed(2))
          } },
        { title: '更正耗时',
          key: 'modify_avg_carrier_hours',
          align: 'center',
          render: (h, params) => {
          // 获取当前行数据
            const row = params.row
            // 判断当前行是否处于编辑状态
            const isEditing = this.editingRow === row.port_carrier_time_id

            if (isEditing) {
            // 编辑状态：显示输入框和保存图标
              return h('div', {
                style: {
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center'
                }
              }, [
                h('i-input', {
                  props: {
                    value: row.modify_avg_carrier_hours || '',
                    type: 'number'
                  },
                  on: {
                    input: (value) => {
                    // 更新数据
                      this.$set(row, 'modify_avg_carrier_hours', value)
                    }
                  }
                }),
                h('Icon', {
                  props: {
                    type: 'md-checkmark',
                    size: 18
                  },
                  style: {
                    marginLeft: '5px',
                    cursor: 'pointer',
                    color: 'green'
                  },
                  on: {
                    click: () => this.saveData(row)
                  }
                })
              ])
            } else {
            // 非编辑状态：显示数据和编辑图标
              let modify_avg_carrier_hours = row.modify_avg_carrier_hours ? parseFloat(row.modify_avg_carrier_hours).toFixed(2) : '-'
              return h('div', [
                h('span', modify_avg_carrier_hours),
                h('Icon', {
                  props: {
                    type: 'md-brush',
                    size: 18
                  },
                  style: {
                    marginLeft: '5px',
                    cursor: 'pointer',
                    color: '#2d8cf0'
                  },
                  on: {
                    click: () => this.editData(row)
                  }
                })
              ])
            }
          } },
        { title: '操作', slot: 'action', width: 180, align: 'center' }
      ],
      emptyColumns: [
        { title: '船舶', key: 'ship_name', align: 'center' },
        { title: '起始港', key: 'port_name_positive', align: 'center' },
        { title: '终到港', key: 'port_name_negative', align: 'center' },
        { title: '船队平均时长',
          key: 'avg_sail_hours',
          align: 'center',
          render: (h, params) => {
            return h('div', {}, parseFloat(params.row.avg_sail_hours).toFixed(2))
          } },
        { title: '单船平均时长',
          key: 'ship_avg_carrier_hours',
          align: 'center',
          render: (h, params) => {
            return h('div', {}, parseFloat(params.row.ship_avg_sail_hours).toFixed(2))
          } },
        { title: '更正耗时',
          key: 'modify_avg_sail_hours',
          align: 'center',
          render: (h, params) => {
          // 获取当前行数据
            const row = params.row
            // 判断当前行是否处于编辑状态
            const isEditing = this.editingRow === row.port_sail_time_id

            if (isEditing) {
            // 编辑状态：显示输入框和保存图标
              return h('div', {
                style: {
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center'
                }
              }, [
                h('i-input', {
                  props: {
                    value: row.modify_avg_sail_hours || '',
                    type: 'number'
                  },
                  on: {
                    input: (value) => {
                    // 更新数据
                      this.$set(row, 'modify_avg_sail_hours', value)
                    }
                  }
                }),
                h('Icon', {
                  props: {
                    type: 'md-checkmark',
                    size: 18
                  },
                  style: {
                    marginLeft: '5px',
                    cursor: 'pointer',
                    color: 'green'
                  },
                  on: {
                    click: () => this.saveData(row)
                  }
                })
              ])
            } else {
            // 非编辑状态：显示数据和编辑图标
              let modify_avg_sail_hours = row.modify_avg_sail_hours ? parseFloat(row.modify_avg_sail_hours).toFixed(2) : '-'
              return h('div', [
                h('span', modify_avg_sail_hours),
                h('Icon', {
                  props: {
                    type: 'md-brush',
                    size: 18
                  },
                  style: {
                    marginLeft: '5px',
                    cursor: 'pointer',
                    color: '#2d8cf0'
                  },
                  on: {
                    click: () => this.editData(row)
                  }
                })
              ])
            }
          } },
        { title: '操作', slot: 'action', width: 180, align: 'center' }
      ],
      list: []
    }
  },
  created () {
    this.getBaseData()
    this.getList()
  },
  methods: {
    getList () {
      this.list = []
      this.listLoading = true
      if (this.curTab === 'empty') { // 空载列表
        API.queryStatPortToPortSailTimePage(this.queryParam).then(res => {
          this.listLoading = false
          if (res.data.Code === 10000) {
            this.list = res.data.Result
            this.total = res.data.Total
          } else {
            this.$Message.warning(res.data.Message)
          }
        })
      }
      if (this.curTab === 'full') { // 承运列表
        API.queryStatPortToPortCarrierTimePage(this.queryParam).then(res => {
          this.listLoading = false
          if (res.data.Code === 10000) {
            this.list = res.data.Result
            this.total = res.data.Total
          } else {
            this.$Message.warning(res.data.Message)
          }
        })
      }
    },
    // 同步数据
    refreshList () {
      this.isRefresh = true
      if (this.curTab === 'empty') {
        API.batchAddStatPortToPortSailTimeHistory().then(res => {
          this.isRefresh = false
          if (res.data.Code === 10000) {
            this.getList()
            this.$Message.success(res.data.Message)
          } else {
            this.$Message.warning(res.data.Message)
          }
        })
      }
      if (this.curTab === 'full') {
        API.batchAddStatPortToPortCarrierTimeHistory().then(res => {
          this.isRefresh = false
          if (res.data.Code === 10000) {
            this.getList()
            this.$Message.success(res.data.Message)
          } else {
            this.$Message.warning(res.data.Message)
          }
        })
      }
    },
    // 获取基础数据
    getBaseData () {
      this.shipList = JSON.parse(localStorage.getItem('shipNameList')) || []
      this.setSearchData.ship_id.selectData = this.shipList.filter(list => list.business_model === '1').filter(t => !t.ship_name.includes('善')).map(item => {
        return {
          label: item.ship_name,
          value: item.ship_id
        }
      })
      // 获取港口
      queryPortList(this.loadPortListQuery).then(res => {
        if (res.data.Code === 10000) {
          this.portNameList = res.data.Result
          this.setSearchData.port_id_negative.selectData = this.setSearchData.port_id_positive.selectData = this.portNameList.map(item => {
            return {
              label: item.port_name,
              value: item.id
            }
          })
        }
      })
    },
    handleSave () {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          let _param = {}
          if (this.curTab === 'empty') { // 空载新增
            Object.assign(_param, {
              ship_id: this.formData.ship_id,
              port_id_positive: this.formData.load_port_id,
              port_id_negative: this.formData.unload_port_id,
              modify_avg_sail_hours: this.formData.avg_sail_hours
            })
            API.addStatPortToPortSailTime(_param).then(res => {
              if (res.data.Code === 10000) {
                this.$Message.success(res.data.Message)
                this.resetForm()
                this.getList()
              } else {
                this.$Message.error(res.data.Message)
              }
            })
          }
          if (this.curTab === 'full') { // 承运新增
            Object.assign(_param, {
              ship_id: this.formData.ship_id,
              port_id_from: this.formData.load_port_id,
              port_id_to: this.formData.unload_port_id,
              modify_avg_carrier_hours: this.formData.avg_sail_hours
            })
            API.addStatPortToPortCarrierTime(_param).then(res => {
              if (res.data.Code === 10000) {
                this.$Message.success(res.data.Message)
                this.resetForm()
                this.getList()
              } else {
                this.$Message.error(res.data.Message)
              }
            })
          }
        }
      })
    },
    handleCancel () {
      this.isAdd = false
      this.resetForm()
    },
    resetForm () {
      this.formData = {
        ship_id: '',
        load_port_id: '',
        unload_port_id: '',
        avg_sail_hours: 0
      }
      if (this.$refs.addForm) {
        this.$refs.addForm.resetFields()
      }
    },
    // 修改编辑
    editData (row) {
      // 记录当前编辑行的ID
      this.editingRow = this.curTab === 'empty' ? row.port_sail_time_id : row.port_carrier_time_id
      // 记录原始值，用于取消编辑时恢复
      this.originalValue = this.curTab === 'empty' ? row.modify_avg_sail_hours : row.modify_avg_carrier_hours
    },
    // 保存修改
    saveData (row) {
      this.editingRow = null
      const newVal = this.curTab === 'empty' ? row.modify_avg_sail_hours : row.modify_avg_carrier_hours
      if (newVal === this.originalValue) return // 数据没改变就不走接口
      if (this.curTab === 'empty') {
        API.updateStatPortToPortSailTime(row).then(res => {
          if (res.data.Code === 10000) {
            this.getList()
            this.$Message.success(res.data.Message)
          } else {
            this.$Message.warning(res.data.Message)
          }
        })
      }
      if (this.curTab === 'full') {
        API.updateStatPortToPortCarrierTime(row).then(res => {
          if (res.data.Code === 10000) {
            this.getList()
            this.$Message.success(res.data.Message)
          } else {
            this.$Message.warning(res.data.Message)
          }
        })
      }
    },
    deleteData (row) {
      this.$Modal.confirm({
        title: '确认删除',
        content: `确定要删除这条数据吗？`,
        onOk: () => {
          if (this.curTab === 'empty') {
            API.delStatPortToPortSailTime({ port_sail_time_id: row.port_sail_time_id }).then(res => {
              if (res.data.Code === 10000) {
                this.$Message.success(res.data.Message)
                this.getList()
              } else {
                this.$Message.warning(res.data.Message)
              }
            })
          }
          if (this.curTab === 'full') {
            API.delStatPortToPortCarrierTime({ port_carrier_time_id: row.port_carrier_time_id }).then(res => {
              if (res.data.Code === 10000) {
                this.$Message.success(res.data.Message)
                this.getList()
              } else {
                this.$Message.warning(res.data.Message)
              }
            })
          }
        }
      })
    },
    handlePortAdd () {
      this.isAdd = true
      if (this.curTab === 'empty') {
        this.addTitle = '空载航时新增'
      }
      if (this.curTab === 'full') {
        this.addTitle = '重载耗时新增'
      }
    },
    tabChange (name) {
      this.curTab = name
      this.queryParam.pageIndex = 1
      this.getList()
    },
    // 查询
    searchResults (e) {
      if (e.target) delete e.target
      this.queryParam.pageIndex = 1

      if (this.curTab === 'empty') Object.assign(this.queryParam, e)
      if (this.curTab === 'full') {
        Object.assign(this.queryParam, {
          ship_id: e.ship_id,
          port_id_from: e.port_id_positive,
          port_id_to: e.port_id_negative
        })
      }
      this.getList()
    },
    selectOnChanged (e) {

    },
    // 重置查询条件
    resetResults () {
      this.setSearchData.ship_id.selected = ''
      this.setSearchData.port_id_positive.selected = ''
      this.setSearchData.port_id_negative.selected = ''
      this.queryParam = { // 列表请求参数
        pageSize: 10,
        pageIndex: 1
      }
      this.getList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.queryParam.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.queryParam.pageIndex = val
      this.getList()
    }
  }
})
</script>
<style>
  .drawer-footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    border-top: 1px solid #e8e8e8;
    padding: 6px 16px;
    text-align: right;
    left: 0;
    background: #fff;
  }
  .spin-icon-load{
    animation: ani-spin 1s linear infinite;
  }
  @keyframes ani-spin {
    from { transform: rotate(0deg);}
    50%  { transform: rotate(180deg);}
    to   { transform: rotate(360deg);}
  }
</style>
