<template>
  <div class="ais_area" :class="collapsed ? 'map-box-collapsed' : 'map-box'">
    <fullscreen ref="fullscreen" @change="fullscreenChange">
      <!-- 地图 -->
      <div id="map"></div>
      <MaskCanvas :map="map" ref="maskCanvas" @hideOtherArea="hideOtherArea"/>
      <MarkerDraw ref="markerDrawer" :map="map"></MarkerDraw>
      <!-- 搜索船舶 -->
      <div class="select-box" v-if="!fullscreen">
        <Select class="select-box-content" :transfer="false" placeholder="搜索船舶码头港口"
                clearable filterable remote
                :remote-method="getShipList"
                :loading="selectLoading"
                @on-change="shipSelect"
                @on-clear="queryClear"
                @on-query-change="queryChange">
                <OptionGroup label="船舶">
                  <Option v-for="(item, index) in shipList" :value="item.value" :label="item.label" :key="index">
                    <span>{{ item.label }}</span>
                    <span style="font-size: 12px; color: #999; margin-left: 5px;">mmsi: {{ item.value.split('__')[0]}} {{ shipTypeTrans(item.type) }}</span>
                    <Icon v-if="item.id" type="md-close" @click.stop="delHis(item.id)" style="float: right;"></Icon>
                  </Option>
                </OptionGroup>
                <OptionGroup label="港口">
                  <Option v-for="(item, index) in searchPortList" :value='`{"id": "${item.id}","type":1,"name":"${item.name}","latitude":${item.latitude},"longitude":${item.longitude},"level":12}`' :label="item.port_name" :key="index">
                    <span>{{ item.name }}</span>
                    <Icon type="md-close" @click.stop="delHis(item.uid)" style="float: right;"></Icon>
                  </Option>
                </OptionGroup>
                <OptionGroup label="码头">
                  <Option v-for="(item, index) in searchWharfList" :value='`{"id": "${item.id}","type":2,"name":"${item.name}","latitude":${item.latitude},"longitude":${item.longitude},"level":14}`' :label="item.wharf_name" :key="index">
                    <span>{{ item.name }}</span>
                    <Icon type="md-close" @click.stop="delHis(item.uid)" style="float: right;"></Icon>
                  </Option>
                </OptionGroup>
        </Select>
        <span class="select-search-btn">
          <Icon type="md-search" size="24" color="#fff"></Icon>
        </span>
      </div>
      <!-- 功能按钮 -->
      <ButtonGroup class="tool-box"> <!--v-if="!fullscreen"-->
        <Tooltip :content="isShipShow ? '隐藏船舶' : '显示船舶'" placement="bottom">
          <div class="full-screen-btn">
            <Icon style="cursor: pointer; line-height: 30px;" @click.native="shipShowHide" :type="isShipShow ? 'md-eye' : 'md-eye-off'" :size="23"></Icon>
          </div>
        </Tooltip>
        <Button :type="isSignShow ? 'primary' : 'default'" icon="md-pricetag" style="margin-right: 5px;" @click="signBtnClick">标记</Button>
        <Button :type="isToolShow ? 'primary' : 'default'" icon="ios-briefcase" style="margin-right: 5px;" @click="showTool">工具</Button>
        <!-- <Button :type="isShipTypeSearch ? 'primary' : 'default'" icon="ios-boat" style="margin-right: 5px;" @click="searchBoat">找船</Button> -->
        <!-- <Button :type="toolStr === '港口' ? 'primary' : 'default'" @click="toolChange('港口')">港口</Button> -->
        <!-- <Button :type="toolStr === '预报' ? 'primary' : 'default'" @click="toolChange('预报')">预报</Button> -->
        <Button :type="toolStr === '潮汐' ? 'primary' : 'default'" @click="toolChange('潮汐')">潮汐</Button>
        <!-- <Button :type="toolStr === '气温' ? 'primary' : 'default'" @click="toolChange('气温')">气温</Button> -->
        <!-- <Button :type="toolStr === '洋流' ? 'primary' : 'default'" @click="toolChange('洋流')">洋流</Button> -->
        <Button :type="toolStr === '台风' ? 'primary' : 'default'" @click="toolChange('台风')">台风</Button>
        <Button type='default' @click="showShipDetailList" style="margin-left: 5px;">公司船舶列表</Button>
        <!-- <Select class="select-ship-content" :transfer="false" v-model="nowCheckedShip" placeholder="公司船舶列表"
                clearable @on-change="shipSelectList" @on-open-change="selectOpenClose">
          <Row class="datalisthead">
            <Col span="6"  style="text-align: center;">船名</Col>
            <Col span="7" style="text-align: center;">目的港</Col>
            <Col span="5" style="text-align: center;">
              状态
              <Icon style="cursor: pointer;" :type="isUp ? 'md-arrow-dropup' : 'md-arrow-dropdown'" @click="statusChange"></Icon>
            </Col>
            <Col span="6" style="text-align: center;">预警(h)</Col>
          </Row>
          <Option v-for="(item, index) in selfShipList" :value="item.mmsi" :key="index">
            <Row justify="center" align="middle">
              <Col span="6" style="text-align: center;">
                <div>{{ item.ship_name }}</div>
              </Col>
              <Col span="7" style="text-align: center;">
                <div>{{ item.port_name }}</div>
              </Col>
              <Col span="5" style="text-align: center;">
                <div>{{ item.navistatus }}</div>
              </Col>
              <Col span="6" style="text-align: center;">
                <div :style="getDelayStyle(item.delayHour)">{{ item.delayHour }}</div>
              </Col>
            </Row>
          </Option>
        </Select> -->
        <Tooltip :content="fullscreen ? '退出全屏' : '全屏'" placement="bottom">
          <div class="full-screen-btn">
            <Icon style="cursor: pointer; line-height: 30px;" @click.native="toggleFullScreen" :type="fullscreen ? 'md-contract' : 'md-expand'" :size="23"></Icon>
          </div>
        </Tooltip>
      </ButtonGroup>
      <!-- 信息展示框 -->
      <div class="home-modal" :transfer="false" v-if="showShipDetail && !isDynamicShow && !shipDrawerDetailShow">
        <Form label-position="right" :label-width="70">
          <div class="modal-header">
            <span>{{ shipdetaildata.cnname }}</span>
            <span class="modal-header-en">
              {{ triShipDetail.mapbase_vessel_info_vessel_name_cn || shipdetaildata.name }}
              <span class="ship_detail_mt">({{triShipDetail.mapbase_vessel_info_estimate_load ? triShipDetail.mapbase_vessel_info_estimate_load / 100 + 'MT' : ''}})</span>
            </span>
            <Icon class="modal-header-close" @click.native="cancelModal" type="md-close" color="#fff"></Icon>
          </div>
          <Divider v-if="isVoyageShow" class="padding: 0 10px;" orientation="left">航次信息</Divider>
          <div class="voyage-box" @click="redirectVoyage">
            <Row v-if="isVoyageShow">
              <Col class="voyage-port" span="9">
                <div class="port_con">
                  <div class="after-port" v-for="(item, index) in curVoyage.curVoyageLoadport" :key="index">
                    <!-- {{ item.wharf_name || item.port_name}} -->
                    <div>{{ item.port_name }}</div>
                    <div style="font-size: 14px; font-weight: 100;">{{ item.wharf_name }}</div>
                  </div>
                </div>
              </Col>
              <Col span="6" class="voyage-line">
                <div style="margin-bottom: -5px; width: 100%;display: inline-block;">{{ curVoyage.result.voyage_no }}</div>
                <img :src="arrowBlue" alt="">
                <div v-for="(item, index) in curVoyage.curVoyageCargo" :key="index">{{ item.goods_name }} - {{ item.amounts }}T</div>
              </Col>
              <Col class="voyage-port" span="9">
                <div class="port_con">
                  <div class="after-port" v-for="(item, index) in curVoyage.curVoyageUnloadport" :key="index">
                    <!-- {{ item.wharf_name || item.port_name}} -->
                    <div>{{ item.port_name }}</div>
                    <div style="font-size: 14px; font-weight: 100;">{{ item.wharf_name }}</div>
                  </div>
                </div>
              </Col>
            </Row>
            <Row v-if="!isVoyageShow && triShipDetail && triShipDetail.trace" style="margin-top: 10px;">
              <Col class="voyage-port" span="9">
                <div class="port_con">
                  <div class="after-port">
                    <div>{{ triShipDetail.trace.origin_port_name || '-' }}</div>
                    <div style="font-size: 14px; font-weight: 100;">{{ triShipDetail.trace.origin_terminals_name || '' }}</div>
                  </div>
                </div>
              </Col>
              <Col span="6" class="voyage-line">
                <div style="margin-bottom: -5px; width: 100%;display: inline-block;">{{ shipStatus || '' }}</div>
                <img :src="arrowBlue" alt="">
                <!-- <div>{{ triShipDetail.realtime.speed === '' ? '' : `航速：${triShipDetail.realtime.speed}kn` }}</div> -->
              </Col>
              <Col class="voyage-port" span="9">
                <div class="port_con">
                  <div class="after-port">
                    <div>
                      {{ triShipDetail.trace.destination_port_name || '-' }}
                      <span v-if="(isTriDetail && triShipDetail.trace.load_prediction)" class="load_text">{{ (isTriDetail && triShipDetail.trace.load_prediction) ? triShipDetail.trace.load_prediction === 'LOAD' ? '装' : '卸' : '' }}</span>
                    </div>
                    <div style="font-size: 14px; font-weight: 100;">{{ triShipDetail.trace.destination_terminals_name || '' }}</div>
                    <div style="font-size: 12px; font-weight: 100;">{{ berthName }}</div>
                  </div>
                </div>
              </Col>
            </Row>
            <Row v-if="isVoyageShow">
              <div style="padding-left: 15px;" v-if="curVoyage.result.node_date !== '' || curVoyage.result.dynamic_name !== ''" class="dynamicdataclass">
                <span class="graysize" v-if="curVoyage.result.dynamic_port_name !== ''">{{curVoyage.result.dynamic_port_name}}</span>
                <span>{{curVoyage.result.node_date.toString().substring(5)}}</span>
                <span class="greensize"> {{curVoyage.result.node_name}} </span>
                <div>{{ curVoyage.result.dynamic_name }}</div>
                <!-- <div v-if="curVoyage.dynamicData.PortName !== '' && (curVoyage.dynamicData.dynamic_node === '1' || curVoyage.dynamicData.dynamic_node === '2')">
                  <div v-if="curVoyage.dynamicData.expect_date !== ''">预计 {{curVoyage.dynamicData.expect_date.toString().substring(5)}} 到达 <span class="blacksize">{{curVoyage.dynamicData.PortName}}港</span></div>
                  <div v-if="curVoyage.dynamicData.expect_date === ''">前往 <span class="blacksize">{{curVoyage.dynamicData.PortName}}港</span></div>
                </div>
                <div v-if="['3', '7', '11', '17', '18', '19', '20', '21'].indexOf(curVoyage.dynamicData.dynamic_node) !== '-1'">
                  {{curVoyage.dynamicData.dynamic_context}}
                </div>
                <div v-if="curVoyage.dynamicData.dynamic_node === '9' && curVoyage.dynamicData.wharfName !== '' && curVoyage.dynamicData.berthName !== ''">
                  前往 {{curVoyage.dynamicData.wharfName}} 码头 {{curVoyage.dynamicData.berthName}} 泊位
                </div>
                <div v-if="curVoyage.dynamicData.dynamic_node === '10' && curVoyage.dynamicData.wharfName !== '' && curVoyage.dynamicData.berthName !== ''">
                  靠泊 {{curVoyage.dynamicData.wharfName}} 码头 {{curVoyage.dynamicData.berthName}} 泊位
                </div>
                <div v-if="curVoyage.dynamicData.expect_date !== '' && curVoyage.dynamicData.PortName !== '' && curVoyage.dynamicData.dynamic_node === '31'">
                  <div>预计 {{curVoyage.dynamicData.expect_date.toString().substring(5)}} 到达 <span class="blacksize">{{curVoyage.dynamicData.PortName}}港</span></div>
                </div> -->
              </div>
            </Row>
          </div>
          <Divider v-if="isPlanVoyageShow" style="padding: 0 10px;" orientation="left">计划航次</Divider>
          <div v-if="isPlanVoyageShow">
            <div v-for="plan in planVoyage" :key="plan.id" class="voyage-box box-plan">
              <div style="margin-left: 20px; font-size: 12px;">
                <span style="margin-right: 10px;">{{ plan.result.voyage_no }}</span>
                <span v-for="(item, index) in plan.planVoyageCargo" :key="index" style="margin-right: 10px;">{{ item.goods_name }} - {{ item.amounts }}T</span>
              </div>
              <Row>
                <Col class="voyage-port" span="9">
                  <div class="port_con">
                    <div style="color: #777;" class="after-port" v-for="(item, index) in plan.planVoyageLoadport" :key="index">
                      <!-- {{ item.wharf_name || item.port_name}} -->
                      <div style="font-size: 16px;">{{ item.port_name }}</div>
                      <div style="font-size: 12px; font-weight: 100;">{{ item.wharf_name }}</div>
                    </div>
                  </div>
                </Col>
                <Col span="6" class="voyage-line" style="margin-top: 18px;">
                  <img :src="arrowGrey" alt="">
                </Col>
                <Col style="color: #777;" class="voyage-port" span="9">
                  <div class="port_con">
                    <div class="after-port" v-for="(item, index) in plan.planVoyageUnloadport" :key="index">
                      <!-- {{ item.wharf_name || item.port_name}} -->
                      <div style="font-size: 16px;">{{ item.port_name }}</div>
                      <div style="font-size: 12px; font-weight: 100;">{{ item.wharf_name }}</div>
                    </div>
                  </div>
                </Col>
              </Row>
            </div>
          </div>
          <Divider style="padding: 0 10px;" orientation="left">AIS信息</Divider>
          <Row>
            <Col span="12"><FormItem label="MMSI:"><span>{{shipdetaildata.mmsi}}</span></FormItem></Col>
            <Col span="12"><FormItem label="船首向:"><span>{{shipdetaildata.hdg}}度</span></FormItem></Col>
          </Row>
          <Row>
            <Col span="12"><FormItem label="呼号:"><span>{{shipdetaildata.callsign}}</span></FormItem></Col>
            <Col span="12"><FormItem label="航迹向:"><span>{{shipdetaildata.cog}}度</span></FormItem></Col>
          </Row>
          <Row>
            <Col span="12"><FormItem label="IMO:"><span>{{shipdetaildata.imo?shipdetaildata.imo:"空"}}</span></FormItem></Col>
            <Col span="12"><FormItem label="航速:"><span>{{shipdetaildata.sog}}节</span></FormItem></Col>
          </Row>
          <Row>
            <Col span="12"><FormItem label="类型:"><span>{{shipdetaildata.type?shipdetaildata.type:"无"}}</span></FormItem></Col>
            <Col span="12"><FormItem label="经度:"><span>{{shipdetaildata.lng}} E</span></FormItem></Col>
          </Row>
          <Row>
            <Col span="12"><FormItem label="状态:"><span>{{shipdetaildata.navistatus || '-'}}</span></FormItem></Col>
            <Col span="12"><FormItem label="纬度:"><span>{{shipdetaildata.lat}} N</span></FormItem></Col>
          </Row>
          <Row>
            <Col span="12"><FormItem label="船长:"><span>{{shipdetaildata["length"]}}米</span></FormItem></Col>
            <Col span="12"><FormItem label="目的地:"><span>{{shipdetaildata.dest_std || shipdetaildata.dest || '-'}}</span></FormItem></Col>
          </Row>
          <Row>
            <Col span="12"><FormItem label="船宽:"><span>{{shipdetaildata.width}}米</span></FormItem></Col>
            <Col span="12"><FormItem label="预抵时间:"><span>{{shipdetaildata.eta_std || shipdetaildata.eta}}</span></FormItem></Col>
          </Row>
          <Row>
            <Col span="12"><FormItem label="吃水:"><span>{{shipdetaildata.draught}}米</span></FormItem></Col>
            <Col span="12"><FormItem label="更新时间:"><span>{{shipdetaildata.lasttime}}</span></FormItem></Col>
          </Row>
        </Form>
        <div class="modal-footer">
          <Button type="primary" @click="trackShowHide">轨迹</Button>
          <Button type="primary" @click="getHistoryVoyage">历史航次</Button>
          <Button type="primary" @click="getShipDetail" :disabled="!triShipDetail">船舶资料</Button>
        </div>
      </div>
      <!-- 轨迹查询 -->
      <div v-show="isTrackPanel" class="track-panel-box">
        <div class="modal-header">
          <span>轨迹查询</span>
          <Icon class="track-panel-close" size="20" @click.native="isTrackPanel = false" type="md-close" color="#fff"></Icon>
        </div>
        <DatePicker :value="dateArr" :transfer="true" :start-date="startDate" style="margin: 10px; width: 280px;" :options="dateOptions" type="datetimerange" @on-change="dateChange" placeholder="选择日期"></DatePicker>
        <Button type="info" @click="searchTrack">查询</Button>
        <Button type="info" @click="clearTrack" style="margin-left: 5px;">清除</Button>
      </div>
      <!-- 船舶详情列表 -->
      <div class="home-modal" style="left: 470px;" :transfer="false" v-if="isShipDetailShow && triShipDetail">
        <Form label-position="right" :label-width="120">
          <div class="modal-header">
            <span>船舶资料</span>
            <Icon class="modal-header-close" @click.native="cancelShipDetailModal" type="md-close" color="#fff"></Icon>
          </div>
          <Row>
            <Col span="12"><FormItem label="类型："><span>{{ triShipTypeTrans(triShipDetail.mapbase_vessel_info_type) }}</span></FormItem></Col>
            <Col span="12"><FormItem label="建造年份："><span>{{ triShipDetail.mapbase_vessel_info_build_date }}</span></FormItem></Col>
          </Row>
          <Row>
            <Col span="12"><FormItem label="船长(米)："><span>{{ triShipDetail.mapbase_vessel_info_length / 1000 }}</span></FormItem></Col>
            <Col span="12"><FormItem label="船宽(米)："><span>{{ triShipDetail.mapbase_vessel_info_width / 1000 }}</span></FormItem></Col>
          </Row>
          <Row>
            <Col span="12"><FormItem label="最大吃水(米)："><span>{{ triShipDetail.mapbase_vessel_info_max_draft / 1000 }}</span></FormItem></Col>
            <Col span="12"><FormItem label="总吨(t)："><span>{{ triShipDetail.mapbase_vessel_info_gross_weight }}</span></FormItem></Col>
          </Row>
          <Row>
            <Col span="12"><FormItem label="净吨(t)："><span>{{ triShipDetail.mapbase_vessel_info_net_weight }}</span></FormItem></Col>
            <Col span="12"><FormItem label="载重吨(t)："><span>{{ triShipDetail.mapbase_vessel_info_estimate_load / 100 }}</span></FormItem></Col>
          </Row>
          <Row>
            <FormItem label="曾用名："><span>{{ triShipDetail.mapbase_vessel_info_used_name }}</span></FormItem>
          </Row>
          <Row>
            <FormItem label="船舶经营者："><span>{{ triShipDetail.mapbase_vessel_info_shipmanager_ch || triShipDetail.mapbase_vessel_info_operator }}</span></FormItem>
          </Row>
          <Row>
            <FormItem label="船舶所有者："><span>{{ triShipDetail.mapbase_vessel_info_shipowner_ch || triShipDetail.mapbase_vessel_info_owner }}</span></FormItem>
          </Row>
          <Row>
            <FormItem label="联系方式："><span>{{ triShipDetail.mapbase_vessel_info_call }}</span></FormItem>
          </Row>
          <Row>
            <FormItem label="船舶所有者地址："><span>{{triShipDetail.mapbase_vessel_info_owneraddress_ch || triShipDetail.mapbase_vessel_info_owneraddress_en}}</span></FormItem>
          </Row>
          <Row>
            <FormItem label="船舶管理者："><span>{{triShipDetail.mapbase_vessel_info_shipmanager_ch}}</span></FormItem>
          </Row>
          <Row>
            <FormItem label="船舶管理者地址："><span>{{triShipDetail.mapbase_vessel_info_shipmanageraddress_ch || triShipDetail.mapbase_vessel_info_owneraddress_en}}</span></FormItem>
          </Row>
          <Row>
            <FormItem label="备注："><span>{{ triShipDetail.mapbase_vessel_info_remark || triShipDetail.personal_vessel_remark}}</span></FormItem>
          </Row>
        </Form>
      </div>
      <!-- 航次历史列表 -->
      <div v-if="isVoyagePanel" class="voyage-panel-box">
        <div class="modal-header">
          <span>历史航次<span style="color: #cecece; font-size: 12px; margin-left: 10px;">(点击查看轨迹)</span></span>
          <Icon class="modal-header-close" @click.native="voyageTableClose" type="md-close" color="#fff"></Icon>
        </div>
        <Table stripe border highlight-row max-height="280" :loading="voyageLoading" @on-row-click="voyageCurClick" :columns="isSelfVoyage ? selfVoyageColumn : voyageColumn" :data="voyageList"></Table>
      </div>
      <div class="ship_search_box" v-if="isToolShow">
        <div v-if="isShipSearch" class="ship_search_main" style="padding: 10px;">
          <div class="search_title">
            <span>工具箱</span>
            <Upload action="" :before-upload="handleInnerUpload" style="margin-left: 20px;">
              <Button icon="ios-cloud-upload-outline" size="small">国内航线</Button>
            </Upload>
            <Upload action="" :before-upload="handleNationUpload" style="margin-left: 8px;">
              <Button icon="ios-cloud-upload-outline" size="small">国际航线</Button>
            </Upload>
          </div>
          <CheckboxGroup v-model="toolShowName" @on-change="toolCheckChange">
            <Checkbox :label="item" v-for="item in toolBtnList" :key="item">
              <span>{{ item }}</span>
            </Checkbox>
          </CheckboxGroup>
        </div>
      </div>
      <!-- 找船搜索框 -->
      <div class="ship_search_box" v-if="isShipTypeSearch">
        <div v-if="isShipSearch" class="ship_search_main" style="padding: 10px;">
          <div class="search_title">
            <span>船舶类型</span>
            <Icon class="ship_search_table_close" type="md-close" @click="shipSearchTypeClose"/>
          </div>
          <CheckboxGroup v-model="shipTypeStr">
            <Checkbox :label="item.entry_key" v-for="item in shipTypeList" :key="item.id">
              <span>{{ item.entry_name }}</span>
            </Checkbox>
          </CheckboxGroup>
          <Divider />
          <div class="search_title">载重吨</div>
          <Row>
            <Col span="8">
              <Input v-model="deadweight_tonnage_from" placeholder="请输入载重吨范围" type="number"/>
            </Col>
            <Col span="1" offset="1"><span style="color: #D8D8D8;">_</span></Col>
            <Col span="8">
              <Input v-model="deadweight_tonnage_to" placeholder="请输入载重吨范围" type="number"/>
            </Col>
          </Row>
          <div class="search_submit">
            <Button class="search_reset" @click="searchReset">重置</Button>
            <Button type="primary" @click="searchSubmit">提交</Button>
          </div>
        </div>
        <div v-if="!isShipSearch" class="ship_search_table">
          <div class="ship_search_table_title">
            <span>搜索结果</span>
            <Icon class="ship_search_table_close" type="md-close" @click="shipSearchClose"/>
          </div>
          <div style="padding: 10px;">
            <Table :columns="searchTypeColumn" :data="searchTypeList" @on-row-click="searchShipTableClick"></Table>
            <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.searchParam.pageSize" :current.sync="searchCurrent"
            :total="searchShipTotal" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
          </div>
        </div>
      </div>
      <div class="voyage-sign-box"> <!-- v-if="isSignShow"-->
        <div class="modal-header" v-if="isSignShow">
          <span>标注</span>
          <Icon class="modal-header-close" @click.native="signTableClose" type="md-close" color="#fff"></Icon>
        </div>
        <DrawTool :map="map" :isShow="isSignShow"></DrawTool>
      </div>
      <homeDynamicDraw ref="homeDynamicDrawer"></homeDynamicDraw>
      <TideModal :modalData="tideData"></TideModal>
      <Modal v-model="isDynamicShow" title="船舶动态" :width="isMoreDynamicShow ? 650 : 435" @on-cancel="dynamicHide" closable :transfer="false" :mask="false" :styles="fullscreen ? {position: 'absolute', top: '45px', right: '10px'} : {position: 'absolute', top: '100px', right: '10px'}">
        <Icon class="dynamic_arrow" size="20" :type="isMoreDynamicShow ? 'ios-arrow-forward' : 'ios-arrow-back'" @click="moreDynamicShow"></Icon>
        <Table class="dynamic_table" :loading="dynamicLoading" :columns="dynamicColumns" :data="dynamicTableData" :height="fullscreen ? (winHeight + 60) : winHeight" @on-row-click="dynamicRowClick"></Table>
        <div slot="footer" style="display: none;"></div>
      </Modal>
      <Drawer class="ship_detail_drawer" v-if="shipDrawerDetailShow" v-model="shipDrawerDetailShow" inner :transfer="false" :mask="false" width="600">
        <ShipMessDetail :isAis="shipDrawerDetailShow" :map="map" @shipDetailSelect="shipSelect" @shipDetailReload="showShipDetailList"></ShipMessDetail>
      </Drawer>
    </fullscreen>
    <Spin v-if="voyageLoading" style="z-index: 99999;" size="large" fix ></Spin>
  </div>
</template>

<script>
// import PortAPI from '@/api/ais/mask'
import config from '@/config'
import { getToken } from '@/libs/util'
import API from '@/api/shipManagement'
import { queryTyphoonInfoAll, queryTyphoonInfo, queryCurVoyageMmsi, queryHistoryVoyageMmsi, queryVoyagePlanConsolePage, queryAisShipMsg, getCustomerDictEntryList, addAisSearchRecord, delAisSearchRecord, queryAisSearchRecordGroupList } from '@/api/ais'
import arrowBlue from '@/assets/images/arrow_blue.png'
import arrowGrey from '@/assets/images/arrow_grey.png'
import homeDynamicDraw from './homeDynamicDraw'
import MaskCanvas from './maskCanvas'
import MarkerDraw from './markerDraw'
import tideData from './tidePort.json'
import TideModal from './modal/tideModal'
import DrawTool from './modal/drawTool'
import ShipMessDetail from '../../single-page/ship/shipMessDetail'

export default {
  name: 'home',
  components: {
    homeDynamicDraw,
    MaskCanvas,
    MarkerDraw,
    TideModal,
    DrawTool,
    ShipMessDetail
  },
  data () {
    return {
      worker: null,
      shipDrawerDetailShow: false, // 预警时长框显示
      formDataList: [],
      isUp: false,
      winHeight: 0, // 窗口高度
      isShipClick: false, // 判断人为点击
      isSignShow: false, // 标注表盘显隐
      dynamicLoading: false, // 船舶动态数据加载
      isDynamicShow: false, // 船舶动态页面
      isMoreDynamicShow: false, // 船舶动态更多展示
      dynamicTableData: [], // 动态列表数据
      dynamicColumns: [{
        title: '船舶',
        width: 50,
        align: 'center',
        key: 'ship_name',
        className: 'rowClassName'
      }, {
        title: '本航次',
        width: 130,
        align: 'center',
        key: 'current_voyage',
        className: 'rowClassName',
        render: function (t, e) {
          return t('span', {
            domProps: {
              innerHTML: e.row.current_voyage
            }
          })
        }
      }, {
        title: '动态',
        width: 220,
        align: 'center',
        key: 'dynamic_info',
        tooltip: !0,
        className: 'rowClassName',
        render: function (t, e) {
          return t('span', {
            domProps: {
              innerHTML: e.row.dynamic_info
            }
          })
        }
      }],
      tideData: {
        modal: false,
        id: '',
        data: {}
      },
      tideUrl: require('@/assets/images/chaoxi.png'),
      tideShow: false, // 潮汐显示隐藏
      typhoonShow: false, // 台风显示隐藏
      isToolShow: false, // 工具箱显示隐藏
      isShipShow: false, // 船舶的显示隐藏
      toolBtnList: [
        '船舶动态', '找船', '港口码头', '避风区域', '领海基线'
      ], // 工具列表
      toolShowName: ['港口码头'], // 当前显示的工具
      searchPortList: [], // 搜索港口列表
      searchWharfList: [], // 搜索码头列表
      isShipTypeSearch: false, // 是否打开找船功能
      isShipSearch: true, // 是否在找船的搜索页面
      shipTypeStr: [], // 船舶类型
      shipTypeList: [], // 船舶类型列表
      searchCurrent: 1,
      searchShipTotal: 1,
      deadweight_tonnage_from: undefined, // 载重吨范围开始
      deadweight_tonnage_to: undefined, // 载重吨范围结束
      searchTypeList: [], // 找船船舶列表
      isShipDetailShow: false, // 是否显示船舶详情
      shipDetailList: [], // 船舶详情列表
      searchTypeColumn: [
        {
          title: '船名',
          align: 'center',
          key: 'ship_name'
        },
        {
          title: '载重吨(T)',
          align: 'center',
          key: 'expected_deadweight'
        },
        {
          title: '类型',
          align: 'center',
          key: 'ship_type_name'
        }
      ],
      searchParam: {
        ship_type_in: undefined,
        deadweight_tonnage_from: undefined,
        deadweight_tonnage_to: undefined,
        pageSize: 10,
        pageIndex: 1
      },
      selfShipList: [],
      map: null,
      event: null,
      options: '',
      toolStr: '',
      dateArr: [],
      arrowBlue: arrowBlue, // 航向图标
      arrowGrey: arrowGrey, // 计划航向图标
      mmsiStr: '', // mmsi字符串
      shipList: [], // 船舶列表
      curVoyage: {
        result: {},
        curVoyageLoadport: [],
        curVoyageUnloadport: [],
        curVoyageCargo: []
      }, // 当前航次信息数据
      planVoyage: [], // 计划航次信息数据
      voyageList: [], // 航线列表
      voyageLoading: false, // 历史航次列表加载loading
      isSelfVoyage: false, // 历史航次默认船讯网获取
      isVoyageShow: false, // 航次信息面板显示状态
      isPlanVoyageShow: false, // 航次信息面板计划信息显示状态
      isTrackPanel: false, // 轨迹查询面板显示状态
      isVoyagePanel: false, // 航次信息面板显示状态
      trackShow: false, // 轨迹显示状态
      trackOption: '', // 轨迹线参数
      mapNowRoom: 5, // 当前地图缩放程度
      nowCheckedShip: '', // 选中船监听，当前选中的船
      zoomTimer: null, // 定时获取当前地图缩放的定时器
      showShipDetail: false, // 是否显示船舶详情
      shipdetaildata: {}, // 船舶详情数据
      shipStatus: '', // 船舶状态
      berthName: '', // 船舶预抵泊位名称
      isTriDetail: false, // 是否有三方船舶数据
      triShipDetail: [], // 三方船舶数据信息
      fullscreen: false, // 是否全屏展示
      startTimer: '', // 轨迹查询起始时间
      endTimer: '', // 轨迹查询结束时间
      typhoonTimer: '', // 台风风圈绘制时间
      forcastTimer: '', // 台风节点绘制时间
      myLayerGroupLine: null, // 台风线节点集合
      myLayerGroupPoint: null, // 台风点集合
      myLayerGroupMode: null, // 台风状态节点集合
      selectLoading: false, // 查询船舶时loading
      dateOptions: {
        disabledDate (date) {
          let dateTime = new Date()
          let currentYear = dateTime.getFullYear()
          let currentMonth = dateTime.getMonth()
          let currentDate = dateTime.getDate()
          let startDay = new Date(currentYear - 1, currentMonth, currentDate)  // 起始可以选一年前的日期  轨迹可以拉取一年轨迹
          return (
            date.valueOf() < new Date(startDay).getTime() ||
            date.valueOf() > new Date().getTime()
          )
        }
      },
      // 本土历史航次列表
      selfVoyageColumn: [
        {
          title: '航次号',
          align: 'center',
          width: 75,
          key: 'voyage_no'
        },
        {
          title: '航线',
          align: 'center',
          render: (h, param) => {
            let curLoadName = ''
            let curUnloadName = ''
            param.row.CargoResult.forEach(res => {
              res.PortDetailResult.forEach(item => {
                if (item.port_type === '1') {
                  curLoadName = item.wharf_name || item.port_name
                } else if (item.port_type === '2') {
                  curUnloadName = item.wharf_name || item.port_name
                }
              })
            })
            let curLineStr = curLoadName + ' - ' + curUnloadName
            return h('span', {}, curLineStr)
          }
        },
        {
          title: '货品 - 货量',
          align: 'center',
          tooltip: true,
          maxWidth: 125,
          render: (h, param) => {
            let curList = []
            param.row.CargoResult.forEach(item => {
              curList.push(`${item.goods_name} - ${item.amounts}`)
            })
            return h('div', {}, curList.join('\n'))
          }
        },
        {
          title: '时间',
          align: 'center',
          tooltip: true,
          render: (h, param) => {
            let leaveDate = param.row.leave_port_date.substring(0, 10)
            let arriveDate = param.row.arrival_port_date.substring(0, 10)
            let curDate = arriveDate + ' ~ ' + leaveDate
            return h('span', {}, curDate)
          }
        }
      ],
      // 船讯网历史航次列表
      voyageColumn: [
        {
          title: '起始港',
          key: 'startPort',
          width: 100,
          align: 'center'
        },
        {
          title: '离港时间',
          key: 'start_atd',
          align: 'center'
        },
        {
          title: '到达港',
          key: 'endPort',
          width: 100,
          align: 'center'
        },
        {
          title: '到港时间',
          key: 'end_ata',
          align: 'center'
        }
      ]
    }
  },
  computed: {
    collapsed () {
      return this.$store.state.app.collapsed
    },
    startDate: function () {
      let dateTime = new Date()
      let currentYear = dateTime.getFullYear()
      let currentMonth = dateTime.getMonth()
      let currentDate = dateTime.getDate()
      let startDay = new Date(currentYear, currentMonth - 1, currentDate) // 起始可以选一个月前的日期  轨迹可以拉取一年轨迹
      return startDay
    }
  },
  methods: {
    // 国内上传
    handleInnerUpload (file) {
      this.formDataList.push(file)
      let formData = new FormData()
      let through = false // 判断是否需要上传文件
      this.formDataList = this.formDataList.filter(item => {
        if (item.type !== undefined) {
          through = true
          formData.append('file', item)
        }
        return item.type === undefined
      })
      if (through) {
        API.internalExcelReader(formData).then(res => {
          if (res.data.Code === 10000) {
            this.$Message.success(res.data.Message)
          } else {
            this.$Message.error(res.data.Message)
          }
        })
      }
    },
    // 国内上传
    handleNationUpload (file) {
      this.formDataList.push(file)
      let formData = new FormData()
      let through = false // 判断是否需要上传文件
      this.formDataList = this.formDataList.filter(item => {
        if (item.type !== undefined) {
          through = true
          formData.append('file', item)
        }
        return item.type === undefined
      })
      if (through) {
        API.internationExcelReader(formData).then(res => {
          if (res.data.Code === 10000) {
            this.$Message.success(res.data.Message)
          } else {
            this.$Message.error(res.data.Message)
          }
        })
      }
    },
    // 获取预警时间警戒色
    getDelayStyle (time) {
      if (parseFloat(time) > 24 && parseFloat(time) < 48) {
        return 'color: #0167C5; font-weight: bold;'
      }
      if (parseFloat(time) > 48 && parseFloat(time) < 72) {
        return 'color: #ff6c00; font-weight: bold;'
      }
      if (parseFloat(time) > 72) {
        return 'color: red; font-weight: bold;'
      }
    },
    showShipDetailList () {
      this.shipDrawerDetailShow = false
      setTimeout(() => {
        this.shipDrawerDetailShow = true
      }, 100)
    },
    // 船舶显隐
    shipShowHide () {
      this.isShipShow = !this.isShipShow
      if (this.isShipShow) {
        this.map.shipsService.deleteAllFleetShips()
        this.map.shipsService.setFilter({
          shipType: '',
          'shipLength': [[81, 160], [161, 240], [241, 320], [321, 9999]],
          'shipSog': '0,1',
          'shipCountry': '0,1' // 0：中国（412、413、416），1：外国，空：全部
        })
      } else {
        this.map.shipsService.deleteAllFleetShips()
        this.map.shipsService.setFilter({
          shipType: '',
          'shipLength': [[999999, 9999999]]
        })
      }
      let curMmsiList = this.selfShipList.map(item => item.mmsi)
      curMmsiList.forEach(item => {
        this.getManyShip(item)
      })
    },
    // 找船重置
    searchReset () {
      this.shipTypeStr = []
      this.deadweight_tonnage_from = undefined
      this.deadweight_tonnage_to = undefined
    },
    // 找船查询提交
    searchSubmit () {
      this.isShipSearch = false
      this.searchParam = {
        ship_type_in: this.shipTypeStr.join(),
        deadweight_tonnage_from: this.deadweight_tonnage_from,
        deadweight_tonnage_to: this.deadweight_tonnage_to,
        pageSize: 10,
        pageIndex: 1
      }
      this.getShipSearchList()
    },
    // 关闭找船搜索页
    shipSearchTypeClose () {
      let shipIdx = this.toolShowName.findIndex(item => item === '找船')
      this.toolShowName.splice(shipIdx, 1)
      this.isShipTypeSearch = false
      let curMmsiList = this.selfShipList.map(item => item.mmsi)
      this.map.shipsService.deleteAllFleetShips()
      curMmsiList.forEach(item => {
        this.getManyShip(item)
      })
    },
    // 删除历史记录
    delHis (val) {
      delAisSearchRecord({ id: val }).then(res => {
        if (res.data.Code === 10000) {
          this.getHisSearchList()
        }
      })
    },
    // 关闭找船详情页
    shipSearchClose () {
      this.isShipSearch = true
      this.searchReset()
    },
    searchShipTableClick (row) {
      window.ShipService.locationShip(row.mmsi, true)
      // this.map.shipsService.locationShip(row.mmsi, true)
    },
    // 页面跳转
    handleSizeChange (val) {
      this.searchParam.pageSize = val
      this.getShipSearchList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.searchParam.pageIndex = val
      this.getShipSearchList()
    },
    // 获取找船查询列表
    getShipSearchList () {
      API.queryBasicShipPage(this.searchParam).then(res => {
        if (res.data.Code === 10000) {
          this.searchTypeList = res.data.Result
          this.searchShipTotal = res.data.Total
          // let curMmsiList = this.selfShipList.map(item => item.mmsi)
          let searchMmsiList = this.searchTypeList.map(item => item.mmsi)
          // let allMmsiList = [...curMmsiList, ...searchMmsiList]
          this.map.shipsService.deleteAllFleetShips()
          searchMmsiList.forEach(item => {
            this.getManyShip(item, 'searchboat')
          })
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    getShipList (query) {
      this.shipList = []
      if (query === '') {
        return
      }
      this.map.options.ak = 'a5bb8f37140d428391e1546d7b704413' // '57df9eaa033b44809d4bdaf919af457e' 青岛ak
      let shipListUrl = 'http://searchv3.shipxy.com/shipdata/search3.ashx?f=auto&kw=' + query
      let that = this
      this.selectLoading = true
      // 左上角搜索船舶列表
      jQuery.getJSON(shipListUrl, function (result) {
        that.selectLoading = false
        result.ship.map(item => {
          that.shipList.push({
            value: `${item.m}__${query}`, // 方便汉字加显
            label: item.n,
            type: item.t
          })
        })
      })
      // 模糊查找港口信息
      API.queryMapBaseList({ name: query, base_type: 1 }).then(res => {
        if (res.data.Code === 10000) {
          this.searchPortList = res.data.Result
        }
      })
      // 模糊查找码头信息
      API.queryMapBaseList({ name: query, base_type: 2 }).then(res => {
        if (res.data.Code === 10000) {
          this.searchWharfList = res.data.Result
        }
      })
      // 旧接口
      // queryInnerPortAndWharfList({ search_content: query }).then(res => {
      //   if (res.data.Code === 10000) {
      //     this.searchPortList = res.data.Result.Port
      //     this.searchWharfList = res.data.Result.Wharf
      //   }
      // })
    },
    // 获取历史搜索记录
    getHisSearchList () {
      this.shipList = []
      this.searchPortList = []
      this.searchWharfList = []
      queryAisSearchRecordGroupList().then(res => {
        if (res.data.Code === 10000) {
          res.data.Result.forEach((item, idx) => {
            if (item.search_type === '1') { // 船舶列表
              if (!item.List || item.List.length === 0) return
              // 使用Set实现按search_content字段去重
              let _tempShipList = Array.from(new Set(item.List.map(item => item.search_content))).map(search_content => {
                return item.List.find(item => item.search_content === search_content)
              })
              _tempShipList.slice(0, 5).map(list => {
                this.shipList.push({
                  id: list.id,
                  value: `${list.mmsi}__${list.search_content}`, // 方便汉字加显
                  label: list.search_content,
                  type: list.search_type
                })
              })
            }
            if (item.search_type === '2') { // 港口列表
              if (!item.List || item.List.length === 0) return
              // 使用Set实现按search_content字段去重
              let _tempPortList = Array.from(new Set(item.List.map(item => item.search_content))).map(search_content => {
                return item.List.find(item => item.search_content === search_content)
              })
              _tempPortList.slice(0, 3).map(list => {
                this.searchPortList.push({
                  uid: list.id,
                  id: list.mmsi,
                  name: list.search_content,
                  latitude: list.lat,
                  longitude: list.lng,
                  level: 12
                })
              })
            }
            if (item.search_type === '3') { // 码头列表
              if (!item.List || item.List.length === 0) return
              // 使用Set实现按search_content字段去重
              let _tempWharfList = Array.from(new Set(item.List.map(item => item.search_content))).map(search_content => {
                return item.List.find(item => item.search_content === search_content)
              })
              _tempWharfList.slice(0, 3).map(list => {
                this.searchWharfList.push({
                  uid: list.id,
                  id: list.mmsi,
                  name: list.search_content,
                  latitude: list.lat,
                  longitude: list.lng,
                  level: 14
                })
              })
            }
          })
        }
      })
    },
    selectOpenClose (val) {
      if (val) {
        this.$refs['markerDrawer'].portModalShow = false
      }
    },
    // 查询船舶,来自船讯网
    shipSelect (query) {
      this.dynamicHide()
      this.isDynamicShow = false
      if (!query) return
      let hisQuery = { // 搜索历史内容提交参数
        search_type: undefined, // 搜索类型（1船舶；2港口；3码头）
        search_content: undefined, // 搜索内容
        mmsi: undefined, // mmsi
        lat: undefined, // 纬度
        lng: undefined, // 经度
        level: undefined // 层级
      }
      if (query.indexOf('__') === -1) { // 港口 码头搜索
        let curObj = JSON.parse(query)
        let curLatLng = L.latLng(curObj.latitude, curObj.longitude)
        this.$refs.maskCanvas.defaultModal()
        if (curObj.type === 1) { // 调用港口数据
          this.map.flyTo(curLatLng, 12, { // 港口默认定位到12级
            animate: true,
            duration: 2,
            noMoveStart: true
          })
          this.$refs['markerDrawer'].getPortList(curObj.id, curObj.name)
        }
        if (curObj.type === 2) { // 调用码头数据
          this.map.flyTo(curLatLng, 14, { // 码头默认定位到14级
            animate: true,
            duration: 2,
            noMoveStart: true
          })
          this.$refs['markerDrawer'].isTerminalSingalClick = true
          this.$refs['markerDrawer'].getTerminalList(curObj.id, curObj.name)
          this.$refs['markerDrawer'].getAllBerthList(curObj.id)
        }
        // if (curObj.type === 2) { // 调用港口数据
        //   this.map.flyTo(curLatLng, 12, { // 港口默认定位到12级
        //     animate: true,
        //     duration: 2,
        //     noMoveStart: true
        //   })
        // }
        // if (curObj.type === 3) { // 调用码头数据
        //   this.map.flyTo(curLatLng, 14, { // 码头默认定位到14级
        //     animate: true,
        //     duration: 2,
        //     noMoveStart: true
        //   })
        // }
        // 给搜索历史参数赋值
        Object.assign(hisQuery, {
          mmsi: curObj.id,
          search_type: parseInt(curObj.type) + 1,
          search_content: curObj.name,
          lat: curObj.latitude,
          lng: curObj.longitude,
          level: curObj.level
        })
      } else { // 船舶搜索
        this.nowCheckedShip = query.split('__')[0] // 方便汉字加显
        this.isShipClick = false
        window.ShipService.locationShip(this.nowCheckedShip, true)
        // this.map.shipsService.locationShip(this.nowCheckedShip, true)
        this.map.options.ak = 'a5bb8f37140d428391e1546d7b704413' // 重置为瀛海ak
        // 给搜索历史参数赋值
        Object.assign(hisQuery, {
          search_type: 1,
          search_content: query.split('__')[1],
          mmsi: this.nowCheckedShip
        })
        let ship = window.ShipService.locationShip(this.nowCheckedShip, true)
        // let ship = this.map.shipsService.locationShip(this.nowCheckedShip, true)
        if (ship.navistatus === 5 || parseInt(ship.sog) === 0) {
          this.map.setZoom(16)
        } else {
          this.map.setZoom(12)
        }
      }
      this.isVoyagePanel = false
      this.clearTrack()
      this.curVoyage = {
        result: {},
        curVoyageLoadport: [],
        curVoyageUnloadport: [],
        curVoyageCargo: []
      }
      this.planVoyage = []
      // let isInHis = this.checkInHisData(hisQuery)
      // if (isInHis) return
      addAisSearchRecord(hisQuery).then(res => {
        this.getHisSearchList()
      }) // 添加历史记录
      // setTimeout(() => { // 重绘港口码头数据
      //   this.$refs.maskCanvas.draw()
      // }, 300)
    },
    // 判断是否在历史记录里
    checkInHisData (obj) {
      let isShip = this.shipList.some(item => item.search_content === obj.search_content)
      let isPort = this.searchPortList.some(item => item.search_content === obj.name)
      let isWharf = this.searchWharfList.some(item => item.search_content === obj.name)
      return isShip || isPort || isWharf
    },
    shipSelectList (mmsi) {
      this.shipDrawerDetailShow = true
      if (!mmsi || mmsi === '') return
      this.nowCheckedShip = mmsi
      this.isShipClick = false
      this.dynamicHide()
      window.ShipService.locationShip(this.nowCheckedShip, true)
      // this.map.shipsService.locationShip(this.nowCheckedShip, true)
      this.isVoyagePanel = false
      this.clearTrack()
      this.curVoyage = {
        result: {},
        curVoyageLoadport: [],
        curVoyageUnloadport: [],
        curVoyageCargo: []
      }
      this.planVoyage = []
      this.$refs.maskCanvas.defaultModal()
      // setTimeout(() => { // 重绘港口码头数据
      //   this.$refs.maskCanvas.draw()
      // }, 300)
      // this.map.options.ak = '57df9eaa033b44809d4bdaf919af457e' // 重置为瀛海ak
    },
    queryClear () {
      this.searchTxt = ''
      this.getHisSearchList()
    },
    queryChange (query) {
      if (query) {
        this.searchTxt = query
      }
    },
    // 隐藏船舶及历史航次板块
    hideOtherArea () {
      // this.cancelModal()
      this.isVoyagePanel = false
      this.showShipDetail = false
      this.isShipDetailShow = false
      this.$nextTick(() => {
        this.nowCheckedShip = ''
        this.map.shipsService.cancelSelectedShip() // 取消選中
      })
    },
    // 开启船视图
    openShip (map) {
      // 默认 MT_SATELLITE 卫星图 MT_GOOGLE 谷歌地图 MT_SEA 海图
      map.basemapsControl.changeMap('MT_SEA')
      // 开启区域船服务
      const canvasShips = ShipxyAPI.ShipService(map, {
        enableAreaShip: true, // 区域船
        enableFleetShip: true, // 船队船
        // enableDShip: true, // D+船
        lableFont: ['600 12px Arial', '600 12px 宋体'], // 船舶名称，文字字体，默认值：["600 12px Arial", "500 12px Arial"]
        lableTxtColor: ['#000', '#eee'], // 船舶名称，文字颜色，默认值：["#000","#fff"]
        lableLineColor: ['rgba(1, 30, 62, 1)', 'rgba(1, 30, 62, 1)'], //  边框颜色，默认值：["#000","#000"]
        lableLinefillColor: ['rgba(255, 255, 255, 0.7)', 'rgba(1, 30, 62, 0.3)'], // 框内填充颜色，默认值：[null,null]
        obliqueLineColor: ['#000', '#000'], // 船舶名称，斜线颜色，默认值：[null,null]
        dShipColor: '#FF6437' // D+船颜色，默认：#ff6347
      })
      window.ShipService = canvasShips
      window.ShipService.setPointerEvents(!0)
      canvasShips.addSelectedListener(ship => {
        this.isShipClick = true
        // 选中船监听
        this.nowCheckedShip = ship.mmsi + ''
      })
      // 开启轨迹服务
      ShipxyAPI.TrackService(map)
      // 专业气象，开启
      ShipxyAPI.WeatherService(map)
      // 开启,港口瓦片
      ShipxyAPI.portDataSymbol(map)
      // 开启自定义瓦片图层服务
      ShipxyAPI.customeTileLayerService(map)
      // 开启航次服务
      ShipxyAPI.voyageService(map)
      // 开启光栅
      // this.showTileLayer()
    },
    // 开启光栅
    showTileLayer () {
      var options = {
        cid: 'titleLayer',
        attribution: '',
        crs: L.CRS.EPSG3857,
        maxZoom: 9,
        label: '光栅图',
        // errorTileUrl: 'http://m.shipxy.com/img/erroshipxy.png',
        tileFunction: function (view) {
          var url = 'http://r-g.shipxy.com/r/sp.dll?cmd=112&scode=11111111&x={x}&y={y}&z={z}'
          var _url = url
            .replace('{x}', view.tile.column)
            .replace('{y}', view.tile.row)
            .replace('{z}', view.zoom)
          return _url
        }
      }
      this.map.customeTileLayerService.addLayer(options)
    },
    /**
    * 获取指定的多船，并绘制, 传入多船mmsi，用，分开
    * @param mmsis   477172700,477232800
    */
    getManyShip (mmsi, str, idx) {
      let _that = this
      let fleetships = []
      let ids = mmsi // "413697810,413697790";
      if (ids.length === 0) {
        layer.msg('请传入有效船名mmsi')
        return
      }
      var c_ship = window.ShipService.getShipByMmsi(mmsi)
      // 匹配mmsi捞自己库里的中文名称
      let shipList = (str && str === 'searchboat') ? this.searchTypeList : JSON.parse(localStorage.getItem('shipNameList'))
      let curArr = shipList.filter(item => item.mmsi === mmsi)
      let searchArr = _that.searchTypeList.filter(item => item.mmsi === '' + mmsi)
      if (c_ship === null) return
      if (curArr[0]) {
        c_ship.name = curArr[0].ship_name
      } else {
        c_ship.name = searchArr[0].ship_name
      }
      c_ship.istop = true
      c_ship.hdg = c_ship.hdg
      c_ship.cog = c_ship.cog
      c_ship.draught = c_ship.draught
      c_ship.lat = c_ship.lat
      c_ship.lng = c_ship.lng
      c_ship.width = c_ship.width
      c_ship.length = c_ship.length
      c_ship.color = 'yellow'
      fleetships.push(c_ship)
      // window.ShipService.addShips(fleetships) // 影响船舶移动显隐问题
      window.ShipService.addFleetShips(fleetships)
      if (str === 'getAnchor') { // 公司船舶列表获取锚泊专用
        Object.assign(this.selfShipList[idx], {
          status_code: c_ship.navistatus,
          port_name: c_ship.dest,
          ata: c_ship.eta_std,
          navistatus: c_ship.navistatus === 0 ? '在航' : CanvasShipUtils.getDisValue(c_ship.navistatus, 'naviStatus', 'zh_CN')
        })
        this.getHistoryVoyagePortSelect(mmsi, idx)
      }
    },
    // 排序
    statusChange () {
      this.isUp = !this.isUp
      let preShipList = this.selfShipList.filter(item => item.ship_name.substring(0, 1) === '兴') // 提取兴通的船舶
      let afterShipList = this.selfShipList.filter(item => item.ship_name.substring(0, 1) === '善') // 提取万邦的船舶
      // let preSortShipList = []
      // let afterSortShipList = []
      if (this.isUp) {
        if (preShipList.length > 0 || afterShipList.length > 0) {
          let preSortShipList = preShipList.sort((a, b) => {
            if (a.delayTime > b.delayTime) {
              return -1
            } else if (a.delayTime < b.delayTime) {
              return 1
            } else {
              if (a.status_code < b.status_code) {
                return -1
              } else if (a.status_code > b.status_code) {
                return 1
              } else {
                return 0
              }
            }
          })
          let afterSortShipList = afterShipList.sort((a, b) => {
            if (a.delayTime > b.delayTime) {
              return -1
            } else if (a.delayTime < b.delayTime) {
              return 1
            } else {
              if (a.status_code < b.status_code) {
                return -1
              } else if (a.status_code > b.status_code) {
                return 1
              } else {
                return 0
              }
            }
          })
          this.selfShipList = [...preSortShipList, ...afterSortShipList]
        } else {
          this.selfShipList = this.selfShipList.sort((a, b) => b.delayTime - a.delayTime)
        }
        // if (preShipList.length > 0 || afterShipList.length > 0) {
        //   preSortShipList = preShipList.sort((a, b) => b.status_code - a.status_code)
        //   afterSortShipList = afterShipList.sort((a, b) => b.status_code - a.status_code)
        //   this.selfShipList = [...preSortShipList, ...afterSortShipList]
        // } else {
        //   this.selfShipList = this.selfShipList.sort((a, b) => b.status_code - a.status_code)
        // }
      } else {
        // if (preShipList.length > 0 || afterShipList.length > 0) {
        //   preSortShipList = preShipList.sort((a, b) => a.status_code - b.status_code)
        //   afterSortShipList = afterShipList.sort((a, b) => a.status_code - b.status_code)
        //   this.selfShipList = [...preSortShipList, ...afterSortShipList]
        // } else {
        //   this.selfShipList = this.selfShipList.sort((a, b) => a.status_code - b.status_code)
        // }
        if (preShipList.length > 0 || afterShipList.length > 0) {
          let preSortShipList = preShipList.sort((a, b) => {
            if (a.delayTime > b.delayTime) {
              return -1
            } else if (a.delayTime < b.delayTime) {
              return 1
            } else {
              if (a.status_code > b.status_code) {
                return -1
              } else if (a.status_code < b.status_code) {
                return 1
              } else {
                return 0
              }
            }
          })
          let afterSortShipList = afterShipList.sort((a, b) => {
            if (a.delayTime > b.delayTime) {
              return -1
            } else if (a.delayTime < b.delayTime) {
              return 1
            } else {
              if (a.status_code > b.status_code) {
                return -1
              } else if (a.status_code < b.status_code) {
                return 1
              } else {
                return 0
              }
            }
          })
          this.selfShipList = [...preSortShipList, ...afterSortShipList]
        } else {
          this.selfShipList = this.selfShipList.sort((a, b) => b.delayTime - a.delayTime)
        }
      }
    },
    // 获取历史靠泊港口数据 -- 船讯网
    async getHistoryVoyagePortSelect (mmsi, idx) {
      const that = this
      let dateTime = new Date()
      let currentYear = dateTime.getFullYear()
      let currentMonth = dateTime.getMonth()
      let currentDate = dateTime.getDate()
      let startDay = new Date(currentYear, currentMonth - 1, currentDate).getTime().toString().substring(0, 10)
      let endDay = dateTime.getTime().toString().substring(0, 10)
      // 获取时间段内航次 船舶靠港记录
      let url1 = 'http://api.shipxy.com/apicall/GetPortOfCallByShip?k=' + this.map.options.ak + '&v=2&mmsi=' + mmsi + '&timetype=2&begin=' + startDay + '&end=' + endDay
      url1 = url1.replace('{k}', this.map.options.ak)
      // 获取港口及到港时间
      await jQuery.getJSON(url1 + '&jsf=?', function (result) {
        if (result.records.length > 0) {
          if (that.selfShipList[idx].status_code !== 1) { // 非锚泊状态
            Object.assign(that.selfShipList[idx], {
              port_name: that.selfShipList[idx].status_code === 0 ? that.selfShipList[idx].port_name : result.records[result.records.length - 1].portname_cn,
              ata: that.selfShipList[idx].status_code === 0 ? that.selfShipList[idx].ata : result.records[result.records.length - 1].ata,
              delayTime: 0,
              delayHour: '--',
              delayTimeStr: '--'
            })
          } else { // 锚泊状态
            // 计算锚泊预期时间
            let voyageAta = new Date(result.records[result.records.length - 1].ata).getTime() // 最后一个航次到港时间
            let curAta = new Date(that.selfShipList[idx].ata).getTime() // 预抵时间
            if (curAta - voyageAta > 0) { // 判断最后一个航次与预抵时间哪个时间最大，按大的进行换算锚泊时长
              Object.assign(that.selfShipList[idx], {
                port_name: that.selfShipList[idx].port_name,
                ata: that.selfShipList[idx].ata
              })
              that.diffTime(new Date(that.selfShipList[idx].ata), dateTime, idx)
            } else {
              Object.assign(that.selfShipList[idx], {
                port_name: result.records[result.records.length - 1].portname_cn,
                ata: result.records[result.records.length - 1].ata
              })
              that.diffTime(new Date(result.records[result.records.length - 1].ata), dateTime, idx)
            }
          }
        } else {
          Object.assign(that.selfShipList[idx], {
            port_name: '--',
            ata: '--'
          })
        }
      })
      setTimeout(() => {
        let preShipList = this.selfShipList.filter(item => item.ship_name.substring(0, 1) === '兴') // 提取兴通的船舶
        let afterShipList = this.selfShipList.filter(item => item.ship_name.substring(0, 1) === '善') // 提取万邦的船舶
        if (preShipList.length > 0 || afterShipList.length > 0) {
          // let preSortShipList = preShipList.sort((a, b) => b.delayTime - a.delayTime)
          // let afterSortShipList = afterShipList.sort((a, b) => b.delayTime - a.delayTime)
          let preSortShipList = preShipList.sort((a, b) => {
            if (a.delayTime > b.delayTime) {
              return -1
            } else if (a.delayTime < b.delayTime) {
              return 1
            } else {
              if (a.status_code > b.status_code) {
                return -1
              } else if (a.status_code < b.status_code) {
                return 1
              } else {
                return 0
              }
            }
          })
          let afterSortShipList = afterShipList.sort((a, b) => {
            if (a.delayTime > b.delayTime) {
              return -1
            } else if (a.delayTime < b.delayTime) {
              return 1
            } else {
              if (a.status_code > b.status_code) {
                return -1
              } else if (a.status_code < b.status_code) {
                return 1
              } else {
                return 0
              }
            }
          })
          this.selfShipList = [...preSortShipList, ...afterSortShipList]
        } else {
          this.selfShipList = this.selfShipList.sort((a, b) => b.delayTime - a.delayTime)
          // this.selfShipList = this.selfShipList.sort((a, b) => {
          //   if (a.delayTime > b.delayTime) {
          //     return -1
          //   } else if (a.delayTime < b.delayTime) {
          //     return 1
          //   } else {
          //     if (a.status_code > b.status_code) {
          //       return -1
          //     } else if (a.status_code < b.status_code) {
          //       return 1
          //     } else {
          //       return 0
          //     }
          //   }
          // })
        }
      }, 500)
    },
    diffTime (startDate, endDate, idx) {
      let diffvalue = endDate.getTime() - startDate.getTime()
      if (diffvalue > 0) {
        let backStr = ''
        let minute = 1000 * 60
        let hour = minute * 60
        let day = hour * 24
        let halfamonth = day * 15
        let month = day * 30

        var monthC = diffvalue / month
        var weekC = diffvalue / (7 * day)
        var dayC = diffvalue / day
        var hourC = diffvalue / hour
        var minC = diffvalue / minute
        if (parseInt(monthC) >= 1) {
          backStr = parseInt(monthC) + '个月前'
        } else if (parseInt(dayC) > 1) {
          backStr = parseInt(dayC) + '天前'
        } else if (parseInt(dayC) === 1) {
          backStr = '昨天'
        } else if (parseInt(hourC) >= 1) {
          backStr = parseInt(hourC) + '小时前'
        } else if (parseInt(minC) >= 1) {
          backStr = parseInt(minC) + '分钟前'
        } else {
          backStr = '刚刚'
        }
        Object.assign(this.selfShipList[idx], {
          delayTime: parseInt(diffvalue), // 时间戳差
          delayHour: parseFloat(hourC).toFixed(2),
          delayTimeStr: backStr // 相差时间字符
        })
      } else {
        Object.assign(this.selfShipList[idx], {
          delayTime: 1,
          delayHour: '--',
          delayTimeStr: '刚刚' // 相差时间字符
        })
      }
    },
    // 轨迹显示隐藏
    trackShowHide () {
      this.isTrackPanel = !this.isTrackPanel
      this.isShipDetailShow = false
    },
    // 获取历史靠泊港口数据 -- 船讯网
    getHistoryVoyagePort () {
      const that = this
      let dateTime = new Date()
      let currentYear = dateTime.getFullYear()
      let currentMonth = dateTime.getMonth()
      let currentDate = dateTime.getDate()
      let startDay = new Date(currentYear, currentMonth - 1, currentDate).getTime().toString().substring(0, 10)
      let endDay = dateTime.getTime().toString().substring(0, 10)
      let curMmsi = this.nowCheckedShip || this.shipdetaildata.mmsi
      this.isShipClick = false
      // 获取时间段内航次 船舶靠港记录
      let url1 = 'http://api.shipxy.com/apicall/GetPortOfCallByShip?k=' + this.map.options.ak + '&v=2&mmsi=' + curMmsi + '&timetype=2&begin=' + startDay + '&end=' + endDay
      url1 = url1.replace('{k}', this.map.options.ak)
      jQuery.getJSON(url1 + '&jsf=?', function (result) {
        that.voyageLoading = false
        if (result.records.length > 0) {
          result.records.forEach((item, idx) => {
            if (result.records[idx + 1]) {
              that.voyageList.unshift({
                endPort: result.records[idx + 1].portname_cn,
                end_ata: result.records[idx + 1].ata,
                end_atb: result.records[idx + 1].atb,
                end_atd: result.records[idx + 1].atd,
                end_stayinport: result.records[idx + 1].stayinport,
                end_stayinterminal: result.records[idx + 1].stayinterminal,
                startPort: item.portname_cn,
                start_ata: item.ata,
                start_atb: item.atb,
                start_atd: item.atd,
                start_stayinport: item.stayinport,
                start_stayinterminal: item.stayinterminal
              })
            }
          })
        }
      })
    },
    // 当前历史航次点击 展示轨迹
    voyageCurClick (item) {
      let endDate = new Date((item.end_ata || item.leave_port_date).replace(/-/g, '/')).getTime() / 1000
      let startDate = new Date((item.start_atd || item.arrival_port_date).replace(/-/g, '/')).getTime() / 1000
      let _tempDate
      if (startDate > endDate) { // 判断异常调换下位置
        _tempDate = endDate
        endDate = startDate
        startDate = _tempDate
      }
      const overLayer = document.querySelector('.leaflet-overlay-pane')
      if (overLayer) { // 显示轨迹时把轨迹的框可见打开
        let canvasList = overLayer.querySelectorAll('canvas')
        canvasList.forEach(canvas => {
          canvas.style.display = 'block'
        })
      }
      this.trackShow = true
      this.map.trackService.removeAll()
      this.map.trackService.addAndShowByUrl(this.nowCheckedShip, startDate, endDate, this.trackOption)
    },
    // 标记按钮点击事件
    signBtnClick () {
      this.isSignShow = !this.isSignShow
      if (this.isSignShow) {
        this.isToolShow = false
      }
    },
    // 标注表盘关闭
    signTableClose () {
      this.isSignShow = false
      if (this.trackShow) {
        this.clearTrack()
      }
    },
    // 航次历史表盘关闭
    voyageTableClose () {
      this.isVoyagePanel = false
      this.showShipDetail = true
      if (this.trackShow) {
        this.clearTrack()
      }
    },
    // 关闭信息框
    cancelModal () {
      this.showShipDetail = false
      this.nowCheckedShip = ''
      // if (this.map.getZoom() > 7) {
      //   this.map.setZoom(6)
      // }
      this.map.shipsService.cancelSelectedShip() // 取消選中
      this.clearTrack()
      this.isTrackPanel = false
    },
    // 时间选择
    dateChange (dateArr) {
      this.startTimer = new Date(dateArr[0]).getTime() / 1000
      this.endTimer = new Date(dateArr[1]).getTime() / 1000
    },
    // 轨迹查询
    searchTrack () {
      if (this.startTimer === '' || this.endTimer === '') {
        this.$Message.error('请先选择要查询轨迹的时间')
        return
      }
      // 显示轨迹时船舶的层级降下来
      const canvasShips = document.querySelector('.div-canvas-ships')
      if (canvasShips) {
        canvasShips.style.setProperty('z-index', '300', 'important')
      }
      const overLayer = document.querySelector('.leaflet-overlay-pane')
      if (overLayer) { // 显示轨迹时把轨迹的框可见打开
        let canvasList = overLayer.querySelectorAll('canvas')
        canvasList.forEach(canvas => {
          canvas.style.display = 'block'
        })
      }
      this.trackShow = true
      this.map.trackService.removeAll()
      this.map.trackService.addAndShowByUrl(this.nowCheckedShip, this.startTimer, this.endTimer, this.trackOption)
    },
    // 清除轨迹
    clearTrack () {
      this.dateArr = []
      this.startTimer = ''
      this.endTimer = ''
      this.trackShow = false
      this.map.trackService.removeAll()
      // 移除轨迹时船舶的层级降下来
      const canvasShips = document.querySelector('.div-canvas-ships')
      if (canvasShips) {
        canvasShips.style.setProperty('z-index', '400', 'important')
      }
      const overLayer = document.querySelector('.leaflet-overlay-pane')
      if (overLayer) { // 移除轨迹时把轨迹的内容不可见，省得影响其他点击事件
        let canvasList = overLayer.querySelectorAll('canvas')
        canvasList.forEach(canvas => {
          canvas.style.display = 'none'
        })
      }
    },
    // 船舶详情展示
    shipDetail (_mmsi) {
      let mmsi = _mmsi + ''
      if (mmsi.length > 0) {
        this.getHZDetail(_mmsi)
        this.$refs.maskCanvas.defaultModal() // 隐藏港口 码头区域
        let ship = window.ShipService.getShipByMmsi(mmsi, true) // this.map.shipsService.locationShip(mmsi, true)
        if (!this.isShipClick) { // 非人为地图上点击，判断层级跳转
          this.map.shipsService.locationShip(mmsi, true)
          if (ship.navistatus === 5) {
            this.map.setZoom(16)
          } else {
            this.map.setZoom(10)
          }
        }
        if (this.isDynamicShow && !this.isShipClick) {
          if (this.curDynamicShipData.current_voyage !== '') {
            let html = `<p style="text-align: center; font-weight: bold; background: #4880FF; color: #fff; font-size: 16px;">${this.curDynamicShipData.ship_name_cn}</p>\n<div style="display: flex; padding: 10px 5px;">\n
                     <span style="width: 60px; text-align: right;">本航次：</span>\n
                     <span style="width: 240px">${this.curDynamicShipData.current_voyage}</span>\n
                     </div>\n
                     <div style="display: flex; padding: 10px 5px;">\n
                     <span style="width: 60px; text-align: right;">动 态：</span>\n
                     <span style="width: 240px">${this.curDynamicShipData.dynamic_info}</span>\n
                     </div>\n
                     <div style="display: flex; padding: 10px 5px;">\n
                     <span style="width: 60px; text-align: right;">计 划：</span>\n
                     <span style="width: 240px">${this.curDynamicShipData.voyage_plan}</span>\n
                    </div>\n`
            let _offset = L.point(0, -12)
            L.popup({
              className: 'dynamic_info_area',
              offset: _offset,
              closeOnClick: false,
              closeButton: true,
              autoClose: true,
              closePopupOnClick: false
            }).setLatLng([ship.lat, ship.lng]).setContent(html).openOn(this.map)
          } else {
            this.map.closePopup()
          }
        }
        this.isShipClick = false
        if (ship === null) {
          // 没有查询的船+
          this.$Message.error('没找到船')
        } else {
          this.shipdetaildata = {
            name: ship.name,
            cnname: ship.cnname,
            callsign: ship.callsign,
            dest: ship.dest,
            dest_std: ship.dest_std,
            imo: ship.imo,
            mmsi: ship.mmsi,
            length: ship.length,
            width: ship.width,
            draught: ship.draught,
            eta: this.resetEtaTime(ship.eta),
            eta_std: ship.eta_std,
            sog: (ship.sog / 1).toFixed(1), // ship.sog ? (ship.sog / 514).toFixed(1) : 0,
            hdg: ship.hdg ? (+ship.hdg).toFixed(1) : 0,
            cog: ship.cog ? (+ship.cog).toFixed(1) : 0,
            lat: this.toLnglat(ship.lat),
            lng: this.toLnglat(ship.lng),
            type: CanvasShipUtils.getShipTypeCN(ship.type), // 暂时固定 4
            navistatus: CanvasShipUtils.getDisValue(ship.navistatus, 'naviStatus', 'zh_CN'),
            lasttime: this.nowTime('datetime', ship.lasttime * 1000)
          }
          // this.getShipMessage(_mmsi)
          this.showShipDetail = true
          this.isVoyagePanel = false
          // setTimeout(() => { // 重绘港口码头数据
          //   this.$refs.maskCanvas.draw()
          // }, 300)
          this.getCurVoyage(mmsi)
          this.getPlanVoyage(mmsi)
        }
      }
    },
    // 获取三方船舶信息
    getHZDetail (mmsi) {
      if (mmsi === '636093206') { // 临时特殊处理海豚基础数据
        mmsi = '413398530'
      }
      let _param = {
        mapbase_vessel_info_vessel_id: mmsi
      }
      this.shipStatus = ''
      this.berthName = ''
      API.queryShipFormulaInfo({
        url: 'https://mobile.shipformula.com/v1/api/node/map/MapBaseControl/getVesselDetail',
        paramMap: JSON.stringify(_param)
      }).then(res => {
        if (res.data.Code === 10000) {
          if (res.data.Result.errno === '0') {
            this.isTriDetail = true
            this.triShipDetail = res.data.Result.info
            this.shipdetaildata.type = this.triShipTypeTrans(this.triShipDetail.mapbase_vessel_info_type)
            if (this.triShipDetail.trace && this.triShipDetail.trace.destination_terminals_id) {
              this.searchShipBerthName(this.triShipDetail.trace.destination_terminals_id, mmsi) // 通过港口信息查找船舶预抵泊位
            }
          } else {
            this.isTriDetail = false
            this.triShipDetail = []
          }
        }
      })
    },
    // 三方船舶类型转换
    triShipTypeTrans (typeStr) {
      let curShipType = ''
      switch (typeStr) {
        case 'BITUMEN':
          curShipType = '沥青船'
          break
        case 'BUNKER':
          curShipType = '加油船'
          break
        case 'CELLULARCONTAINER':
          curShipType = '罐箱船'
          break
        case 'CHEMICAL':
          curShipType = '化学品船'
          break
        case 'CHEMICALANDPRODUCT':
          curShipType = '油化船'
          break
        case 'CRUDE':
          curShipType = '原油船'
          break
        case 'DIRTY':
          curShipType = '重油船'
          break
        case 'EDIBLEOILCARRIER':
          curShipType = '食用油运输船'
          break
        case 'ETHANECARRIER':
          curShipType = '乙烷船'
          break
        case 'FSO':
          curShipType = '浮式储油船'
          break
        case 'FSRU':
          curShipType = '浮式储气船'
          break
        case 'LEG':
          curShipType = '乙烯船'
          break
        case 'LNG':
          curShipType = '液化天然气船'
          break
        case 'LNGBunkering':
          curShipType = '加气船'
          break
        case 'LPG':
          curShipType = '液化石油气船'
          break
        case 'OIL':
          curShipType = '油轮'
          break
        case 'OILBARGE':
          curShipType = '油驳船'
          break
        case 'OILRECOVERY':
          curShipType = '污油回收船'
          break
        case 'PRODUCT':
          curShipType = '成品油船'
          break
        case 'OTHER':
          curShipType = '其他'
          break
        case 'SHUTTLE':
          curShipType = '穿梭油船'
          break
        case 'SULFUR':
          curShipType = '硫磺船'
          break
        case 'SUPPLYVESSEL':
          curShipType = '补给船'
          break
        case 'TANKER':
          curShipType = '油槽船'
          break
      }
      return curShipType
    },
    // 通过mmsi查找船舶预抵泊位信息
    searchShipBerthName (terminalId, mmsi) {
      let _param = {
        terminalsId: terminalId
      }
      API.queryShipFormulaInfo({
        url: 'https://mobile.shipformula.com/v1/api/backend/toolbox/TerminalLine/refreshMyShipStatus',
        paramMap: JSON.stringify(_param)
      }).then(res => {
        if (res.data.Code === 10000) {
          let curShipList = res.data.Result.info.filter(item => String(item.mmsi) === mmsi)
          this.berthName = '泊位：' + curShipList[0].berthName
          if (curShipList[0].status === 'WORKING') {
            this.shipStatus = '作业'
          }
          if (curShipList[0].status === 'WAITING') {
            this.shipStatus = '等泊'
          }
          if (curShipList[0].status === 'SAILING') {
            this.shipStatus = '航行'
          }
        }
      })
    },
    // 跳转到航次动态页
    redirectVoyage () {
      if (!this.isVoyageShow) return
      this.$refs.homeDynamicDrawer.currentVoyageInfo = this.curVoyage.result.ship_name + ' - ' + this.curVoyage.result.voyage_no
      this.$refs.homeDynamicDrawer.voyage_id = this.curVoyage.result.id
      this.$refs.homeDynamicDrawer.drawerShow = true
    },
    // 获取当前航次信息
    getCurVoyage (mmsi) {
      this.isVoyageShow = false
      this.curVoyage.curVoyageLoadport = []
      this.curVoyage.curVoyageUnloadport = []
      this.curVoyage.curVoyageCargo = []
      queryCurVoyageMmsi({ 'mmsi': mmsi }).then(res => {
        if (res.data.Code === 10000 && res.data.Result.length > 0) {
          this.isVoyageShow = true
          this.curVoyage.result = res.data.Result[0]
          res.data.Result[0].portResult.map(itemPo => {
            if (itemPo.port_type === '1') { // 装港
              this.curVoyage.curVoyageLoadport.push(itemPo)
            } else if (itemPo.port_type === '2') { // 卸港
              this.curVoyage.curVoyageUnloadport.push(itemPo)
            }
          })
          res.data.Result[0].cargoResult.map(itemCar => {
            this.curVoyage.curVoyageCargo.push(itemCar)
          })
        }
      })
    },
    // 获取当前船舶计划航次
    getPlanVoyage (mmsi) {
      this.planVoyage = []
      this.isPlanVoyageShow = false
      queryVoyagePlanConsolePage({
        mmsi: mmsi,
        pageSize: 1000,
        pageIndex: 1
      }).then(res => {
        if (res.data.Code === 10000 && res.data.Result.length > 0) {
          this.isPlanVoyageShow = true
          res.data.Result.forEach((item, idx) => {
            this.planVoyage.push({
              result: item,
              planVoyageLoadport: item.portResult.filter(list => list.port_type === '1'),
              planVoyageUnloadport: item.portResult.filter(list => list.port_type === '2'),
              planVoyageCargo: item.cargoResult
            })
          })
          // this.planVoyage.result = res.data.Result[0]
          // res.data.Result[0].portResult.map(itemPo => {
          //   if (itemPo.port_type === '1') { // 装港
          //     this.planVoyage.planVoyageLoadport.push(itemPo)
          //   } else if (itemPo.port_type === '2') { // 卸港
          //     this.planVoyage.planVoyageUnloadport.push(itemPo)
          //   }
          // })
          // res.data.Result[0].cargoResult.map(itemCar => {
          //   this.planVoyage.planVoyageCargo.push(itemCar)
          // })
        }
      })
    },
    // 获取船舶详情
    getShipMessage (mmsi) {
      queryAisShipMsg({ mmsi: mmsi }).then(res => {
        if (res.data.Code === 10000) {
          let ship = res.data.Result.data[0]
          Object.assign(this.shipdetaildata, {
            name: ship.name,
            cnname: ship.cnname,
            // callsign: ship.callsign,
            // dest_std: ship.dest,
            // imo: ship.imo,
            // mmsi: ship.mmsi,
            // length: ship.length / 10,
            // width: ship.width / 10,
            // draught: ship.draught / 1000,
            // eta: ship.eta,
            // eta_std: new Date().getFullYear() + '-' + ship.eta,
            sog: ship.sog, // ship.sog ? (ship.sog / 514).toFixed(1) : 0,
            hdg: ship.hdg, // ship.hdg ? (+ship.hdg / 100).toFixed(1) : 0,
            cog: ship.cog, // ? (+ship.cog / 100).toFixed(1) : 0,
            lat: this.toLnglat(ship.lat),
            lng: this.toLnglat(ship.lng),
            type: CanvasShipUtils.getShipTypeCN(ship.type)(ship.type) // this.shipTypeTrans(ship.type) 暂时固定
            // navistatus: CanvasShipUtils.getDisValue(ship.navistatus, 'naviStatus', 'zh_CN'),
            // lasttime: this.nowTime('datetime', ship.lasttime * 1000)
          })
        }
      })
    },
    // 获取历史航次列表
    getHistoryVoyage () {
      this.voyageLoading = true
      queryHistoryVoyageMmsi({ 'mmsi': this.nowCheckedShip }).then(res => {
        this.isVoyagePanel = true
        this.isTrackPanel = false
        this.showShipDetail = false
        this.isShipDetailShow = false
        this.voyageList = []
        // 历史航次在库里调用,否则调用船讯网数据
        if (res.data.Code === 10000 && res.data.Result.length > 0) {
          this.isSelfVoyage = true
          this.voyageLoading = false
          this.voyageList = res.data.Result
        } else {
          this.isSelfVoyage = false
          this.getHistoryVoyagePort()
        }
      })
    },
    // 船舶资料按钮点击事件
    getShipDetail () {
      this.isTrackPanel = false
      this.isShipDetailShow = true
      // API.queryBasicShipList({ mmsi: this.nowCheckedShip }).then(res => {
      //   if (res.data.Result.length === 0) {
      //     this.$Message.warning('暂无相关资料')
      //   } else {
      //     this.isTrackPanel = false
      //     this.isShipDetailShow = true
      //     this.shipDetailList = res.data.Result[0]
      //   }
      // })
      // API.queryShipFormulaInfo({
      //   url: 'https://mobile.shipformula.com/v1/api/node/map/MapBaseControl/getVesselDetail',
      //   paramMap: JSON.stringify(_param)
      // }).then(res => {

      // })
    },
    cancelShipDetailModal () {
      this.isShipDetailShow = false
    },
    // 恢复工具默认不显示状态
    toolDefault () {
      let _that = this
      this.tideShow = false
      this.typhoonShow = false
      if (!this.tideShow) {
        this.tideData.modal = false
        this.map.eachLayer(function (layer) {
          if (layer && layer.type === 'tide') {
            _that.map.removeLayer(layer)
          }
        })
      }
      if (!this.typhoonShow) {
        this.map.eachLayer(function (layer) {
          if (layer && layer.type === 'typhoon') {
            _that.map.removeLayer(layer)
          }
        })
      }
      this.map.portDataSymbol.hide()
      this.map.weatherService.hideSeaAreaForecasts()
      this.map.weatherService.hideTides()
      this.map.weatherService.hideTempTile()
      this.map.weatherService.hideSeaWaves()
      this.map.weatherService.hideOceanCurrentFiel()
      this.map.weatherService.hideTyphoonInfoList()
    },
    // 关闭工具显示
    closeToolShow () {
      this.toolDefault()
      this.toolStr = ''
    },
    // 全屏显示
    toggleFullScreen () {
      this.$refs['fullscreen'].toggle()
    },
    fullscreenChange (fullscreen) {
      this.fullscreen = fullscreen
      fullscreen ? this.map.basemapsControl.hide() : this.map.basemapsControl.show()
    },
    // 显示工具
    showTool () {
      this.isToolShow = !this.isToolShow
      if (this.isToolShow) {
        this.isSignShow = false
      }
    },
    // 工具选中变化
    toolCheckChange (e) {
      if (e.includes('船舶动态')) this.getShipDynamic()
      if (e.includes('找船')) this.searchBoat()
      if (e.includes('领海基线')) {
        this.$refs.maskCanvas.mapLine()
      } else {
        this.$refs.maskCanvas.removeLineLayer()
      }
      if (e.includes('港口码头')) {
        this.$refs.markerDrawer.isShow = true
        this.$refs.markerDrawer.drawPort()
        this.$refs.markerDrawer.drawTerminal()
        this.$refs.markerDrawer.drawBerth()
      } else {
        this.$refs.markerDrawer.isShow = false
        this.$refs.markerDrawer.removeAllLayer()
      }
      if (e.includes('避风区域')) {
        this.$refs.maskCanvas.anchorageShow = true
        this.$refs.maskCanvas.draw()
      } else {
        this.$refs.maskCanvas.anchorageShow = false
        this.$refs.maskCanvas.draw()
      }
    },
    dynamicHide: function () {
      if (this.isMoreDynamicShow) {
        this.dynamicColumns.splice(2, 1)
        this.dynamicColumns.pop()
      }
      let _idx = this.toolShowName.findIndex(item => item === '船舶动态')
      this.toolShowName.splice(_idx)
      this.isDynamicShow = false
      this.isMoreDynamicShow = false
    },
    dynamicRowClick: function (e) {
      this.isShipClick = false
      this.curDynamicShipData = e
      this.nowCheckedShip = e.mmsi
      this.map.shipsService.locationShip(e.mmsi, false)
    },
    // 显示更多船舶动态
    moreDynamicShow () {
      this.isMoreDynamicShow = !this.isMoreDynamicShow
      if (this.isMoreDynamicShow) {
        this.dynamicColumns.splice(2, 0, {
          title: '货量',
          align: 'center',
          width: 80,
          slot: 'domPorps',
          key: 'goods_amount',
          className: 'rowClassName',
          render: function (t, e) {
            return t('span', {
              domProps: {
                innerHTML: e.row.goods_amount
              }
            })
          }
        })
        this.dynamicColumns.push({
          title: '计划',
          align: 'center',
          key: 'voyage_plan',
          className: 'rowClassName',
          render: function (t, e) {
            return t('span', {
              domProps: {
                innerHTML: e.row.voyage_plan
              }
            })
          }
        })
      } else {
        this.dynamicColumns.splice(2, 1)
        this.dynamicColumns.pop()
      }
    },
    // 船舶动态信息
    getShipDynamic () {
      this.showShipDetail = false
      this.isToolShow = false
      this.isDynamicShow = true
      this.dynamicLoading = true
      API.queryAisShipDynamicList().then(res => {
        this.dynamicLoading = false
        if (res.data.Code === 10000) {
          this.dynamicTableData = res.data.Result
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 找船按钮点击
    searchBoat () {
      this.searchReset()
      this.isShipSearch = true
      this.isShipTypeSearch = true
      if (!this.isShipTypeSearch) {
        let curMmsiList = this.selfShipList.map(item => item.mmsi)
        this.map.shipsService.deleteAllFleetShips()
        curMmsiList.forEach(item => {
          this.getManyShip(item)
        })
      }
    },
    // 工具选择变化
    toolChange (val) {
      let _that = this
      this.toolDefault()
      if (this.toolStr === val) {
        if (this.toolStr === '台风') { // 关闭台风路径显示
          clearInterval(this.typhoonTimer)
          clearInterval(this.forcastTimer)
          this.myLayerGroupLine.clearLayers()
          this.myLayerGroupPoint.clearLayers()
          this.myLayerGroupMode.clearLayers()
          this.map.eachLayer(function (layer) {
            if (layer && layer.type === 'typhoon') {
              _that.map.removeLayer(layer)
            }
          })
        }
        this.toolStr = ''
        return
      }
      this.toolStr = val
      // this.map.options.ak = '57df9eaa033b44809d4bdaf919af457e'
      switch (val) {
        case '港口':
          this.map.portDataSymbol.show()
          break
        case '预报':
          this.map.weatherService.showSeaAreaForecasts()
          break
        case '潮汐':
          this.tideShow = !this.tideShow
          if (this.tideShow) {
            this.showTides()
          } else {
            let _that = this
            this.map.eachLayer(function (layer) {
              if (layer && layer.type === 'tide') {
                _that.map.removeLayer(layer)
              }
            })
          }
          // this.map.weatherService.showTides()
          break
        case '气温':
          this.map.weatherService.showTempTile(CanvasShipUtils.dataFormat(new Date(), 'yyyy-MM-dd'))
          break
        case '海浪':
          this.map.weatherService.showSeaWaves(CanvasShipUtils.dataFormat(new Date(), 'yyyy-MM-dd'))
          break
        case '洋流':
          let yubao_date = CanvasShipUtils.dataFormat(new Date(), 'yyyy-MM-dd')
          if (this.map.getZoom() > 7) {
            this.map.setZoom(6)
          }
          // 显示
          this.map.weatherService.showOceanCurrentFiel(yubao_date)
          break
        case '台风':
          this.typhoonShow = !this.typhoonShow
          if (this.typhoonShow) {
            this.showTyphoon()
          } else {
            // clearInterval(this.typhoonTimer)
            // clearInterval(this.forcastTimer)
            // this.myLayerGroupLine.clearLayers()
            // this.myLayerGroupPoint.clearLayers()
            // this.myLayerGroupMode.clearLayers()
            this.map.eachLayer(function (layer) {
              if (layer && layer.type === 'typhoon') {
                _that.map.removeLayer(layer)
              }
            })
          }
          break
        default:
      }
    },
    // 台风显示 入口
    showTyphoon () {
      // this.map.options.ak = 'a5bb8f37140d428391e1546d7b704413' // '57df9eaa033b44809d4bdaf919af457e' 青岛ak
      this.map.weatherService.showTyphoonInfoList(function () {}, true, null, { showWarningLine: true, autoLocation: true, showTimeTipMinZoom: 6 })
      queryTyphoonInfoAll().then(res => {
      // axios.get('/apicall/GetAllTyphoon').then(res => {
        let _tyPhoonList = res.data.Result.data.filter(item => {
          return item.datamark === 'ing'
        })
        _tyPhoonList.forEach(item => {
          queryTyphoonInfo({ tid: item.id }).then(res => {
          // axios.get('/apicall/GetSingleTyphoon?tid=' + item.id).then(res => {
            let _typhoonObj = {
              id: item.id,
              name: item.chnname || item.enname,
              forecast: [], // 主线信息
              forcastList: [] // 预测线信息
            }
            let _allTyphoonData = res.data.Result.data
            // let _forecastData = [] // 所有预测节点信息
            // let _endForecastData = [] // 最后一个预测节点列表
            let _lastForecastData = [] // 最终预测节点信息
            let _typhoonData = _allTyphoonData.filter(item => { // 获取所有台风执行过的节点
              return item.forecast === ''
            })
            _allTyphoonData.map(list => {
              if (list.fhour === '12') _lastForecastData.unshift(list)
              if (list.fhour === '24') _lastForecastData.unshift(list)
              if (list.fhour === '36') _lastForecastData.unshift(list)
              if (list.fhour === '48') _lastForecastData.unshift(list)
              if (list.fhour === '60') _lastForecastData.unshift(list)
              if (list.fhour === '72') _lastForecastData.unshift(list)
              if (list.fhour === '96') _lastForecastData.unshift(list)
              if (list.fhour === '120') _lastForecastData.unshift(list)
            })
            _typhoonObj.forecast = _typhoonData
            _typhoonObj.forcastList = _lastForecastData
            let typhoonLinInfo = this.typhoonDataHandler(_typhoonObj)
            this.animateDrawTyphoonLine(typhoonLinInfo)
          })
        })
      })
      setTimeout(() => {
        this.map.flyTo([28.0431749158, 121.9380381277], 6, { // 港口默认定位到6级
          animate: true,
          duration: 1,
          noMoveStart: true
        })
      }, 50)
    },
    // 动态绘制台风路线
    animateDrawTyphoonLine (typhoonObj) {
      let _that = this
      let markerMode, labellayer, lineLayer, pointLayer
      let count = 0
      // eslint-disable-next-line new-cap
      this.myLayerGroupLine = new L.layerGroup()
      // eslint-disable-next-line new-cap
      this.myLayerGroupPoint = new L.layerGroup()
      // eslint-disable-next-line new-cap
      this.myLayerGroupMode = new L.layerGroup()
      let length = typhoonObj.polylinePoints.length
      this.myLayerGroupLine.clearLayers()
      this.myLayerGroupPoint.clearLayers()
      this.myLayerGroupMode.clearLayers()
      Object.assign(this.myLayerGroupLine, {
        type: 'typhoon'
      })
      Object.assign(this.myLayerGroupPoint, {
        type: 'typhoon'
      })
      Object.assign(this.myLayerGroupMode, {
        type: 'typhoon'
      })
      // 台风轨迹显示时把层级提高
      const canvasShips = document.querySelector('.leaflet-overlay-pane')
      if (canvasShips) {
        canvasShips.style.setProperty('z-index', '500', 'important')
      }
      this.typhoonTimer = setInterval(() => {
        if (count < length) {
          let _color = this.getTyphoonColor(typhoonObj.radius[count].grade)
          if (count < length - 1) {
            lineLayer = L.polyline([typhoonObj.polylinePoints[count], typhoonObj.polylinePoints[count + 1]], { color: _color })
            this.myLayerGroupLine.addLayer(lineLayer)
            this.map.addLayer(this.myLayerGroupLine)
          }
          pointLayer = L.circleMarker(typhoonObj.polylinePoints[count], { className: 'dIcon', radius: 3, color: _color, fillcolor: '#fff', fillOpacity: 1 }).bindTooltip(
            _that.getTyphoonInfoHtml(typhoonObj.radius[count], 0), { className: 'typhoon_info' }
          ).closeTooltip()
          this.myLayerGroupPoint.addLayer(pointLayer)
          // this.map.addLayer(myLayerGroupPoint)
          count++
          if (count === length) { // 最后一个画风圈
            labellayer = L.marker(typhoonObj.polylinePoints[length - 1], {
              icon: L.divIcon({
                html: `<div><img class="dIcon" src="data:image/gif;base64,R0lGODlhKAAoAIABAP///////yH/C05FVFNDQVBFMi4wAwEAAAAh/wtYTVAgRGF0YVhNUDw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNS1jMDE0IDc5LjE1MTQ4MSwgMjAxMy8wMy8xMy0xMjowOToxNSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIChNYWNpbnRvc2gpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjUzNjM1MUNBMDk5ODExRTRBNDM0OTFBMjNDNzk5QTY1IiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjUzNjM1MUNCMDk5ODExRTRBNDM0OTFBMjNDNzk5QTY1Ij4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6RTU1MTlCRUIwOTk3MTFFNEE0MzQ5MUEyM0M3OTlBNjUiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6RTU1MTlCRUMwOTk3MTFFNEE0MzQ5MUEyM0M3OTlBNjUiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz4B//79/Pv6+fj39vX08/Lx8O/u7ezr6uno5+bl5OPi4eDf3t3c29rZ2NfW1dTT0tHQz87NzMvKycjHxsXEw8LBwL++vby7urm4t7a1tLOysbCvrq2sq6qpqKempaSjoqGgn56dnJuamZiXlpWUk5KRkI+OjYyLiomIh4aFhIOCgYB/fn18e3p5eHd2dXRzcnFwb25tbGtqaWhnZmVkY2JhYF9eXVxbWllYV1ZVVFNSUVBPTk1MS0pJSEdGRURDQkFAPz49PDs6OTg3NjU0MzIxMC8uLSwrKikoJyYlJCMiISAfHh0cGxoZGBcWFRQTEhEQDw4NDAsKCQgHBgUEAwIBAAAh+QQJFAABACwAAAAAKAAoAAACvoyPmaDtv4CEtMpZs7tY+8M13PVF2IiOZUCmbuih7Ptm8kwqbQepKsgCnXg6mNFlGPZyx1qHyEjuaDTgZjhB1pKIaJPqhXHH0jAYS7R+z8I0mpHqxsXdcvYn/9FndrP7tneCA7c3GIInxXeYkzfoGKG4ZUhICbXGNvXHdIZlKHK5lXnFOFlJGflw87k5tYQ42XYX5crmyWcDFjubRkuHyGvBVCpkMyoZ9DFbVKiscTi8sKL7iixdB2edrb3tUQAAIfkECRQAAQAsAAAAACgAKAAAAsKMj6nL7Q9jAIqCe6WjCfuvdZzxYdPkhSdSpqKWHW3bRTEJsuhIPub+w81gnJJwFtxkjECkq2Gy/GgyJmMpLYIsrFwlBXbemteoWRzzds9N9JFXxaKH6qP92Z7i79R19g+385ZkJ4j1lWYVJ2e24KL4Fnkz8mhF9TS2OOhUOEmZM8coo7MJeKaV2LXoVkn5hVN6WgilVaoJNIooVbM6qwQZSdbzG9qmEmoaaFMsqFJxwvWp7ExMHbJnXUaY7bjC/Q1RAAAh+QQJFAABACwAAAAAKAAoAAACw4yPqcvtASCY8Nk1c933Zt2FxyeWxhehaWoi6gub7wmjVjSSUj17uqqIMXA7nO3EKYISxJ8TKKFhcpSiEkpZSmlZXsxYHXHB3ms1HLWae9DkmeTVKbdRYG9MRufqXTn1Hda0YnXkcqT1tyJk+CSGh+cHOYYBV5MIEvm49qWRqRnXCUjEJAeqODlUuEk218CWOKlKacra1WHKtZPms3hFN0TKEoQ16spYoVioB4wWdxzSCblcfFs23HJJjYRdos39DY5dAAAh+QQJFAABACwAAAAAKAAoAAACw4yPqcvdEKCbNNqKGZBq+50tIPKVZRiNh/mlXvgarLmq0ztfcuzeI0u68HQaCOjkOg05HWVsRlsVcU8o0kc9XmstRzerXXanRjCUy2xKrOdkGr1jA91kt3g+1lW3cVqetDfUl0QnAxgmSNiytfb1R0eVUoPWyAMUafPjJ1epBbhjx1loI4nIyeGYEBm6GCjV1MNa5mqoludIW2v7aDf4KpIza7lEcdrJ9EaW00eKIjvnrKGYGO1FXb2L3WGxrC3tDZ5RAAAh+QQFFAABACwAAAAAKAAoAAACv4yPqcvtBwKY8VlGc75cz1VxoCZ2iEeGpRGiESqtqsdKrurMG3uTj/6xpYQ+TIvC2yVgxmSlKDxBIc/dpucMSrPEHtIGXn5SXlpQywMjy69rWzxes4lhNLmt7HLD3JvCqgRUFYe2Fyglx4SY2AI3ONU12ACo6JKU88VGE1Xj+OhH+ZOH5fY1OYRX6oSxWEb3aqG5CklF5TWbx1pbI6hWOPIITNaRKctJrHmEczEntgIR+Wv6LPJLHbt8naPNzVEAADs="/></div>`,
                iconSize: [15, 15]
              }),
              riseOnHover: true,
              keyboard: true
            }).bindTooltip(typhoonObj.typhoonName + '(' + _that.getTyphoonDate(typhoonObj.radius[length - 1].time, 0, true) + ')', {
              direction: 'right',
              offset: [10, -5],
              permanent: true,
              opacity: '1',
              className: 'typhoon_info'
            }).openTooltip()
            this.myLayerGroupPoint.addLayer(labellayer)
            this.map.addLayer(this.myLayerGroupPoint)

            markerMode = L.marker(typhoonObj.polylinePoints[length - 1], {
              icon: L.divIcon({
                html: `<div><img class="dIcon" src="data:image/gif;base64,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"/></div>`,
                iconSize: [15, 15]
              })
            }).bindTooltip(
              _that.getTyphoonInfoHtml(typhoonObj.radius[length - 1], 0), { className: 'typhoon_info' }
            ).closeTooltip()
            this.myLayerGroupMode.addLayer(markerMode)
            this.map.addLayer(this.myLayerGroupPoint)
            this.map.addLayer(this.myLayerGroupMode)
          }
        } else {
          clearInterval(this.typhoonTimer)
          let forcastCount = 0
          let forcastLen = typhoonObj.forcastPoints.length
          if (typhoonObj.forcastPoints.length > 0) { // 未来台风预测节点绘制
            typhoonObj.forcastPoints.unshift(typhoonObj.polylinePoints[length - 1])
            typhoonObj.forcastInfo.unshift(typhoonObj.radius[length - 1])
            this.forcastTimer = setInterval(() => {
              if (forcastCount < forcastLen) {
                // if (forcastCount < forcastLen - 1) {
                lineLayer = L.polyline([typhoonObj.forcastPoints[forcastCount], typhoonObj.forcastPoints[forcastCount + 1]], { color: '#FEF301', dashArray: '10' })
                this.myLayerGroupLine.addLayer(lineLayer)
                this.map.addLayer(this.myLayerGroupLine)
                // }
                pointLayer = L.circleMarker(typhoonObj.forcastPoints[forcastCount], { className: 'dIcon', radius: 3, color: '#FEF301', fillcolor: '#fff', fillOpacity: 1 }).bindTooltip(
                  _that.getTyphoonInfoHtml(typhoonObj.forcastInfo[forcastCount], Number(typhoonObj.forcastInfo[forcastCount].fhour)), { className: 'typhoon_info' }
                ).closeTooltip()
                this.myLayerGroupPoint.addLayer(pointLayer)
                this.map.addLayer(this.myLayerGroupPoint)
                forcastCount++
                if (forcastCount === forcastLen) { // 最后一个画风圈
                  pointLayer = L.circleMarker(typhoonObj.forcastPoints[forcastLen], { className: 'dIcon', radius: 3, color: '#FEF301', fillcolor: '#fff', fillOpacity: 1 }).bindTooltip(
                    _that.getTyphoonInfoHtml(typhoonObj.forcastInfo[forcastLen], Number(typhoonObj.forcastInfo[forcastLen].fhour)), { className: 'typhoon_info' }
                  ).closeTooltip()
                  this.myLayerGroupPoint.addLayer(pointLayer)
                  this.map.addLayer(this.myLayerGroupPoint)
                }
              } else {
                clearInterval(this.forcastTimer)
              }
            }, 50)
          }
        }
      }, 50)
    },
    // 台风节点弹窗显示文本样式
    getTyphoonInfoHtml (data, addTime) {
      let _that = this
      let html = `
        <div>
          <h3>${data.name}</h3>
          <div>
            <span>时间：</span>
            <span>${_that.getTyphoonDate(data.time, addTime)}</span>
          </div>
          <div>
            <span>纬度：</span>
            <span>${data.lat}N</span>
          </div>
          <div>
            <span>经度：</span>
            <span>${data.lon}E</span>
          </div>
          <div>
            <span>风速：</span>
            <span>${data.mspeed}米/秒</span>
          </div>
          <div>
            <span>风力：</span>
            <span>${data.grade}级</span>
          </div>
          <div>
            <span>气压：</span>
            <span>${data.pressure}百帕</span>
          </div>
          <div>
            <span>移向：</span>
            <span>${_that.getTyphoonDirect(data.direction)}</span>
          </div>
          <div>
            <span>移速：</span>
            <span>${data.kspeed}千米/时</span>
          </div>
          <div>
            <span>七级半径：</span>
            <span>${data.radius7 === '0' ? '--' : data.radius7 + '千米'}</span>
          </div>
          <div>
            <span>十级半径：</span>
            <span>${data.radius10 === '0' ? '--' : data.radius10 + '千米'}</span>
          </div>
        </diiv>
      `
      return html
    },
    // 台风日期加8小时返回
    getTyphoonDate (str, addTime, ignore = false) {
      let _curDate = str.substring(0, 4) + '/' + str.substring(4, 6) + '/' + str.substring(6, 8) + ' ' + str.substring(8, 10) + ':' + str.substring(10, 12)
      let add8Time = new Date(_curDate).getTime() + (1000 * 60) * 60 * (addTime + 8) // 获取加8小时后的时间戳
      let _backDate = new Date(add8Time)
      let _backStr = ''
      if (ignore) { // 去除年份
        _backStr = (_backDate.getMonth() + 1) + '月' + _backDate.getDate() + '日 ' + _backDate.getHours() + '时'
      } else {
        _backStr = _backDate.getFullYear() + '年' + (_backDate.getMonth() + 1) + '月' + _backDate.getDate() + '日 ' + _backDate.getHours() + '时'
      }
      return _backStr
    },
    // 返回台风中文风向
    getTyphoonDirect (str) {
      let _backStr = '西北'
      if (str === 'E') _backStr = '东'
      if (str === 'W') _backStr = '西'
      if (str === 'S') _backStr = '南'
      if (str === 'N') _backStr = '北'
      if (str === 'NE') _backStr = '东北'
      if (str === 'NW') _backStr = '西北'
      if (str === 'SW') _backStr = '西南'
      if (str === 'SE') _backStr = '东南'
      if (str === 'ENE') _backStr = '东北偏东'
      if (str === 'NNE') _backStr = '东北偏北'
      if (str === 'NNW') _backStr = '西北偏北'
      if (str === 'WNW') _backStr = '西北偏西'
      if (str === 'WSW') _backStr = '西南偏西'
      if (str === 'SSW') _backStr = '西南偏南'
      if (str === 'SSE') _backStr = '东南偏南'
      if (str === 'ESE') _backStr = '东南偏东'
      return _backStr
    },
    //  台风路径颜色
    getTyphoonColor (num) {
      let _typhoonGrade = Number(num)
      if (_typhoonGrade < 10) return '#FEF301'
      if (_typhoonGrade < 12) return '#FC8F2B'
      if (_typhoonGrade < 14) return '#FE0405'
      if (_typhoonGrade < 16) return '#FE3BA3'
      if (_typhoonGrade > 15) return '#AD00D8'
      return '#FEF301'
    },
    // 台风主点数据处理
    typhoonDataHandler (data) {
      let polylinePoints = [] // 主点线坐标
      let radius = {} // 主所有风圈信息，点中信息
      let forcastPoints = [] // 预测线坐标
      let forcastInfo = [] // 预测线信息
      let unique = (arr, val) => {
        const res = new Map()
        return arr.filter(item => !res.has(item[val]) && res.set(item[val], 1))
      }
      let compare = (property) => { // 根据字段排序
        return function (a, b) {
          let val1 = a[property]
          let val2 = b[property]
          return val1 - val2
        }
      }
      data.forecast.forEach((item, idx) => {
        polylinePoints.push([Number(item.lat), Number(item.lon)])
        radius[idx] = item
        radius[idx].name = data.name
      })
      data.forcastList = unique(data.forcastList, 'fhour')
      data.forcastList.sort(compare('fhour'))
      data.forcastList.forEach((item, idx) => {
        forcastPoints.push([Number(item.lat), Number(item.lon)])
        forcastInfo[idx] = item
        forcastInfo[idx].name = data.name
      })
      let info = {
        polylinePoints: polylinePoints,
        radius: radius,
        typhoonName: data.name,
        forcastPoints: forcastPoints,
        forcastInfo: forcastInfo
      }
      return info
    },
    // 潮汐显示
    showTides () {
      let tideList = tideData.data
      tideList.forEach(item => {
        // eslint-disable-next-line no-undef
        let myIcon = L.icon({
          iconUrl: this.tideUrl
        })
        this.markerPoint = [ item.lat, item.lon ]
        // eslint-disable-next-line no-undef
        let markerLayer = L.marker(this.markerPoint, { title: item.cnname, icon: myIcon, zIndexOffset: 10 })
        Object.assign(markerLayer, {
          tideId: item.id,
          data: item,
          type: 'tide'
        })
        this.map.addLayer(markerLayer)
        markerLayer.on('click', item => {
          this.tideData = {
            modal: true,
            id: item.target.tideId,
            data: item.target.data
          }
        })
      })
    },
    // 船舶类型转换
    shipTypeTrans (typein) {
      let curShipType = ''
      let type = typein
      switch (true) {
        case (type >= 20 && type <= 29):
          curShipType = '地效应船'
          break
        case (type === 30):
          curShipType = '捕捞'
          break
        case (type === 31):
          curShipType = '拖引'
          break
        case (type === 32):
          curShipType = '拖引并且船长>200m 或船宽>25m'
          break
        case (type === 33):
          curShipType = '疏浚或水下作业'
          break
        case (type === 34):
          curShipType = '潜水作业'
          break
        case (type === 35):
          curShipType = '参与军事行动'
          break
        case (type === 36):
          curShipType = '帆船航行'
          break
        case (type === 37):
          curShipType = '娱乐船'
          break
        case (type >= 40 && type <= 49):
          curShipType = '高速船'
          break
        case (type === 50):
          curShipType = '引航船'
          break
        case (type === 51):
          curShipType = '搜救船'
          break
        case (type === 52):
          curShipType = '拖轮'
          break
        case (type === 53):
          curShipType = '港口供应船'
          break
        case (type === 54):
          curShipType = '载有防污染装置和设备的船舶'
          break
        case (type === 55):
          curShipType = '执法艇'
          break
        case (type === 56):
          curShipType = '备用-用于当地船舶的任务分配'
          break
        case (type === 57):
          curShipType = '备用-用于当地船舶的任务分配'
          break
        case (type === 58):
          curShipType = '医疗船（如 1949 年日内瓦公约及附加条款所规定）'
          break
        case (type === 59):
          curShipType = '符合 18 号决议（Mob-83）的船舶'
          break
        case (type >= 60 && type <= 69):
          curShipType = '客船'
          break
        case (type >= 60 && type <= 79):
          curShipType = '货船'
          break
        case (type >= 80 && type <= 89):
          curShipType = '油轮'
          break
        case (type >= 90 && type <= 99):
          curShipType = '其他类型的船舶'
          break
        case (type === 100):
          curShipType = '集装箱'
          break
        default:
          curShipType = '未知类型'
      }
      return curShipType
    },
    // 经纬度转换
    toLnglat (data) {
      let newData = data + ''
      if (data < 1) {
        newData = data * 1000000 + ''
      }
      if (newData.indexOf('.') < 0) {
        let _curIntData = data / 1000000
        newData = _curIntData + ''
      }
      let newArr = newData.split('.')
      let newStr = newArr[0] + '-' + (parseInt(newArr[1]) / 16666.36).toFixed(3)
      return newStr
    },
    // 预抵时间转换
    resetEtaTime (time) {
      if (time === '') return '-'
      let myDate = new Date()
      let year = myDate.getFullYear()
      if (parseInt(time.split('.')[0]) < 10) {
        return year + '-0' + time.split('.')[0] + '-' + time.split('.')[1]
      } else {
        return year + '-' + time.split('.')[0] + '-' + time.split('.')[1]
      }
    },
    // 时间转换
    nowTime (type, slefdefined) {
      return (() => {
        let myDate = slefdefined ? new Date(slefdefined) : new Date()
        let year = myDate.getFullYear()
        let month = myDate.getMonth() + 1
        let date = myDate.getDate()
        let h = myDate.getHours() // 获取当前小时数(0-23)
        let m = myDate.getMinutes() // 获取当前分钟数(0-59)
        let s = myDate.getSeconds()
        // 获取当前时间
        if (type === 'date') {
          return `${conver(year)}-${conver(month)}-${conver(date)}`
        } else if (type === 'datetime') {
          return `${conver(year)}-${conver(month)}-${conver(date)} ${conver(
            h
          )}:${conver(m)}:${conver(s)}`
        } else if (type === 'time') {
          return `${conver(h)}:${conver(m)}:${conver(s)}`
        } else if (type === 'timestamp') {
          return myDate.getTime()
        }
        return `${conver(h)}:${conver(m)}:${conver(s)}`
      })()
      // 日期时间处理
      function conver (s) {
        return s < 10 ? '0' + s : s
      }
    }
  },
  beforeMount () {
    // 地图配置
    this.options = {
      ak: 'a5bb8f37140d428391e1546d7b704413', // '57df9eaa033b44809d4bdaf919af457e',
      // 初始中心点坐标
      centerPoint: [30.1431749158, 121.9380381277],
      // 初始缩放级别
      zoom: this.mapNowRoom,
      // zoomSnap: 0.1, // 缩放递度
      // 最小缩放级别
      minZoom: 4,
      // 最大缩放级别
      maxZoom: 17,
      mapTypes: ['MT_GOOGLE', 'MT_SEA'],
      // 栅格
      gratings: { isShow: false },
      // 公司版权信息( 支持html )，默认Elane Inc.
      attribution: {
        isShow: true,
        emptyString:
          '&copy;2019 &nbsp;<a >兴通海运股份有限公司 保留所有版权 闽ICP备15001600号-3</a>'
      },
      // 默认图源设置（url，errorTileUrl），默认图源类型有：sea、google、satellite
      // tileLayer: {
      //   'sea': {
      //     url: 'http://m12.shipxy.com/tile.c?l=Na&m=o&x={x}&y={y}&z={z}',
      //     errorTileUrl: 'http://m.shipxy.com/img/erroshipxy.png'
      //   },
      //   'google': {
      //     url: 'http://mt2.google.cn/vt/lyrs=m@180000000&hl=zh-CN&gl=cn&src=app&x={x}&y={y}&z={z}&s=Gal',
      //     errorTileUrl: 'http://m.shipxy.com/img/erroshipxy.png'
      //   },
      //   'satellite': {
      //     url: {
      //       12: {
      //         // 船讯网瓦片
      //         url: 'http://g1.shipxy.com/tile.g?m=1&x={x}&y={y}&z={z}'
      //       },
      //       18: {
      //         // 谷歌卫星
      //         url: 'http://mt1.google.cn/vt?lyrs=y&hl=zh-CN&gl=CN&x={x}&y={y}&z={z}&s=Gal'
      //       }
      //     },
      //     errorTileUrl: 'http://m.shipxy.com/img/erroshipxy.png'
      //   }
      // },
      // miniMapControl: { isShow: true, options: {} }, // 鹰眼控件
      // 测距控件
      measureCtrl: {
        // 是否开启测距控件，默认：true
        isShow: false,
        // 是否显示测距按钮，默认：true
        showMeasurementsMeasureControl: false,
        // 是否显示删除按钮，默认：true
        showMeasurementsClearControl: false,
        // 是否显示切换单位按钮，默认：true
        showUnitControl: false,
        position: 'topleft'
      },
      // 鼠标移动悬浮经纬度控件
      mousePostionCtrl: { isShow: false, position: 'bottomright' },
      // 缩放工具控件的显示隐藏
      zoomControlElane: { isShow: true, position: 'bottomleft' },
      // 缩放级别显示控件
      zoomviewControl: { isShow: false, position: 'bottomleft' },
      // 地图切换控件的位置
      basemapsControl: { isShow: true, position: 'bottomright' },
      // 比例尺，控件
      scaleCtrl: { isShow: true, position: 'bottomleft' }
    }
    // 海图层单独添加，后台中转获取月更新数据
    // eslint-disable-next-line no-undef
    const baseUrl = process.env.NODE_ENV === 'development' ? config.baseUrl.dev : config.baseUrl.pro
    let token = getToken()
    let setMapUrl = baseUrl + 'ais/image/aisImage?token=' + token + '&l=Na&m=v&x={x}&y={y}&z={z}'
    var cwxSea = L.tileLayer(setMapUrl, {
      // eslint-disable-next-line no-undef
      crs: L.CRS.EPSG3395,
      subdomains: '',
      maxZoom: 17,
      minZoom: 2,
      label: '海图',
      id: 'MT_SEA_VIP',
      _name: 'MT_SEA_VIP',
      offsetC: false// 中国区域是否纠偏
    })
    // eslint-disable-next-line no-undef
    var cwx = L.tileLayer('http://tile.hifleet.com/wmts/google-sat-zh/osm_grid/{z}/{x}/{y}.png', {
    // var cwx = L.tileLayer('http://************:8081/xt_voyage_shipowner/ais/image/aisImage?token=2249e1b53ebcdef0c15a5b2871d683762245f598809080a440fa125c06f9f6b1&l=Na&m=v&x={x}&y={y}&z={z}', {
      // eslint-disable-next-line no-undef
      crs: L.CRS.EPSG3857,
      subdomains: '',
      maxZoom: 17,
      minZoom: 2,
      label: '卫星图',
      id: 'MT_SATELLITE',
      _name: 'MT_SATELLITE',
      offsetC: true// 中国区域是否纠偏

    })
    this.options.addTileLayer = [cwxSea, cwx]
    this.trackOption = {
      lineColor: 'tomato', // 线，颜色
      lineWeight: 2, // 线，粗细
      dash: true, // 是否为虚线
      dashArray: [5, 5], // 虚线间隔
      circleOverColor: 'yellow', // 轨迹点,鼠标划过时颜色
      isDilute: true // 是否抽稀模式显示
    }
  },
  created () {
    this.worker = new Worker('/worker.js')
    this.worker.postMessage(5)
    this.getHisSearchList()
    getCustomerDictEntryList({ dic_code: 'shipType' }).then(res => {
      if (res.data.Code === 10000) {
        this.shipTypeList = res.data.Result
      }
    })
  },
  mounted () {
    let _that = this
    this.winHeight = window.innerHeight - 230
    window.onresize = function () {
      return (function () {
        _that.winHeight = window.innerHeight - 230
      }())
    }
    // 创建地图示例
    this.map = new ShipxyAPI.Map('map', this.options)
    this.openShip(this.map)
    let options = {
      shipType: '', // typeList.join(','),
      'shipSog': '',
      'shipLength': [[999999, 9999999]],
      'shipCountry': '' // 0:中国(412、413、416),1:外国,空:全部
      // 'validIMO': false // 是否校验IMO合法性
    }
    // this.map.shipsService.setFilter({
    //   'shipLength': [[0, 40], [41, 80], [81, 160], [161, 240], [241, 320], [321, 9999]]
    // })
    if (!localStorage.shipNameList) return
    this.selfShipList = JSON.parse(localStorage.shipNameList)
    let _idx = this.selfShipList.findIndex(item => item.mmsi === '413693020') // 移除兴通油59 2022/12/12
    if (_idx) {
      this.selfShipList.splice(_idx, 1)
    }
    let curMmsiList = this.selfShipList.map(item => item.mmsi)
    this.map.shipsService.deleteAllFleetShips()
    setTimeout(() => {
      this.map.shipsService.setFilter(options)
      this.map.shipsService.setPointerEvents(!0)
      curMmsiList.forEach((item, idx) => {
        this.getManyShip(item, 'getAnchor', idx)
      })

      this.$nextTick(() => { // 调整缩略图为自定义月更新数据
        const imageContainer = document.querySelector('.MT_SEA_VIP')
        if (imageContainer) {
          const imgElement = imageContainer.querySelector('img')
          if (imgElement) {
            imgElement.src = require('@/assets/images/seaImage.jpg')
          }
        }
      })
      // this.getManyShip(curMmsiList.join(',')) // 旧的可多船查询
    }, 100)
  },
  beforeDestroy () {
    window.ShipService._timerStop()
  },
  watch: {
    mapNowRoom (n, o) {},
    nowCheckedShip (n, o) {
      if (n) {
        this.shipDetail(n)
      }
    }
  }
}
</script>

<style>
  .ship_detail_drawer .ivu-drawer-body {
    height: calc(100% - 30px) !important;
    padding-top: 0 !important;
  }
  .ship_detail_drawer .ivu-drawer-close {
    z-index: 100;
  }
  .map-box-collapsed {
    position: absolute;
    width: calc(100% - 30px);
    height: calc(100% - 40px);
    overflow: hidden;
  }
  .map-box {
    position: absolute;
    width: calc(100% - 35px);
    height: calc(100% - 40px);
    overflow: hidden;
  }
  #map {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: #a3ccff;
    z-index: 10;
 }
 .graysize {
  padding: 2px 7px;
  margin-right: 3px;
  border-radius: 25px;
  color: #353C50;
  background-color: #DAE8FF;
  font-weight: bold;
}
.greensize {
  color: #44BD46;
  font-weight: bold;
}
 .home-modal {
    position: absolute;
    width: 448px;
    max-height: 650px;
    overflow: auto;
    z-index: 999;
    left: 10px;
    top: 50px;
    background: #fff;
 }
 .home-modal::-webkit-scrollbar{
    display: none;
  }
  ::-webkit-scrollbar{width:0px}
 .home-modal .ivu-modal {
   position: absolute;
   top: 100px;
   left: 220px;
   background: #fff;
 }
 .home-modal .ivu-form .ivu-form-item-label {
    color: #7C8093;
    padding: 8px 12px 8px 0;
 }
 .home-modal .ivu-form .ivu-form-item-content {
    line-height: 28px;
    color: #383D4A;
 }
 .home-modal .modal-header, .voyage-panel-box .modal-header, .voyage-sign-box .modal-header {
  background: #4880FF;
  font-size: 20px;
  height: 50px;
  line-height: 50px;
  padding: 0 20px;
  color: #fff;
  position: sticky;
  top: 0;
  z-index: 9999;
 }
 .home-modal .modal-header-en {
   font-size: 14px;
   margin-left: 15px;
 }
 .home-modal .modal-header-close, .voyage-panel-box .modal-header .modal-header-close, .voyage-sign-box .modal-header .modal-header-close {
   position: absolute;
   cursor: pointer;
   line-height: 50px;
   right: 10px;
 }
 .home-modal .ivu-modal-header .ivu-modal-header-inner {
   color: #fff;
 }
 .home-modal .ivu-modal-body {
   padding: 10px 0;
 }
 .home-modal .ivu-form-item {
   margin-bottom: -5px;
 }
 .home-modal .ivu-divider-default {
   margin: 12px 0 5px;
 }
 .home-modal .modal-footer {
    display: flex;
    flex-flow: row nowrap;
    justify-content: space-between;
    height: 50px;
    padding: 10px 0;
    margin-top: 10px;
    align-items: center;
    border-top: 1px solid #e8eaec;
 }
 .modal-footer::before, .modal-footer::after {
   content: '';
   display: block;
 }
 .modal-footer button {
   width: 90px;
   background: #4880FF;
 }
 .track-panel-box {
    position: absolute;
    background: #fff;
    width: 448px;
    z-index: 999;
    margin-top: 10px;
    left: 470px;
    top: 40px;
 }
 .track-panel-box .modal-header {
  background: #4880FF;
  font-size: 14px;
  height: 50px;
  line-height: 50px;
  padding: 0 20px;
  color: #fff;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 9999;
 }
 .track-panel-close {
    position: absolute;
    right: 15px;
    top: 15px;
    cursor: pointer;
 }
 .voyage-panel-box {
    position: absolute;
    background: #fff;
    width: 550px;
    z-index: 500;
    left: 10px;
    top: 50px;
 }
 .voyage-sign-box {
    position: absolute;
    background: #fff;
    width: 450px;
    z-index: 500;
    right: 5px;
    top: 50px;
 }
 .ship_search_box {
  position: absolute;
  background: #fff;
  width: 480px;
  z-index: 500;
  right: 65px;
  top: 50px;
  box-shadow: 12px 8px 44px -12px rgba(27, 58, 146, 0.16);
 }
.ship_search_box .search_title {
  display: flex;
  color: #383D4A;
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  margin-bottom: 10px;
}
.ship_search_box .search_submit {
   text-align: right;
    margin-top: 30px;
 }
.ship_search_box .search_submit .search_reset {
   color: #007DFF;
   border: 1px solid #007DFF;
   margin-right: 10px;
 }
.ship_search_box .ship_search_table_title {
  height: 50px;
  line-height: 50px;
  background: #007DFF;
  color: #fff;
  font-size: 16px;
  padding: 0 10px;
}
.ship_search_box .ship_search_table_close {
  position: absolute;
  right: 10px;
  margin: 15px;
  cursor: pointer;
}
 .select-box {
  position: absolute;
  height: 30px;
  top: 5px;
  left: 10px;
  z-index: 99999;
 }
 .select-box .ivu-select-dropdown {
   max-height: 500px;
 }
 .select-search-btn {
    position: absolute;
    display: inline-block;
    width: 52px;
    height: 40px;
    line-height: 40px;
    background: #4880FF;
    text-align: center;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
    margin-left: -2px;
 }
 .select-box-content {
   width: 398px;
   height: 40px;
   line-height: 40px;
 }
 .select-box-content .ivu-select-selection {
   height: 40px;
 }
 .select-ship-content {
   width: 110px;
   margin-left: 5px;
   height: 32px;
 }
 .select-ship-content .ivu-select-selection {
   height: 32px;
 }
 .tool-box {
    position: absolute;
    z-index: 999;
    right: 5px;
    top: 5px;
 }
 .tool-box .ivu-select-single .ivu-select-selection .ivu-select-placeholder {
   color: #383D4A;
 }
 .tool-box .ivu-select-dropdown {
  min-width: 300px !important;
  left: 75px !important;
  max-height: 500px;
 }
 .full-screen-btn {
    width: 30px;
    height: 30px;
    line-height: 30px;
    background: #fff;
    text-align: center;
    border-radius: 2px;
    margin-left: 5px;
 }
 .voyage-box {
   cursor: pointer;
   background: #ECF2FF;
   margin: 0 8px;
   padding: 5px 10px;
 }
 .box-plan {
  background:  #F5F5F5;
  color: #666;
  margin-bottom: 10px;
  padding: 5px 10px 0;
 }
 .box-plan .voyage-port{
  height: 52px !important;
 }
 .voyage-box .voyage-port {
   height: 60px;
   color: #383D4A;
   font-size: 20px;
   font-weight: bold;
   display: flex;
  align-items: center;
  flex-flow: wrap;
 }
 .voyage-box .voyage-port .after-port {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
    width: 100%;
  }
 .voyage-box .voyage-line {
   text-align: center;
 }
 .voyage-box .load_text {
  background: #FFC742;
  padding: 5px;
  border-radius: 20px;
  color: #fff;
  font-size: 12px;
}
 .port_con {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
 }
 .typhoon_info {
    background: rgba(0,0,0,0.7);
    padding: 10px;
    color: #fff;
    font-size: 12px;
    line-height: 20px;
    border: none;
 }
 .dIcon {
    width: 32px;
    height: 32px;
    margin-left: -10px;
    margin-top: -10px;
 }
 .ship_detail_mt {
    color: #e1d9d9;
    font-size: 12px;
    margin-left: 2px;
 }
 .ais_area .ivu-modal-header {
    background: #4880ff;
    color: #fff;
  }
  .ais_area .ivu-modal-wrap {
    z-index: 60!important;
  }
  .ais_area .ivu-modal-close .ivu-icon-ios-close, .ais_area .ivu-modal-header .ivu-modal-header-inner {
    color: #fff;
  }
 .dynamic_arrow {
    position: absolute;
    top: 50%;
    left: -1px;
    cursor: pointer;
  }
  .dynamic_table .ivu-table-cell {
    padding-left: 2px;
    padding-right: 2px;
  }
  .rowClassName {
    font-size: 14px;
  }
  .dynamic_info_area .leaflet-popup-content-wrapper {
    padding: 0;
    border-radius: 0;
    font-size: 14px;
  }
  .dynamic_info_area .leaflet-popup-content p {
    margin: 0;
    padding: 6px 10px;
  }
  .datalisthead {
    margin: 5px 0 -10px 0;
    padding: 0 15px;
    font-size: 14px;
    font-weight: bold;
    text-align: center;
  }
  .datalisthead .ivu-col {
    padding: 0 5px;
  }
</style>
