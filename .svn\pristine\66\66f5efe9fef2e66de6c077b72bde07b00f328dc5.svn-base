<template>
  <div>
    <Card>
      <search @searchResults='searchResults' @selectOnChanged='selectOnChanged' :setSearch='setSearchData'
      @keydown.enter.native='searchResults' @resetResults='resetResults' @showTemplate="showTemplate"></search>
    </Card>
    <Card style="margin-top:10px;padding-top:6px;">
      <p slot='title' class="bold-font">动态导出</p>
      <div class="extra" slot="extra" >
        <Button v-if="!showBtn" @click="handlePreview" type="primary">预览</Button>
        <Button v-if="!showBtn" @click="downBtn" type="primary" style="margin-left: 5px;">下载</Button>
        <Button v-if="showBtn" @click="handlePreview" type="primary">预览并生成表格</Button>
      </div>
      <!-- 模板图片预览 -->
      <Modal
        v-model="modalTemplate"
        title="每日动态"
        width="1100"
        class="vertical-center-modal">
        <div class="modal_content">
          <img src="../../../assets/images/defaultTemplate.png" alt="" v-if="modalTemplateIndex === 0"><!-- 每日动态 -->
          <img src="../../../assets/images/shipTemplate.png" alt="" v-if="modalTemplateIndex === 1"><!-- 船舶动态 -->
          <img src="../../../assets/images/cnoocTemplate.png" alt="" v-if="modalTemplateIndex === 2"><!-- 中海油 -->
          <img src="../../../assets/images/petrifactionTemplate.png" alt="" v-if="modalTemplateIndex === 3"><!-- 浙石化 -->
          <img src="../../../assets/images/wanxingTemplate.png" alt="" v-if="modalTemplateIndex === 4"><!-- 万兴 -->
          <img src="../../../assets/images/fuLianTemplate.png" alt="" v-if="modalTemplateIndex === 5"><!-- 福炼 -->
        </div>
        <div slot="footer">
          <Button type="info" size="large" long @click="closeModalTemplate">确定</Button>
        </div>
      </Modal>
      <!-- 查询模板 0：公司每日动态 1：中海油 2：浙石化 3：万兴船舶 4：福炼 5:船舶 -->
      <defaultTemplateComponents :templateData="templateData" v-if="listData.template_key === '0'"></defaultTemplateComponents>
      <shipTemplateComponents ref="shipTemplateComponents" :shipTemplateData="shipTemplateData" v-if="listData.template_key === '5'"></shipTemplateComponents>
      <cnoocTemplateComponents :templateData="templateData" v-if="listData.template_key === '1'"></cnoocTemplateComponents>
      <petrifactionTemplateComponents :templateData="templateData" v-if="listData.template_key === '2'"></petrifactionTemplateComponents>
      <wanxingTemplateComponents :templateData="templateData" v-if="listData.template_key === '3'"></wanxingTemplateComponents>
      <fuLianTemplateComponents :templateData="templateData" v-if="listData.template_key === '4'"></fuLianTemplateComponents>
      <!-- 导出报表名称 -->
      <Modal
        width="285"
        @on-ok="ok"
        @on-cancel="cancel"
        :closable="false"
        v-model="modalReportName">
        <label class="labelname">表格名称</label>
        <Input class="inputText" v-model="reportName" />
      </Modal>
    </Card>
  </div>
</template>

<script>
import search from '_c/search' // 查询组件
import Cookies from 'js-cookie'
import API from '@/api/dynamicReport/exportReport'
import defaultTemplateComponents from './defaultTemplate' // 默认模块
import shipTemplateComponents from './shipTemplate'
import cnoocTemplateComponents from './cnoocTemplate' // 中海油船舶模板
import petrifactionTemplateComponents from './petrifactionTemplate' // 中海油船舶模板
import wanxingTemplateComponents from './wanxingTemplate' // 万兴船舶模板
import fuLianTemplateComponents from './fuLianTemplate' // 福炼船舶模板

export default {
  components: {
    search,
    defaultTemplateComponents,
    shipTemplateComponents,
    cnoocTemplateComponents,
    petrifactionTemplateComponents,
    wanxingTemplateComponents,
    fuLianTemplateComponents
  },
  data () {
    return {
      setSearchData: {// 查询设置，对象key值为回调参数
        node_date: {
          type: 'date',
          label: '时间',
          selected: '',
          width: 130,
          value: '',
          isdisable: false
        },
        cargo_company_id: {
          type: 'select',
          label: '公司',
          selectData: [],
          selected: '',
          placeholder: Cookies.get('company_name'),
          selectName: '',
          width: 230,
          value: '',
          filterable: true
        },
        template_key: {
          type: 'autoComplete',
          label: '模板',
          autoCompleteData: [],
          autoComplete: '',
          placeholder: '请选择',
          selectName: '',
          width: 150,
          value: '',
          filterable: true
        }
      },
      modalTemplateIndex: '', // 模板预览图片类型
      modalTemplate: false, // 模板预览弹窗
      reportName: '', // 报表名称
      modalReportName: false, // 报表名称弹窗
      listData: { // 获取模板数据
        node_date: '',
        cargo_company_id: '',
        template_key: '0'
      },
      templateData: [], // 存储模板数据
      shipTemplateData: [],
      listLoading: false,
      showBtn: false
    }
  },
  created () {
    this.getCurrentDate()
    // 获取公司
    API.customerCompanyList({ id: this.$store.state.conpanyId }).then(Response => {
      if (Response.data.Code === 10000) {
        Response.data.Result.map(item => {
          this.setSearchData.cargo_company_id.selectData.push({
            value: item.customer_company_id,
            label: item.customer_company_name
          })
        })
      }
    })
    this.setSearchData.template_key.autoCompleteData = ['每日动态', '船舶动态'] // , '中海油船舶动态模板', '浙石化动态模板', '万兴船舶动态模板', '福炼船舶动态模板'
    this.setSearchData.template_key.autoComplete = '每日动态'
    let month = new Date().getMonth() + 1
    let date = new Date().getDate()
    month = month < 10 ? '0' + month : month
    date = date < 10 ? '0' + date : date
    this.listData.node_date = new Date().getFullYear() + '-' + month + '-' + date
    this.getDefaultTemplate()
  },
  methods: {
    // 查询时间默认当前日期
    getCurrentDate () {
      this.setSearchData.node_date.selected = new Date()
    },
    // 开启模板预览弹窗
    showTemplate (index) {
      this.modalTemplateIndex = index
      this.modalTemplate = true
    },
    // 关闭模板预览弹窗
    closeModalTemplate () {
      this.modalTemplate = false
    },
    // 查询
    searchResults (e) {
      if (e.target) delete e.target
      this.listData.node_date = this.node_date
      this.listData.cargo_company_id = e.cargo_company_id
      let templateKeyValue = this.setSearchData.template_key.autoComplete
      if (templateKeyValue === '') {
        this.$Message.warning('模板类型不能为空!')
      } else {
        if (templateKeyValue === '每日动态') {
          this.showBtn = false
          this.listData.template_key = '0'
        } else if (templateKeyValue === '船舶动态') {
          this.showBtn = true
          this.listData.template_key = '5'
        } else if (templateKeyValue === '中海油船舶动态模板') {
          this.showBtn = false
          this.listData.template_key = '1'
        } else if (templateKeyValue === '浙石化动态模板') {
          this.showBtn = false
          this.listData.template_key = '2'
        } else if (templateKeyValue === '万兴船舶动态模板') {
          this.showBtn = false
          this.listData.template_key = '3'
        } else if (templateKeyValue === '福炼船舶动态模板') {
          this.showBtn = false
          this.listData.template_key = '4'
        }
        this.getDefaultTemplate()
      }
    },
    // 时间格式
    selectOnChanged (e) {
      if (e.key === undefined) {
        let month = new Date().getMonth() + 1
        let date = new Date().getDate()
        month = month < 10 ? '0' + month : month
        date = date < 10 ? '0' + date : date
        this.node_date = new Date().getFullYear() + '-' + month + '-' + date
      } else {
        this.node_date = e.key
      }
    },
    // 重置
    resetResults () {
      this.setSearchData.cargo_company_id.selected = ''
      this.setSearchData.template_key.autoComplete = '每日动态'
      this.getCurrentDate()
      this.listData.template_key = '0'
      this.listData.cargo_company_id = ''
      this.getDefaultTemplate()
    },
    // 获取模板
    getDefaultTemplate () {
      this.listLoading = true
      this.shipTemplateData = []
      API.exportTemplateList(this.listData).then(response => {
        if (response.data.Code === 10000) {
          if (this.listData.template_key === '5') {
            this.shipTemplateData = response.data.Result
          } else {
            this.templateData = response.data.Result
          }
          this.listLoading = false
        } else {
          this.$Message.error(response.data.Message)
        }
      }).catch(
        setTimeout(() => {
          this.listLoading = false
        }, 100)
      )
    },
    // 预览操作
    handlePreview () {
      if (this.setSearchData.template_key.autoComplete === '') {
        this.$Message.warning('模板不能为空！')
      } else {
        this.modalReportName = true
        let d = new Date()
        this.reportName = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate() + ' ' + this.setSearchData.template_key.autoComplete
      }
    },
    // 下载操作
    downBtn () {
      if (this.setSearchData.template_key.autoComplete === '') {
        this.$Message.warning('模板不能为空！')
      } else {
        let data = {
          node_date: this.listData.node_date,
          cargo_company_id: this.listData.cargo_company_id,
          template_key: this.listData.template_key,
          template_name: this.setSearchData.template_key.autoComplete
        }
        API.exportTemplateReport(data).then(res => {
          if (res.data.Code === 10000) {
            window.open(res.data.fileUrl, '_blank')
          } else {
            this.$Message.error(res.data.Message)
          }
        })
      }
    },
    // 确认导出报表
    ok () {
      let data = {}
      if (this.listData.template_key === '5') {
        if (this.$refs.shipTemplateComponents.shipList.length === 0) return
        let curShipObj = {}
        let curDetailJson = []
        this.$refs.shipTemplateComponents.shipList.map(item => {
          let objStr = item.pidx + '' // 转为字符串
          if (!curShipObj[item.pidx]) {
            curShipObj[objStr] = []
            delete item.pidx
            delete item.cidx
            delete item.dnmIsEdit
            delete item.vmIsEdit
            delete item.nvmIsEdit
            curShipObj[objStr].push(item)
          } else {
            delete item.pidx
            delete item.cidx
            delete item.dnmIsEdit
            delete item.vmIsEdit
            delete item.nvmIsEdit
            curShipObj[objStr].push(item)
          }
        })
        for (let idx in curShipObj) {
          curDetailJson.push({
            ship_type: curShipObj[idx][0].ship_type,
            ship_type_name: curShipObj[idx][0].ship_type_name,
            shipDynamicInfo: curShipObj[idx]
          })
        }
        data = {
          node_date: this.listData.node_date,
          cargo_company_id: this.listData.cargo_company_id,
          template_key: this.listData.template_key,
          template_name: this.reportName,
          detailJson: JSON.stringify(curDetailJson)
        }
      } else {
        data = {
          node_date: this.listData.node_date,
          cargo_company_id: this.listData.cargo_company_id,
          template_key: this.listData.template_key,
          template_name: this.reportName
        }
      }
      API.exportTemplateReport(data).then(response => {
        if (response.data.Code === 10000) {
          sessionStorage.setItem('wpsUrl', response.data.wpsUrl)
          sessionStorage.setItem('token', response.data.token)
          const jump = this.$router.resolve({ name: 'viewFile' })
          window.open(jump.href, '_blank')
        } else {
          this.$Message.error(response.data.Message)
        }
      })
    },
    // 关闭报表名称弹窗
    cancel () {
      this.modalReportName = false
    }
  }
}
</script>
<style scoped>
.labelname {
  font-size: 13px;
  line-height: 32px;
}
.inputText {
  float: right;
  width: calc(100% - 65px);
}
.modal_content img {
  max-width: 100%;
}
</style>
