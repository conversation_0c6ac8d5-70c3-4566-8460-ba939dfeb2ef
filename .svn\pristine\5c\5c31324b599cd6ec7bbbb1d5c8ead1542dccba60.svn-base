import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 获取公司管理列表
export function queryCompanyPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/us/company/queryUsCompanyPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取公司类型
export function queryCompanyTypeList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/us/company/queryUsCompanyTypeList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 同步管理员
export function adminConfig (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/auth/user/config/addSpUserAuthConfig',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 航次动态PC权限配置
export function permissionConfig (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/auth/company/config/changeSpComConfigAuth',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 公司入驻
export function addUsCompany (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/us/company/addUsCompany',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 注销删除
export function delectCompany (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/us/company/delUsCompany',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 修改（船东公司或货主公司修改）
export function updateCompany (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/us/company/updateUsCompany',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 成员列表
export function queryCompanyUserPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/us/company/getUsCompanyUserListPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 添加成员-获取账号下拉
export function accountList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/auth/pass/config/queryAccountList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 添加成员-新增保存
export function joinCompanyBatch (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/us/company/designeeJoinCompanyBatch',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 审核入驻公司（列表-查看-企业信息-入驻状态：通过/拒绝）
export function auditCompany (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/us/company/auditUsCompany',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 公司管理-成员信息-批量移除公司
export function delectSelectedCompany (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/us/company/removeMemberBatch',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  queryCompanyPage,
  queryCompanyTypeList,
  adminConfig,
  permissionConfig,
  addUsCompany,
  delectCompany,
  queryCompanyUserPage,
  updateCompany,
  accountList,
  joinCompanyBatch,
  auditCompany,
  delectSelectedCompany
}