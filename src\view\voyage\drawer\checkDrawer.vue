<template>
  <Drawer
    v-model="modalData.modal"
    :title="modalData.title"
    width="700"
    :closable="false"
    :mask-closable="false"
    @on-visible-change="visibleChange"
  >
    <div v-if="modalData.modal" slot="header" class="drawer_header">
      <span class="title_span">{{ modalData.data.ship_name }}</span>
      <span class="title_span">{{ modalData.data.voyage_no }}</span>
      <span class="title_span">{{ modalData.data.port_name }} - {{ modalData.data.wharf_name }}</span>
      <span class="title_span con_text con_load">{{ modalData.data.port_type === '1' ? '装' : '卸' }}</span>
    </div>
    <div class="add_btn">
      <Button type="primary" @click="addNode">新增</Button>
    </div>
    <div v-if="modalData.type === 'common'">
      <div v-for="(item, idx) in nodeList" :key="idx" :class="idx <= nodeList.length - 1 ? 'line_bottom' : ''">
        <div style="position: relative;">
          <span class="line-area">
            <span style="width: 50px;">节点：</span>{{ item.node_name }}
          </span>
          <span class="line-area">
            节点时间：
            <DatePicker type="datetime" format="yyyy-MM-dd HH:mm" :value="item.node_date" @on-change="data=>item.node_date=data" style="width: 150px"></DatePicker>
          </span>
          <Button class="del_btn" size="small" @click="delNode(item)">删除</Button>
        </div>
        <div style="margin-top: 10px;" v-if="item.node_status_code == 'ZY'">
          <span class="line-area">
            <span style="width: 50px;">货主：</span>{{ item.dynamicCustomer.cargo_company_name }}
          </span>
          <span class="line-area">
            <span style="width: 50px;">货品：</span>{{ item.dynamicCustomer.goods_name }} &nbsp; {{ item.dynamicCustomer.amount }} 吨
          </span>
        </div>
        <div class="line-area">
          <span style="width: 50px;">备注：</span>
          <Input type="textarea" :autosize="true" class="text-area" v-model="item.remark" />
        </div>
      </div>
    </div>
    <div v-if="modalData.type === 'mount'" style="position: relative;">
      <div v-for="(item, idx) in nodeList" :key="idx" :class="idx <= nodeList.length - 1 ? 'line_bottom' : ''">
        <span class="line-area"><span style="width: 50px;">节点：</span>{{ item.node_name ? item.node_name : '--' }}</span>
        <span class="line-area">
          节点时间：
          <DatePicker type="datetime" format="yyyy-MM-dd HH:mm" :value="item.node_date" @on-change="data=>item.node_date=data" @on-ok="handleok(idx)" style="width: 150px"></DatePicker>
          <Button class="del_btn" size="small" @click="delNode(item)">删除</Button>
        </span>
        <div style="margin-top: 10px;">
          <span class="line-area">
            <span style="width: 50px;">货主：</span>{{ item.dynamicCustomer.cargo_company_name }}
          </span>
          <span class="line-area">
            <span style="width: 50px;">货品：</span>{{ item.dynamicCustomer.goods_name }} &nbsp; {{ item.dynamicCustomer.amount }}吨
          </span>
        </div>
        <span class="line-area" style="margin-top: 10px;">
          <div style="margin-right: 20px;" v-if="item.dynamicCustomer && item.dynamicCustomer.load_flowmeter_amount && item.dynamicCustomer.load_flowmeter_amount !== ''">
            <span style="width: 90px;">装港流量计：</span>
            <Input style="width: 80px; margin-left: 5px;" v-model="item.dynamicCustomer.load_flowmeter_amount" />
            <span style="margin-left: 5px;">吨</span>
          </div>
          <div style="margin-right: 20px;" v-if="item.dynamicCustomer && item.dynamicCustomer.load_ship_amount && item.dynamicCustomer.load_ship_amount !== ''">
            <span style="width: 100px;">装港商检船板量:</span>
            <Input style="width: 80px; margin-left: 5px;" v-model="item.dynamicCustomer.load_ship_amount" />
            <span style="margin-left: 5px;">吨</span>
          </div>
          <div style="margin-right: 20px;" v-if="item.dynamicCustomer && item.dynamicCustomer.load_tank_amount && item.dynamicCustomer.load_tank_amount !== ''">
            <span style="width: 90px;">装港岸罐量:</span>
            <Input style="width: 80px; margin-left: 5px;" v-model="item.dynamicCustomer.load_tank_amount" />
            <span style="margin-left: 5px;">吨</span>
          </div>
          <div style="margin-right: 20px;" v-if="item.dynamicCustomer && item.dynamicCustomer.unload_ship_amount && item.dynamicCustomer.unload_ship_amount !== ''">
            <span style="width: 90px;">卸港商检船板量:</span>
            <Input style="width: 80px; margin-left: 5px;" v-model="item.dynamicCustomer.unload_ship_amount" />
            <span style="margin-left: 5px;">吨</span>
          </div>
          <div style="margin-right: 20px;" v-if="item.dynamicCustomer && item.dynamicCustomer.unload_tank_amount && item.dynamicCustomer.unload_tank_amount !== ''">
            <span style="width: 90px;">卸港岸罐量:</span>
            <Input style="width: 80px; margin-left: 5px;" v-model="item.dynamicCustomer.unload_tank_amount" />
            <span style="margin-left: 5px;">吨</span>
          </div>
        </span>
        <div class="line-area">
          <span style="width: 50px;">备注：</span>
          <Input type="textarea" :autosize="true" class="text-area" v-model="item.remark" />
        </div>
      </div>
    </div>
    <div v-if="modalData.type === 'mate'">
      <span>航线：{{ modalData.data.port_line }}</span>
      <span class="line-area">
        <span>航程：</span>
        <Input v-model="modalData.data.sea_mile" />
        <span style="margin-left: 10px;">海里</span>
      </span>
    </div>
    <div class="demo-drawer-footer">
      <Button style="margin-right: 10px;" @click="handleCancel">取消</Button>
      <Button type="primary" @click="saveModalData">保存</Button>
    </div>
    <editModal :modalData="editModal" @addSuccess="editBack"></editModal>
  </Drawer>
</template>
<script>
import API from '@/api/voyageManage/voyageCheck'
import { queryVoHistoryMPage } from '@/api/voyageHistory'
import editModal from '../../voyageHistory/editModal'

export default {
  props: {
    modalData: Object
  },
  components: {
    editModal
  },
  data () {
    return {
      editModal: {},
      nodeList: [], // 节点数据列表
      nodeObj: { // 货运量节点信息
        node_name: '',
        node_date: '',
        remark: ''
      }
    }
  },
  methods: {
    // 获取列表数据
    getList () {
      API.queryDynamicList({
        dynamic_wharf_id: this.modalData.data.wharf_id,
        port_type: this.modalData.data.port_type,
        ship_id: this.modalData.data.ship_id,
        voyage_id: this.modalData.data.voyage_id_index,
        node_code_in: this.modalData.node_code_in
      }).then(res => {
        // if (this.modalData.type === 'mount') {
        //   this.nodeObj = res.data.Result[0]
        // }
        // if (this.modalData.type === 'common') {
        this.nodeList = res.data.Result
        // }
      })
    },
    addNode () {
      queryVoHistoryMPage({
        voyage_id: this.modalData.data.voyage_id_index,
        pageSize: 10,
        pageIndex: 1
      }).then(res => {
        if (res.data.Code === 10000) {
          this.modalData.modal = false
          this.editModal = {
            modal: true,
            isCheck: true,
            data: res.data.Result[0]
          }
        }
      })
    },
    // 新增返回
    editBack () {
      this.modalData.modal = true
      this.editModal.modal = false
      this.$emit('nodeDataBack')
      this.getList()
    },
    // 删除节点
    delNode (item) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>是否确定删除节点信息？</p>',
        loading: true,
        onOk: () => {
          API.delDynamic({
            voyage_id: this.modalData.data.voyage_id_index,
            id: item.id
          }).then(res => {
            if (res.data.Code === 10000) {
              this.$Message.success(res.data.Message)
              if (this.modalData.type === 'mount') {
                this.$Modal.remove()
                this.$nextTick(() => {
                  this.modalData.modal = false
                  this.$emit('nodeDataBack')
                })
              } else {
                this.$nextTick(() => {
                  this.$Modal.remove()
                  this.getList()
                })
              }
            } else {
              this.$Message.warning(res.data.Message)
            }
          })
        }
      })
    },
    // 保存数据
    saveModalData () {
      if (this.modalData.type === 'mate') { // 航程保存
        API.updateMonthReportDetail({
          voyage_id: this.modalData.data.voyage_id_index,
          port_id: this.modalData.data.port_id,
          wharf_id: this.modalData.data.wharf_id,
          port_type: this.modalData.data.port_type,
          sea_mile: this.modalData.data.sea_mile
        }).then(res => {
          if (res.data.Code === 10000) {
            this.$Message.success(res.data.Message)
            this.$nextTick(() => {
              this.$emit('nodeDataBack')
              this.modalData.modal = false
            })
          } else {
            this.$Message.warning(res.data.Message)
          }
        })
      }

      // if (this.modalData.type === 'mount') { // 货量保存
      //   let _param = {
      //     id: this.nodeObj.id,
      //     node_code: this.nodeObj.node_code,
      //     node_status_code: this.nodeObj.node_status_code,
      //     unit: this.modalData.data.unit,
      //     voyage_id: this.nodeObj.voyage_id,
      //     node_date: this.nodeObj.node_date,
      //     remark: this.nodeObj.remark
      //   }
      //   if (this.nodeObj.dynamicCustomer && this.nodeObj.dynamicCustomer.load_flowmeter_amount) { // 装港流量计入参
      //     Object.assign(_param, {
      //       load_flowmeter_amount: this.nodeObj.dynamicCustomer.load_flowmeter_amount
      //     })
      //   }
      //   if (this.nodeObj.dynamicCustomer && this.nodeObj.dynamicCustomer.load_ship_amount) { // 装港船板量
      //     Object.assign(_param, {
      //       load_ship_amount: this.nodeObj.dynamicCustomer.load_ship_amount
      //     })
      //   }
      //   if (this.nodeObj.dynamicCustomer && this.nodeObj.dynamicCustomer.load_tank_amount) { // 装港罐量
      //     Object.assign(_param, {
      //       load_tank_amount: this.nodeObj.dynamicCustomer.load_tank_amount
      //     })
      //   }
      //   if (this.nodeObj.dynamicCustomer && this.nodeObj.dynamicCustomer.unload_ship_amount) { // 卸港船板量
      //     Object.assign(_param, {
      //       unload_ship_amount: this.nodeObj.dynamicCustomer.unload_ship_amount
      //     })
      //   }
      //   if (this.nodeObj.dynamicCustomer && this.nodeObj.dynamicCustomer.unload_tank_amount) { // 卸港罐量
      //     Object.assign(_param, {
      //       unload_tank_amount: this.nodeObj.dynamicCustomer.unload_tank_amount
      //     })
      //   }
      //   API.updateDynamic(_param).then(res => {
      //     if (res.data.Code === 10000) {
      //       this.$Message.success(res.data.Message)
      //       this.$nextTick(() => {
      //         this.$emit('nodeDataBack')
      //         this.modalData.modal = false
      //       })
      //     } else {
      //       this.$Message.warning(res.data.Message)
      //     }
      //   })
      // }

      if (this.modalData.type === 'common' || this.modalData.type === 'mount') {
        let detailList = this.nodeList.map(item => {
          if (this.modalData.type === 'common') {
            return {
              id: item.id,
              node_date: item.node_date,
              expect_node_id: item.expect_node_id,
              expect_date: item.expect_date,
              berth_id: item.berth_id,
              remark: item.remark
            }
          }
          if (this.modalData.type === 'mount') {
            return {
              id: item.id,
              node_date: item.node_date,
              unit: item.dynamicCustomer.unit,
              node_status_code: item.node_status_code,
              node_code: item.node_code,
              load_flowmeter_amount: item.dynamicCustomer.load_flowmeter_amount,
              load_ship_amount: item.dynamicCustomer.load_ship_amount,
              load_tank_amount: item.dynamicCustomer.load_tank_amount,
              unload_ship_amount: item.dynamicCustomer.unload_ship_amount,
              unload_tank_amount: item.dynamicCustomer.unload_tank_amount,
              remark: item.remark
            }
          }
        })
        API.updateDynamicBatch({
          voyage_id: this.modalData.data.voyage_id_index,
          detailJson: JSON.stringify(detailList)
        }).then(res => {
          if (res.data.Code === 10000) {
            this.$Message.success(res.data.Message)
            this.$nextTick(() => {
              this.$emit('nodeDataBack')
              this.modalData.modal = false
            })
          } else {
            this.$Message.warning(res.data.Message)
          }
        })
      }
    },
    // 时间日期触发
    handleok (idx) {
      let d = new Date()
      let dData = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate() + ' ' + d.getHours() + ':' + d.getMinutes()
      if (this.nodeList[idx].node_date === '') {
        this.nodeList[idx].node_date = dData
      }
    },
    // 取消保存
    handleCancel () {
      this.modalData.modal = false
    },
    // 弹窗显隐操作
    visibleChange (val) {
      if (val && this.modalData.type !== 'mate') {
        this.getList()
      }
    }
  }
}
</script>
<style scoped>
  .drawer_header {
    padding: 5px 0;
  }
  .title_span {
    margin-right: 10px;
  }
  .con_text {
    width: 25px;
    height: 25px;
    line-height: 25px;
    text-align: center;
    display: inline-block;
    border-radius: 50%;
    -moz-border-radius: 50%;
    -webkit-border-radius: 50%;
  }
  .con_load {
    color: #017db4;
    background-color: #facd91;
  }
  .line-area {
    display: inline-flex;
    align-items: center;
    margin-left: 30px;
  }
  .line-area span {
    width: 60px;
  }
  .text-area {
    width: 580px;
    padding: 10px;
    margin-top: 10px;
  }
  .add_btn {
    position: absolute;
    right: 15px;
    top: 10px;
  }
  .del_btn {
    position: absolute;
    right: 12px;
    border: 1px solid #f36262;
    color: #f36262;
  }
  .line_bottom {
    padding-bottom: 10px;
    margin-bottom: 25px;
    border-bottom: 1px solid rgba(45,45,45,0.1);
    box-shadow: 2px 2px 1px 1px rgba(0,0,0,0.1);
  }
</style>
