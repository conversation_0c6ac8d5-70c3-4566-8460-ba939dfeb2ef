<template>
  <div :class="isYunZhiJia ? 'yun_area' : ''">
    <Row v-if="isHisShow" class="tab_btn_area">
      <Col span="12">
        <div class="tab_btn" :style="isCurTab === 'his' ? 'background: #2d8cf0; color: #fff' : 'background: #fff; color: #000'" @click="isCurTab = 'his'">{{ hisText }}</div>
      </Col>
      <Col span="12">
        <span v-if="isNewTip">
          <span>点击</span>
          <span style="color: red;">
            查看最新动态
            <Icon type="md-trending-down" />
          </span>
        </span>
        <div class="tab_btn" :style="isCurTab === 'new' ? 'background: #2d8cf0; color: #fff' : 'background: #fff; color: #000'" @click="isCurTab = 'new', isNewTip = false">{{ newText }}</div>
      </Col>
    </Row>
    <div class="time_end" v-if="isAis">
      <p><span style="font-weight: bold;">倒计时:</span><span style="color: red;"> {{ minutes }} 分钟 {{ seconds }} 秒</span> 后自动<a @click="refreshList">刷新</a></p>
    </div>
    <Row v-if="!isYunZhiJia" class="datalisthead" :style="(isYunZhiJia || !isAis) ? 'top: -18px' : ''">
      <Col span="3"  style="text-align: center;">船名</Col>
      <Col span="4" style="text-align: center;">目的港</Col>
      <Col span="6" style="text-align: center;">到港时间</Col>
      <Col span="4" style="text-align: center;">
        状态
        <Icon style="cursor: pointer;" :type="isUp ? 'md-arrow-dropup' : 'md-arrow-dropdown'" @click="statusChange"></Icon>
      </Col>
      <Col span="4" style="text-align: center;">预警(h)</Col>
      <Col span="3" style="text-align: center;">人员</Col>
    </Row>
    <!-- 历史数据展示 -->
    <div v-if="isHisShow && isCurTab === 'his'">
      <!-- 历史数据开始 -->
      <H3 v-if="!isYunZhiJia" style="margin-top: 15px;">自营船舶</H3>
      <div v-for="(item, index) in hisModelList" :key="'his' + index" class="voyage-list" @click="shipClick(item)">
        <div v-if="isYunZhiJia" class="voyage-area-info-mobile" justify="center" align="middle" :style="getHisDelayStyle(item)">
          <Row>
            <Col span="6">
              <div class="detail_title">船名：</div>
              <div class="detail_con">{{ item.ship_name }}</div>
            </Col>
            <Col span="7">
              <div class="detail_title">目的港：</div>
              <div class="detail_con">{{ item.destination_port }}</div>
            </Col>
            <Col span="11">
              <div class="detail_title">到港时间：</div>
              <div class="detail_con">{{ item.arrival_time }}</div>
            </Col>
          </Row>
          <Row class="detail_row">
            <Col span="6">
              <div class="detail_title">状态：</div>
              <div class="detail_con">{{ item.node_type }}</div>
            </Col>
            <Col span="7">
              <div class="detail_title">预警(h)：</div>
              <div class="detail_con">{{ item.warning_time }}</div>
            </Col>
            <Col span="11">
              <div class="detail_title">操作员：</div>
              <div class="detail_con">{{ item.bussiness_name }}</div>
            </Col>
          </Row>
        </div>
        <Row v-else class="voyage-area-info" justify="center" align="middle" :style="getHisDelayStyle(item)">
          <Col span="3" style="text-align: center;">
            <div>{{ item.ship_name }}</div>
          </Col>
          <Col span="4" style="text-align: center;">
            <div>{{ item.destination_port }}</div>
          </Col>
          <Col span="6" style="text-align: center;">
            <div>{{ item.arrival_time }}</div>
          </Col>
          <Col span="4" style="text-align: center;">
            <div>{{ item.node_type }}</div>
          </Col>
          <Col span="4" style="text-align: center;">
            <div>{{ item.warning_time }}</div>
          </Col>
          <Col span="3" style="text-align: center;">
            <div>{{ item.bussiness_name }}</div>
          </Col>
        </Row>
      </div>
      <div v-if="hisModelList.length === 0" class="datalistempty">暂无数据</div>
      <!-- 历史数据结束 -->

      <!-- 万邦历史数据开始 -->
      <H3 v-if="!isYunZhiJia && isWanBang" style="margin-top: 15px;">万邦船舶</H3>
      <H3 v-if="isYunZhiJia && isWanBang" style="margin-top: 15px; ">兴通万邦船舶</H3>
      <template v-if="isWanBang">
        <div v-for="(item, index) in wanbangHisList" :key="'wanbanghis' + index" class="voyage-list" @click="shipClick(item)">
          <div v-if="isYunZhiJia" class="voyage-area-info-mobile" justify="center" align="middle" :style="getHisDelayStyle(item)">
            <Row>
              <Col span="6">
                <div class="detail_title">船名：</div>
                <div class="detail_con">{{ item.ship_name }}</div>
              </Col>
              <Col span="7">
                <div class="detail_title">目的港：</div>
                <div class="detail_con">{{ item.destination_port }}</div>
              </Col>
              <Col span="11">
                <div class="detail_title">到港时间：</div>
                <div class="detail_con">{{ item.arrival_time }}</div>
              </Col>
            </Row>
            <Row class="detail_row">
              <Col span="6">
                <div class="detail_title">状态：</div>
                <div class="detail_con">{{ item.node_type }}</div>
              </Col>
              <Col span="7">
                <div class="detail_title">预警(h)：</div>
                <div class="detail_con">{{ item.warning_time }}</div>
              </Col>
              <Col span="11">
                <div class="detail_title">操作员：</div>
                <div class="detail_con">{{ item.bussiness_name }}</div>
              </Col>
            </Row>
          </div>
          <Row v-else class="voyage-area-info" justify="center" align="middle" :style="getHisDelayStyle(item)">
            <Col span="3" style="text-align: center;">
              <div>{{ item.ship_name }}</div>
            </Col>
            <Col span="4" style="text-align: center;">
              <div>{{ item.destination_port }}</div>
            </Col>
            <Col span="6" style="text-align: center;">
              <div>{{ item.arrival_time }}</div>
            </Col>
            <Col span="4" style="text-align: center;">
              <div>{{ item.node_type }}</div>
            </Col>
            <Col span="4" style="text-align: center;">
              <div>{{ item.warning_time }}</div>
            </Col>
            <Col span="3" style="text-align: center;">
              <div>{{ item.bussiness_name }}</div>
            </Col>
          </Row>
        </div>
      </template>
      <div v-if="wanbangHisList.length === 0 && isWanBang" class="datalistempty">暂无数据</div>
      <!-- 万邦历史数据结束 -->
    </div>
    <!-- 最新数据展示 -->
    <div v-if="!isHisShow || (isHisShow && isCurTab === 'new')">
      <!-- 自营开始 -->
      <H3 v-if="!isYunZhiJia" style="margin-top: 15px;">自营船舶</H3>
      <div v-for="(item, index) in selfModelList" :key="'self' + index" class="voyage-list" @click="shipClick(item)">
        <div v-if="isYunZhiJia" class="voyage-area-info-mobile" justify="center" align="middle" :style="getDelayStyle(item)">
          <Row>
            <Col span="6">
              <div class="detail_title">船名：</div>
              <div class="detail_con">{{ item.ship_name }}</div>
            </Col>
            <Col span="7">
              <div class="detail_title">目的港：</div>
              <div class="detail_con">{{ item.port_name }}</div>
            </Col>
            <Col span="11">
              <div class="detail_title">到港时间：</div>
              <div class="detail_con">{{ item.ata }}</div>
            </Col>
          </Row>
          <Row class="detail_row">
            <Col span="6">
              <div class="detail_title">状态：</div>
              <div v-if="isOtherStatus(item.status_code)" class="detail_con" style="color: red;">
                <Icon type="md-alert" size="10" color="red" />
                {{ item.navistatus }}
              </div>
              <div v-else class="detail_con">{{ item.navistatus }}</div>
            </Col>
            <Col span="7">
              <div class="detail_title">预警(h)：</div>
              <div class="detail_con">{{ item.delayHour }}</div>
            </Col>
            <Col span="11">
              <div class="detail_title">操作员：</div>
              <div class="detail_con">{{ item.business_name }}</div>
            </Col>
          </Row>
        </div>
        <Row v-else class="voyage-area-info" justify="center" align="middle" :style="getDelayStyle(item)">
          <Col span="3" style="text-align: center;">
            <div>{{ item.ship_name }}</div>
          </Col>
          <Col span="4" style="text-align: center;">
            <div>{{ item.port_name }}</div>
          </Col>
          <Col span="6" style="text-align: center;">
            <div>{{ item.ata }}</div>
          </Col>
          <Col span="4" style="text-align: center;">
            <div v-if="isOtherStatus(item.status_code)" style="color: red;">
              <Icon type="md-alert" size="10" color="red" />
              {{ item.navistatus }}
            </div>
            <div v-else>{{ item.navistatus }}</div>
          </Col>
          <Col span="4" style="text-align: center;">
            <div>{{ item.delayHour }}</div>
          </Col>
          <Col span="3" style="text-align: center;">
            <div>{{ item.business_name }}</div>
          </Col>
        </Row>
      </div>
      <div v-if="selfModelList.length === 0" class="datalistempty">暂无数据</div>
      <!-- 自营结束 -->
      <!-- 期租开始 -->
      <H3 style="margin-top: 15px; " v-if="!isYunZhiJia">期租船舶</H3>
      <template v-if="!isYunZhiJia">
        <div v-for="(item, index) in charterModelList" :key="'charter' + index" class="voyage-list" @click="shipClick(item)">
          <Row :class="isYunZhiJia ? 'voyage-area-info-mobile' : 'voyage-area-info'" justify="center" align="middle" :style="getDelayStyle(item)">
            <Col span="3" style="text-align: center;">
              <div>{{ item.ship_name }}</div>
            </Col>
            <Col span="4" style="text-align: center;">
              <div>{{ item.port_name }}</div>
            </Col>
            <Col span="6" style="text-align: center;">
              <div>{{ item.ata }}</div>
            </Col>
            <Col span="4" style="text-align: center;">
              <div v-if="isOtherStatus(item.status_code)" style="color: red;">
                <Icon type="md-alert" size="10" color="red" />
                {{ item.navistatus }}
              </div>
              <div v-else>{{ item.navistatus }}</div>
            </Col>
            <Col span="4" style="text-align: center;" :style="item.delayHour >= 72 ? 'color: red;' : ''">
              <div>{{ item.delayHour }}</div>
            </Col>
            <Col span="3" style="text-align: center;">
              <div>{{ item.business_name }}</div>
            </Col>
          </Row>
        </div>
        <div v-if="charterModelList.length === 0" class="datalistempty">暂无数据</div>
      </template>
      <!-- 期租结束 -->
      <!-- 国际开始 -->
      <H3 v-if="!isYunZhiJia" style="margin-top: 15px; ">国际船舶</H3>
      <template v-if="!isYunZhiJia">
        <div v-for="(item, index) in internalModelList" :key="'internal' + index" class="voyage-list" @click="shipClick(item)">
          <Row :class="isYunZhiJia ? 'voyage-area-info-mobile' : 'voyage-area-info'" justify="center" align="middle" :style="getDelayStyle(item)">
            <Col span="3" style="text-align: center;">
              <div>{{ item.ship_name }}</div>
            </Col>
            <Col span="4" style="text-align: center;">
              <div>{{ item.port_name }}</div>
            </Col>
            <Col span="6" style="text-align: center;">
              <div>{{ item.ata }}</div>
            </Col>
            <Col span="4" style="text-align: center;">
              <div v-if="isOtherStatus(item.status_code)" style="color: red;">
                <Icon type="md-alert" size="10" color="red" />
                {{ item.navistatus }}
              </div>
              <div v-else>{{ item.navistatus }}</div>
            </Col>
            <Col span="4" style="text-align: center;" :style="item.delayHour >= 72 ? 'color: red;' : ''">
              <div>{{ item.delayHour }}</div>
            </Col>
            <Col span="3" style="text-align: center;">
              <div>{{ item.business_name }}</div>
            </Col>
          </Row>
        </div>
        <div v-if="internalModelList.length === 0" class="datalistempty">暂无数据</div>
      </template>
      <!-- 国际结束 -->
      <!-- 万邦开始 -->
      <H3 v-if="isWanBang" style="margin-top: 15px; ">兴通万邦船舶</H3>
      <template v-if="isWanBang">
        <div v-for="(item, index) in otherModelList" :key="'other' + index" class="voyage-list" @click="shipClick(item)">
          <div v-if="isYunZhiJia" class="voyage-area-info-mobile" justify="center" align="middle" :style="getDelayStyle(item)">
            <Row>
              <Col span="6">
                <div class="detail_title">船名：</div>
                <div class="detail_con">{{ item.ship_name }}</div>
              </Col>
              <Col span="7">
                <div class="detail_title">目的港：</div>
                <div class="detail_con">{{ item.port_name }}</div>
              </Col>
              <Col span="11">
                <div class="detail_title">到港时间：</div>
                <div class="detail_con">{{ item.ata }}</div>
              </Col>
            </Row>
            <Row class="detail_row">
              <Col span="6">
                <div class="detail_title">状态：</div>
                <div v-if="isOtherStatus(item.status_code)" class="detail_con" style="color: red;">
                  <Icon type="md-alert" size="10" color="red" />
                  {{ item.navistatus }}
                </div>
                <div v-else class="detail_con">{{ item.navistatus }}</div>
              </Col>
              <Col span="7">
                <div class="detail_title">预警(h)：</div>
                <div class="detail_con">{{ item.delayHour }}</div>
              </Col>
              <Col span="11">
                <div class="detail_title">操作员：</div>
                <div class="detail_con">{{ item.business_name }}</div>
              </Col>
            </Row>
          </div>
          <Row v-else class="voyage-area-info" justify="center" align="middle" :style="getDelayStyle(item)">
            <Col span="3" style="text-align: center;">
              <div>{{ item.ship_name }}</div>
            </Col>
            <Col span="4" style="text-align: center;">
              <div>{{ item.port_name }}</div>
            </Col>
            <Col span="6" style="text-align: center;">
              <div>{{ item.ata }}</div>
            </Col>
            <Col span="4" style="text-align: center;">
              <div v-if="isOtherStatus(item.status_code)" style="color: red;">
                <Icon type="md-alert" size="10" color="red" />
                {{ item.navistatus }}
              </div>
              <div v-else>{{ item.navistatus }}</div>
            </Col>
            <Col span="4" style="text-align: center;">
              <div>{{ item.delayHour }}</div>
            </Col>
            <Col span="3" style="text-align: center;">
              <div>{{ item.business_name }}</div>
            </Col>
          </Row>
        </div>
      </template>
      <div v-if="otherModelList.length === 0 && isWanBang" class="datalistempty">暂无数据</div>
      <!-- 万邦结束 -->
      <div v-if="!isAis" style="position: absolute; right: 0px; bottom: 40px; cursor: pointer;z-index: 9999999; background: rgba(0,0,0,0.3); color: #fff;">
        <Icon type="md-refresh" size="26" @click="getList"/>
      </div>
      <Spin fix v-if="loading"></Spin>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import API from '@/api/shipManagement'
import { pushMsg } from '@/api/setting/historicalSMS'

export default {
  props: {
    isAis: {
      default: false
    }
  },
  data () {
    return {
      isMsgSended: false, // 短信推送验证
      isNewTip: true,
      isCurTab: 'his', // 当前所在tab页
      hisText: '历史',
      newText: '最新动态', // 最新时间
      isHisShow: false, // 是否显示历史数据
      addTimer: null,
      isYunZhiJia: false, // 是否云之家嵌入
      countdown: 30 * 60, // 初始倒计时时间，单位为秒
      timerId: null, // setTimeout 的计时器 ID
      loading: false,
      map: null,
      trackId: 0, // 获取轨迹获取次数
      isWanBang: false,
      options: {},
      isUp: true,
      shipIgnoreList: ['413375790', '413375810', '413376840', '412379380', '412379370', '413376570', '413693020'],
      selfShipList: [], // 船舶列表
      anchorMmsiList: [], // 锚泊船舶mmsi列表
      anchorIdx: [], // 锚泊船舶位置记录
      shipDetailList: [], // 船舶详细信息
      dataList: [], // 船舶显示列表
      selfModelList: [], // 自营船舶列表
      charterModelList: [], // 期租船舶列表
      internalModelList: [], // 国际船舶列表
      otherModelList: [], // 其它船舶列表
      wanbangHisList: [],
      hisModelList: [] // 历史船舶列表
    }
  },
  computed: {
    minutes () {
      return Math.floor(this.countdown / 60)
    },
    seconds () {
      return this.countdown % 60
    }
    // isWanBang () { // 用爱兰的号限制万邦船舶展示
    //   return localStorage.getItem('userDataId') === '18' || localStorage.getItem('userDataId') === '4498'
    // }
  },
  methods: {
    async getList (val = 0) {
      let that = this
      this.trackId = 0
      if (!localStorage.shipNameList) return
      this.loading = true
      this.selfShipList = JSON.parse(localStorage.shipNameList)
      this.selfShipList = this.selfShipList.filter(item => item.business_model !== '')
      this.dataList = []
      this.anchorMmsiList = []
      this.anchorIdx = []
      let totalShipList = []
      let mmsiList = []
      this.selfShipList.forEach((item) => { // 剔除万邦船舶
        if (val === 1) { // 针对爱兰账号处理万邦船舶展示
          totalShipList.push(item)
          mmsiList.push(item.mmsi)
        } else {
          if (this.isWanBang) {
            totalShipList.push(item)
            mmsiList.push(item.mmsi)
          } else {
            if (!this.shipIgnoreList.includes(item.mmsi)) {
              totalShipList.push(item)
              mmsiList.push(item.mmsi)
            }
          }
        }
      })
      let queryUrl = 'https://api.shipxy.com/apicall/GetManyShip?v=2&k=a5bb8f37140d428391e1546d7b704413&enc=1&id=' + mmsiList.join(',')
      await axios.get(queryUrl).then(res => {
        that.shipDetailList = res.data.data
        totalShipList.forEach((item, idx) => {
          that.dataList.push({
            business_model: item.business_model,
            business_name: item.business_name,
            mmsi: item.mmsi,
            ship_name: item.ship_name,
            status_code: that.shipDetailList[idx].navistat,
            port_name: that.shipDetailList[idx].dest,
            ata: that.shipDetailList[idx].eta_std,
            navistatus: CanvasShipUtils.getDisValue(that.shipDetailList[idx].navistat, 'naviStatus', 'zh_CN'),
            delayTime: 0,
            delayHour: '--',
            delayTimeStr: '--'
          })

          if (that.dataList[idx].status_code === 1) { // 锚泊状态
            that.anchorIdx.push(idx)
            that.getLastVoyageListShipXy(item.mmsi, idx, totalShipList.length)
          }
        })
      })
      if (this.isAis || this.isYunZhiJia) {
        let paramList = []
        this.addTimer = setTimeout(() => { // 5分钟后再提交数据  防止有人开起来就提交导致数据重复
          this.selfModelList.map(item => {
            if (parseFloat(item.delayTime) > 0) {
              paramList.push({
                mmsi: item.mmsi,
                ship_name: item.ship_name,
                destination_port: item.port_name,
                arrival_time: item.ata,
                status_code: item.status_code,
                node_type: item.navistatus,
                warning_time: item.delayTime
              })
            }
          })
          this.otherModelList.map(item => {
            if (parseFloat(item.delayTime) > 0) {
              paramList.push({
                mmsi: item.mmsi,
                ship_name: item.ship_name,
                destination_port: item.port_name,
                arrival_time: item.ata,
                status_code: item.status_code,
                node_type: item.navistatus,
                warning_time: item.delayTime
              })
            }
          })
          if (paramList.length === 0) {
            paramList.push({
              mmsi: 413219350,
              ship_name: '空船舶',
              destination_port: '空港口',
              arrival_time: new Date(),
              status_code: item.status_code,
              node_type: 0,
              warning_time: 0
            })
          }
          let _param = { detailJson: JSON.stringify(paramList) }
          API.addBatchAisEarlyWarning(_param).then(res => {
            clearTimeout(this.addTimer)
            if (res.data.Code === 10000) {
              console.log('数据提交成功!')
            } else {
              console.log('数据提交失败!')
            }
          })
        }, 5 * 60 * 1000)
      }
    },
    isOtherStatus (statusStr) {
      let _status = parseFloat(statusStr)
      if (_status === 0 || _status === 1 || _status === 5) {
        return false
      }
      return true
    },
    startCountdown () {
      this.timerId = setInterval(() => {
        if (this.countdown > 0) {
          this.countdown--
        } else {
          this.refreshList()
        }
      }, 1000)
    },
    refreshList () {
      this.$nextTick(() => {
        this.stopCountdown()
        this.getList()
        this.restartCountdown()
      })
    },
    restartCountdown () {
      this.countdown = 30 * 60 // 重新设置倒计时时间
      this.startCountdown() // 开始新的倒计时
    },
    stopCountdown () {
      clearInterval(this.timerId) // 清除计时器
    },
    // 船舶点击事件
    shipClick (item) {
      if (!this.isAis) return
      this.$emit('shipDetailSelect', item.mmsi + '__' + item.shipName)
    },
    // 获取预警时间警戒色 -- 最新数据
    getDelayStyle (item) {
      if (item.business_model !== '1') return ''
      if (parseFloat(item.delayHour) >= 24 && parseFloat(item.delayHour) < 36) {
        return 'background: #ADECAD;'
      }
      if (parseFloat(item.delayHour) >= 36 && parseFloat(item.delayHour) < 48) {
        return 'background: yellow;'
      }
      if (parseFloat(item.delayHour) >= 48 && parseFloat(item.delayHour) < 72) {
        return 'background: #ff6c00; color: #fff;'
      }
      if (parseFloat(item.delayHour) >= 72) {
        return 'background: red; color: #fff;'
      }
    },
    // 历史数据预警时间警戒色
    getHisDelayStyle (item) {
      if (parseFloat(item.warning_time) >= 24 && parseFloat(item.warning_time) < 36) {
        return 'background: #ADECAD;'
      }
      if (parseFloat(item.warning_time) >= 36 && parseFloat(item.warning_time) < 48) {
        return 'background: yellow;'
      }
      if (parseFloat(item.warning_time) >= 48 && parseFloat(item.warning_time) < 72) {
        return 'background: #ff6c00; color: #fff;'
      }
      if (parseFloat(item.warning_time) >= 72) {
        return 'background: red; color: #fff;'
      }
    },
    // 排序
    statusChange () {
      this.isUp = !this.isUp
      if (this.isUp) {
        this.dataList.sort((a, b) => {
          if (parseFloat(a.delayTime) > parseFloat(b.delayTime)) {
            return -1
          } else if (parseFloat(a.delayTime) < parseFloat(b.delayTime)) {
            return 1
          } else {
            if (parseFloat(a.status_code) > parseFloat(b.status_code)) {
              return -1
            } else if (parseFloat(a.status_code) < parseFloat(b.status_code)) {
              return 1
            } else {
              return 0
            }
          }
        })
      } else {
        this.dataList.sort((a, b) => {
          if (parseFloat(a.delayTime) > parseFloat(b.delayTime)) {
            return -1
          } else if (parseFloat(a.delayTime) < parseFloat(b.delayTime)) {
            return 1
          } else {
            if (parseFloat(b.status_code) > parseFloat(a.status_code)) {
              return -1
            } else if (parseFloat(b.status_code) < parseFloat(a.status_code)) {
              return 1
            } else {
              return 0
            }
          }
        })
      }
      this.modelSort()
    },
    // 按运营类型分类
    modelSort () {
      this.selfModelList = [] // 自营船舶列表
      this.charterModelList = [] // 期租船舶列表
      this.internalModelList = [] // 国际船舶列表
      this.otherModelList = [] // 其它船舶列表
      let _outTime = parseInt(localStorage.getItem('outTime'))
      this.dataList.forEach(item => {
        if (item.business_model === '1' && !this.shipIgnoreList.includes(item.mmsi)) { // 自营的根据人员限制
          if (this.isYunZhiJia && _outTime) {
            if (parseFloat(item.delayTime) >= 72 && _outTime === 72) {
              this.selfModelList.push(item)
            } else if (parseFloat(item.delayTime) >= 48 && _outTime === 48) {
              this.selfModelList.push(item)
            } else if (parseFloat(item.delayTime) >= 36 && _outTime === 36) {
              this.selfModelList.push(item)
            } else if (parseFloat(item.delayTime) >= 24 && _outTime === 24) {
              this.selfModelList.push(item)
            }
          } else {
            if (this.isYunZhiJia) { // 云之家回显只显示锚泊数据
              if (parseFloat(item.delayTime) > 0) {
                this.selfModelList.push(item)
              }
            } else {
              this.selfModelList.push(item)
            }
          }
        } else if (item.business_model === '2') {
          this.charterModelList.push(item)
        } else if (item.business_model === '3') {
          this.internalModelList.push(item)
        } else {
          if (this.isYunZhiJia) { // 云之家回显只显示锚泊数据
            if (parseFloat(item.delayTime) > 0) {
              this.otherModelList.push(item)
            }
          } else {
            this.otherModelList.push(item)
          }
        }
      })
      // this.dataList = [...[], ...selfModelList, ...charterModelList, ...internalModelList, ...otherModelList]
    },
    // 获取历史航次列表 shipXy
    async getLastVoyageListShipXy (mmsi, idx, len) {
      const that = this
      let dateTime = new Date()
      let currentYear = dateTime.getFullYear()
      let currentMonth = dateTime.getMonth()
      let currentDate = dateTime.getDate()
      let startDay = new Date(currentYear, currentMonth - 1, currentDate).getTime().toString().substring(0, 10)
      let endDay = dateTime.getTime().toString().substring(0, 10)
      // 获取时间段内航次 船舶靠港记录
      let url1 = 'http://api.shipxy.com/apicall/GetPortOfCallByShip?k=a5bb8f37140d428391e1546d7b704413' + '&v=2&mmsi=' + mmsi + '&timetype=2&begin=' + startDay + '&end=' + endDay
      // 获取港口及到港时间
      await jQuery.getJSON(url1 + '&jsf=?', function (result) {
        if (result.records.length > 0) {
          let startDate = result.records[result.records.length - 1].atd || result.records[result.records.length - 1].ata // 先拿离港，如果没有离港就拿到港时间
          that.getCurWarnList(mmsi, idx, len, startDate)
        }
      })
    },
    // 获取预警时间 hifleet
    async getCurWarnList (mmsi, idx, len, startDate) {
      const that = this
      const date = new Date()
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')

      let currentTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
      let _param = {
        mmsis: mmsi,
        zoom: 1,
        startdates: startDate,
        endates: currentTime
      }
      await API.getShipsTrajectory(_param).then(res => {
        if (res.data.status !== '1') {
          this.pushMsg()
        }
        if (res.data.data.length > 0) {
          let delayHour = that.getDelayHours(mmsi, res.data.data[0].offers)
          Object.assign(that.dataList[idx], {
            delayTime: parseFloat(delayHour) === 0 ? 0.1 : delayHour,
            delayHour: parseFloat(delayHour) === 0 ? 0.1 : delayHour,
            delayTimeStr: parseFloat(delayHour) === 0 ? 0.1 : delayHour
          })
        } else {
          Object.assign(that.dataList[idx], {
            port_name: '--',
            ata: '--'
          })
        }
        this.trackId += 1
        if (this.trackId === this.anchorIdx.length) {
          setTimeout(() => {
            that.dataList.sort((a, b) => {
              if (parseFloat(a.delayTime) > parseFloat(b.delayTime)) {
                return -1
              } else if (parseFloat(a.delayTime) < parseFloat(b.delayTime)) {
                return 1
              } else {
                if (parseInt(a.status_code) > parseInt(b.status_code)) {
                  return -1
                } else if (parseInt(a.status_code) < parseInt(b.status_code)) {
                  return 1
                } else {
                  return 0
                }
              }
            })
            that.$nextTick(() => {
              that.modelSort()
            })
            that.loading = false
          }, 2000)
        }
      }).catch(() => {
        this.pushMsg()
      })
    },
    pushMsg () {
      if (this.isMsgSended) { // 已经推送过就不再调用
        return
      }
      this.isMsgSended = true
      let _param = {
        mobiles: '15260223700, 15260337735',
        sms_content: '航次预警信息登录异常，请及时查看'
      }
      pushMsg(_param).then(res => {
        if (res.data.Code === 10000) {

        }
      })
      // 设置3分钟内不允许重复调用
      setTimeout(() => {
        this.isMsgSended = false
      }, 3 * 60 * 1000)
    },
    // 获取船舶当前航行状态数据 -- 船队在线
    async getCurVoyagePort (mmsi, idx) {
      const that = this
      // 获取时间段内航次 船舶靠港记录
      let url = 'https://www.hifleet.com/portofcall/getshipcurrentvoyagestatus?mmsi=' + mmsi + '&_v=5.3.77&'
      // 获取港口及到港时间
      await axios.get(url).then(result => {
        if (result.data.list) {
          Object.assign(that.dataList[idx], {
            port_name: that.dataList[idx].port_name, // result.data.list.startport.indexOf('[') > -1 ? result.data.list.startport.split('[')[0] : result.data.list.startport,
            ata: that.dataList[idx].status_code === 0 ? that.dataList[idx].ata : that.getBerthAta(result.data.list.stoppedHourFormat, that.dataList[idx].ata),
            delayTime: 0,
            delayHour: '--',
            delayTimeStr: '--'
          })
        }
      })
    },
    getDelayHours (mmsi, list) {
      let totalMinutes = 0
      let backHour = 0
      if (list.length > 0) {
        list.forEach(item => {
          if (item.accumulatetime && item.accumulatetime !== '-') {
            const timeStr = item.accumulatetime.replace('h', ' ').replace('m', '')
            const [hours, minutes] = timeStr.split(' ')
            totalMinutes += parseInt(hours) * 60 + parseInt(minutes)
          }
        })
        backHour = (parseFloat(totalMinutes / 60)).toFixed(1)
      } else {
        backHour = '-'
      }
      return backHour
    },
    hoursChange (time) { // 时间转换
      let backHours = '--'
      if (time && time !== '') {
        if (time.indexOf('d') > -1) { // 带有天数的时间
          let regex = /^(\d+)d([\d.]+)h$/
          let match = regex.exec(time)
          let days = parseInt(match[1], 10)
          let hours = parseFloat(match[2])
          backHours = days * 24 + hours
        } else { // 只有小时的时间
          backHours = parseFloat(time.split('h')[0])
        }
      }
      return backHours
    },
    getBerthAta (time, ata) { // 重新计算靠泊状态下的到港时间
      let backAta = '--'
      let curTime = new Date()
      if (time && time !== '') {
        if (time.indexOf('d') > -1) { // 带有天数的时间
          let regex = /^(\d+)d([\d.]+)h$/
          let match = regex.exec(time)
          let days = parseInt(match[1], 10)
          let hours = parseFloat(match[2])
          let totalHours = days * 24 + hours
          let disTime = new Date(curTime.getTime() - totalHours * 60 * 60 * 1000)
          let dayStr = disTime.getDay()
          if (disTime.getDay() < 10) {
            dayStr = '0' + dayStr
          }
          backAta = disTime.getFullYear() + '-' + disTime.getMonth() + 1 + '-' + dayStr + ' ' + disTime.toLocaleTimeString()
        } else { // 只有小时的时间
          let hours = parseFloat(time.split('h')[0])
          let disTime = new Date(curTime.getTime() - hours * 60 * 60 * 1000)
          let dayStr = disTime.getDay()
          if (disTime.getDay() < 10) {
            dayStr = '0' + dayStr
          }
          backAta = disTime.getFullYear() + '-' + disTime.getMonth() + 1 + '-' + dayStr + ' ' + disTime.toLocaleTimeString()
        }
      } else {
        backAta = ata
      }
      return backAta
    },
    // 获取历史靠泊港口数据 -- 船讯网 废弃
    async getHistoryVoyagePort (mmsi, idx) {
      const that = this
      let dateTime = new Date()
      let currentYear = dateTime.getFullYear()
      let currentMonth = dateTime.getMonth()
      let currentDate = dateTime.getDate()
      let startDay = new Date(currentYear, currentMonth - 1, currentDate).getTime().toString().substring(0, 10)
      let endDay = dateTime.getTime().toString().substring(0, 10)
      // 获取时间段内航次 船舶靠港记录
      let url1 = 'http://api.shipxy.com/apicall/GetPortOfCallByShip?k=a5bb8f37140d428391e1546d7b704413' + '&v=2&mmsi=' + mmsi + '&timetype=2&begin=' + startDay + '&end=' + endDay
      // 获取港口及到港时间
      await jQuery.getJSON(url1 + '&jsf=?', function (result) {
        if (result.records.length > 0) {
          if (that.dataList[idx].status_code !== 1) { // 非锚泊状态
            Object.assign(that.dataList[idx], {
              port_name: that.dataList[idx].status_code === 0 ? that.dataList[idx].port_name : result.records[result.records.length - 1].portname_cn,
              ata: that.dataList[idx].status_code === 0 ? that.dataList[idx].ata : result.records[result.records.length - 1].ata,
              delayTime: 0,
              delayHour: '--',
              delayTimeStr: '--'
            })
          } else { // 锚泊状态
            // 计算锚泊预期时间
            let voyageAta = new Date(result.records[result.records.length - 1].ata).getTime() // 最后一个航次到港时间
            let curAta = new Date(that.dataList[idx].ata).getTime() // 预抵时间
            if (curAta - voyageAta > 0) { // 判断最后一个航次与预抵时间哪个时间最大，按大的进行换算锚泊时长
              Object.assign(that.dataList[idx], {
                port_name: that.dataList[idx].port_name,
                ata: that.dataList[idx].ata
              })
              that.diffTime(new Date(that.dataList[idx].ata), dateTime, idx)
            } else {
              Object.assign(that.dataList[idx], {
                port_name: result.records[result.records.length - 1].portname_cn,
                ata: result.records[result.records.length - 1].ata
              })
              that.diffTime(new Date(result.records[result.records.length - 1].ata), dateTime, idx)
            }
          }
          setTimeout(() => {
            that.dataList.sort((a, b) => {
              if (a.delayTime > b.delayTime) {
                return -1
              } else if (a.delayTime < b.delayTime) {
                return 1
              } else {
                if (a.status_code > b.status_code) {
                  return -1
                } else if (a.status_code < b.status_code) {
                  return 1
                } else {
                  return 0
                }
              }
            })
            that.$nextTick(() => {
              that.modelSort()
            })
            that.loading = false
          }, 500)
        } else {
          Object.assign(that.dataList[idx], {
            port_name: '--',
            ata: '--'
          })
        }
      })
    },
    diffTime (startDate, endDate, idx) {
      let diffvalue = endDate.getTime() - startDate.getTime()
      if (diffvalue > 0) {
        let backStr = ''
        let minute = 1000 * 60
        let hour = minute * 60
        let day = hour * 24
        let month = day * 30

        var monthC = diffvalue / month
        var dayC = diffvalue / day
        var hourC = diffvalue / hour
        var minC = diffvalue / minute
        if (parseInt(monthC) >= 1) {
          backStr = parseInt(monthC) + '个月前'
        } else if (parseInt(dayC) > 1) {
          backStr = parseInt(dayC) + '天前'
        } else if (parseInt(dayC) === 1) {
          backStr = '昨天'
        } else if (parseInt(hourC) >= 1) {
          backStr = parseInt(hourC) + '小时前'
        } else if (parseInt(minC) >= 1) {
          backStr = parseInt(minC) + '分钟前'
        } else {
          backStr = '刚刚'
        }
        Object.assign(this.dataList[idx], {
          delayTime: parseInt(diffvalue), // 时间戳差
          delayHour: parseFloat(hourC).toFixed(2),
          delayTimeStr: backStr // 相差时间字符
        })
      } else {
        Object.assign(this.dataList[idx], {
          delayTime: 1,
          delayHour: '--',
          delayTimeStr: '刚刚' // 相差时间字符
        })
      }
    },
    parseTime (str) {
      let _backStr = str
      if (parseFloat(str) < 10) {
        _backStr = '0' + str
      }
      return _backStr
    },
    getAisMess (insert_time) {
      API.queryAisEarlyWarningList({ insert_time: insert_time }).then(res => {
        this.loading = false
        if (res.data.Code === 10000) {
          // this.hisModelList = res.data.Result
          let _outTime = parseInt(localStorage.getItem('outTime'))
          if (res.data.Result.length > 0) {
            res.data.Result.map(item => {
              if (this.isYunZhiJia && _outTime) {
                if (parseFloat(item.warning_time) >= 72 && _outTime === 72) {
                  if (this.shipIgnoreList.includes(item.mmsi)) {
                    this.wanbangHisList.push(item)
                  } else {
                    this.hisModelList.push(item)
                  }
                } else if (parseFloat(item.warning_time) >= 48 && _outTime === 48) {
                  if (this.shipIgnoreList.includes(item.mmsi)) {
                    this.wanbangHisList.push(item)
                  } else {
                    this.hisModelList.push(item)
                  }
                } else if (parseFloat(item.warning_time) >= 36 && _outTime === 36) {
                  if (this.shipIgnoreList.includes(item.mmsi)) {
                    this.wanbangHisList.push(item)
                  } else {
                    this.hisModelList.push(item)
                  }
                } else if (parseFloat(item.warning_time) >= 24 && _outTime === 24) {
                  if (this.shipIgnoreList.includes(item.mmsi)) {
                    this.wanbangHisList.push(item)
                  } else {
                    this.hisModelList.push(item)
                  }
                }
              } else {
                if (this.shipIgnoreList.includes(item.mmsi)) {
                  this.wanbangHisList.push(item)
                } else {
                  this.hisModelList.push(item)
                }
              }

              // if (parseFloat(item.warning_time) >= 72 && _outTime === 72) {
              //   if (this.shipIgnoreList.includes(item.mmsi)) {
              //     this.wanbangHisList.push(item)
              //   } else {
              //     this.hisModelList.push(item)
              //   }
              // } else if (parseFloat(item.warning_time) >= 48 && _outTime === 48) {
              //   if (this.shipIgnoreList.includes(item.mmsi)) {
              //     this.wanbangHisList.push(item)
              //   } else {
              //     this.hisModelList.push(item)
              //   }
              // } else if (parseFloat(item.warning_time) >= 36 && _outTime === 36) {
              //   if (this.shipIgnoreList.includes(item.mmsi)) {
              //     this.wanbangHisList.push(item)
              //   } else {
              //     this.hisModelList.push(item)
              //   }
              // } else if (parseFloat(item.warning_time) >= 24 && _outTime === 24) {
              //   if (this.shipIgnoreList.includes(item.mmsi)) {
              //     this.wanbangHisList.push(item)
              //   } else {
              //     this.hisModelList.push(item)
              //   }
              // } else {
              //   if (this.shipIgnoreList.includes(item.mmsi)) {
              //     this.wanbangHisList.push(item)
              //   } else {
              //     this.hisModelList.push(item)
              //   }
              // }
            })
          }
        }
      })
    }
  },
  destroyed () {
    localStorage.removeItem('isYunZhiJia') // 为了云之家调用用的
    localStorage.removeItem('outTime')
    this.stopCountdown()
  },
  mounted () {
    let _that = this
    this.isWanBang = (localStorage.getItem('userDataId') === '18' || localStorage.getItem('userDataId') === '4498')
    // console.log(_that.$route.query)
    // 测试数据
    // if (_that.$route.query.insert_time) {
    //   let curDate = new Date()
    //   let hisDate = new Date(_that.$route.query.insert_time)
    //   if(Math.abs((hisDate.getTime() - curDate) / 60 / 1000) > 30) { // 如果与系统时间差超过30分钟才展示历史数据
    //     _that.isHisShow = true
    //     _that.getAisMess(_that.$route.query.insert_time)
    //     _that.newText = '最新动态：' + _that.parseTime((curDate.getMonth() + 1)) + '-' + _that.parseTime(curDate.getDate()) + ' ' + _that.parseTime(curDate.getHours()) + ':' + _that.parseTime(curDate.getMinutes())
    //     _that.hisText = '推送时间：' + _that.parseTime((hisDate.getMonth() + 1)) + '-' + _that.parseTime(hisDate.getDate()) + ' ' + _that.parseTime(hisDate.getHours()) + ':' + _that.parseTime(hisDate.getMinutes())
    //   }
    // }
    qing.call('getPersonInfo', { // 针对云之家小程序限定
      success: function (res) {
        if (res.data.openId && res.data.openId !== '') {
          localStorage.setItem('isYunZhiJia', true)
          _that.isYunZhiJia = true
          let seventyTwoList = ['63292e15e4b00a6b3607d63d'] // 志阳
          let fortyEightList = ['63292e15e4b00a6b3607d63e', '63565e26e4b0a9a155e6295f', '63565e26e4b0a9a155e62970'] // 陈总 柯总 史浩
          let thirdtySixList = [''] // 虹南 632cfe9be4b00130127ee36c
          let tweentyFourList = ['632cfe9be4b00130127ee36c', '63565e26e4b0a9a155e62977', '63565e26e4b0a9a155e6296e', '634cae03e4b0b6064a5d8e8c', '63565e26e4b0a9a155e6299d', '63565e26e4b0a9a155e629a0', '63565e26e4b0a9a155e62991', '63565e26e4b0a9a155e6299e', '63565e26e4b0a9a155e629a9', '63565e26e4b0a9a155e629a2', '63565e26e4b0a9a155e62964', '63565e26e4b0a9a155e6298d', '63511092e4b051cac3c10476', '63565e26e4b0a9a155e6296a', '63565e26e4b0a9a155e62960', '63565e26e4b0a9a155e6297d', '63565e26e4b0a9a155e62966', '649cf319e4b000e4d9e8e3b5']
          if (seventyTwoList.includes(res.data.openId)) {
            localStorage.setItem('outTime', 72)
          }
          if (fortyEightList.includes(res.data.openId)) {
            localStorage.setItem('outTime', 48)
          }
          if (thirdtySixList.includes(res.data.openId)) {
            localStorage.setItem('outTime', 36)
          }
          if (tweentyFourList.includes(res.data.openId)) {
            localStorage.setItem('outTime', 24)
          }
          if (res.data.openId === '63565e26e4b0a9a155e62969' || res.data.openId === '632cfe9be4b00130127ee36c') { // 爱兰, 虹南账号特殊处理
            _that.isWanBang = true
            _that.getList(1)
          } else {
            _that.isWanBang = false
            _that.getList()
          }
          // 测试开始
          // localStorage.setItem('outTime', 12)
          // _that.isHisShow = true
          // let curDate = new Date()
          // let hisDate = new Date('2024-06-08 09:06:38')
          // _that.getAisMess('2024-06-08 09:06:38')
          // _that.newText = '最新动态：' + _that.parseTime((curDate.getMonth() + 1)) + '-' + _that.parseTime(curDate.getDate()) + ' ' + _that.parseTime(curDate.getHours()) + ':' + _that.parseTime(curDate.getMinutes())
          // _that.hisText = '推送时间：' + _that.parseTime((hisDate.getMonth() + 1)) + '-' + _that.parseTime(hisDate.getDate()) + ' ' + _that.parseTime(hisDate.getHours()) + ':' + _that.parseTime(hisDate.getMinutes())
          // 测试结束
          if (_that.$route.query.insert_time) {
            let curDate = new Date()
            let hisDate = new Date(_that.$route.query.insert_time)
            let _insertTime = _that.$route.query.insert_time
            if (Math.abs((hisDate.getTime() - curDate) / 60 / 1000) > 30) { // 如果与系统时间差超过30分钟才展示历史数据
              _that.isHisShow = true
              setTimeout(() => {
                _that.loading = true
                _that.getAisMess(_insertTime)
              }, 300)
              _that.newText = '最新动态：' + _that.parseTime((curDate.getMonth() + 1)) + '-' + _that.parseTime(curDate.getDate()) + ' ' + _that.parseTime(curDate.getHours()) + ':' + _that.parseTime(curDate.getMinutes())
              _that.hisText = '推送时间：' + _that.parseTime((hisDate.getMonth() + 1)) + '-' + _that.parseTime(hisDate.getDate()) + ' ' + _that.parseTime(hisDate.getHours()) + ':' + _that.parseTime(hisDate.getMinutes())
            }
          }
        }
      },
      error: function () {
        _that.getList()
      }
    })
    if (this.isAis) {
      this.startCountdown()
      if (window.ShipService) {
        window.ShipService._timerStop()
      }
    }
  }
}
</script>
<style lang="less">
.tab_btn_area {
  position: fixed;
  bottom: 0;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  width: 100vw;
  margin-left: -5px;
  z-index: 9999;
  .tab_btn {
    display: inline-block;
    border: 1px solid #e3e5e8;
    padding: 10px;
    width: 100%;
    background: #fff;
    text-align: center;
    background: #2d8cf0;
    color: #fff;
  }
}
.voyage-area-info {
    cursor: pointer;
    &:hover {
      background: #ebf7ff;
    }
    display: flex;
    min-height: 32px;
    justify-content: center;
    justify-items: center;
    align-items: center;
    padding: 0 20px;
    background:#fff;
    border-radius:4px;
    border:1px solid #D9D9D9;
    margin-top: 10px;
    color: #333;
    text-align: left;
    .btn-area {
      border: none;
      color: #333;
      &:hover {
        color: #007DFF;
      }
    }
    .copy_btn {
      padding: 0 8px;
      margin-left: 12px;
      font-size: 12px;
      transform: scale(0.833,0.833);
      *font-size: 10px;
      color: #007DFF;
      background:#fff;
      border-radius:12px;
      border:1px solid #007DFF;
      cursor: pointer;
    }
    .cargo_result {
      font-weight: normal;
      margin-top: 10px;
    }
  }
  .voyage-area-info-mobile {
    cursor: pointer;
    &:hover {
      background: #ebf7ff;
    }
    // display: flex;
    min-height: 32px;
    justify-content: center;
    justify-items: center;
    align-items: center;
    padding: 8px 20px;
    background:#fff;
    border-radius:4px;
    border:1px solid #D9D9D9;
    margin-top: 10px;
    color: #333;
    text-align: left;
    .btn-area {
      border: none;
      color: #333;
      &:hover {
        color: #007DFF;
      }
    }
    .copy_btn {
      padding: 0 8px;
      margin-left: 12px;
      font-size: 12px;
      transform: scale(0.833,0.833);
      *font-size: 10px;
      color: #007DFF;
      background:#fff;
      border-radius:12px;
      border:1px solid #007DFF;
      cursor: pointer;
    }
    .cargo_result {
      font-weight: normal;
      margin-top: 10px;
    }
  }
  .datalisthead {
    position: sticky;
    background: #fff;
    top: 0;
    margin-top: -10px;
    padding-top: 5px;
    z-index: 99 !important;
    margin: 5px 0 -10px 0;
    font-size: 14px;
    font-weight: bold;
    text-align: center;
    .ivu-col {
      padding: 0 5px;
      color: #918c8c;
    }
  }
  .time_end {
    position: absolute;
    bottom: 10px;
    right: 10px;
  }
  .yun_area {
    height: 95vh;
    margin: 0 -13px;
  }
  .datalistempty {
    border-radius: 4px;
    padding: 10px 15px;
    text-align: center;
    border: 1px solid #D9D9D9;
  }
  .map_area {
    position: absolute;
    display: none;
    width: 0;
    height: 0;
    left: -1000px;
    top: -1000px;
  }
  .detail_title {
    font-size: 0.9rem;
    color: #c1c2c2;
  }
  .detail_con {
    font-size: 1.1rem;
    // color: #333;
  }
</style>
