self.importScripts('https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js')
self.onmessage = async function (e) {
  try {
    // 接收来自主线程的多个请求配置
    const requests = e.data

    // 创建请示Promise数组
    const requestPromises = requests.map(request => {
      return axios({
        url: request.url,
        method: request.method || 'post',
        headers: request.headers,
        data: request.data
      }).then(res => ({
        success: true,
        data: res.data
      })).catch(error => ({
        success: false,
        error: error.message
      }))
    })

    // 等待所有请求完成
    const results = await Promise.all(requestPromises)
    
    // 检查是否所有请求都成功
    const allSuccess = results.every(result => result.success)

    // 发送结果回主线程
    self.postMessage({
      allSuccess,
      results
    })
  } catch (error) {
    // 全局错误处理
    self.postMessage({
      allSuccess: false,
      error: error.message,
      results: []
    })
  }
}