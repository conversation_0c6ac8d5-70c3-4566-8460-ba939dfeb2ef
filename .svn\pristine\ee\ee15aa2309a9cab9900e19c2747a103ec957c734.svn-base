<template>
  <Modal
    v-model="modalData.modal"
    :data="modalData.data"
    :title="modalData.title"
    :width="1200"
    :mask-closable="false"
    @on-visible-change="visibleChange">
    <div slot="header">
      <span class="set_title_txt">{{ modalData.title }}</span>
      <div class="set_add_btn">
        <Button type="primary" size="small" @click="setAdd">新增</Button>
      </div>
    </div>
    <Table border :columns="columns" :data="cargoList">
      <template slot-scope="{ row }" slot="action">
        <ButtonGroup size="small">
          <Button type="info" icon="md-funnel" @click="cargoSplit(row)">货盘拆分</Button>
          <Button style="margin-left: 10px;" type="primary" icon="md-create" @click="editSetCargo(row)">编辑</Button>
          <Button style="margin-left: 10px;" type="error" icon="md-trash" @click="deleteSetCargo(row)">删除</Button>
        </ButtonGroup>
      </template>
    </Table>
    <Page show-sizer :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="queryParam.pageSize" :current.sync="queryParam.pageIndex"
          :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
    <CargoSetEditDrawer :modalData="editModal" @editClose="editClose"></CargoSetEditDrawer>
  </Modal>
</template>
<script>
import API from '@/api/shipSchedule'
import CargoSetEditDrawer from './cargoSetEditDrawer'

export default ({
  components: {
    CargoSetEditDrawer
  },
  props: {
    modalData: Object
  },
  data() {
    return {
      queryParam: {
        pageSize: 10,
        pageIndex: 1
      },
      total: 0,
      columns: [
        { title: '船名', key: 'ship_names', align: 'center' },
        { title: '货主', key: 'shipper_name', align: 'center' },
        { title: '货品', key: 'goods_name', align: 'center' },
        { title: '货量', key: 'amounts', align: 'center' },
        { title: '运价(元/吨)', key: 'freight_rate', align: 'center' },
        { title: '运费(元)', key: 'shipping_fee', align: 'center' },
        { title: '装港', key: 'load_port_name', align: 'center' },
        { title: '卸港', key: 'unload_port_name', align: 'center' },
        { title: '操作', slot: 'action', width: 260, align: 'center' }
      ],
      cargoList: [],
      editModal: {
        type: '',
        title: '包运编辑',
        modal: false,
        data: {}
      }
    }
  },
  methods: {
    // 获取包运配置列表
    getCargoList() {
      API.queryVoyageGuaranteePlanPage(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          this.cargoList = res.data.Result
          this.total = res.data.Total
        } else {
          this.$Message.warning(res.data.Message)
        }
      })
    },
    // 包运配置项新增
    setAdd() {
      this.modalData.modal = false
      this.editModal = {
        type: 'add',
        title: '包运新增',
        modal: true,
        data: {}
      }
    },
    // 包运货盘拆分
    cargoSplit(row) {
      let selectedMonth
      this.$Modal.confirm({
        title: '生成排期',
        render: (h) => {
          return h('div', [
            h('DatePicker', {
              props: {
                type: 'month',
                placeholder: '请选择所属月份',
                value: selectedMonth
              },
              style: {
                width: '100%'
              },
              on: {
                'on-change': (val) => {
                  selectedMonth = val
                }
              }
            })
          ]);
        },
        onOk: () => {
          if (!selectedMonth) {
            this.$Message.warning('请选择所属月份');
          } else {
            let _param = {
              voyage_guarantee_plan_id: row.voyage_guarantee_plan_id,
              belong_month: selectedMonth
            }
            API.autoDecompositionGuaranteePlanByShips(_param).then(res => {
              if(res.data.Code === 10000) {
                this.$Message.success(res.data.Message)
              } else {
                this.$Message.error(res.data.Message)
              }
            })
          }
        },
        onCancel: () => {
          this.$Message.info('已取消');
        }
      });
    },
    // 编辑
    editSetCargo(item) {
      this.modalData.modal = false
      this.editModal = {
        type: 'modify',
        title: '包运编辑',
        modal: true,
        data: item
      }
    },
    // 删除
    deleteSetCargo(item) {
      this.$Modal.confirm({
        title: '确认删除',
        content: `确定要删除 ${item.goods_name} 为 ${item.amounts}吨 货源吗？`,
        onOk: () => {
          API.delVoyageGuaranteePlan({ voyage_guarantee_plan_id: item.voyage_guarantee_plan_id }).then(res => {
            if(res.data.Code === 10000) {
              this.$Message.success(res.data.Message)
              this.getCargoList()
            } else {
              this.$Message.error(res.data.Message)
            }}
          )
        }
      })
    },
    editClose() {
      this.editModal.modal = false
      this.modalData.modal = true
    },
    // 页面跳转
    handleSizeChange (val) {
      this.queryParam.pageSize = val
      this.getCargoList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.queryParam.pageIndex = val
      this.getCargoList()
    },
    visibleChange(val) {
      if(val) {
        this.getCargoList()
      }
    }
  }
})
</script>
<style scoped>
  .set_title_txt {
    display: inline-block;
    width: 100%;
    height: 20px;
    line-height: 20px;
    font-size: 14px;
    color: #17233d;
    font-weight: bold;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .set_add_btn {
    position: absolute;
    top: 13px;
    right: 40px;
  }
</style>
