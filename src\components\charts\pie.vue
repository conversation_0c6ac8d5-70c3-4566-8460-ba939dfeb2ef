<template>
  <div ref="dom" class="charts chart-pie"></div>
</template>

<script>
import echarts from 'echarts'
import tdTheme from './theme.json'
import { on, off } from '@/libs/tools'
echarts.registerTheme('tdTheme', tdTheme)
export default {
  name: 'ChartPie',
  props: {
    value: Array,
    text: String,
    subtext: String,
    isOnlyValue: { // 是否只显示值
      type: Boolean,
      default: false
    },
    toolTipShow: { // 是否显示提示层
      type: Boolean,
      default: true
    },
    legendShow: {
      type: Boolean,
      default: true
    },
    unit: {
      type: String,
      default: ''
    },
    radius: {
      type: Number,
      default: 60
    },
    center: {
      type: Array,
      default: () => {
        return ['50%', '60%']
      }
    },
    color: {
      type: Array,
      default: () => {
        return ['#ff7f7f', '#ff7fbf', '#ff7fff', '#bf7fff', '#7f7fff', '#7fbfff', '#7fffff', '#7fffbf', '#7fff7f', '#bfff7f', '#DFDFDF']
      }
    },
    legend: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      dom: null
    }
  },
  methods: {
    chartInit () {
      this.$nextTick(() => {
        let legend = this.value.map(_ => _.name)
        let option = {
          title: {
            text: this.text,
            subtext: this.subtext,
            x: 'center'
          },
          tooltip: {
            show: this.toolTipShow,
            trigger: 'item',
            formatter: (params) => {
              let str = ''
              if (this.legend) {
                if (this.isOnlyValue) {
                  str = params.name + ':' + params.value + this.unit
                } else {
                  str = params.name + ':' + params.value + this.unit + '(' + params.percent + '%)'
                }
              } else {
                str = params.name + ':' + params.value + this.unit + '(' + params.percent + '%)'
              }
              return str
            }
          },
          legend: {
            show: this.legend, // this.legendShow,
            orient: 'vertical',
            left: 'left',
            data: legend
          },
          color: this.color,
          series: [
            {
              type: 'pie',
              radius: this.radius + '%',
              center: this.center, // ['50%', '60%'],
              data: this.value,
              itemStyle: {
                emphasis: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        }
        this.dom = echarts.init(this.$refs.dom, 'tdTheme')
        this.dom.setOption(option)
        on(window, 'resize', this.resize)
      })
    },
    resize () {
      this.dom.resize()
    }
  },
  mounted () {
    this.chartInit()
  },
  beforeDestroy () {
    off(window, 'resize', this.resize)
  },
  watch: {
    value: {
      handler (n, o) {
        this.chartInit()
      },
      deep: true,
      immediate: true
    }
  }
}
</script>
