<template>
  <div>
    <Table :max-height="`${winHeight}`" border :columns="shipColumns" :data="shipList" :span-method="handleSpan" class="shipTable"></Table>
  </div>
</template>
<script>
export default {
  props: {
    shipTemplateData: Array
  },
  data () {
    return {
      winHeight: 500,
      shipList: [],
      shipColumns: [
        {
          title: '船舶类型',
          key: 'ship_type_name',
          align: 'center',
          width: 50
        },
        {
          title: '船名',
          key: 'ship_name',
          align: 'center',
          width: 100
        },
        {
          title: '本航次',
          align: 'center',
          render: (h, params) => {
            let _that = this
            if (params.row.vmIsEdit) {
              return h('div', [
                h('Input', {
                  style: {
                    textAlign: 'left',
                    width: '100%'
                  },
                  props: {
                    value: params.row.voyage_msg
                  },
                  on: {
                    input (value) {
                      _that.shipList[params.row.cidx].voyage_msg = value
                    },
                    'on-blur' () {
                      params.row.vmIsEdit = false
                    }
                  }
                })
              ])
            } else {
              return h('div', [
                h('span', {
                  style: {
                    float: 'left',
                    textAlign: 'left',
                    width: '75%'
                  }
                }, params.row.voyage_msg),
                h('Button', {
                  style: {
                    float: 'right'
                  },
                  props: {
                    icon: 'md-brush',
                    size: 'small'
                  },
                  on: {
                    click: () => {
                      _that.shipList.map((item, idx) => {
                        if (params.row.cidx === idx) {
                          item.vmIsEdit = true
                          item.dnmIsEdit = false
                          item.nvmIsEdit = false
                        } else {
                          item.vmIsEdit = false
                          item.dnmIsEdit = false
                          item.nvmIsEdit = false
                        }
                      })
                    }
                  }
                })
              ])
            }
          }
        },
        {
          title: '实装量/T',
          key: 'load_amount',
          align: 'center',
          width: 90
        },
        {
          title: '动态',
          align: 'center',
          render: (h, params) => {
            let _that = this
            if (params.row.dnmIsEdit) {
              return h('div', [
                h('Input', {
                  style: {
                    textAlign: 'left',
                    width: '100%'
                  },
                  props: {
                    value: params.row.dynamic_node_msg
                  },
                  on: {
                    input (value) {
                      _that.shipList[params.row.cidx].dynamic_node_msg = value
                    },
                    'on-blur' () {
                      params.row.dnmIsEdit = false
                    }
                  }
                })
              ])
            } else {
              return h('div', [
                h('span', {
                  style: {
                    float: 'left',
                    textAlign: 'left',
                    width: '75%'
                  }
                }, params.row.dynamic_node_msg),
                h('Button', {
                  style: {
                    float: 'right'
                  },
                  props: {
                    icon: 'md-brush',
                    size: 'small'
                  },
                  on: {
                    click: () => {
                      _that.shipList.map((item, idx) => {
                        if (params.row.cidx === idx) {
                          item.dnmIsEdit = true
                          item.vmIsEdit = false
                          item.nvmIsEdit = false
                        } else {
                          item.vmIsEdit = false
                          item.dnmIsEdit = false
                          item.nvmIsEdit = false
                        }
                      })
                    }
                  }
                })
              ])
            }
          }
        },
        {
          title: '下航次',
          align: 'center',
          render: (h, params) => {
            let _that = this
            if (params.row.nvmIsEdit) {
              return h('div', [
                h('Input', {
                  style: {
                    textAlign: 'left',
                    width: '100%'
                  },
                  props: {
                    value: params.row.next_voyage_msg
                  },
                  on: {
                    input (value) {
                      _that.shipList[params.row.cidx].next_voyage_msg = value
                    },
                    'on-blur' () {
                      params.row.nvmIsEdit = false
                    }
                  }
                })
              ])
            } else {
              return h('div', [
                h('span', {
                  style: {
                    float: 'left',
                    textAlign: 'left',
                    width: '75%'
                  }
                }, params.row.next_voyage_msg),
                h('Button', {
                  style: {
                    float: 'right'
                  },
                  props: {
                    icon: 'md-brush',
                    size: 'small'
                  },
                  on: {
                    click: () => {
                      _that.shipList.map((item, idx) => {
                        if (params.row.cidx === idx) {
                          item.nvmIsEdit = true
                          item.vmIsEdit = false
                          item.dnmIsEdit = false
                        } else {
                          item.vmIsEdit = false
                          item.dnmIsEdit = false
                          item.nvmIsEdit = false
                        }
                      })
                    }
                  }
                })
              ])
            }
          }
        },
        {
          title: '岸基分管人员',
          align: 'center',
          children: [
            {
              title: '运营/市场部',
              align: 'center',
              width: 80,
              key: 'business_name'
            },
            {
              title: '海务部',
              align: 'center',
              width: 80,
              key: 'sea_service_name'
            },
            {
              title: '机务部',
              align: 'center',
              width: 80,
              key: 'locomotive_service_name'
            }
          ]
        }
      ]
    }
  },
  mounted () {
    // 挂载浏览器高度获取方法
    const that = this
    that.winHeight = window.innerHeight - 280
    window.onresize = () => {
      return (() => {
        that.winHeight = window.innerHeight - 280
      })()
    }
  },
  methods: {
    handleSpan ({ row, column, rowIndex, columnIndex }) { // 当前行，当前列，当前行索引，当前列索引
      if (rowIndex === 0 && columnIndex === 0) {
        return {
          rowspan: this.shipTemplateData[0].shipDynamicInfo.length,
          colspan: 1
        }
      } else if (rowIndex === this.shipTemplateData[0].shipDynamicInfo.length && columnIndex === 0) {
        return {
          rowspan: this.shipTemplateData[1].shipDynamicInfo.length,
          colspan: 1
        }
      } else if ((rowIndex !== 0 || rowIndex !== this.shipTemplateData[0].shipDynamicInfo.length) && columnIndex === 0) {
        return [0, 0]
      }
    }
  },
  watch: {
    shipTemplateData: {
      handler (newData, oldData) {
        this.shipList = []
        this.shipTemplateData.map((item, pidx) => {
          item.shipDynamicInfo.map((e, cidx) => {
            let _cidx = 0
            if (pidx === 0) {
              _cidx = cidx
            } else {
              _cidx = pidx * this.shipTemplateData[pidx - 1].shipDynamicInfo.length + cidx
            }
            this.shipList.push({
              vmIsEdit: false,
              dnmIsEdit: false,
              nvmIsEdit: false,
              pidx: pidx,
              cidx: _cidx,
              ship_type: item.ship_type,
              ship_type_name: item.ship_type_name,
              business_name: e.business_name,
              dynamic_node_msg: e.dynamic_node_msg,
              load_amount: e.load_amount,
              locomotive_service_name: e.locomotive_service_name,
              next_voyage_msg: e.next_voyage_msg,
              sea_service_name: e.sea_service_name,
              ship_name: e.ship_name,
              voyage_msg: e.voyage_msg
            })
          })
        })
      },
      deep: true
    }
  }
}
</script>
<style>
  .shipTable .ivu-table-cell {
    padding-left: 10px;
    padding-right: 10px;
  }
</style>
