<template>
  <div>
    <!-- 查询 -->
    <Card>
      <search @searchResults='searchResults' @selectOnChanged='selectOnChanged' :setSearch='setSearchData' @keydown.enter.native='searchResults' @resetResults='resetResults'></search>
    </Card>
    <Card style="margin-top:10px;padding-top:6px;">
      <p slot='title' class="bold-font">货品价格</p>
      <Button slot="extra" type="primary" @click="changePrice">今日调价</Button>
      <div style="width: 100%;max-width: 572px;margin: 0 auto;">
        <Table border :loading="listLoading" ref="selection" :columns="columns" :data="list" width="572"></Table>
        <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
          :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
      </div>
    </Card>
    <price-modal :modalData="modalData" @callback="getList"></price-modal>
    <price-detail :modalData="detailDrawerData" @callback="getList"></price-detail>
  </div>
</template>
<script>
import search from '_c/search' // 查询组件
import API from '@/api/basicConfig/goodsConfig'
import { queryBasicCargoList } from '@/api/basicData'
import priceModal from './modal/addGoodsPriceModal'
import priceDetail from './modal/goodsPriceDetailDrawer'

export default {
  components: {
    search,
    priceModal,
    priceDetail
  },
  data () {
    return {
      listLoading: false,
      listCurrent: 1,
      lately_cargo_date: '',
      total: 0,
      list: [],
      modalData: {
        modal: false,
        title: ''
      },
      detailDrawerData: {
        modal: false,
        title: '',
        cargo_id: ''
      },
      listQuery: {
        pageSize: 10,
        pageIndex: 1,
        cargo_name: '',
        lately_cargo_date: ''
      },
      setSearchData: {// 查询设置，对象key值为回调参数
        cargo_name: {
          type: 'select',
          label: '货品',
          selectData: [],
          selected: '',
          filterable: true,
          placeholder: '请选择',
          selectName: '',
          width: 150,
          value: ''
        },
        lately_cargo_date: {
          type: 'date',
          label: '时间',
          selected: '',
          width: 150,
          value: '',
          isdisable: false
        }
      },
      columns: [
        {
          title: '货品',
          key: 'cargo_name',
          align: 'center',
          width: 150
        },
        {
          title: '上次价格(元)',
          align: 'center',
          width: 160,
          render: (h, params) => {
            let prePrice = params.row.lately_cargo_date === '' ? params.row.lately_cargo_price : `${params.row.lately_cargo_price} (${params.row.lately_cargo_date})`
            return h('div', {}, prePrice)
          }
        },
        {
          title: '今日价格(元)',
          key: 'current_cargo_price',
          align: 'center',
          width: 130
        },
        {
          title: '操作',
          align: 'center',
          width: 130,
          render: (h, params) => {
            return h('Button', {
              props: {
                icon: 'md-paper',
                size: 'small'
              },
              on: {
                click: () => {
                  this.handleDetail(params.row)
                }
              }
            }, '查看')
          }
        }
      ]
    }
  },
  created () {
    this.getGoodsList()
    this.getList()
  },
  methods: {
    // 获取列表数据
    getList () {
      this.listLoading = true
      API.queryBasicGoodsPricePage(this.listQuery).then(res => {
        this.listLoading = false
        if (res.data.Code === 10000) {
          this.list = res.data.Result
          this.total = res.data.Total
        }
      })
    },
    // 获取货品列表
    getGoodsList () {
      queryBasicCargoList({ cargo_name: '' }).then(res => {
        if (res.data.Code === 10000) {
          this.setSearchData.cargo_name.selectData = res.data.Result.map(item => {
            return {
              value: item.cargo_name,
              label: item.cargo_name
            }
          })
        }
      })
    },
    changePrice () {
      this.modalData = {
        modal: true,
        title: '今日调价'
      }
    },
    // 查看详情
    handleDetail (row) {
      this.detailDrawerData = {
        modal: true,
        title: row.cargo_name,
        cargo_id: row.id
      }
    },
    // 查询
    searchResults (e) {
      if (e.target) delete e.target
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      this.listQuery.lately_cargo_date = this.lately_cargo_date
      this.listQuery.cargo_name = e.cargo_name
      this.getList()
    },
    selectOnChanged (e) {
      this.lately_cargo_date = e.key
    },
    // 重置
    resetResults () {
      this.listCurrent = 1
      this.listQuery = Object.assign(this.listQuery, {
        pageIndex: 1,
        cargo_name: '',
        lately_cargo_date: ''
      })
      this.lately_cargo_date = ''
      this.setSearchData.cargo_name.selected = ''
      this.setSearchData.lately_cargo_date.selected = ''
      this.getList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    }
  }
}
</script>
