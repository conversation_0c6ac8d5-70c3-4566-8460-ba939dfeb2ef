<template>
  <div style="overflow: hidden;">
    <div class="no-list" style="height: 390px;" v-if="timeLineData.length === 0">
      <Row justify="center" align="middle">
        <img src="../../../../assets/images/no-data.png" />
        <div>暂无列表数据</div>
      </Row>
    </div>
    <Timeline class="timelinestyle">
      <Menu style="width: 100%;" :open-names="[`${index}`]" v-for="(item, index) in timeLineData" :key="index">
        <Submenu :name="`${index}`" >
          <template slot="title">
            <span style="color: #007DFF; margin-right: 12px;">{{ item.port_type === '1' ? '装' : '卸' }}</span>
            <span></span>{{ item.port_name }}{{ item.wharf_name !== '' ? ' — ' + item.wharf_name : '' }}
          </template>
          <MenuItem v-for="(item1, index1) in item.dynamicResult" :name="`${index1}-1`" :key="index1">
            <TimelineItem>
              <Icon :custom="voyageIcon(item1)" size="16" slot="dot"></Icon>
              <div class="timelinecontent">
                <div class="text-content-left">
                  <strong>{{ item1.node_name }}</strong>
                  <Tooltip :content="`录入时间：${item1.enter_date}`" placement="top" style="float: right;">
                    <span style="float: right;">{{ item1.node_date }}</span>
                  </Tooltip>
                </div>
              </div>
              <div style="padding: 0 10px;">
                <nodeList :nodeObj="item1" :nameShow="nameShow"></nodeList>
              </div>
            </TimelineItem>
          </MenuItem>
        </Submenu>
      </Menu>
    </Timeline>
    <Spin fix v-if="spinShow">
      <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
      <div>Loading</div>
    </Spin>
  </div>
</template>
<script>
import API from '@/api/voyageManage/voyageDetail'
import SAPI from '@/api/search'
import nodeList from '@/components/nodeList/nodeNList'

export default {
  props: {
    baseObj: Object
  },
  components: {
    nodeList
  },
  data () {
    return {
      spinShow: false, // loading
      nameShow: false,
      goodsIndex: 0, // 默认第一个货品
      listQuery: {
        voyage_id: '', // 航次id(必填)
        goods_id: '' // 货品id
      },
      timeLineData: [] // 时间轴列表
    }
  },
  methods: {
    // 获取动态信息
    getDynamicInfo () {
      this.spinShow = true
      if (this.$route.name === 'searchDetail') {
        let curParam = {
          voyage_id: this.baseObj.id,
          number_no: this.$route.params.id,
          goods_id: this.baseObj.cargoResult[this.goodsIndex].goods_id
        }
        SAPI.queryWaybillNumberByNamic(curParam).then(res => {
          this.spinShow = false
          if (res.data.Code === 10000) {
            this.timeLineData = res.data.Result
          }
        })
      } else {
        API.getVoyageDynamicById(this.listQuery).then(res => {
          this.spinShow = false
          if (res.data.Code === 10000) {
            this.timeLineData = res.data.Result
          }
        })
      }
    },
    voyageIcon (item) {
      let curStr = ''
      switch (item.node_status_code) {
        case 'ZH': // 在航
          curStr = 'voyagefont iconvoyage-sail'
          break
        case 'MB': // 到港
          curStr = 'voyagefont iconarrive_port'
          break
        case 'KB': // 靠泊
          curStr = 'voyagefont iconvoyage-mooring'
          break
        case 'ZY': // 作业
          curStr = 'voyagefont icontask'
          break
        case 'LG': // 离港
          curStr = 'voyagefont icondeparture'
          break
        default:
          curStr = ''
      }
      return curStr
    }
  },
  created () {
    this.listQuery.voyage_id = this.baseObj.id
    this.getDynamicInfo()
  },
  watch: {
    goodsIndex () {
      this.getDynamicInfo()
    }
  }
}
</script>
<style scoped>
.timelinecontent {
  width: 100%;
  max-width: 600px;
}
.ivu-menu-vertical.ivu-menu-light:after {
  left: 7px;
  width: 2px;
  background-color: #D7E3F1;
  display: none;
}
</style>
<style lang="less">
.no-dynamic {
  height: 80px;
  line-height: 80px;
  font-size: 20px;
  font-weight:400;
  color:rgba(51,51,51,0.6);
  text-align: center;
}
.timelinestyle {
  margin-left: 40px;
  .ivu-menu-light {
    background-color: transparent;
    .ivu-timeline-item {
      padding: 0;
      margin: 0 0 0 -8px !important;
      .ivu-timeline-item-head {
        width: 40px;
        height: 40px;
        border-width: 0;
        background-color: #EFF8FF;
        margin: 22px 0 0 1px;
        line-height: 30px;
        border-radius: 50%;
      }
      .ivu-timeline-item-head::first-child {
        background-color: #007DFF !important;
      }
      .ivu-timeline-item-content {
        margin-left: 10px;
      }
      .timelinecontent {
        padding: 13px;
        color: #515151;
        display: inline-block;
        font-size: 14px;
        border-radius: 5px;
        position: relative;
        strong {
          color: #4A4A4A;
          font-size: 16px;
        }
      }
    }
    &:first-child .ivu-menu-item:first-child {
      .ivu-timeline-item-head {
        background-color: #007DFF;
        .voyagefont {
          color: #fff;
        }
      }
      .timelinecontent {
        background: #EFF8FF;
        strong {
          color: #000;
        }
      }
    }
    li.ivu-menu-submenu {
      .ivu-menu-submenu-title::after {
        content: '';
        display: block;
        border-left: 2px dashed #D7E3F1;
        height: 100%;
        width: 2px;
        position: absolute;
        top: 27px;
        left: -20px;
      }
    }
  }
  .ivu-menu-vertical .ivu-menu-submenu-title {
    height: 44px;
    line-height: 44px;
    padding: 0 0 0 15px;
    font-size: 18px;
    color: #333;
    font-weight: bold;
    color: #4A4A4A;
    border-radius: 22px;
    margin-left: 20px;
    margin-bottom: 10px;
    &::before {
      content: '';
      display: block;
      width: 8px;
      height: 8px;
      background-color: #B8D1FF;
      border-radius: 50%;
      position: absolute;
      left: -23px;
      top: 15px;
      z-index: 9;
    }
  }
  .ivu-menu-vertical .ivu-menu-submenu .ivu-menu::before {
    content: '';
    width: 2px;
    height: 110%;
    position: absolute;
    left: 0;
    top: 0;
    border-left: 2px solid #D7E3F1;
  }
  .ivu-menu-vertical .ivu-menu-submenu .ivu-menu-item {
    cursor: default;
    padding: 0 !important;
    background-color: transparent;
  }
  .ivu-menu-light.ivu-menu-vertical .ivu-menu-item-active:not(.ivu-menu-submenu):after {
    display: none;
  }
  .text-content-left {
    width: 100%;
    float: left;
  }
  .ivu-btn-primary {
    width: 30px;
    height: 30px;
    margin-top: -5px;
    padding: 0;
    border-width: 0;
    color: #999;
    font-size: 16px;
    background-color: transparent !important;
    position: absolute;
    right: 10px;
    top: 35%;
  }
}
</style>
