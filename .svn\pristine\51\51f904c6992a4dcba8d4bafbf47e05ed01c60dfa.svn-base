<template>
  <div>
    <Drawer
      v-model="dynamicFormModal"
      :data="modalData"
      title="编辑"
      :width="800"
      :mask-closable="false"
      @on-visible-change="visibleChange">
      <Card>
        <h3 class="bold-font title-font">基础信息</h3>
        <Form ref="dynamicForm" v-if="dynamicFormModal" :model="dynamicForm" :rules="dynamicRuleValidate" :label-width="55" inline>
          <Row>
            <Col span="7">
              <FormItem label="船名">
                <Input v-model="dynamicForm.ship_name" disabled></Input>
              </FormItem>
            </Col>
            <Col span="7">
              <FormItem label="航次">
                <Input v-model="dynamicForm.voyage_no" disabled></Input>
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="港口码头" :label-width="85">
                <span class="span-ivu bgclass">{{ dynamicForm.dynamic_port_name }} - {{ dynamicForm.dynamic_wharf_name }}</span>
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="7">
              <FormItem label="状态">
                <Input v-model="dynamicForm.node_status_name" disabled></Input>
              </FormItem>
            </Col>
            <Col span="7">
              <FormItem label="节点">
                <Input v-model="dynamicForm.node_name" disabled></Input>
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="节点时间" prop="node_date" :label-width="85">
                <DatePicker
                  type="datetime"
                  format="yyyy-MM-dd HH:mm"
                  :value='dynamicForm.node_date'
                  @on-ok="handleok('1')"
                  @on-change="data=>dynamicForm.node_date=data"></DatePicker>
              </FormItem>
            </Col>
          </Row>
          <!-- 作业状态 -->
          <Row v-if="modalData.node_status_code === 'ZY'">
            <Col span="9">
              <FormItem label="货主">
                <Input v-model="dynamicForm.dynamicCustomer.cargo_company_name" disabled></Input>
              </FormItem>
            </Col>
            <Col span="7">
              <FormItem label="货品">
                <Input v-model="dynamicForm.dynamicCustomer.goods_name + ' ' + dynamicForm.dynamicCustomer.amount + '吨'" disabled></Input>
              </FormItem>
            </Col>
          </Row>
          <!-- 计量结束 -->
          <Row v-if="modalData.node_code === 'BA'">
            <!-- port_mode: 1装,2卸货 -->
            <Row v-if="modalData.port_mode === '1'">
              <Col span="8" v-if="isShowLF">
                <FormItem label="装港流量计" :label-width="115">
                  <Input v-model='dynamicForm.dynamicCustomer.load_flowmeter_amount'></Input>
                </FormItem>
              </Col>
              <Col span="8" v-if="isShowLS">
                <FormItem label="装港商检船板量" :label-width="115">
                  <Input v-model='dynamicForm.dynamicCustomer.load_ship_amount'></Input>
                </FormItem>
              </Col>
              <Col span="8" v-if="isShowLT">
                <FormItem label="装港岸罐量" :label-width="115">
                  <Input v-model='dynamicForm.dynamicCustomer.load_tank_amount'></Input>
                </FormItem>
              </Col>
            </Row>
            <Row v-if="modalData.port_mode === '2'">
              <Col span="8" v-if="isShowUlS">
                <FormItem label="卸港商检船板量" :label-width="115">
                  <Input v-model='dynamicForm.dynamicCustomer.unload_ship_amount'></Input>
                </FormItem>
              </Col>
              <Col span="8" v-if="isShowLlT">
                <FormItem label="卸港岸罐量" :label-width="115">
                  <Input v-model='dynamicForm.dynamicCustomer.unload_tank_amount'></Input>
                </FormItem>
              </Col>
            </Row>
          </Row>
          <!-- 有预计节点的 -->
          <Row v-if="showExpectNode">
            <Col span="8">
              <FormItem label="预计节点" :label-width="80">
                <Select v-model="dynamicForm.expect_node_id" filterable>
                  <Option v-for="(item, index) in expectNodeList" :key="index" :value="item.value">{{ item.label }}</Option>
                </Select>
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="预计时间" :label-width="80">
                <DatePicker
                  type="datetime"
                  format="yyyy-MM-dd HH:mm"
                  :value='dynamicForm.expect_date'
                  @on-ok="handleok('2')"
                  @on-change="data=>dynamicForm.expect_date=data"></DatePicker>
              </FormItem>
            </Col>
          </Row>
          <!-- 起锚、第一条缆绳上岸、船舶系泊 -->
          <Row v-if="modalData.node_code === 'AI' || modalData.node_code === 'AJ' || modalData.node_code === 'AK'">
            <Col span="8">
              <FormItem label="港口">
                <Input v-model="dynamicForm.port_name" disabled></Input>
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="码头">
                <Input v-model="dynamicForm.wharf_name" disabled></Input>
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="泊位">
                <Select v-model="dynamicForm.berth_id">
                  <Option v-for="(item, index) in berthList" :key="index" :value="item.value">{{ item.label }}</Option>
                </Select>
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="24">
              <FormItem label="备注" prop="remark">
                <Input type="textarea" :autosize="true" v-model="dynamicForm.remark" :rows="4"></Input>
              </FormItem>
            </Col>
          </Row>
        </Form>
      </Card>
      <div class="demo-drawer-footer">
        <Button @click="dynamicFormModal = false" style="margin-right: 10px;">取消</Button>
        <Button type="primary" @click="updateData">保存</Button>
      </div>
    </Drawer>
  </div>
</template>
<script>
import { queryNodeList, queryBerthList } from '@/api/basicData'
import { dynamicUpdate } from '@/api/dynamicManagement'

export default {
  data () {
    return {
      showExpectNode: false, // 有预计节点的显示内容
      expectNodeList: [], // 储存所有节点数据
      berthList: [],
      modalData: {},
      isShowLF: false,
      isShowLS: false,
      isShowLT: false,
      isShowUlS: false,
      isShowLlT: false,
      dynamicFormModal: false, // 模态框显示状态
      dynamicForm: {
        id: '',
        ship_name: '',
        voyage_no: '',
        dynamic_port_name: '',
        dynamic_wharf_name: '',
        node_status_name: '', // 状态
        node_name: '', // 节点
        node_date: '', // 节点时间
        expect_node_id: '',
        expect_node_name: '',
        expect_date: '',
        port_name: '',
        wharf_name: '',
        berth_id: '',
        berth_name: '',
        remark: '',
        dynamicCustomer: {
          cargo_company_name: '',
          goods_name: '',
          load_flowmeter_amount: '',
          load_ship_amount: '',
          load_tank_amount: '',
          unload_ship_amount: '',
          unload_tank_amount: ''
        }
      },
      dynamicRuleValidate: {
        node_date: [
          { required: true, message: '此处不能为空', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    // 开启模态框
    visibleChange (d) {
      if (d === true) {
        this.dynamicForm = Object.assign({}, this.modalData)
        this.isShowLF = this.modalData.dynamicCustomer.load_flowmeter_amount !== ''
        this.isShowLS = this.modalData.dynamicCustomer.load_ship_amount !== ''
        this.isShowLT = this.modalData.dynamicCustomer.load_tank_amount !== ''
        this.isShowUlS = this.modalData.dynamicCustomer.unload_ship_amount !== ''
        this.isShowLlT = this.modalData.dynamicCustomer.unload_tank_amount !== ''
        // 起航/航行中/到港/抛锚/输油管接妥/开始装卸货/暂停装卸货/恢复装卸货/结束装卸货
        let expectNodeCodeList = ['AC', 'AG', 'AO', 'AP', 'AQ', 'AR', 'AS', 'AT', 'AU', 'AV', 'AW']
        if (this.modalData.node_status_code === 'ZH' || expectNodeCodeList.includes(this.modalData.node_code)) {
          this.showExpectNode = true
          queryNodeList().then(res => { // 获取预计(所有)节点
            if (res.data.Code === 10000) {
              res.data.Result.map(e => {
                this.expectNodeList.push({
                  label: e.node_name,
                  value: e.id
                })
              })
            }
          })
        } else {
          this.showExpectNode = false
        }
        queryBerthList({ terminal_id: this.dynamicForm.wharf_id }).then(res => { // 获取泊位
          if (res.data.Code === 10000) {
            res.data.Result.map(e => {
              this.berthList.push({
                label: e.berth_name,
                value: e.id
              })
            })
          }
        })
      } else {
        this.clearData()
      }
    },
    // 编辑保存
    updateData () {
      this.$refs['dynamicForm'].validate((valid) => {
        if (valid) {
          this.$Modal.confirm({
            title: '提示',
            content: '<p>是否修改动态信息？</p>',
            loading: true,
            onOk: () => {
              let data = {
                id: this.dynamicForm.id,
                node_date: this.dynamicForm.node_date,
                expect_node_id: this.dynamicForm.expect_node_id,
                expect_date: this.dynamicForm.expect_date,
                berth_id: this.dynamicForm.berth_id,
                remark: this.dynamicForm.remark
              }
              let nodeCodeBA = Object.assign({ // 计量结束节点增加nodeCodeBA传参
                unit: this.dynamicForm.dynamicCustomer.unit,
                amount: this.dynamicForm.dynamicCustomer.amount,
                node_status_code: this.dynamicForm.node_status_code,
                node_code: this.dynamicForm.node_code,
                load_flowmeter_amount: this.dynamicForm.dynamicCustomer.load_flowmeter_amount,
                load_ship_amount: this.dynamicForm.dynamicCustomer.load_ship_amount,
                load_tank_amount: this.dynamicForm.dynamicCustomer.load_tank_amount,
                unload_ship_amount: this.dynamicForm.dynamicCustomer.unload_ship_amount,
                unload_tank_amount: this.dynamicForm.dynamicCustomer.unload_tank_amount
              }, data)
              let param = this.modalData.node_code === 'BA' ? nodeCodeBA : data
              dynamicUpdate(param).then(response => {
                if (response.data.Code === 10000) {
                  this.$Message.success(response.data.Message)
                  this.loading = false
                  this.$Modal.remove()
                  this.dynamicFormModal = false
                  this.$emit('addSuccess')
                } else {
                  this.dynamicFormModal = true
                  this.$Message.error(response.data.Message)
                  this.$Modal.remove()
                  this.loading = false
                }
              }).catch(function () {
                this.$Message.warning('系统异常，请联系管理员')
              })
            }
          })
        }
      })
    },
    // 重置数据
    clearData () {
      this.berthList = []
      this.dynamicForm = {
        id: '',
        ship_name: '',
        voyage_no: '',
        dynamic_port_name: '',
        dynamic_wharf_name: '',
        node_status_name: '',
        node_name: '',
        node_date: '',
        expect_node_id: '',
        expect_node_name: '',
        expect_date: '',
        port_name: '',
        wharf_name: '',
        berth_name: '',
        berth_id: '',
        remark: '',
        dynamicCustomer: {
          cargo_company_name: '',
          goods_name: '',
          load_flowmeter_amount: '',
          load_ship_amount: '',
          load_tank_amount: '',
          unload_ship_amount: '',
          unload_tank_amount: ''
        }
      }
    },
    handleok (index) {
      let d = new Date()
      let dData = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate() + ' ' + d.getHours() + ':' + d.getMinutes()
      if (index === '1' && this.dynamicForm.node_date === '') {
        this.dynamicForm.node_date = dData
      } else if (index === '2' && this.dynamicForm.expect_date === '') {
        this.dynamicForm.expect_date = dData
      }
    }
  }
}
</script>
<style scoped>
.demo-drawer-footer {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  background: #fff;
}
.formModalTitle {
  margin: 0 0 9px;
}
.ivu-form-inline .ivu-form-item {
  width: 100%;
}
.span-ivu {
  width: 100%;
  height: 32px;
  line-height: 1.5;
  padding: 4px 7px;
  font-size: 12px;
  display: inline-block;
  border-radius: 4px;
  border: 1px solid #dcdee2;
}
.span-ivu.bgclass {
  background-color: #f3f3f3;
  color: #ccc;
}
</style>
