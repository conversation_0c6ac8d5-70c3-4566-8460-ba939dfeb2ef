<template>
  <div>
    <!-- 查询 -->
    <Card>
      <search @searchResults='searchResults' @selectOnChanged='selectOnChanged' :setSearch='setSearchData' @keydown.enter.native='searchResults' @resetResults='resetResults'></search>
    </Card>
    <Card style="margin-top:10px;padding-top:6px;">
      <p slot='title' class="bold-font">动态管理</p>
      <div class="extra" slot="extra">
        <formAction :setFormAction='setFormAction' @handleUpdateTable="handleUpdateTable"></formAction>
      </div>
      <Table border :loading="listLoading" ref="selection" :columns="columns" :data="list" class="alignTable"></Table>
      <Page :total="total" :current.sync="listCurrent" :page-size-opts='[5, 10, 15, 20]' show-sizer :styles="{margin:'16px -10px 0 0',textAlign: 'right'}" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
    </Card>
    <dynamicManagementEdit ref="dynamicManagementModal" @addSuccess="getList"></dynamicManagementEdit>
  </div>
</template>

<script>
import search from '_c/search' // 查询组件
import formAction from '_c/formAction' // 表单操作组件
import { dynamicList, dynamicDelete, queryShipList } from '@/api/dynamicManagement'
import { queryPortList, queryNodeStatusList, queryNodeList } from '@/api/basicData'
import dynamicManagementEdit from './dynamicManagementEdit'

export default {
  components: {
    search,
    formAction,
    dynamicManagementEdit
  },
  data () {
    return {
      setSearchData: {// 查询设置，对象key值为回调参数
        ship_name: {
          type: 'select',
          label: '船名',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 150,
          value: '',
          filterable: true
        },
        voyage_no: {
          type: 'text',
          label: '航次',
          placeholder: '',
          width: 150,
          value: ''
        },
        dynamic_port_id: {
          type: 'select',
          label: '港口',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 150,
          value: '',
          filterable: true
        },
        status_id: {
          type: 'select',
          label: '状态',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 150,
          value: '',
          filterable: true,
          change: this.getDynamicnode
        },
        node_id: {
          type: 'select',
          label: '节点',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 150,
          value: '',
          filterable: true,
          isdisabled: true
        },
        node_date_st: {
          type: 'date',
          label: '时间',
          selected: '',
          width: 130,
          value: '',
          isdisable: false
        },
        node_date_ed: {
          type: 'date_end',
          label: '-',
          selected: '',
          width: 130,
          value: '',
          isdisabled: false
        }
      },
      setFormAction: {
        operation: ['updateTable']
      },
      listLoading: true, // 表单列表loding状态
      columns: [
        {
          title: '序号',
          type: 'index',
          align: 'center',
          width: 70
        },
        {
          title: '录入时间',
          key: 'enter_date',
          align: 'center'
        },
        {
          title: '船名',
          key: 'ship_name',
          align: 'center'
        },
        {
          title: '航次',
          key: 'voyage_no',
          align: 'center'
        },
        {
          title: '状态',
          key: 'node_status_name',
          align: 'center'
        },
        {
          title: '节点',
          key: 'node_name',
          align: 'center'
        },
        {
          title: '节点时间',
          key: 'node_date',
          align: 'center'
        },
        {
          title: '预计节点',
          key: 'expect_node_name',
          align: 'center'
        },
        {
          title: '预计时间',
          key: 'expect_date',
          align: 'center'
        },
        {
          title: '操作',
          key: 'operation',
          align: 'center',
          width: 190,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                style: {
                  margin: '4px'
                },
                props: {
                  icon: 'md-brush',
                  size: 'small'
                },
                on: {
                  click: (e) => {
                    e.stopPropagation()
                    this.handleUpdate(params.row)
                  }
                }
              }, '修改'),
              h('Button', {
                style: {
                  margin: '4px'
                },
                props: {
                  icon: 'md-trash',
                  size: 'small'
                },
                on: {
                  click: (e) => {
                    e.stopPropagation()
                    this.handleDelete(params.row)
                  }
                }
              }, '删除')
            ])
          }
        }
      ],
      list: [], // 表单列表数据
      total: null, // 列表数据条数
      listQuery: {// 列表请求参数
        pageSize: 10,
        pageIndex: 1,
        ship_name: '',
        voyage_no: '',
        dynamic_port_id: '',
        status_id: '',
        node_id:''
      },
      listCurrent: 1 // 当前页码
    }
  },
  created () {
    // 获取船名
    queryShipList().then(res => {
      if (res.data.Code === 10000) {
        res.data.Result.map(item => {
          this.setSearchData.ship_name.selectData.push({
            value: item.ship_name,
            label: item.ship_name
          })
        })
      }
    })
    // 获取港口
    queryPortList({ pageSize: 10000, pageIndex: 1 }).then(res => {
      if (res.data.Code === 10000) {
        res.data.Result.map(item => {
          this.setSearchData.dynamic_port_id.selectData.push({
            value: item.id,
            label: item.port_name
          })
        })
      }
    })
    // 获取节点状态
    queryNodeStatusList().then(res => {
      if (res.data.Code === 10000) {
        res.data.Result.map(item => {
          this.setSearchData.status_id.selectData.push({
            value: item.id,
            label: item.node_status_name
          })
        })
      }
    })
    this.getList()
  },
  methods: {
    // 获取动态列表
    getList () {
      this.listLoading = true
      dynamicList(this.listQuery).then(response => {
        if (response.data.Code === 10000) {
          this.list = response.data.Result
          this.total = response.data.Total
        } else {
          this.$Message.error(response.data.Message)
        }
        setTimeout(() => {
          this.listLoading = false
        }, 1 * 800)
      }).catch(
        setTimeout(() => {
          this.listLoading = false
        }, 1 * 800)
      )
    },
    // 根据状态获取节点
    getDynamicnode () {
      if (this.setSearchData.status_id.selected === undefined) return
      this.setSearchData.node_id.isdisabled = false
      this.setSearchData.node_id.selectData = []
      queryNodeList({ node_status_id: this.setSearchData.status_id.selected }).then(res => {
        if (res.data.Code === 10000) {
          res.data.Result.map(e => {
            this.setSearchData.node_id.selectData.push({
              label: e.node_name,
              value: e.id
            })
          })
        }
      })
    },
    // 查询
    searchResults (e) {
      if (e.target) delete e.target
      this.listQuery.pageIndex = 1
      this.listCurrent = 1
      Object.assign(this.listQuery, e)
      this.listQuery.node_date_st = this.node_date_st
      this.listQuery.node_date_ed = this.node_date_ed
      this.getList()
    },
    selectOnChanged (e) {
      if (e.flag === 'date_start') {
        this.node_date_st = e.key
      } else if (e.flag === 'date_end') {
        this.node_date_ed = e.key
      }
    },
    // 重置
    resetResults () {
      this.listCurrent = 1
      this.listQuery = Object.assign(this.listQuery, {
        pageSize: 10,
        pageIndex: 1,
        ship_name: '',
        voyage_no: '',
        dynamic_port_id: '',
        status_id: '',
        node_id:''
      })
      this.setSearchData.ship_name.selected = ''
      this.setSearchData.voyage_no.value = ''
      this.setSearchData.dynamic_port_id.selected = ''
      this.setSearchData.status_id.selected = ''
      this.setSearchData.node_date_st.selected = ''
      this.setSearchData.node_date_ed.selected = ''
      Object.assign(this.setSearchData.node_id, {
        selected: '',
        isdisabled: true
      })
      this.getList()
    },
    // 开启组件
    formModalState (row) {
      this.$refs.dynamicManagementModal.modalData = Object.assign({}, row)
      this.$refs.dynamicManagementModal.dynamicFormModal = true
    },
    // 手动更新列表
    handleUpdateTable () {
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      this.getList()
    },
    // 修改
    handleUpdate (row) {
      this.formModalState(row)
    },
    // 删除
    handleDelete (d) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>是否删除该条动态信息？</p>',
        loading: true,
        onOk: () => {
          let data = {
            'id': d.id
          }
          dynamicDelete(data).then((response) => {
            if (response.data.Code === 10000) {
              this.$Message.success(response.data.Message)
              this.getList()
              this.$Modal.remove()
              this.loading = false
            } else {
              this.$Message.error(response.data.Message)
              this.$Modal.remove()
              this.loading = false
            }
          })
        }
      })
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    }
  }
}
</script>
<style scoped>
.ivu-table td .dynamic-large-state-column {
  display: inline-block;
  background-color: #EDF0F5;
  color: #52575A;
  padding: 4px 15px 3px 7px;
  border-radius: 5x;
}
.ivu-table td .dynamic-large-state-column::before {
  content: '';
  width: 5px;
  height: 5px;
  display: inline-block;
  vertical-align: middle;
  margin-right: 6px;
  border-radius: 50%;
}
.dynamic-large-state-1::before {
  background: #4072EF;
}
.dynamic-large-state-2::before {
  background: #B658F7;
}
.dynamic-large-state-3::before {
  background: #FDC300;
}
.dynamic-large-state-4::before {
  background: #29CB97;
}
.dynamic-large-state-5::before {
  background: #EA494D;
}
</style>
<style>
  .ivu-card-head {
      border-bottom: 1px solid #e8eaec;
      padding: 14px 16px 0;
      line-height: 1;
  }
</style>
