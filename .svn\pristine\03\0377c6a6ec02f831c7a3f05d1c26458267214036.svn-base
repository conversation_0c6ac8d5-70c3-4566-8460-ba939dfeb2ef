<template>
  <div>
    <Card>
      <search @searchResults='searchResults' @selectOnChanged='selectOnChanged' :setSearch='setSearchData' @keydown.enter.native='searchResults' @resetResults='resetResults'></search>
    </Card>
    <Card style="margin-top:10px;padding-top:6px;">
      <p slot='title' class="bold-font">历史短信</p>
      <div class="extra" slot="extra" >
        <formAction :setFormAction='setFormAction' @handleUpdateTable="handleUpdateTable"></formAction>
      </div>
      <Table border :loading="listLoading" :columns="columns" :data="list"></Table>
      <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
          :total="total" prev-text="< 上一页" next-text="下一页 >"
          @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
    </Card>
    <pushComponents ref="pushComponents" @addSuccess="pushAgainAll" @addUpdateList="getList"></pushComponents>
  </div>
</template>

<script>
import search from '_c/search' // 查询组件
import formAction from '_c/formAction' // 表单操作组件
import { historicalSMSPushList, pushAgainSMS } from '@/api/setting/historicalSMS'
import pushComponents from './SMSPushEdit'

export default {
  components: {
    search,
    formAction,
    pushComponents
  },
  data () {
    return {
      setSearchData: {// 查询设置，对象key值为回调参数
        customer_company_name: {
          type: 'text',
          label: '公司名称',
          width: 150,
          value: ''
        },
        ship_name: {
          type: 'text',
          label: '船名',
          width: 150,
          value: ''
        },
        voyage_no: {
          type: 'text',
          label: '航次',
          width: 150,
          value: ''
        },
        node_name: {
          type: 'text',
          label: '节点',
          width: 150,
          value: ''
        },
        update_time: {
          type: 'date',
          label: '发送时间',
          selected: '',
          width: 130,
          value: '',
          isdisable: false
        }
      },
      setFormAction: {
        operation: ['updateTable']
      },
      listLoading: true, // 表单列表loding状态
      columns: [
        {
          title: '公司',
          key: 'customer_company_name',
          align: 'center'
        },
        {
          title: '船名',
          key: 'ship_name',
          align: 'center'
        },
        {
          title: '航次',
          key: 'voyage_no',
          align: 'center',
          width: 160
        },
        {
          title: '节点',
          key: 'node_name',
          align: 'center'
        },
        {
          title: '时间',
          key: 'update_time',
          align: 'center',
          width: 190
        },
        {
          title: '状态',
          key: 'is_success',
          align: 'center',
          width: 160,
          render: (h, params) => {
            return h('span', params.row.is_success === '1' ? '发送成功' : '发送失败')
          }
        },
        {
          title: '操作',
          key: 'operation',
          align: 'center',
          width: 210,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: {
                  icon: 'md-paper',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleDetail(params.row)
                  }
                }
              }, '查看'),
              h('Button', {
                style: {
                  display: params.row.is_success === '0' ? 'inline-block' : 'none',
                  border: 'none',
                  margin: '0 5px'
                },
                props: {
                  icon: 'ios-send',
                  size: 'small',
                  type: 'error'
                },
                on: {
                  click: () => {
                    this.pushAgainAll(params.row)
                  }
                }
              }, '重新发送')
            ])
          }
        }
      ],
      list: [], // 表单列表数据
      total: null, // 列表数据条数
      listQuery: {// 列表请求参数
        pageSize: 10,
        pageIndex: 1,
        customer_company_name: '',
        ship_name: '',
        voyage_no: '',
        node_name: '',
        update_time: ''
      },
      listCurrent: 1 // 当前页码
    }
  },
  created () {
    this.getList()
  },
  methods: {
    // 查询
    searchResults (e) {
      if (e.target) delete e.target
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      Object.assign(this.listQuery, e)
      this.listQuery.update_time = this.update_time
      this.getList()
    },
    // 时间格式
    selectOnChanged (e) {
      this.update_time = e.key
    },
    // 重置
    resetResults () {
      this.listCurrent = 1
      this.listQuery = Object.assign(this.listQuery, {
        pageSize: 10,
        pageIndex: 1,
        customer_company_name: '',
        ship_name: '',
        voyage_no: '',
        node_name: '',
        update_time: ''
      })
      this.setSearchData.customer_company_name.value = ''
      this.setSearchData.ship_name.value = ''
      this.setSearchData.voyage_no.value = ''
      this.setSearchData.node_name.value = ''
      this.setSearchData.update_time.selected = ''
      this.getList()
    },
    // 获取历史记录列表
    getList () {
      historicalSMSPushList(this.listQuery).then(response => {
        if (response.data.Code === 10000) {
          this.list = response.data.Result
          this.total = response.data.Total
        } else {
          this.$Message.error(response.data.Message)
        }
        setTimeout(() => {
          this.listLoading = false
        }, 1 * 800)
      }).catch(
        setTimeout(() => {
          this.listLoading = false
        }, 1 * 800)
      )
    },
    // 查看
    handleDetail (row) {
      this.$refs.pushComponents.dialogType = 'detail'
      this.$refs.pushComponents.title = '详情'
      row.post_maxnum = parseInt(row.post_maxnum)
      this.$refs.pushComponents.modalSMSData = Object.assign({}, row)
      this.$refs.pushComponents.modalSMS = true
    },
    // 重新发送
    pushAgainAll (row) {
      let listUserId = []
      let listUsermobile = []
      row.userInfo.map(item => {
        listUserId.push(item.id)
        listUsermobile.push(item.customer_member_tel)
      })
      let listQueryData = {
        ids: listUserId.join(','),
        mobiles: listUsermobile.join(','),
        sms_content: row.sms_content
      }
      pushAgainSMS(listQueryData).then(response => {
        if (response.data.Code === 10000) {
          this.$Message.success(response.data.Message)
          this.getList()
        } else {
          this.$Message.error(response.data.Message)
        }
      })
    },
    // 手动更新列表
    handleUpdateTable () {
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      this.getList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    }
  }
}
</script>
