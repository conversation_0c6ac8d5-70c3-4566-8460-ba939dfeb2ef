<template>
  <div>
    <Modal
      v-model="formModal"
      :data="modalData"
      :title="formTitle"
      :dialogType="dialogType"
      :width="800"
      :mask-closable="dialogType==='detail'"
      @on-visible-change="visibleChange">
      <Form ref="formValidata" v-if="formModal" :model="formValidata" :rules="ruleValidate" :label-width="90" inline>
        <div>
          <h3 class="bold-font title-font">基础信息</h3>
        </div>
        <Row>
          <Col span="14">
            <FormItem label="公司" style="width: 100%">
              <Input v-model="formValidata.customer_company_name" readonly></Input>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="7">
            <FormItem label="客户姓名" prop="customer_name" style="width: 100%">
              <Input type="text" v-model="formValidata.customer_name" :readonly="dialogType==='detail'"></Input>
            </FormItem>
          </Col>
          <Col span="7">
            <FormItem label="岗位" prop="customer_post" style="width: 100%">
              <Input v-model="formValidata.customer_post" :readonly="dialogType==='detail'"></Input>
            </FormItem>
          </Col>
          <Col span="7">
            <FormItem label="电话" prop="company_tel" style="width: 100%">
              <Input v-model="formValidata.company_tel" :readonly="dialogType==='detail'"></Input>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="7">
            <FormItem label="邮箱" prop="company_email" style="width: 100%">
              <Input type="text" v-model="formValidata.company_email" :readonly="dialogType==='detail'"></Input>
            </FormItem>
          </Col>
          <Col span="7">
            <FormItem label="微信" prop="company_weixin" style="width: 100%">
              <Input v-model="formValidata.company_weixin" :readonly="dialogType==='detail'"></Input>
            </FormItem>
          </Col>
          <Col span="7">
            <FormItem label="QQ" prop="company_qq" style="width: 100%">
              <Input v-model="formValidata.company_qq" :readonly="dialogType==='detail'"></Input>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="21">
            <FormItem label="备注" prop="remark" style="width: 100%">
              <Input type="textarea" :autosize="true" v-model="formValidata.remark" :rows="4" :readonly="dialogType==='detail'"></Input>
            </FormItem>
          </Col>
        </Row>
      </Form>
      <div slot="footer">
        <Button @click="formModal = false" style="margin-right:10px;">取消</Button>
        <Button v-if="dialogType==='create'" type="primary" @click="createData">保存</Button>
        <Button v-if="dialogType==='update'" type="primary" @click="updateData">保存</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import API from '@/api/customerManagement/customerManagement'
import { validateMobilePhone, validateEmail } from '@/libs/iViewValidate'

export default {
  data () {
    return {
      formModal: false, // 模态框显示状态
      modalData: {},
      dialogType: null,
      formTitle: '', // 模态框标题
      formValidata: {
        vy_customer_company_id: '',
        customer_company_name: '',
        customer_name: '', // 客户名称
        customer_post: '', // 职务
        company_tel: '', // 联系方式
        company_email: '', // 邮箱
        company_weixin: '', // 微信
        company_qq: '', // qq
        remark: '' // 备注
      },
      ruleValidate: {
        customer_name: [
          { required: true, message: '此处不能为空', trigger: 'blur' }
        ],
        company_tel: [
          { required: true, message: '此处不能为空', trigger: 'blur' },
          { validator: validateMobilePhone, trigger: 'blur' }
        ],
        company_email: [
          { required: false, validator: validateEmail, trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    // 开启模态框
    visibleChange (d) {
      if (d === true) {
        if (this.dialogType === 'update') {
          this.formValidata = Object.assign({}, this.modalData)
          this.formValidata.vy_customer_company_id = this.modalData.vy_customer_company_id
          delete this.formValidata.company_id
        } else {
          this.formValidata.vy_customer_company_id = this.modalData.id
        }
        this.formValidata.customer_company_name = this.modalData.customer_company_name
        this.$forceUpdate()
      } else {
        this.clearData()
      }
    },
    // 新增保存
    createData () {
      this.$refs['formValidata'].validate((valid) => {
        if (valid) {
          this.$Modal.confirm({
            title: '提示',
            content: '<p>是否新增成员信息？</p>',
            loading: true,
            onOk: () => {
              this.modalData.vy_customer_company_id = this.modalData.id
              let data = Object.assign({}, this.formValidata)
              API.customerInfoAdd(data).then((response) => {
                if (response.data.Code === 10000) {
                  this.$Message.success(response.data.Message)
                  this.loading = false
                  this.$Modal.remove()
                  this.formModal = false
                  this.$emit('addSuccess', this.modalData)
                  this.closeModal()
                } else {
                  this.formModal = true
                  this.$Message.error(response.data.Message)
                  this.$Modal.remove()
                  this.loading = false
                }
              }).catch(function () {
                this.$Message.warning('系统异常，请联系管理员')
              })
            }
          })
        }
      })
    },
    // 编辑保存
    updateData () {
      this.$refs['formValidata'].validate((valid) => {
        if (valid) {
          this.$Modal.confirm({
            title: '提示',
            content: '<p>是否修改成员信息？</p>',
            loading: true,
            onOk: () => {
              let data = Object.assign({}, this.formValidata)
              API.customerInfoUpdate(data).then((response) => {
                if (response.data.Code === 10000) {
                  this.$Message.success(response.data.Message)
                  this.loading = false
                  this.$Modal.remove()
                  this.formModal = false
                  this.$emit('addSuccess', data)
                  this.closeModal()
                } else {
                  this.formModal = true
                  this.$Message.error(response.data.Message)
                  this.$Modal.remove()
                  this.loading = false
                }
              }).catch(function () {
                this.$Message.warning('系统异常，请联系管理员')
              })
            }
          })
        }
      })
    },
    // 关闭模态框
    closeModal () {
      this.formModal = false
      this.clearData()
    },
    // 重置数据
    clearData () {
      this.formValidata = {
        id: '',
        customer_name: '', // 客户名称
        customer_post: '', // 职务
        company_tel: '', // 联系方式
        company_email: '', // 邮箱
        company_weixin: '', // 微信
        company_qq: '', // qq
        remark: ''
      }
    }
  }
}
</script>
<style scoped>
.demo-drawer-footer {
  width: 100%;
  bottom: 0;
  left: 0;
  position: absolute;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  background: #fff;
}
.formModalTitle {
  margin: 0 0 9px;
}
</style>
