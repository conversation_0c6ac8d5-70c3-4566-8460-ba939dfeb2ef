<template>
  <div class="detailCon">
    <Card class="base-box">
      <baseInfo :baseObj="voyageBaseInfo"></baseInfo>
      <Button type="primary" icon="ios-arrow-back" size="small" @click="backPrev" class="backBtn">返回</Button>
    </Card>
    <Tabs type="card" v-model="curTab" value="detail" style="margin-top: 20px;">
      <TabPane label="详情" name="detail" class="detail-con">
        <Row>
          <!-- 左侧港口动态详情 -->
          <Col :span="detailType === 'planVoyage' ? 24 : 14">
            <portDetail ref="portRef" :voyageId="voyageId"></portDetail>
          </Col>
          <!-- 右侧节点动态详情 -->
          <Col v-if="detailType !== 'planVoyage'" span="10">
            <dynamicInfo ref="dynamicRef" v-if="detailType === 'curVoyage'" :baseObj="voyageBaseInfo"></dynamicInfo>
          </Col>
        </Row>
      </TabPane>
      <!-- <TabPane v-if="detailType === 'curVoyage'" label="靠泊计划" name="berth">
        <berthInfo ref="berthRef" showType="searchDetail" :berthObj="berthObj"></berthInfo>
      </TabPane> -->
      <Button @click="handleTabsFresh" type="primary" icon="md-sync" size="small" slot="extra">刷新</Button>
    </Tabs>
  </div>
</template>
<script>
import './voyageDetail.less'
import portDetail from './components/portDetail' // 装卸港详情
import baseInfo from './components/baseInfo' // 顶部基础信息
import dynamicInfo from './components/dynamicInfo' // 动态信息
import berthInfo from '@/view/berthingPlan/index' // 靠泊计划信息

export default {
  components: {
    portDetail,
    baseInfo,
    dynamicInfo,
    berthInfo
  },
  data () {
    return {
      curTab: 'detail',
      selectType: '1',
      detailType: localStorage.detailType,
      berthObj: {}, // 靠泊计划信息,传船名、航次去匹配
      voyageBaseInfo: localStorage.voyageObj ? JSON.parse(localStorage.voyageObj) : {}, // 航次基础信息
      voyageId: '' // 航次id
    }
  },
  created () {
    this.voyageId = this.voyageBaseInfo.id
    this.berthObj = {
      ship_name: this.voyageBaseInfo.ship_name,
      voyage_no: this.voyageBaseInfo.voyage_no
    }
  },
  beforeDestroy () {
    // localStorage.setItem('detailType', '')
    // localStorage.setItem('voyageObj', {})
  },
  methods: {
    // 刷新
    handleTabsFresh () {
      if (this.curTab === 'detail') {
        this.$refs.portRef.getPortInfo()
        if (this.detailType !== 'planVoyage') {
          this.$refs.dynamicRef.getDynamicInfo()
        }
      }
      if (this.curTab === 'berth') {
        this.$refs.berthRef.getList()
      }
    },
    // 返回
    backPrev () {
      this.$router.go(-1)
    }
  }
}
</script>
<style scoped>
  .backBtn {
    right: 8px;
    top: 8px;
    position: absolute;
  }
</style>
