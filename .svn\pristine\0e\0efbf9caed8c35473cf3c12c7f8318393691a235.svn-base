import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 历史航次列表查询
export function queryVoHistoryMPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/record/history/queryVoyageHistoryManagePage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 添加节点
export function addHistoryNode (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/record/dynamic/addHistoryVoyageDynamic',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 添加节点-核对
export function addCheckNode (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/check/info/addHistoryVoyageDynamic',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 航线信息列表
export function queryVoPortLineList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/port/line/queryVoyagePortLineList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 单航线保存
export function changeVoPortLine (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/port/line/changeVoyagePortLine',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 统计同步
export function addSingleVoyage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/stat/single/addSingleVoyageReport',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 航程同步
export function addVoyageReportDetail (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/record/execute/addVoyageMonthReportDetail',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}
