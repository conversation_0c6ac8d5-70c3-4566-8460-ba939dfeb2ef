<template>
  <Drawer
    v-model="modalData.modal"
    :title="modalData.title"
    @on-visible-change="modalShow"
    width="1200">
    <div class="btn-area">
      <Button type="primary" @click="addNewPrice">新增</Button>
      <div class="btn-right">
        <Button :type="curDateIndex === 1 ? 'primary' : 'default'" @click="curDateIndex = 1">近一个月</Button>
        <Button :type="curDateIndex === 2 ? 'primary' : 'default'" @click="curDateIndex = 2">近三个月</Button>
        <Button :type="curDateIndex === 3 ? 'primary' : 'default'" @click="curDateIndex = 3">近半年</Button>
      </div>
    </div>
    <Row>
      <Col span="12">
        <div style="width: 100%;max-width: 572px;margin: 0 auto;">
          <Table border :loading="listLoading" ref="selection" :columns="columns" :data="list" width="572"></Table>
          <Page :total="total" :current.sync="listCurrent" :page-size-opts='[5, 10, 15, 20]' show-sizer :styles="{marginTop:'16px',textAlign: 'center'}" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
        </div>
      </Col>
      <Col span="12">
        <chart-line style="width: 550px; height: 300px;" unit="价格(元)" :value="priceNumData" text="价格趋势"/>
      </Col>
    </Row>
    <single-price-add :modalData="singleModal" @callback="getList"></single-price-add>
  </Drawer>
</template>
<script>
import API from '@/api/basicConfig/goodsConfig'
import { querySysDate } from '@/api/basicData'
import singlePriceAdd from './addSingleGoodsPriceModal'
import { ChartLine } from '_c/charts'
export default {
  props: {
    modalData: Object
  },
  components: {
    singlePriceAdd,
    ChartLine
  },
  data () {
    return {
      curDateIndex: 1, // 当前日期区间
      sysDate: '', // 系统时间
      curYear: '', // 当前年份
      curMonth: '', // 当前月份
      curDate: '', // 当前日期
      listLoading: false,
      list: [],
      priceNumData: {
        xAxis: [],
        legend: [],
        data: []
      },
      total: 0,
      listCurrent: 1,
      singleModal: {
        modal: false,
        title: '',
        cargo_id: ''
      },
      listQuery: {
        pageSize: 10,
        pageIndex: 1,
        cargo_id: '',
        cargo_date_st: '',
        cargo_date_et: ''
      },
      columns: [
        {
          title: '日期',
          key: 'cargo_date',
          align: 'center',
          width: 120
        },
        {
          title: '价格(元)',
          key: 'cargo_price',
          align: 'center',
          render: (h, params) => {
            if (params.row.isEdit) {
              return h('Input', {
                props: {
                  value: params.row.cargo_price
                },
                on: {
                  'on-blur': e => {
                    params.row.cargo_price = e.target.value
                  }
                }
              })
            } else {
              return h('span', {}, params.row.cargo_price)
            }
          }
        },
        {
          title: '操作',
          align: 'center',
          width: 200,
          render: (h, params) => {
            let btnStr = params.row.isEdit ? '保存' : '修改'
            return h('div', [
              h('Button', {
                props: {
                  icon: params.row.isEdit ? 'md-checkmark' : 'md-brush',
                  size: 'small'
                },
                on: {
                  click: () => {
                    if (params.row.isEdit) {
                      let curParam = {
                        id: params.row.id,
                        cargo_id: params.row.cargo_id,
                        cargo_date: params.row.cargo_date,
                        cargo_price: params.row.cargo_price,
                        ship_company_id: 0 // 暂时传0 非必填 留后期使用
                      }
                      API.updateBasicGoodsPrice(curParam).then(res => {
                        if (res.data.Code === 10000) {
                          this.$Message.success(res.data.Message)
                        } else {
                          this.$Message.error(res.data.Message)
                        }
                        this.$set(params.row, 'isEdit', false)
                        this.getList()
                      })
                    } else {
                      this.$set(params.row, 'isEdit', true)
                    }
                  }
                }
              }, btnStr),
              h('Button', {
                props: {
                  icon: 'md-trash',
                  size: 'small'
                },
                style: {
                  marginLeft: '10px'
                },
                on: {
                  click: () => {
                    this.handleDel(params.row)
                  }
                }
              }, '删除')
            ])
          }
        }
      ]
    }
  },
  methods: {
    // 获取列表数据
    getList () {
      this.listLoading = true
      this.priceNumData.xAxis = []
      this.priceNumData.data = []
      API.queryBasicGoodsPriceDetail(this.listQuery).then(res => {
        this.listLoading = false
        if (res.data.Code === 10000) {
          this.list = res.data.Result
          this.total = res.data.Total
          this.list.map(item => {
            this.priceNumData.xAxis.unshift(item.cargo_date)
            this.priceNumData.data.unshift(parseFloat(item.cargo_price))
          })
        }
      })
    },
    // 新增
    addNewPrice () {
      this.singleModal = {
        modal: true,
        title: this.modalData.title,
        cargo_id: this.modalData.cargo_id
      }
    },
    // 修改
    handleUpdate (d) {},
    // 删除
    handleDel (d) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确定删除该条记录？</p>',
        loading: true,
        onOk: () => {
          API.delBasicGoodsPrice({ id: d.id }).then(res => {
            this.$Modal.remove()
            res.data.Code === 10000 ? this.$Message.success(res.data.Message) : this.$Message.error(res.data.Message)
            this.getList()
          })
        }
      })
    },
    // 日期区间转换
    getDateTime () {
      this.curYear = parseInt(this.sysDate.split('-')[0])
      this.curMonth = parseInt(this.sysDate.split('-')[1])
      this.curDate = parseInt(this.sysDate.split('-')[2])
      let outDate = ''
      switch (this.curDateIndex) {
        case 1:
          outDate = this.getLastMonth()
          break
        case 2:
          outDate = this.getLastThreeMonth()
          break
        case 3:
          outDate = this.getLastSixMonth()
          break
        default:
      }
      this.listQuery.cargo_date_st = outDate
      this.getList()
    },
    // 近一个月
    getLastMonth () {
      let curDate = ''
      let nowMonthDay = new Date(this.curYear, this.curMonth, 0).getDate() // 当前月的总天数
      if (this.curMonth - 1 <= 0) { // 如果是1月,年数往前推一年
        curDate = (this.curYear - 1) + '-' + 12 + '-' + this.curDate
      } else {
        let lastMonthDay = new Date(this.curYear, (parseInt(this.curMonth) - 1), 0).getDate()
        if (lastMonthDay < this.curDate) { // 1个月前所在月的总天数小于现在的天日期
          if (this.curDate < nowMonthDay) { // 当前天日期小于当前月总天数
            curDate = this.curYear + '-' + (this.curMonth - 1) + '-' + (lastMonthDay - (nowMonthDay - this.curDate))
          } else {
            curDate = this.curYear + '-' + (this.curMonth - 1) + '-' + lastMonthDay
          }
        } else {
          curDate = this.curYear + '-' + (this.curMonth - 1) + '-' + this.curDate
        }
      }
      return curDate
    },
    // 近三个月
    getLastThreeMonth () {
      let curDate = ''
      let nowMonthDay = new Date(this.curYear, this.curMonth, 0).getDate() // 当前月的总天数
      if (this.curMonth - 3 <= 0) { // 如果是1 2 3月,年数往前推一年
        let lastThreeMonthDay = new Date((this.curYear - 1), (12 - (3 - parseInt(this.curMonth))), 0).getDate() // 3个月前所在月的总天数
        if (lastThreeMonthDay < this.curDate) { // 3个月前所在月的总天数小于现在的天日期
          curDate = (this.curYear - 1) + '-' + (12 - (3 - this.curMonth)) + '-' + lastThreeMonthDay
        } else {
          curDate = (this.curYear - 1) + '-' + (12 - (3 - this.curMonth)) + '-' + this.curDate
        }
      } else {
        let lastThreeMonthDay = new Date(this.curYear, parseInt(this.curMonth - 3), 0).getDate() // 3个月前所在月的总天数
        if (lastThreeMonthDay < this.curDate) { // 3个月前所在月的总天数小于现在的天日期
          if (this.curDate < nowMonthDay) { // 当前天日期小于当前月总天数,2月份比较特殊的月份
            curDate = this.curYear + '-' + (this.curMonth - 3) + '-' + (lastThreeMonthDay - (nowMonthDay - this.curDate))
          } else {
            curDate = this.curYear + '-' + (this.curMonth - 3) + '-' + lastThreeMonthDay
          }
        } else {
          curDate = this.curYear + '-' + (this.curMonth - 3) + '-' + this.curDate
        }
      }
      return curDate
    },
    // 近六个月
    getLastSixMonth () {
      let curDate = ''
      let nowMonthDay = new Date(this.curYear, this.curMonth, 0).getDate() // 当前月的总天数
      if (this.curMonth - 6 <= 0) { // 如果是1 2 3月,年数往前推一年
        let lastSixMonthDay = new Date((this.curYear - 1), (12 - (6 - parseInt(this.curMonth))), 0).getDate() // 3个月前所在月的总天数
        if (lastSixMonthDay < this.curDate) { // 3个月前所在月的总天数小于现在的天日期
          curDate = (this.curYear - 1) + '-' + (12 - (6 - this.curMonth)) + '-' + lastSixMonthDay
        } else {
          curDate = (this.curYear - 1) + '-' + (12 - (6 - this.curMonth)) + '-' + this.curDate
        }
      } else {
        let lastSixMonthDay = new Date(this.curYear, parseInt(this.curMonth - 3), 0).getDate() // 3个月前所在月的总天数
        if (lastSixMonthDay < this.curDate) { // 3个月前所在月的总天数小于现在的天日期
          if (this.curDate < nowMonthDay) { // 当前天日期小于当前月总天数,2月份比较特殊的月份
            curDate = this.curYear + '-' + (this.curMonth - 6) + '-' + (lastSixMonthDay - (nowMonthDay - this.curDate))
          } else {
            curDate = this.curYear + '-' + (this.curMonth - 6) + '-' + lastSixMonthDay
          }
        } else {
          curDate = this.curYear + '-' + (this.curMonth - 6) + '-' + this.curDate
        }
      }
      return curDate
    },
    // 获取系统时间
    getSysDate () {
      querySysDate().then(res => {
        if (res.data.Code === 10000) {
          this.sysDate = res.data.systemDate.split(' ')[0]
          this.listQuery.cargo_date_et = this.sysDate
          this.getDateTime() // 初始化获取
        }
      })
    },
    // 显隐展示
    modalShow (val) {
      if (val) {
        this.listQuery.cargo_id = this.modalData.cargo_id
        this.getSysDate()
      } else {
        this.curDateIndex = 1
        this.$emit('callback')
      }
    },
    // 重置
    resetResults () {
      this.listCurrent = 1
      this.listQuery = Object.assign(this.listQuery, {
        pageIndex: 1,
        cargo_id: '',
        lately_cargo_date: ''
      })
      this.getList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    }
  },
  watch: {
    curDateIndex () {
      this.getDateTime()
    }
  }
}
</script>

<style lang="less" scoped>
  .btn-area {
     margin-bottom: 20px;
  }
  .btn-right {
    float: right;
    button {
      margin-left: 10px;
    }
  }
</style>
