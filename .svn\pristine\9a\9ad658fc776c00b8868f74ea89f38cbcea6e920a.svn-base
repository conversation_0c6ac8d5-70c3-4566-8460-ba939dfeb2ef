<template>
  <div>
    <!-- 港口对比 -->
    <Card>
      <div class="btn_area">
        <span>时间：</span>
        <month-select class="month-select" v-model="baseDate" @on-change="dateSelect"></month-select>
        <Button size="small" type="primary" @click="clearData" style="padding: 3px 7px">重置</Button>
      </div>
      <Row>
        <Col span="8">
          <div class="line-area">
            <ChartBar style="height: 300px;" unit="小时" :value="anchorTimeData" @clickBack="lineBack" :clickable="true" :color="lineColor" text="前十港口锚泊时长"/>
          </div>
          <div class="line-area">
            <ChartBar style="height: 300px;" unit="航次" :value="anchorMoreTimeData" @clickBack="lineBackBig" :clickable="true" :color="lineColor" text="抛锚72小时以上次数"/>
          </div>
          <Spin size="large" fix v-if="spinShow">
            <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
            <div>Loading</div>
          </Spin>
        </Col>
        <Col span="16">
          <search @searchResults='searchResults' :setSearch='setSearchData' @keydown.enter.native='searchResults' @resetResults='resetResults'></search>
          <Table :data="listData" border :loading="loading" :columns="columns" class="list_table"></Table>
          <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.queryParam.pageSize" :current.sync="listCurrent"
          :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
        </Col>
      </Row>
    </Card>
  </div>
</template>
<script>
import { ChartBar } from '_c/charts'
import search from '_c/search' // 查询组件
import MonthSelect from '@/components/monthSelect'
import { queryStatTime, queryPortList, queryWharfList } from '@/api/basicData'
import { querySumOrderByPorts, queryAnchorReport } from '@/api/statistics/anchorStatistics'

export default {
  components: {
    search,
    ChartBar,
    MonthSelect
  },
  data () {
    return {
      spinShow: false,
      baseDate: '',
      chartParam: {
        start_month: '',
        end_month: ''
      },
      lineColor: ['#6699FF'],
      anchorTimeList: [],
      anchorTimeData: {
        xAxis: [],
        data: []
      },
      anchorMoreTimeList: [],
      anchorMoreTimeData: {
        xAxis: [],
        data: []
      },
      setSearchData: {
        port_id: {
          type: 'select',
          label: '港口',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 150,
          value: '',
          filterable: true,
          change: this.getWharf
        },
        wharf_id: {
          type: 'select',
          label: '码头',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 150,
          value: '',
          filterable: true,
          isdisabled: true
        },
        ship_id: {
          type: 'select',
          label: '船舶',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 110,
          value: '',
          filterable: true
        },
        is_seventy_two: {
          type: 'select',
          label: '是否超72小时',
          selectData: [
            {
              value: '1',
              label: '是'
            },
            {
              value: '0',
              label: '否'
            }
          ],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 60,
          value: ''
        }
      },
      queryParam: {
        ship_id: '',
        pageSize: 20,
        pageIndex: 1,
        port_id: '',
        wharf_id: '',
        is_seventy_two: ''
      },
      loading: false,
      total: null,
      listCurrent: 1,
      listData: [],
      columns: [
        {
          title: '港口',
          key: 'port_name',
          align: 'center'
        },
        {
          title: '码头',
          key: 'wharf_name',
          align: 'center'
        },
        {
          title: '状态',
          key: 'port_type_name',
          align: 'center',
          width: 65
        },
        {
          title: '船名',
          key: 'ship_name',
          align: 'center'
        },
        {
          title: '航次',
          key: 'voyage_no',
          align: 'center',
          width: 80
        },
        {
          title: '航线',
          key: 'port_line',
          align: 'center'
        },
        {
          title: '抛锚时长',
          key: 'anchor_time',
          align: 'center',
          width: 100
        },
        {
          title: '备注',
          key: '',
          align: 'center'
        }
      ]
    }
  },
  created () {
    if (window.localStorage.shipNameList) {
      let shipList = JSON.parse(window.localStorage.shipNameList)
      shipList.map(item => {
        if ((item.business_model === '1' || item.business_model === '2') && !item.ship_name.includes('善')) { // 只要自营和船期船舶且不需要万邦船舶  2024/09/09调整
          this.setSearchData.ship_id.selectData.push({
            value: item.ship_id,
            label: item.ship_name
          })
        }
      })
    } else {
      this.setSearchData.ship_id.selectData = []
    }
    queryPortList().then(res => {
      if (res.data.Code === 10000) {
        res.data.Result.map(item => {
          this.setSearchData.port_id.selectData.push({
            value: item.id,
            label: item.port_name
          })
        })
      }
    })
    this.getSysDate()
  },
  methods: {
    // 抛锚时长未超过72小时
    lineBack (val) {
      this.setSearchData.port_id.selected = this.anchorTimeList[val].port_id
      this.queryParam.port_id = this.anchorTimeList[val].port_id
      this.queryParam.is_seventy_two = ''
      this.setSearchData.is_seventy_two.selected = ''
      this.getWharf()
      this.getList()
    },
    // 抛锚时长超过72小时
    lineBackBig (val) {
      this.setSearchData.port_id.selected = this.anchorMoreTimeList[val].port_id
      this.queryParam.port_id = this.anchorMoreTimeList[val].port_id
      this.queryParam.is_seventy_two = '1'
      this.setSearchData.is_seventy_two.selected = '1'
      this.getWharf()
      this.getList()
    },
    // 获取统计折线图
    getChartList () {
      this.resetChart()
      this.spinShow = true
      querySumOrderByPorts(this.chartParam).then(res => {
        if (res.data.Code === 10000) {
          this.spinShow = false
          if (res.data.portAnchorTimeSumArray.length > 0) { // 船舶抛锚时长
            let backArr = res.data.portAnchorTimeSumArray.sort((a, b) => {
              return parseFloat(b.anchor_time_sum) - parseFloat(a.anchor_time_sum)
            })
            backArr.forEach(item => {
              this.anchorTimeData.xAxis.push(item.port_name)
              this.anchorTimeData.data.push(item.anchor_time_sum)
              this.anchorTimeList.push({
                port_id: item.port_id,
                port_name: item.port_name
              })
            })
            // res.data.portAnchorTimeSumArray.forEach(item => {
            //   this.anchorTimeData.xAxis.push(item.port_name)
            //   this.anchorTimeData.data.push(item.anchor_time_sum)
            // })
          }
          if (res.data.portAnchorTimeRedLineNumArray.length > 0) { // 船舶72H抛锚时长次数
            res.data.portAnchorTimeRedLineNumArray.forEach(item => {
              this.anchorMoreTimeData.xAxis.push(item.port_name)
              this.anchorMoreTimeData.data.push(item.port_num)
              this.anchorMoreTimeList.push({
                port_id: item.port_id,
                port_name: item.port_name
              })
            })
          }
        } else {
          this.spinShow = false
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 获取报表
    getList () {
      this.loading = true
      this.queryParam.start_month = this.chartParam.start_month
      this.queryParam.end_month = this.chartParam.end_month
      queryAnchorReport(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          this.loading = false
          this.listData = res.data.Result
          this.total = res.data.Total
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 数据清空
    resetChart () {
      this.anchorTimeData.xAxis = []
      this.anchorMoreTimeData.xAxis = []
      this.anchorTimeList = []
      this.anchorTimeData.data = []
      this.anchorMoreTimeData.data = []
      this.anchorMoreTimeList = []
    },
    clearData () {
      this.chartParam = {
        ship_id: '',
        base_year: '',
        contrast_year: ''
      }
      this.getSysDate()
    },
    // 重置报表
    resetResults () {
      this.queryParam = {
        ship_id: '',
        pageSize: 20,
        pageIndex: 1,
        port_id: '',
        wharf_id: '',
        is_seventy_two: ''
      }
      this.listCurrent = 1
      this.setSearchData.port_id.selected = ''
      this.setSearchData.ship_id.selected = ''
      this.setSearchData.is_seventy_two.selected = ''
      this.getList()
      // this.getChartList()
    },
    // 全局查询
    searchData () {
      this.queryParam.base_year = this.chartParam.base_year
      this.getList()
      this.getChartList()
    },
    // 报表查询
    searchResults () {
      this.listCurrent = 1
      this.queryParam.port_id = this.setSearchData.port_id.selected
      this.queryParam.wharf_id = this.setSearchData.wharf_id.selected
      this.queryParam.ship_id = this.setSearchData.ship_id.selected
      this.queryParam.is_seventy_two = this.setSearchData.is_seventy_two.selected
      this.getList()
    },
    // 系统年月
    getSysDate () {
      this.baseDate = ''
      queryStatTime().then(res => {
        if (res.data.Code === 10000) {
          if (res.data.start_month && res.data.end_month) {
            this.chartParam.start_month = res.data.start_month
            this.chartParam.end_month = res.data.end_month
            this.baseDate = this.chartParam.start_month + '~' + this.chartParam.end_month
            this.getChartList()
            this.getList()
          }
        }
      })
      // querySysDate().then(res => {
      //   if (res.data.Code === 10000 && res.data.systemDate !== '') {
      //     let _year = res.data.systemDate.substring(0, 4)
      //     this.chartParam.start_month = _year + '-01'
      //     this.chartParam.end_month = res.data.systemDate.substring(0, 7)
      //     this.baseDate = this.chartParam.start_month + '~' + this.chartParam.end_month
      //     this.getChartList()
      //     this.getList()
      //   }
      // })
    },
    // 获取码头
    getWharf () {
      this.setSearchData.wharf_id.isdisabled = this.setSearchData.port_id.selected === undefined
      this.setSearchData.wharf_id.selectData = []
      this.setSearchData.wharf_id.selected = ''
      if (this.setSearchData.port_id.selected === undefined) return
      queryWharfList({ port_id: this.setSearchData.port_id.selected }).then(res => {
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.setSearchData.wharf_id.selectData.push({
              value: item.id,
              label: item.terminal_name
            })
          })
        }
      })
    },
    // 日期变化触发
    dateSelect (val) {
      this.chartParam.start_month = val[0]
      this.chartParam.end_month = val[1]
      this.getChartList()
      this.getList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.queryParam.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.queryParam.pageIndex = val
      this.getList()
    }
  },
  computed: {
    // baseDate () {
    //   if (this.chartParam.start_month !== '' && this.chartParam.end_month !== '') {
    //     console.log(this.chartParam.start_month)
    //     return this.chartParam.start_month + '~' + this.chartParam.end_month
    //   }
    //   if (this.chartParam.start_month !== '' && this.chartParam.end_month === '') {
    //     console.log(2222)
    //     return this.chartParam.start_month
    //   }
    //   return ''
    // }
  }
}
</script>
<style lang="less" scoped>
.btn_area {
  margin-bottom: 15px;
   button {
    margin-left: 12px;
  }
}
.line-area,
.list_table {
  margin-top: 20px;
}
</style>
<style lang="less">
.btn_area {
  .month-select-input .ivu-input {
    color: #515a6e;
    line-height: 30px;
    height: 30px;
    background-color: #fff;
    border: 1px solid #dcdee2;
  }
}
.ivu-poptip-popper {
  z-index: 9999 !important;
}
</style>
