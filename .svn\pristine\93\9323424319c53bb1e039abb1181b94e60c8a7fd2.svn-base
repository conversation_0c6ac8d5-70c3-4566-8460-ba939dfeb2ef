export default {
  /**
   * @description 配置显示在浏览器标签的title
   */
  title: '航次动态',
  /**
   * @description token在Cookie中存储的天数，默认7天
   */
  cookieExpires: 7,
  /**
   * 路由模式
   */
  routerModel: 'history', // hash
  /**
   * @description api请求基础路径
   */
  ajaxHeader: { 'Content-Type': 'application/x-www-form-urlencoded' },
  baseUrl: {
    // dev: 'http://hc.xtshipping.com:8078/', // 正式线
    // pro: 'http://hc.xtshipping.com:8078/' // 正式线
    // dev: 'http://iais.xtshipping.net/', // 预正式线
    // pro: 'http://iais.xtshipping.net/' // 预正式线
    // dev: 'http://************:8081/xt_voyage_shipowner', // 其云本地
    dev: 'http://************:8078/', // 测试线
    pro: 'http://************:8078/' // 测试线
    // dev: 'http://************:8060/', // 测试线（正式线副本数据）
    // pro: 'http://************:8060/' // 测试线（正式线副本数据）
  },
  /**
   * @description 默认打开的首页的路由name值，默认为home
   */
  homeName: 'home',
  /**
   * @description 需要加载的插件
   */
  plugin: {
    'error-store': {
      showInHeader: true, // 设为false后不会在顶部显示错误日志徽标
      developmentOff: true // 设为true后在开发环境不会收集错误信息，方便开发中排查错误
    }
  }
}
