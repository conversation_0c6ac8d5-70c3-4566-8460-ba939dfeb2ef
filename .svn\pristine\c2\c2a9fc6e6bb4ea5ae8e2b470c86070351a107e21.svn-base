<template>
  <Layout style="height: 100%" class="main">
    <!-- 左侧菜单导航 -->
    <Sider v-show="menuShow" hide-trigger collapsible :width="186" :collapsed-width="64" v-model="collapsed" class="left-sider" :style="{overflow: 'hidden'}">
      <side-menu ref="sideMenu" :active-name="$route.name" :collapsed="collapsed" @on-select="turnToPage" :menu-list="menuList">
        <div class="logo-con">
          <img v-show="!collapsed" :src="maxLogo" key="max-logo" />
          <img v-show="collapsed" :src="minLogo" key="min-logo" />
        </div>
      </side-menu>
    </Sider>
    <Layout>
      <Header v-show="menuShow" class="header-con">
        <header-bar :collapsed="collapsed" @on-coll-change="handleCollapsedChange">
          <div>
            <Tooltip placement="bottom">
              <Button icon="ios-log-out" size="large" type="text" @click="quitLogin()"></Button>
              <div slot="content">
                <p>退出登录</p>
              </div>
            </Tooltip>
          </div>
          <user :message-unread-count="unreadCount" :user-avatar="userAvatar"/>
          <Dropdown style="margin-right: 30px;">
            <a href="javascript:void(0)">
                <span style="color: #333;">App下载</span>
                <Icon type="md-arrow-dropdown" color="#333"></Icon>
            </a>
            <DropdownMenu slot="list">
              <DropdownItem>
                <img style="width: 200px; height: 200px;" :src="appCode"/>
              </DropdownItem>
            </DropdownMenu>
          </Dropdown>
          <Input class="header-search" v-model="searchText" search placeholder="请输入货运单号" @on-search="searchVoyage"/>
          <!-- <Button icon="ios-shirt" size="large" type="text" style="font-size:20px;color:rgb(148,151,155);margin-top: 6px;"></Button> -->
        </header-bar>
      </Header>
      <Content ref="mainContent" :class="menuShow ? 'main-content-con' : 'main-content-con-full'">
        <Layout class="main-layout-con">
          <Content class="content-wrapper">
            <keep-alive :include="cacheList">
              <router-view/>
            </keep-alive>
            <ABackTop :height="100" :bottom="80" :right="50" container=".content-wrapper"></ABackTop>
          </Content>
        </Layout>
      </Content>
    </Layout>
  </Layout>
</template>
<script>
import SideMenu from './components/side-menu'
import HeaderBar from './components/header-bar'
// import TagsNav from './components/tags-nav'
import User from './components/user'
import ABackTop from './components/a-back-top'
import { mapMutations, mapActions, mapGetters } from 'vuex'
import { getNewTagList, routeEqual, getMenuShow } from '@/libs/util'
import routers from '@/router/routers'
import appCode from '@/assets/images/app.png'
import minLogo from '@/assets/images/logo-min.png'
import maxLogo from '@/assets/images/logo.png'
import './main.less'
import API from '@/api/search'
export default {
  name: 'Main',
  components: {
    SideMenu,
    HeaderBar,
    // TagsNav,
    User,
    ABackTop
  },
  data () {
    return {
      collapsed: false,
      searchText: '',
      appCode,
      minLogo,
      maxLogo,
      show: false
    }
  },
  computed: {
    ...mapGetters([
      'errorCount'
    ]),
    tagNavList () {
      return this.$store.state.app.tagNavList
    },
    tagRouter () {
      return this.$store.state.app.tagRouter
    },
    menuShow () {
      let isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Mobi/i.test(navigator.userAgent) || window.innerWidth < 500
      if (isMobile) return false
      return (getMenuShow() || this.$store.state.user.menuShow || false)
    },
    userAvatar () {
      return this.$store.state.user.avatarImgPath
    },
    cacheList () {
      const list = ['ParentView', ...this.tagNavList.length ? this.tagNavList.filter(item => !(item.meta && item.meta.notCache)).map(item => item.name) : []]
      return list
    },
    menuList () {
      return this.$store.getters.menuList
    },
    hasReadErrorPage () {
      return this.$store.state.app.hasReadErrorPage
    },
    unreadCount () {
      return this.$store.state.user.unreadCount
    }
  },
  methods: {
    ...mapMutations([
      'setBreadCrumb',
      'setTagNavList',
      'addTag',
      // 'setLocal',
      'setHomeRoute',
      'closeTag'
    ]),
    ...mapActions([
      'phoneLogin',
      'handleLogin',
      'handleSuperLogin',
      'handleLogOut'
    ]),
    turnToPage (route) {
      let { name, params, query } = {}
      if (typeof route === 'string') name = route
      else {
        name = route.name
        params = route.params
        query = route.query
      }
      if (name.indexOf('isTurnByHref_') > -1) {
        window.open(name.split('_')[1])
        return
      }
      this.$router.push({
        name,
        params,
        query
      })
    },
    // 开始查询
    searchVoyage (val) {
      if (val === '') return
      API.queryWaybillNumber({ number_no: val }).then(res => {
        if (res.data.Code === 10000) {
          if (res.data.Result.length === 0) {
            this.$Notice.warning({
              title: '无此单号',
              desc: '查无此单号信息,请确认单号信息是否正确!'
            })
          } else {
            localStorage.setItem('voyageObj', JSON.stringify(res.data.Result[0]))
            this.searchText = ''
            this.$router.push({
              name: 'searchInDetail',
              params: {
                id: val
              }
            })
          }
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    handleCollapsedChange (state) {
      this.collapsed = state
      this.$store.commit('setCollapsed', state)
      // localStorage.setItem('collapsed', state)
    },
    handleCloseTag (res, type, route) {
      if (type !== 'others') {
        if (type === 'all') {
          // this.turnToPage('home')
          this.turnToPage(this.$config.homeName)
        } else {
          if (routeEqual(this.$route, route)) {
            this.closeTag(route)
          }
        }
      }
      this.setTagNavList(res)
    },
    handleClick (item) {
      this.turnToPage(item)
    },
    // 退出登录
    quitLogin () {
      let toRouteName = this.$store.state.user.access === 'super_admin' || this.$store.state.user.access === 'super_set' ? 'manageLogin' : 'login'
      this.handleLogOut().then(() => {
        this.$router.push({
          name: toRouteName
        })
      })
    }
  },
  watch: {
    '$route' (newRoute) {
      const { name, query, params, meta } = newRoute
      this.addTag({
        route: { name, query, params, meta },
        type: 'push'
      })
      this.setBreadCrumb(newRoute)
      this.setTagNavList(getNewTagList(this.tagNavList, newRoute))
      this.$refs.sideMenu.updateOpenName(newRoute.name)
    }
  },
  mounted () {
    // this.collapsed = Boolean(localStorage.collapsed)
    /**
     * @description 初始化设置面包屑导航和标签导航
     */
    this.setTagNavList()
    this.setHomeRoute(routers)
    const { name, params, query, meta } = this.$route
    this.addTag({
      route: { name, params, query, meta }
    })
    this.setBreadCrumb(this.$route)
    // 设置初始语言
    // this.setLocal(this.$i18n.locale)
    // 如果当前打开页面不在标签栏中，跳到homeName页
    if (!this.tagNavList.find(item => item.name === this.$route.name)) {
      this.$router.push({
        name: this.$config.homeName
        // name: 'home'
      })
    }
  }
}
</script>
