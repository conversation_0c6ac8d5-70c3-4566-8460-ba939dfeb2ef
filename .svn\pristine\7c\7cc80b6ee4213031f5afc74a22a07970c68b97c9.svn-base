<template>
  <div>
    <Spin size="large" fix v-if="numSpinShow">
      <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
      <div>Loading</div>
    </Spin>
    <Card>
      <div class="btn-area">
        <Button size="small" type="primary" icon="ios-arrow-back" @click="$emit('callback')">返回</Button>
        <Select size="small" class="select-ship-content" :transfer="false" v-model="queryParam.ship_id" placeholder="请选择船舶"
                clearable @on-change="shipSelect">
          <Option v-for="(item, index) in selfShipList" :value="item.ship_id" :key="index">{{ item.ship_name }}</Option>
        </Select>
        <month-select @on-change="dateSelect"></month-select>
        <Button size="small" type="primary" icon="md-eye" @click="handleReport('view')">预览报表</Button>
        <Button size="small" type="primary" icon="md-download" @click="handleReport('down')">下载报表</Button>
      </div>
      <div class="line-area">
        <Row>
          <Col span="17">
            <chart-bar style="height: 300px;" unit="万吨" :value="barData" text="船舶营运总览"/>
          </Col>
          <Col span="7">
            <chart-pie style="height: 300px;" :value="pieData" text="运输货品占比"></chart-pie>
          </Col>
        </Row>
      </div>
      <div class="total-num">
        <Row>
          <Col span="6">
            <div class="num-title">执行航次数</div>
            <count-to class="num-data" :end="parseFloat(baseData.voyage_sum)" unitText="次" unitClass="num-unit" usegroup/>
          </Col>
          <Col span="6">
            <div class="num-title">航程</div>
            <count-to class="num-data" :end="parseFloat(baseData.mile_sum)" unitText="万海里" unitClass="num-unit" usegroup :decimals="decimals2"/>
          </Col>
          <Col span="6">
            <div class="num-title">货运量</div>
            <count-to class="num-data" :end="parseFloat(baseData.amount_sum)" unitText="万吨" unitClass="num-unit" usegroup :decimals="decimals2"/>
          </Col>
          <Col span="6">
            <div class="num-title">周转量</div>
            <count-to class="num-data" :end="parseFloat(baseData.turnover_sum)" unitText="万吨公里" unitClass="num-unit" usegroup :decimals="decimals2"/>
          </Col>
        </Row>
        <Row class-name="num-row">
          <Col span="6">
            <div class="num-title">待泊时长</div>
            <count-to class="num-data" :end="parseFloat(baseData.wait_berth_sum)" unitText="小时" unitClass="num-unit" usegroup/>
          </Col>
          <Col span="6">
            <div class="num-title">平均待泊</div>
            <count-to class="num-data" :end="parseFloat(baseData.wait_berth_average)" unitText="小时" unitClass="num-unit" usegroup :decimals="decimals2"/>
          </Col>
          <Col span="6">
            <div class="num-title">总损耗</div>
            <count-to class="num-data" :end="parseFloat(baseData.goods_loss_sum)" unitText="万吨" unitClass="num-unit" usegroup :decimals="decimals4"/>
          </Col>
          <Col span="6">
            <div class="num-title">平均损耗率</div>
            <count-to class="num-data" :end="parseFloat(baseData.goods_loss_average)" unitText="‰" unitClass="num-unit" usegroup :decimals="decimals2"/>
          </Col>
        </Row>
      </div>
    </Card>
    <Card class="card-distance">
      <Row>
        <Col span="17">
          <ChartBarAndLine style="height: 300px;" :value="goodsNumData"></ChartBarAndLine>
        </Col>
        <Col span="7">
          <div class="goods_avg">
            <h3>月平均数据</h3>
            <Row class-name="goods_avg_row">
              <Col span="12">
                <div class="goods_avg_title">月平均货运量</div>
                <div class="goods_avg_data">
                  {{ baseData.month_amount_average }}
                  <span class="goods_avg_unit">万吨</span>
                </div>
              </Col>
              <Col span="12">
                <div class="goods_avg_title">月平均周转量</div>
                <div class="goods_avg_data">
                  {{ baseData.month_turnover_average }}
                  <span class="goods_avg_unit">万吨公里</span>
                </div>
              </Col>
            </Row>
            <Row class-name="goods_avg_row">
              <Col span="12">
                <div class="goods_avg_title">月平均航次数</div>
                <div class="goods_avg_data">
                  {{ baseData.month_voyage_average }}
                  <span class="goods_avg_unit">次</span>
                </div>
              </Col>
              <Col span="12">
                <div class="goods_avg_title">月平均航程</div>
                <div class="goods_avg_data">
                  {{ baseData.month_mile_average }}
                  <span class="goods_avg_unit">公里</span>
                </div>
              </Col>
            </Row>
          </div>
        </Col>
      </Row>
    </Card>
    <Row :gutter="20" class="card-distance">
      <Col span="6">
        <Card class="card-title-area">
          <p slot="title" class="card-title">航次数</p>
          <p slot="extra" class="card-extra">{{ baseData.voyage_sum }}<span>次</span></p>
          <ul class="card-voyage-list">
            <li v-for="(item, idx) in baseData.VoyagePortNumberArray" :key="idx">
              <span class="voyage-list-idx">{{ (idx + 1) }}</span>
              <span class="voyage-list-name">{{ item.load_port_name }} - {{item.unload_port_name}}</span>
              <span class="voyage-list-num">{{ item.num }}<span>次</span></span>
            </li>
          </ul>
        </Card>
      </Col>
      <Col span="6">
        <Card class="card-title-area">
          <p slot="title" class="card-title">作业效率</p>
          <div class="rate-area">
            <div class="load-rate-icon"><Icon type="ios-download-outline" size="22"/></div>
            <div class="rate-text-area">
              <div class="rate-text">装货速率<span class="rate-data">{{ baseData.load_month_rate}} 吨/小时</span></div>
              <div class="rate-detail">装货平均时长：{{ baseData.load_time_diff }} 小时</div>
            </div>
          </div>
          <div class="rate-area">
            <div class="unload-rate-icon"><Icon type="ios-cloud-upload-outline" size="22"/></div>
            <div class="rate-text-area">
              <div class="rate-text">卸货速率<span class="rate-data">{{ baseData.unload_month_rate}} 吨/小时</span></div>
              <div class="rate-detail">卸货平均时长：{{ baseData.unload_time_diff }} 小时</div>
            </div>
          </div>
        </Card>
      </Col>
      <Col span="12">
        <Card>
          <chart-line style="height: 248px;" unit="海里" :value="voyageMileNumData" text="航程"/>
        </Card>
      </Col>
    </Row>
  </div>
</template>
<script>
import { ChartLine, ChartBar, ChartPie, ChartBarAndLine } from '_c/charts'
import CountTo from '_c/count-to'
import MonthSelect from '@/components/monthSelect'
import API from '@/api/statistics'

export default {
  components: {
    ChartLine,
    ChartBarAndLine,
    ChartBar,
    ChartPie,
    MonthSelect,
    CountTo
  },
  data () {
    return {
      numSpinShow: false,
      decimals2: 2,
      decimals3: 3,
      decimals4: 4,
      spinShow: false,
      selfShipList: [], // 船舶列表
      queryParam: {
        ship_id: '',
        start_month: '',
        end_month: ''
      },
      baseData: {
        voyage_sum: 0,
        mile_sum: 0,
        amount_sum: 0,
        turnover_sum: 0,
        wait_berth_sum: 0,
        wait_berth_average: 0,
        goods_loss_sum: 0,
        goods_loss_average: 0,
        month_amount_average: 0,
        month_turnover_average: 0,
        month_voyage_average: 0,
        month_mile_average: 0,
        VoyagePortNumberArray: [],
        load_month_rate: 0,
        load_time_diff: 0,
        unload_month_rate: 0,
        unload_time_diff: 0
      },
      pieData: [],
      barData: {
        xAxis: [],
        data: []
      },
      goodsNumData: {
        xAxis: [],
        yAxis: [
          { name: '货运量(万吨)' },
          { name: '周转量(万吨/公里)' }
        ],
        legend: ['货运量', '周转量'],
        seriesData: [
          {
            name: '货运量',
            type: 'bar',
            smooth: 0.2,
            symbol: 'circle',
            data: []
          },
          {
            name: '周转量',
            type: 'line',
            smooth: 0.2,
            symbol: 'circle',
            data: []
          }
        ]
      },
      voyageMileNumData: {
        xAxis: [],
        legend: ['航程'],
        symbol: ['circle'],
        data: []
      }
    }
  },
  methods: {
    async getList () {
      this.resetData()
      await API.queryShipServiceByMonth(this.queryParam).then(res => {
        this.numSpinShow = false
        if (res.data.Code === 10000) {
          this.baseData = res.data
          // 船舶营运总览数据
          this.baseData.shipAmountSummary.forEach(item => {
            this.barData.xAxis.push(item.ship_name)
            this.barData.data.push(item.amount_sum)
          })
          // 运输货品占比
          this.baseData.goodsAmountArray.forEach(item => {
            this.pieData.push({
              value: item.goods_amount_sum,
              name: item.goods_name
            })
          })
          // 货运量 周转量数据
          this.baseData.totalMonthAmountArray.forEach(item => {
            this.goodsNumData.xAxis.push(item.voyage_over_month)
            this.goodsNumData.seriesData[0].data.push(item.month_amount_sum)
            this.goodsNumData.seriesData[1].data.push(item.month_turnover_sum)
          })
          // 航程数据
          this.baseData.monthMileArray.forEach(item => {
            this.voyageMileNumData.xAxis.push(item.voyage_over_month)
            this.voyageMileNumData.data.push(item.month_mile_sum)
          })
        } else {
          this.$Message.error(res.data.message)
        }
      })
    },
    // 船舶选择触发
    shipSelect () {
      this.getList()
    },
    // 日期变化触发
    dateSelect (dateObj) {
      this.queryParam.start_month = dateObj[0] ? dateObj[0] : ''
      this.queryParam.end_month = dateObj[1] ? dateObj[1] : ''
      this.getList()
    },
    // 数据清空 重置
    resetData () {
      this.numSpinShow = true
      this.baseData = {
        voyage_sum: 0,
        mile_sum: 0,
        amount_sum: 0,
        turnover_sum: 0,
        wait_berth_sum: 0,
        wait_berth_average: 0,
        goods_loss_sum: 0,
        goods_loss_average: 0,
        month_amount_average: 0,
        month_turnover_average: 0,
        month_voyage_average: 0,
        month_mile_average: 0,
        VoyagePortNumberArray: [],
        load_month_rate: 0,
        load_time_diff: 0,
        unload_month_rate: 0,
        unload_time_diff: 0
      }
      this.barData.xAxis = []
      this.barData.data = []
      this.pieData = []
      this.goodsNumData.xAxis = []
      this.goodsNumData.seriesData[0].data = []
      this.goodsNumData.seriesData[1].data = []
      this.voyageMileNumData.xAxis = []
      this.voyageMileNumData.data = []
    },
    // 报表预览 下载
    handleReport (str) {
      // if (!this.queryParam.ship_id || this.queryParam.ship_id === '') {
      //   this.$Notice.warning({
      //     title: '操作异常',
      //     desc: '请至少选择一艘船再进行操作'
      //   })
      //   return
      // }
      API.voyageCbyyzlReportTeplateAll(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          if (str === 'view') { // 预览
            sessionStorage.setItem('wpsUrl', res.data.wpsUrl)
            sessionStorage.setItem('token', res.data.token)
            const jump = this.$router.resolve({ name: 'viewFile' })
            window.open(jump.href, '_blank')
          } else { // 下载
            window.open(res.data.fileUrl, '_blank')
          }
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    }
  },
  created () {
    this.selfShipList = JSON.parse(localStorage.shipNameList)
    this.getList()
  }
}
</script>

<style lang="less" scoped>
  .btn-area {
    text-align: right;
    margin-bottom: 10px;
    button {
      margin-left: 12px;
    }
  }
  .total-num {
    background: #F1F5FF;
    padding: 32px 25px;
    color: #2B304C;
    margin-top: 35px;
    .num-row {
      margin-top: 40px;
    }
    .num-title {
      font-size: 14px;
    }
    .num-data {
      font-size: 24px;
      font-weight: bold;
    }
  }
  .line-area {
    margin-top: 40px;
  }
  .card-distance {
    margin-top: 14px;
    .ivu-card-head {
      border-bottom: none;
    }
  }
  .rate-area {
    margin: 6px 0 52px 0;
    .rate-text-area {
      display: inline-block;
      margin-left: 10px;
      .rate-text {
        color: #555564;
        font-size: 14px;
        font-weight: 600;
      }
      .rate-data {
        color: #2B2B3D;
        font-size: 18px;
        font-weight: 600;
        float: right;
        position: absolute;
        right: 10px;
      }
      .rate-detail {
        color: #555564;
        font-size: 10px;
        margin-top: 5px;
      }
    }
    .load-rate-icon {
      display: inline-block;
      width:44px;
      height:44px;
      line-height: 44px;
      color: #fff;
      text-align: center;
      background:#2EBD41;
      border-radius:12px;
      vertical-align: top;
    }
    .unload-rate-icon {
      display: inline-block;
      width:44px;
      height:44px;
      line-height: 44px;
      color: #fff;
      text-align: center;
      background:#4880FF;
      border-radius:12px;
      vertical-align: top;
    }
  }
  .card-title {
    font-size: 18px;
  }
  .card-extra {
    font-size: 18px;
    span {
      font-size: 12px;
      margin-left: 3px;
    }
  }
  .card-voyage-list {
    padding: 0 20px;
    list-style-type:none;
    height: 200px;
    overflow: auto;
    color: #2B304C;
    font-size: 18px;
    li {
      height: 28px;
      line-height: 28px;
      margin-bottom: 22px;
    }
    .voyage-list-idx {
      display: inline-block;
      width: 28px;
      height: 28px;
      background: #4880FF;
      color: #fff;
      line-height: 28px;
      text-align: center;
      border-radius: 50%;
    }
    .voyage-list-name {
      font-weight: bold;
      margin-left: 18px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .voyage-list-num {
      float: right;
      span {
        font-size: 12px;
        margin-left: 5px;
      }
    }
  }
  .goods_avg {
    width: calc(100% + 5px);
    height: 216px;
    margin-left: -30px;
    margin-top: 60px;
    // border: 1px solid rgba(204, 204, 204, 0.5);
    border-bottom-width: 2px;
    border-right: none;
    padding: 8px 15px;
    color: #2B304C;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    h3 {
      text-align: center;
    }
    .goods_avg_row {
      margin: 15px 0;
    }
    .goods_avg_title {
       font-size: 14px;
    }
    .goods_avg_data {
      font-size: 30px;
      font-weight: bold;
    }
    .goods_avg_unit {
      font-size: 20px;
      font-weight: bold;
    }
  }
</style>
<style>
  .num-unit {
    font-size: 10px;
    font-weight: 100;
    margin-left: 6px;
  }
  .card-title-area .ivu-card-head {
    border-bottom: none;
  }
  .btn-area .select-ship-content {
   width: 110px;
   margin-left:12px !important;
   height: 32px !important;
 }
 .btn-area .select-ship-content .ivu-select-selection {
   height: 32px !important;
   background: #007DFF;
 }
 .btn-area .select-ship-content .ivu-select-selection .ivu-select-selected-value {
   color: #fff;
 }
 .btn-area .select-ship-content .ivu-select-selection .ivu-select-arrow {
   color: #fff;
 }
</style>
