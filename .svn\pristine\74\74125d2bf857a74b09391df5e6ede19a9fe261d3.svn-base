<template>
  <Modal :width="768" :mask-closable="false" v-model="modalData.modal" @on-ok="viewVoyageCom" :title="modalData.title" @on-visible-change="modalStatusChange">
    <Card>
      <search @searchResults='searchResults' :setSearch='setSearchData' @keydown.enter.native='searchResults' @addSuccess="getList" @resetResults='resetResults'></search>
    </Card>
    <Table border highlight-row :loading="listLoading" :current="curPage" ref="selection" :columns="columns" :data="list" @on-row-click="voyageRowClick" @on-row-dblclick="voyageDbClick"></Table>
    <Page :total="total" :current.sync="listCurrent" :page-size-opts='[5, 10, 15, 20]' :page-size="5" show-sizer :styles="{margin:'10px -10px 0 0',textAlign: 'right'}" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
  </Modal>
</template>

<script>
import search from '_c/search' // 查询组件
import formAction from '_c/formAction' // 表单操作组件
import API from '@/api/voyageManage/planVoyage'

export default {
  props: {
    modalData: Object
  },
  components: {
    search,
    formAction
  },
  data () {
    return {
      listLoading: false, // 数据加载
      curPage: 1, // 当前页
      listCurrent: 1, // 分页当前页
      total: 0, // 分页总页数
      list: [], // 列表
      curVoyageObj: {},
      listQuery: {
        pageSize: 5,
        pageIndex: 1,
        ship_id: ''
      },
      setSearchData: {// 查询设置，对象key值为回调参数
        ship_id: {
          type: 'select',
          label: '船名',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          flag: 'ship_id',
          selectName: '',
          width: 150,
          value: ''
        }
      },
      columns: [
        {
          title: '船名',
          key: 'ship_name',
          align: 'center',
          width: 100
        },
        {
          title: '航次',
          key: 'voyage_no',
          align: 'center',
          maxWidth: 80
        },
        {
          title: '装港/码头',
          key: 'load_port_names',
          align: 'center',
          render: (h, params) => {
            if (!params.row.cargoParam || params.row.cargoParam.length <= 0) return ''
            let curList = []
            params.row.cargoParam.forEach(list => {
              if (list.portParam && list.portParam.length > 0) {
                list.portParam.forEach(item => {
                  if (item.port_type === '1') {
                    curList.push(item)
                  }
                })
              }
            })
            let portList = curList.map(d => {
              return d.wharf_name === '' ? d.port_name : d.port_name + '/' + d.wharf_name
            })
            return h('div', {
              style: 'white-space: pre;'
            }, portList.join('\n'))
          }
        },
        {
          title: '卸港/码头',
          key: 'unload_port_names',
          align: 'center',
          render: (h, params) => {
            if (!params.row.cargoParam || params.row.cargoParam.length <= 0) return ''
            let curList = []
            params.row.cargoParam.forEach(list => {
              if (list.portParam && list.portParam.length > 0) {
                list.portParam.forEach(item => {
                  if (item.port_type === '2') {
                    curList.push(item)
                  }
                })
              }
            })
            let portList = curList.map(d => {
              return d.wharf_name === '' ? d.port_name : d.port_name + '/' + d.wharf_name
            })
            return h('div', {
              style: 'white-space: pre;'
            }, portList.join('\n'))
          }
        },
        {
          title: '货品',
          key: 'goods_names',
          align: 'center',
          render: (h, params) => {
            if (!params.row.cargoParam || params.row.cargoParam.length <= 0) return ''
            let curList = []
            params.row.cargoParam.forEach(list => {
              if (list.portParam && list.portParam.length > 0) {
                curList.push(list)
              }
            })
            let goodsList = curList.map(d => {
              return d.goods_name
            })
            return h('div', {
              style: 'white-space: pre;'
            }, goodsList.join('\n'))
          }
        },
        {
          title: '货量(吨)',
          key: 'amounts',
          align: 'center',
          render: (h, params) => {
            if (!params.row.cargoParam || params.row.cargoParam.length <= 0) return ''
            let curList = []
            params.row.cargoParam.forEach(list => {
              if (list.portParam && list.portParam.length > 0) {
                curList.push(list)
              }
            })
            let goodsList = curList.map(d => {
              return d.amounts
            })
            return h('div', {
              style: 'white-space: pre;'
            }, goodsList.join('\n'))
          }
        }
      ]
    }
  },
  methods: {
    modalStatusChange (val) { // 弹窗展示取消
      if (val) {
        this.getShipList()
        this.resetResults()
      }
    },
    getShipList () { // 获取船舶列表
      // 获取船名
      if (window.localStorage.shipNameList) {
        let shipList = JSON.parse(window.localStorage.shipNameList)
        shipList.map(item => {
          this.setSearchData.ship_id.selectData.push({
            value: item.ship_id,
            label: item.ship_name
          })
        })
      } else {
        this.setSearchData.ship_id.selectData = []
      }
    },
    getList () { // 获取航次列表
      this.listLoading = true
      API.queryVoyagePlanViewPage(this.listQuery).then(res => {
        if (res.data.Code === 10000) {
          this.list = res.data.Result
          this.total = res.data.Total
          this.listLoading = false
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 表格行点击
    voyageRowClick (row) {
      this.curVoyageObj = row
    },
    voyageDbClick (row) {
      this.modalData.modal = false
      this.$emit('callback', row)
    },
    viewVoyageCom () {
      this.$emit('callback', this.curVoyageObj)
    },
    // 查询
    searchResults (e) {
      if (e.target) delete e.target
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      Object.assign(this.listQuery, e)
      this.getList()
    },
    // 重置
    resetResults () {
      this.listCurrent = 1
      this.listQuery = { // 列表请求参数
        pageSize: 5,
        pageIndex: 1,
        ship_id: ''
      }
      this.setSearchData.ship_id.selected = ''
      this.getList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    }
  }
}
</script>
