<template>
  <div>
    <Card>
      <search @searchResults='searchResults' @selectOnChanged='selectOnChanged' :setSearch='setSearchData' @keydown.enter.native='searchResults' @resetResults='resetResults'></search>
    </Card>
    <Card class="cardDiv">
      <Row>
        <Col span="3" :style="`height: ${winHeight}px; overflow: auto;`">
          <div v-for="(item, index) in shipList" :key="index" @click="changeShip(item, index)" class="shiplist" :class="{'curidx': curShip === index}">
            {{ item.ship_name }}
          </div>
        </Col>
        <Col span="3" offset="1" :style="`height: ${winHeight}px; overflow: auto;`">
          <!-- <Scroll :on-reach-bottom="handleReachBottom" :height="winHeight"> -->
            <div v-for="(item, index) in voyageList" :key="index" @click="changeVoyageDocument(item, index)" class="shiplist" :class="{'curidx': curVoyage === index}">
              {{ item.voyage_no }}
            </div>
          <!-- </Scroll> -->
        </Col>
        <Col span="16" offset="1">
          <div>
            <span v-for="(list,idx) in curVoyageList" :key="idx" class="port-tab" :class="{'cur-port': curPort === idx}" @click="changeVoyagePort(list, idx)">
              <span v-if="list.port_type === '1'" class="port-load">装</span>
              <span v-if="list.port_type === '2'" class="port-unload">卸</span>
              {{ list.port_name }} - {{ list.wharf_name }}
            </span>
          </div>
          <Table border :loading="loading" :columns="columns" :data="documentList"></Table>
          <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.picQuery.pageSize" :current.sync="picCurrent"
              :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
        </Col>
      </Row>
    </Card>
  </div>
</template>
<script>
import search from '_c/search' // 查询组件
import API from '@/api/document'

export default {
  components: {
    search
  },
  data () {
    return {
      winHeight: 500, // 浏览器高度
      shipList: [], // 公司船舶列表
      loading: false,
      curShip: 0, // 当前船舶
      curVoyage: 0, // 默认航次
      curPort: 0, // 默认港口
      voyageList: [], // 航次列表
      voyageTotal: 0, // 航次列表条数
      documentList: [], // 文件列表
      curVoyageList: [], // 当前航次
      listQuery: {// 列表请求参数
        voyage_date: '', // 归属年月
        pageSize: 10000,
        pageIndex: 1
      },
      picQuery: {
        voyage_id: '', // 航次id
        port_detail_id: '', // 细表id
        port_id: '', // 港口id
        wharf_id: '', // 码头id
        pageIndex: 1, // 页码
        pageSize: 10 // 页数
      },
      total: 0, // 总页数
      picCurrent: 1, // 当前页码
      setSearchData: {// 查询设置，对象key值为回调参数
        voyage_date: {
          type: 'year',
          label: '时间',
          selected: '',
          width: 130,
          value: '',
          isdisable: false
        }
      },
      columns: [
        {
          title: '文件分类',
          key: 'picture_type_name',
          align: 'center'
        },
        {
          title: '文件名',
          key: 'picture_name',
          align: 'center',
          render: (h, params) => {
            if (!params.row.isEdit) {
              return h('div', [
                h('span', {
                  style: {// 设置样式
                    height: '50px',
                    padding: '5px 0'
                  }
                }, params.row.picture_name),
                h('Button', {
                  style: {
                    margin: '0 10px 0'
                  },
                  props: {
                    icon: 'md-brush',
                    size: 'small'
                  },
                  on: {
                    click: () => {
                      this.$set(params.row, 'isEdit', true)
                    }
                  }
                })
              ])
            } else {
              return h('div', [
                h('input', {
                  style: {// 设置样式
                    height: '32px',
                    padding: '5px 0',
                    border: '1px solid #cccccc66',
                    'padding-left': '5px'
                  },
                  domProps: {
                    value: params.row.picture_name
                  },
                  on: {
                    input: function (event) {
                      params.row.picture_name = event.target.value
                    }
                  }
                }),
                h('Button', {
                  style: {
                    margin: '0 10px 0'
                  },
                  props: {
                    icon: 'md-checkmark',
                    size: 'small'
                  },
                  on: {
                    click: () => {
                      this.$set(params.row, 'isEdit', false)
                      API.updateVoyagePicture({
                        id: params.row.id,
                        picture_name: params.row.picture_name
                      }).then(res => {
                        if (res.data.Code === 10000) {
                          this.$Message.success(res.data.Message)
                        } else {
                          this.$Message.error(res.data.Message)
                        }
                      })
                    }
                  }
                })
              ])
            }
          }
        },
        {
          title: '照片',
          key: 'picture_oos_sfurl',
          align: 'center',
          render: (h, params) => {
            return h('img', {
              style: {// 设置样式
                height: '50px',
                padding: '5px 0',
                cursor: 'pointer',
                'border-radius': '5%'
              },
              attrs: {
                src: params.row.picture_oos_sfurl
              },
              on: {
                click: () => {
                  window.open(params.row.picture_oos_url, '_blank')
                }
              }
            })
          }
        },
        {
          title: '操作',
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                directives: [
                  {
                    name: 'down',
                    value: params.row.picture_oos_url
                  }
                ]
              }, '下载')
            ])
          }
        }
      ]
    }
  },
  methods: {
    // 获取航次列表
    getList () {
      API.queryMainVoyagePage(this.listQuery).then(res => {
        if (res.data.Code === 10000) {
          this.voyageList = res.data.Result
          this.voyageTotal = res.data.Total
          if (this.voyageList[0] && this.voyageList[0].detailData.length > 0) {
            this.curVoyageList = this.voyageList[0].detailData
            this.picQuery = Object.assign(this.picQuery, {
              voyage_id: this.voyageList[0].detailData[0].voyage_id,
              port_detail_id: this.voyageList[0].detailData[0].id,
              port_id: this.voyageList[0].detailData[0].port_id,
              wharf_id: this.voyageList[0].detailData[0].wharf_id
            })
            this.getDocumentMess()
          } else {
            this.curVoyageList = []
            this.documentList = []
          }
        } else {
          this.$Message.warning(res.data.Message)
        }
      })
    },
    // 获取航次图片列表
    getDocumentMess () {
      API.queryVoyagePicturePage(this.picQuery).then(res => {
        if (res.data.Code === 10000) {
          // 测试数据
          // this.documentList = [{
          //   'picture_name': '图片名称',
          //   'picture_oos_url': 'https://axure-file.lanhuapp.com/6d00773d-5645-4780-8517-60e29d406235__14c79295c74e6b61f2a9b23ecffc7e15.png', // '图片阿里云路径',
          //   'picture_oos_sfurl': 'https://axure-file.lanhuapp.com/6d00773d-5645-4780-8517-60e29d406235__14c79295c74e6b61f2a9b23ecffc7e15.png' // '图片缩图路径'
          // }]
          // this.total = 1
          this.documentList = res.data.Result
          this.total = res.data.Total
        }
      })
    },
    changeVoyagePort (list, idx) {
      this.curPort = idx
      this.picQuery = Object.assign(this.picQuery, {
        pageIndex: 1,
        voyage_id: list.voyage_id,
        port_detail_id: list.id,
        port_id: list.port_id,
        wharf_id: list.wharf_id
      })
      this.getDocumentMess()
    },
    // 航次变化触发
    changeVoyageDocument (item, idx) {
      this.curVoyageList = item.detailData
      this.curVoyage = idx
      this.curPort = 0
      if (item.detailData && item.detailData.length > 0) {
        this.picQuery = Object.assign(this.picQuery, {
          pageIndex: 1,
          voyage_id: item.detailData[0].voyage_id,
          port_detail_id: item.detailData[0].id,
          port_id: item.detailData[0].port_id,
          wharf_id: item.detailData[0].wharf_id
        })
        this.getDocumentMess()
      }
    },
    handleReachBottom (dir) {
      return new Promise(resolve => {
        setTimeout(() => {
          let _voyagePage = Math.ceil(this.voyageTotal / this.listQuery.pageSize)
          if (this.listQuery.pageIndex < _voyagePage) {
            this.listQuery.pageIndex += 1
            this.getList()
          }
        })
      })
    },
    // 船舶变化触发
    changeShip (item, idx) {
      this.curShip = idx
      this.curVoyage = 0
      this.curPort = 0
      this.listQuery.ship_id = item.ship_id
      this.picQuery.pageIndex = 1
      this.getList()
    },
    // 查询
    searchResults (e) {
      if (e.target) delete e.target
      this.picCurrent = 1
      this.listQuery.pageIndex = 1
      this.curVoyage = 0
      this.curPort = 0
      this.voyageList = []
      Object.assign(this.listQuery, e)
      this.getList()
    },
    // 时间格式
    selectOnChanged (e) {
      this.setSearchData.voyage_date.value = e.key
    },
    // 重置
    resetResults () {
      this.picCurrent = 1
      this.listQuery.pageIndex = 1
      this.listQuery.voyage_date = ''
      this.setSearchData.voyage_date.selected = ''
      this.setSearchData.voyage_date.value = ''
      this.curVoyage = 0
      this.curPort = 0
      this.getList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.picQuery.pageSize = val
      this.getDocumentMess()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.picQuery.pageIndex = val
      this.getDocumentMess()
    }
  },
  created () {
    this.shipList = localStorage.shipNameList ? JSON.parse(localStorage.shipNameList) : []
    if (this.shipList.length > 0) {
      this.shipList = this.shipList.filter(item => ((item.business_model === '1' || item.business_model === '2') && !item.ship_name.includes('善')))  // 只要自营和船期船舶且不需要万邦船舶  2024/09/09调整
      this.listQuery.ship_id = this.shipList[0].ship_id
      this.getList()
    }
  },
  mounted () {
    // 挂载浏览器高度获取方法
    const that = this
    that.winHeight = window.innerHeight - 240
    window.onresize = () => {
      return (() => {
        that.winHeight = window.innerHeight - 240
      })()
    }
  }
}
</script>
<style scoped>
.cardDiv {
  margin-top: 20px;
  border: none;
  background: transparent;
}
.shiplist {
  cursor: pointer;
  color: #333;
  height: 60px;
  line-height: 60px;
  text-align: center;
  margin-bottom: 10px;
  background-color: white;
}
.port-tab {
  display: inline-block;
  cursor: pointer;
  color: #333;
  height: 45px;
  line-height: 45px;
  padding: 0 25px;
  text-align: center;
  margin-right: 20px;
  margin-bottom: 10px;
  background-color: white;
  border: 1px solid #333;
  border-radius: 8px;
}
.curidx {
  color: white;
  background-color: #007DFF;
}
.cur-port {
  color: white;
  background-color: #007DFF;
  border: none;
}
.port-load {
  display: inline-block;
  color:#F59A23;
  margin-right: 20px;
}
.port-unload {
  display: inline-block;
  color: #02A7F0;
  margin-right: 20px;
}
</style>
