<template>
  <div>
    <Drawer
      v-model="formModal"
      :title="title"
      :data="modalData"
      :dialogType="dialogType"
      width="1000"
      :mask-closable="dialogType !== 'create' && dialogType !== 'update' && dialogType !== 'update_plan' ? true : false"
      @on-visible-change="visibleChange">
      <Tabs :value="showTabName" :animated="false" @on-click="tabChange" v-if="formModal">
        <TabPane label="装货作业" name="loadingOperation" v-if="voyageStatus == '4' && dialogType !== 'create' && dialogType !== 'update' && dialogType !== 'update_plan' && dialogType !== 'detail_plan'">
          <loadingOperation ref="loadingOperation"></loadingOperation>
        </TabPane>
        <TabPane label="卸货作业" name="dischargeCargo" v-if="voyageStatus == '4' && dialogType !== 'create' && dialogType !== 'update' && dialogType !== 'update_plan' && dialogType !== 'detail_plan'">
          <dischargeCargo ref="dischargeCargo"></dischargeCargo>
        </TabPane>
      </Tabs>
    </Drawer>
  </div>
</template>

<script>
import loadingOperation from './loadingOperation' // 装货作业
import dischargeCargo from './dischargeCargo' // 卸货作业
export default {
  components: {
    loadingOperation,
    dischargeCargo
  },
  data () {
    return {
      tabName: '',
      showTabName: 'loadingOperation', // tab默认显示项
      voyageStatus: ''
    }
  },
  methods: {
    // 开启模态框
    visibleChange (d) {
      if (d === true) {
        if (this.dialogType === 'loadingOperation') {
          this.showTabName = 'loadingOperation'
          this.$nextTick(() => {
            this.$refs.loadingOperation.getLoadingOperationList()
          })
        } else if (this.dialogType === 'dischargeCargo') {
          this.showTabName = 'dischargeCargo'
          this.$nextTick(() => {
            this.$refs.dischargeCargo.getDischargeCargoList()
          })
        }
      }
    },
    tabChange (name) {
      if (this.dialogType === 'create' || this.dialogType === 'update') return
      this.tabName = name
      switch (name) {
        case 'loadingOperation':
          this.dialogType = 'loadingOperation'
          this.$refs.loadingOperation.getLoadingOperationList()
          break
        case 'dischargeCargo':
          this.dialogType = 'dischargeCargo'
          this.$refs.dischargeCargo.getDischargeCargoList()
          break
        default:
      }
    }
  }
}
</script>
<style scoped>
.demo-drawer-footer {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  background: #fff;
}
</style>
