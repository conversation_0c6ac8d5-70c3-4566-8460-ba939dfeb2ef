<style lang="less">
  @import './login.less';
</style>

<template>
  <div v-show="!isYunZhiJia" class="login">
    <div class="header">
        <div class="content fn-clear">
          <a href="/" class="logo">
            <img src="../../assets/images/login-logo.png" alt="海运管家logo" title="海运管家logo">
          </a>
          <span class="logo-font">
            <img src="../../assets/images/logo_ani.gif" alt="">
          </span>
          <div class="fn-right free-tell animated bounceInRight">
            <span class="f-name">
              <Dropdown style="margin-right: 30px;">
                <a href="javascript:void(0)">
                    <span style="color: #333;">APP</span>
                    <img style="width: 14px; height: 14px; margin: 2px 4px 0;" src="../../assets/images/wx_code.png" />
                </a>
                <DropdownMenu slot="list">
                  <DropdownItem>
                    <img style="width: 200px; height: 200px;" :src="appCode"/>
                  </DropdownItem>
                </DropdownMenu>
              </Dropdown>
            </span>
            <span class="f-name"><Button type="text" @click="isLoginInBar = !isLoginInBar" style="font-size: 16px; font-weight: 600; line-height: 16px; margin-top: -5px;">登录</Button></span>
          </div>
        </div>
        <div class="swNav">
          <ul class="content top-nav">
            <li><a target="_blank" href="http://gj.xtshipping.net">首页</a></li>
            <li><a target="_blank" href="http://gj.xtshipping.net/shipDate/list.html">找船找货</a></li>
            <li><a target="_blank" href="http://cd.xtshipping.net/uc/agent-list.html">船舶代理</a></li>
            <li><a target="_blank" href="http://gj.xtshipping.net/emergency">海上应急</a></li>
            <li><a target="_self" href="/" class="current">航次动态</a></li>
          </ul>
        </div>
      </div>
    <div class="login-search" v-if="this.$route.name === 'login'">
      <div class="search-title">国际一流、国内领先的化工供应链综合服务商</div>
      <div class="search-subtitle">一流的船队 • 一流的服务 • 一流的管理 • 一流的团队 • 一流的文化</div>
      <div class="search-con">
        <Input search enter-button="查询" placeholder="请输入货运单号" @on-search="searchVoyage">
        </Input>
      </div>
    </div>
    <Modal v-model="isLoginInBar" width="330" :transition-names="['ease', 'fade']" :transfer="false" class-name="vertical-center-modal" :mask-closable="false">
      <div class="login-con">
        <p slot="header" class="login-text">欢迎登录</p>
        <!-- <Card icon="log-in" title="欢迎登录" :bordered="false"> -->
        <div class="form-con">
          <login-form @on-success-valid="handleSubmit" @on-super-valid="handleSuperSubmit"></login-form>
        </div>
        <p slot="footer"></p>
        <!-- </Card> -->
      </div>
    </Modal>
  </div>
</template>

<script>
import LoginForm from '_c/login-form/login-form'
import { mapActions } from 'vuex'
import API from '@/api/search'
import { previewVoyageWord } from '@/api/voyageManage/curVoyage'
import appCode from '@/assets/images/app.png'

export default {
  components: {
    LoginForm
  },
  data () {
    return {
      appCode,
      isYunZhiJia: true,
      selectType: '1', // 默认单号类型
      isLoginInBar: false // 登录框显隐
    }
  },
  // computed: {
  //   isYunZhiJia () {
  //     return localStorage.setItem('isYunZhiJia', true)
  //   }
  // },
  created () {
    let _that = this
    let _user = this.getUrlKey('user')
    let _pwd = this.getUrlKey('pwd')
    let router = this.getUrlKey('router')
    let _way = '1'
    if (_user && _pwd) {
      this.handleLogin({ 'userName': _user, 'password': _pwd, _pwd, 'way': _way }).then(res => {
        if (res.Code === 10000) {
          if (res.Role === 1) {
            this.$router.push({
              name: this.$config.homeName
            })
          } else {
            this.$router.push({
              name: this.$config.homeName
            })
          }
        } else {
          this.$Message.error(res.Message)
        }
      })
    }
    qing.call('getPersonInfo', { // 针对云之家小程序限定
      success: function (res) {
        _that.isYunZhiJia = true
        _that.$store.commit('setMenuShow', false)
      },
      error: function (err) {
        _that.isYunZhiJia = false
        _that.$store.commit('setMenuShow', true)
      }
    })
    // qing.call('getPersonInfo', { // 针对云之家小程序限定
    //   success: function (res) {
    //     // _that.$store.commit('setToken', '')
    //     localStorage.removeItem('outTime')
    //     if (res.data.openId && res.data.openId !== '') {
    //       _that.phoneLogin({ 'userName': '15260337735', 'way': '1' }).then(result => {
    //         if (result.Code === 10000) {
    //           localStorage.setItem('isYunZhiJia', true)
    //           if (res.data.openId === '63292e15e4b00a6b3607d63d') { // 陈总  63565e26e4b0a9a155e62970   志阳  63292e15e4b00a6b3607d63d    虹南  632cfe9be4b00130127ee36c
    //             localStorage.setItem('outTime', 72)
    //           }
    //           if (res.data.openId === '63292e15e4b00a6b3607d63e') { // 史浩 48小时以上
    //             localStorage.setItem('outTime', 48)
    //           }
    //           if (res.data.openId === '632cfe9be4b00130127ee36c') { // 虹南 36小时以上
    //             localStorage.setItem('outTime', 36)
    //           }
    //           if (res.data.openId === '62b2d46be4b04a463c82c1a2') { // 其云 24小时以上
    //             localStorage.setItem('outTime', 24)
    //           }
    //           if (router) {
    //             if (router.indexOf('/') > 0) {
    //               _that.$router.push({
    //                 name: router.split('/')[0],
    //                 params: {
    //                   id: router.split('/')[1]
    //                 }
    //               })
    //             } else {
    //               // _that.$router.push({
    //               //   name: router
    //               // })
    //               _that.$router.replace(router) // 以防webview后退退回登录页
    //             }
    //           }
    //         }
    //       })
    //     }
    //   },
    //   error: function (res) {
    //   }
    // })
    if (_user && (document.referrer.includes('ierptest.xtshipping.com') || document.referrer.includes('erp.xtshipping.com'))) {
      this.phoneLogin({ 'userName': _user, 'way': _way }).then(res => {
        if (res.Code === 10000) {
          if (router) {
            if (router.indexOf('/') > 0) {
              this.$router.push({
                name: router.split('/')[0],
                params: {
                  id: router.split('/')[1]
                }
              })
            } else {
              if (router === 'viewFile') { // 航次命令特殊处理
                previewVoyageWord({ id: this.getUrlKey('voyageId') }).then(res => {
                  if (res.data.Code === 10000) {
                    sessionStorage.setItem('wpsUrl', res.data.wpsUrl)
                    sessionStorage.setItem('token', res.data.token)
                    const jump = this.$router.resolve({ name: 'viewFile' })
                    this.$nextTick(() => {
                      window.open(jump.href, '_self')
                    })
                  } else {
                    this.$Message.error(res.data.Message)
                    this.$router.push({
                      name: this.$config.homeName
                    })
                  }
                })
              } else {
                this.$router.push({
                  name: router
                })
              }
            }
          } else {
            this.$router.push({
              name: this.$config.homeName
            })
          }
        } else {
          this.$Message.error(res.Message)
        }
      })
    }
  },
  methods: {
    ...mapActions([
      'handleLogin',
      'phoneLogin',
      'handleSuperLogin'
    ]),
    getUrlKey (name) {
      // eslint-disable-next-line no-sparse-arrays
      return decodeURIComponent((new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(location.href) || [, ''])[1].replace(/\+/g, '%20')) || null
    },
    handleSubmit ({ userName, password, way }) {
      this.handleLogin({ userName, password, way }).then(res => {
        if (res.Code === 10000) {
          this.$router.push({
            name: this.$config.homeName
          })
        } else {
          this.$Message.error(res.Message)
        }
      })
    },
    handleSuperSubmit  ({ userName, password, way }) {
      this.handleSuperLogin({ userName, password, way }).then(res => {
        if (res.Code === 10000) {
          if (res.Role === 1) {
            this.$router.push({
              name: this.$config.homeName
            })
          } else {
            this.$router.push({
              name: this.$config.homeName
            })
          }
        } else {
          this.$Message.error(res.Message)
        }
      })
    },
    // 开始查询
    searchVoyage (val) {
      if (val === '') {
        this.$Notice.error({
          title: '空单号',
          desc: '请输入单号信息再进去查询!'
        })
        return
      }
      API.queryWaybillNumber({ number_no: val }).then(res => {
        if (res.data.Code === 10000) {
          if (res.data.Result.length === 0) {
            this.$Notice.warning({
              title: '无此单号',
              desc: '查无此单号信息,请确认单号信息是否正确!'
            })
          } else {
            localStorage.setItem('voyageObj', JSON.stringify(res.data.Result[0]))
            this.$router.push({
              name: 'searchDetail',
              params: {
                id: val
              }
            })
          }
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    }
  }
}
</script>
