<!-- 货运量港口货流量 -->
<template>
  <div>
    <Card>
      <div>
        时间：<month-select class="month-select" @on-change="dateSelect" :placeMent="startPlace" v-model="curDate"></month-select>&nbsp;&nbsp;&nbsp;&nbsp;
        港口：<Select class="select-content" clearable v-model="queryParam.port_id" filterable @on-change="portSelector">
                <Option v-for="(item, index) in portNameList" :key="index" :value="item.value">{{ item.label }}</Option>
              </Select>&nbsp;&nbsp;&nbsp;&nbsp;
        码头：<Select class="select-content" v-model="queryParam.wharf_id" :disabled="queryParam.port_id === ''" clearable filterable>
                <Option v-for="(item, index) in wharfNameList" :key="index" :value="item.value">{{ item.label }}</Option>
              </Select>&nbsp;&nbsp;&nbsp;&nbsp;
        货品：<Select class="select-content" v-model="queryParam.goods_id" clearable filterable>
                <Option v-for="(item, index) in goodsNameList" :key="index" :value="item.value">{{ item.label }}</Option>
              </Select>&nbsp;&nbsp;&nbsp;&nbsp;
        船名：<Select label-in-value size="small" class="select-content" :transfer="false" v-model="queryParam.ship_id" placeholder="请选择船舶"
                  clearable>
                <Option v-for="item in shipList" :value="item.value" :key="item.value">{{ item.label }}</Option>
        </Select>
        <Button style="margin-left: 20px;" type="primary" size="small" @click="searchData">查询</Button>
        <Button style="margin-left: 10px;" size="small" @click="searchReset">重置</Button>
      </div>
      <Row>
        <Col span="12">
          <div class="type_area">
            <span class="type_btn" :class="queryParam.port_type === 3 ? 'cur' : ''" @click="typeChange(3)">全部</span>
            <span class="type_btn" :class="queryParam.port_type === 1 ? 'cur' : ''" @click="typeChange(1)">装货</span>
            <span class="type_btn" :class="queryParam.port_type === 2 ? 'cur' : ''" @click="typeChange(2)">卸货</span>
          </div>
          <ChartBar class="box-chart" :value="lineData" unit="万吨" @clickBack="lineBack" :clickable="true" :color="lineColor" text="港口流量" />
        </Col>
        <Col span="12">
          <Table class="table_box" border :columns="columns" :data="data"></Table>
          <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="queryParam.pageSize" :current.sync="queryParam.pageIndex"
          :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
        </Col>
      </Row>
    </Card>
  </div>
</template>
<script>
import MonthSelect from '@/components/monthSelect'
import API from '@/api/statistics/transPortView'
import { queryStatTime, queryPortList, queryWharfList, queryBasicCargoList } from '@/api/basicData'
import { ChartBar } from '_c/charts'

export default {
  components: {
    MonthSelect,
    ChartBar
  },
  computed: {
    curDate () {
      if (this.queryParam.start_month !== '' && this.queryParam.end_month !== '') {
        return this.queryParam.start_month + '~' + this.queryParam.end_month
      }
      if (this.queryParam.start_month !== '' && this.queryParam.end_month === '') {
        return this.queryParam.start_month
      }
      return ''
    }
  },
  data () {
    return {
      startPlace: 'bottom-start',
      portNameList: [], // 港口列表数据
      wharfNameList: [], // 码头列表数据
      goodsNameList: [], // 货品列表数据
      shipList: [], // 船舶列表
      start_default_date: '', // 开始默认时间
      end_default_date: '', // 结束默认时间
      queryParam: {
        start_month: '',
        end_month: '',
        port_type: 3, // 装卸状态（3全部；1装；2卸，默认3全部）
        pageSize: 10,
        pageIndex: 1,
        ship_id: '',
        port_id: '',
        wharf_id: '',
        goods_id: ''
      },
      lineColor: ['#5B8FF9', '#73DEB3'],
      lineChartPortList: [], // 用来点击柱状图定位使用
      lineData: {
        xAxis: [],
        data: []
      },
      columns: [
        {
          title: '港口',
          key: 'port_name',
          align: 'center'
        },
        {
          title: '码头',
          key: 'wharf_name',
          align: 'center'
        },
        {
          title: '状态',
          key: 'port_type_name',
          align: 'center'
        },
        {
          title: '货品',
          key: 'goods_name',
          align: 'center'
        },
        {
          title: '货量(万吨)',
          key: 'unit_amount',
          align: 'center'
        }
      ],
      data: [],
      total: 0
    }
  },
  methods: {
    getList () {
      this.loading = true
      API.queryStatPortsGoodsAmount(this.queryParam).then(res => {
        this.lineData.xAxis = []
        this.lineData.data = []
        this.lineChartPortList = []
        if (res.data.Code === 10000) {
          this.loading = false
          this.data = res.data.reportPortPage.Result
          this.total = res.data.reportPortPage.Total
          res.data.portsArray.forEach(item => {
            this.lineChartPortList.push({
              port_id: item.port_id,
              port_name: item.port_name
            })
            this.lineData.xAxis.push(item.port_name)
            this.lineData.data.push(item.amount)
          })
        }
      })
    },
    lineBack (val) {
      this.queryParam.port_id = this.lineChartPortList[val].port_id
      this.getList()
    },
    getSysDate () {
      queryStatTime().then(res => {
        if (res.data.Code === 10000) {
          if (res.data.start_month && res.data.end_month) {
            this.queryParam.start_month = res.data.start_month
            this.queryParam.end_month = res.data.end_month
            this.getList()
          }
        }
      })
    },
    dateSelect (dateObj) {
      this.queryParam.start_month = dateObj[0] ? dateObj[0] : ''
      this.queryParam.end_month = dateObj[1] ? dateObj[1] : ''
    },
    searchData () {
      this.getList()
    },
    searchReset () {
      this.queryParam = {
        start_month: this.start_default_date,
        end_month: this.end_default_date,
        port_type: 3, // 装卸状态（3全部；1装；2卸，默认3全部）
        pageSize: 10,
        pageIndex: 1,
        ship_id: '',
        port_id: '',
        wharf_id: '',
        goods_id: ''
      }
      this.getList()
    },
    // 获取港口
    getPortDataList () {
      queryPortList().then(res => {
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.portNameList.push({
              value: item.id,
              label: item.port_name
            })
          })
        }
      })
    },
    // 获取港口选择器
    portSelector () {
      this.wharfNameList = []
      this.getWharf()
    },
    // 获取码头
    getWharf () {
      queryWharfList({ port_id: this.queryParam.port_id }).then(response => {
        if (response.data.Code === 10000) {
          response.data.Result.map(item => {
            this.wharfNameList.push({
              value: item.id,
              label: item.terminal_name
            })
          })
        }
      })
    },
    // 获取货品列表
    getGoodsList () {
      queryBasicCargoList({ cargo_name: '' }).then(res => {
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.goodsNameList.push({
              value: item.id,
              label: item.cargo_name
            })
          })
        }
      })
    },
    typeChange (val) {
      this.queryParam.port_type = val
      this.getList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.queryParam.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.queryParam.pageIndex = val
      this.getList()
    }
  },
  created () {
    if (window.localStorage.shipNameList) {
      let shipList = JSON.parse(window.localStorage.shipNameList)
      shipList.map(item => {
        if ((item.business_model === '1' || item.business_model === '2') && !item.ship_name.includes('善')) { // 只要自营和船期船舶且不需要万邦船舶  2024/09/09调整
          this.shipList.push({
            value: item.ship_id,
            label: item.ship_name
          })
        }
      })
    }
    this.getPortDataList() // 获取港口信息
    this.getGoodsList() // 获取货品信息
    this.getSysDate()
    // this.getList()
  }
}
</script>
<style scoped>
  .box-chart {
    height: 300px;
    margin-top: 20px;
  }
  .type_area {
    margin-top: 20px;
  }
  .type_btn {
    color: #169BD5;
    padding: 5px 10px;
    margin-right: 10px;
    border: 1px solid #333;
    cursor: pointer;
  }
  .type_btn.cur {
    background: #57a3f3;
    color: #333;
  }
  .select-content {
    width: 110px;
    margin-left:12px !important;
  }
  .table_box {
    margin-top: 20px;
  }
</style>
