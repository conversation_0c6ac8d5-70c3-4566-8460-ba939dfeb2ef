<template>
  <div class="tide-chart-container">
    <div ref="tideChart" class="tide-chart"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: 'TideChart',
  props: {
    tideData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null,
      defaultTideData: []
    };
  },
  computed: {
    displayData() {
      // 如果传入的数据为空，则使用默认测试数据
      return this.tideData.length > 0 ? this.tideData : this.defaultTideData;
    }
  },
  mounted() {
    this.generateDefaultData();
    this.initChart();
    
    // 监听窗口大小变化，调整图表大小
    window.addEventListener('resize', this.resizeChart);
  },
  beforeDestroy() {
    // 组件销毁前清除事件监听
    window.removeEventListener('resize', this.resizeChart);
    
    // 销毁图表实例
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
  },
  methods: {
    generateDefaultData() {
      // 生成24小时的潮汐测试数据
      const now = new Date();
      now.setHours(0, 0, 0, 0); // 从当天0点开始
      
      const tideData = [];
      
      // 潮汐高度基准值和波动范围
      const baseHeight = 150; // 基准高度(cm)
      const fluctuation = 100; // 波动范围(cm)
      
      // 生成一天内的潮汐数据，包括两次高潮和两次低潮
      // 增加数据点密度，每小时2个点
      for (let i = 0; i < 48; i++) {
        const time = new Date(now.getTime() + (i * 30) * 60 * 1000);
        
        // 使用正弦函数模拟潮汐变化
        // 一天内有两个完整的潮汐周期
        const height = baseHeight + Math.sin((i / 48) * Math.PI * 4) * fluctuation;
        
        tideData.push({
          time: time,
          height: Math.round(height)
        });
      }
      
      // 添加潮汐极值点标记
      const highTides = [
        { time: new Date(now.getTime() + 5 * 3600 * 1000), height: 250, type: 'high' },
        { time: new Date(now.getTime() + 17 * 3600 * 1000), height: 250, type: 'high' }
      ];
      
      const lowTides = [
        { time: new Date(now.getTime() + 11 * 3600 * 1000), height: 50, type: 'low' },
        { time: new Date(now.getTime() + 23 * 3600 * 1000), height: 50, type: 'low' }
      ];
      
      this.defaultTideData = {
        regular: tideData,
        highTides: highTides,
        lowTides: lowTides,
        currentTime: new Date()
      };
    },
    
    initChart() {
      if (!this.$refs.tideChart) return;
      
      // 初始化图表
      this.chart = echarts.init(this.$refs.tideChart);
      this.updateChart();
    },
    
    updateChart() {
      if (!this.chart) return;
      
      const data = this.displayData;
      if (!data.regular || data.regular.length === 0) return;
      
      // 准备数据
      const times = data.regular.map(item => {
        const time = new Date(item.time);
        const hours = time.getHours();
        const minutes = time.getMinutes();
        return minutes === 0 ? `${hours}:00` : `${hours}:${minutes}`;
      });
      
      const heights = data.regular.map(item => item.height);
      
      // 找出当前时间对应的索引
      const currentTime = data.currentTime || new Date();
      const currentHour = currentTime.getHours();
      const currentMinutes = currentTime.getMinutes();
      const currentTimeValue = currentHour + (currentMinutes / 60);
      
      let currentIndex = 0;
      data.regular.forEach((item, index) => {
        const itemTime = new Date(item.time);
        const itemHour = itemTime.getHours();
        const itemMinutes = itemTime.getMinutes();
        const itemTimeValue = itemHour + (itemMinutes / 60);
        
        if (Math.abs(itemTimeValue - currentTimeValue) < 0.5) {
          currentIndex = index;
        }
      });
      
      // 准备高潮和低潮的标记点
      const markPoints = [];
      
      // 添加高潮标记
      if (data.highTides && data.highTides.length > 0) {
        data.highTides.forEach(tide => {
          const tideTime = new Date(tide.time);
          const tideHour = tideTime.getHours();
          const tideMinutes = tideTime.getMinutes();
          const tideTimeValue = tideHour + (tideMinutes / 60);
          
          let index = 0;
          let minDiff = 24;
          
          data.regular.forEach((item, i) => {
            const itemTime = new Date(item.time);
            const itemHour = itemTime.getHours();
            const itemMinutes = itemTime.getMinutes();
            const itemTimeValue = itemHour + (itemMinutes / 60);
            
            const diff = Math.abs(itemTimeValue - tideTimeValue);
            if (diff < minDiff) {
              minDiff = diff;
              index = i;
            }
          });
          
          markPoints.push({
            name: '高潮',
            value: tide.height,
            xAxis: index,
            yAxis: tide.height,
            itemStyle: {
              color: '#1890ff'
            },
            label: {
              formatter: '{b}\n{c}cm',
              color: '#fff',
              position: 'top'
            }
          });
        });
      }
      
      // 添加低潮标记
      if (data.lowTides && data.lowTides.length > 0) {
        data.lowTides.forEach(tide => {
          const tideTime = new Date(tide.time);
          const tideHour = tideTime.getHours();
          const tideMinutes = tideTime.getMinutes();
          const tideTimeValue = tideHour + (tideMinutes / 60);
          
          let index = 0;
          let minDiff = 24;
          
          data.regular.forEach((item, i) => {
            const itemTime = new Date(item.time);
            const itemHour = itemTime.getHours();
            const itemMinutes = itemTime.getMinutes();
            const itemTimeValue = itemHour + (itemMinutes / 60);
            
            const diff = Math.abs(itemTimeValue - tideTimeValue);
            if (diff < minDiff) {
              minDiff = diff;
              index = i;
            }
          });
          
          markPoints.push({
            name: '低潮',
            value: tide.height,
            xAxis: index,
            yAxis: tide.height,
            itemStyle: {
              color: '#ff4d4f'
            },
            label: {
              formatter: '{b}\n{c}cm',
              color: '#fff',
              position: 'bottom'
            }
          });
        });
      }
      
      // 设置图表选项
      const option = {
        tooltip: {
          trigger: 'axis',
          formatter: function(params) {
            const data = params[0];
            return `${data.axisValue}<br/>潮高: ${data.value}cm`;
          }
        },
        grid: {
          left: '5%',
          right: '5%',
          bottom: '10%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: times,
          axisLine: {
            lineStyle: {
              color: '#e0e0e0'
            }
          },
          axisLabel: {
            color: '#e0e0e0',
            interval: function(index, value) {
              // 只显示整点时间的标签
              return value.endsWith(':00');
            },
            formatter: function(value) {
              // 简化标签显示
              return value.replace(':00', '');
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '潮高(cm)',
          min: 0,  // 设置最小值为0
          max: 300, // 设置最大值，确保图表不会被压缩
          nameTextStyle: {
            color: '#e0e0e0'
          },
          axisLine: {
            lineStyle: {
              color: '#e0e0e0'
            }
          },
          axisLabel: {
            color: '#e0e0e0'
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          }
        },
        series: [
          {
            name: '潮高',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            sampling: 'average', // 添加采样，优化大数据量显示
            data: heights,
            markPoint: {
              data: markPoints,
              symbolSize: 40
            },
            markLine: {
              data: [
                {
                  name: '当前时间',
                  xAxis: currentIndex,
                  lineStyle: {
                    color: '#fff',
                    type: 'dashed',
                    width: 2
                  },
                  label: {
                    formatter: '当前',
                    color: '#fff',
                    position: 'start'
                  }
                }
              ]
            },
            lineStyle: {
              width: 4,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#1890ff' },
                { offset: 1, color: '#39c8c8' }
              ])
            },
            areaStyle: {
              opacity: 0.3,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(24, 144, 255, 0.5)' },
                { offset: 1, color: 'rgba(57, 200, 200, 0.1)' }
              ])
            },
            itemStyle: {
              color: '#1890ff',
              borderWidth: 2
            },
            emphasis: {
              itemStyle: {
                color: '#fff',
                borderColor: '#1890ff',
                borderWidth: 3,
                shadowColor: 'rgba(24, 144, 255, 0.5)',
                shadowBlur: 10
              }
            }
          }
        ]
      };
      
      this.chart.setOption(option);
    },
    
    resizeChart() {
      if (this.chart) {
        this.chart.resize();
      }
    }
  },
  watch: {
    tideData: {
      handler() {
        this.updateChart();
      },
      deep: true
    }
  }
};
</script>

<style lang="less" scoped>
.tide-chart-container {
  width: 100%;
  height: 100%;
  
  .tide-chart {
    width: 100%;
    height: 100%;
    min-height: 220px;
  }
}
</style> 