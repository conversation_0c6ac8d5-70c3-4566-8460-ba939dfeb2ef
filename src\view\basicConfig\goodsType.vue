<template>
  <div>
    <!-- 查询 -->
    <Card>
      <search @searchResults='searchResults' :setSearch='setSearchData' @keydown.enter.native='searchResults' @resetResults='resetResults'></search>
    </Card>
    <Card style="margin-top:10px;padding-top:6px;">
      <p slot='title' class="bold-font">货品种类</p>
      <div style="width: 100%;max-width: 572px;margin: 0 auto;">
        <Table border :loading="listLoading" ref="selection" :columns="columns" :data="list" width="572"></Table>
        <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
          :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
      </div>
    </Card>
  </div>
</template>
<script>
import search from '_c/search' // 查询组件
import API from '@/api/basicConfig/goodsConfig'
import { queryBasicCargoPage, queryBasicCargoList } from '@/api/basicData'

export default {
  components: {
    search
  },
  data () {
    return {
      listLoading: false,
      listCurrent: 1,
      total: 0,
      list: [],
      listQuery: {
        pageSize: 10,
        pageIndex: 1,
        cargo_name: ''
      },
      setSearchData: {// 查询设置，对象key值为回调参数
        cargo_name: {
          type: 'select',
          label: '货品',
          selectData: [],
          selected: '',
          filterable: true,
          placeholder: '请选择',
          selectName: '',
          width: 150,
          value: ''
        }
      },
      columns: [
        {
          title: '货品',
          key: 'cargo_name',
          align: 'center',
          width: 130
        },
        {
          title: '种类',
          key: 'cargoType',
          align: 'center',
          width: 310,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                style: {
                  border: 'none',
                  margin: '0 5px'
                },
                props: {
                  size: 'small',
                  type: params.row.cargo_type === '2' ? 'primary' : 'default'
                },
                on: {
                  click: () => {
                    if (params.row.cargo_type !== '2') {
                      let curParam = {
                        id: params.row.id,
                        cargoName: params.row.cargo_name,
                        cargoType: 2,
                        isVisible: params.row.isVisible
                      }
                      this.changeGoodsShow(curParam)
                    }
                  }
                }
              }, '油品'),
              h('Button', {
                style: {
                  border: 'none',
                  margin: '0 5px'
                },
                props: {
                  size: 'small',
                  type: params.row.cargo_type === '1' ? 'primary' : 'default'
                },
                on: {
                  click: () => {
                    if (params.row.cargo_type !== '1') {
                      let curParam = {
                        id: params.row.id,
                        cargoName: params.row.cargo_name,
                        cargoType: 1,
                        isVisible: params.row.isVisible
                      }
                      this.changeGoodsShow(curParam)
                    }
                  }
                }
              }, '化学品'),
              h('Button', {
                style: {
                  border: 'none',
                  margin: '0 5px'
                },
                props: {
                  size: 'small',
                  type: params.row.cargo_type === '3' ? 'primary' : 'default'
                },
                on: {
                  click: () => {
                    if (params.row.cargo_type !== '3') {
                      let curParam = {
                        id: params.row.id,
                        cargoName: params.row.cargo_name,
                        cargoType: 3,
                        isVisible: params.row.isVisible
                      }
                      this.changeGoodsShow(curParam)
                    }
                  }
                }
              }, '液化气')
            ])
          }
        },
        {
          title: '是否展示',
          key: 'isVisible',
          align: 'center',
          width: 130,
          render: (h, params) => {
            return h('i-switch', {
              props: {
                value: params.row.isVisible === '1'
              },
              on: {
                'on-change': () => {
                  let curParam = {
                    id: params.row.id,
                    cargoName: params.row.cargo_name,
                    cargoType: params.row.cargoType,
                    isVisible: params.row.isVisible === '0' ? '1' : '0'
                  }
                  this.changeGoodsShow(curParam)
                }
              }
            })
          }
        }
      ]
    }
  },
  created () {
    this.getGoodsList()
    this.getList()
  },
  methods: {
    // 获取列表数据
    getList () {
      this.listLoading = true
      queryBasicCargoPage(this.listQuery).then(res => {
        this.listLoading = false
        if (res.data.Code === 10000) {
          this.list = res.data.Result
          this.total = res.data.Total
        }
      })
    },
    // 获取货品列表
    getGoodsList () {
      queryBasicCargoList({ cargo_name: '' }).then(res => {
        if (res.data.Code === 10000) {
          this.setSearchData.cargo_name.selectData = res.data.Result.map(item => {
            return {
              value: item.cargo_name,
              label: item.cargo_name
            }
          })
        }
      })
    },
    changeGoodsShow (param) {
      API.updateBasicGoods(param).then(res => {
        if (res.data.Code === 10000) {
          this.$Message.success(res.data.Message)
          this.getList()
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 查询
    searchResults (e) {
      if (e.target) delete e.target
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      Object.assign(this.listQuery, e)
      this.getList()
    },
    // 重置
    resetResults () {
      this.listCurrent = 1
      this.listQuery = Object.assign(this.listQuery, {
        pageIndex: 1,
        cargo_name: ''
      })
      this.setSearchData.cargo_name.selected = ''
      this.getList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    }
  }
}
</script>
