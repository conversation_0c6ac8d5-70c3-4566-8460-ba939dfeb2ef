<template>
  <span class="node-list-con" :style="`font-size:${fontSize}px`">
    <div>
      <span v-if="dateShow" :style="`color:${textColor}`">{{ nodeObj.node_date }}</span>
      <span v-if="nameShow" :style="`color:${nameColor}`"> {{ nodeObj.node_name }}</span>
    </div>
    <div :style="nameShow ? `color:${textColor};` : `color:${textColor};white-space: pre-wrap;`" v-if="nodeObj.node_name !== '计量结束' || !nameShow" v-text="checkDynamicName(nodeObj.dynamic_name)"></div>
  </span>
</template>
<script>
export default {
  props: {
    nodeObj: Object,
    dateShow: {
      type: Boolean,
      default: false
    },
    nameShow: {
      type: Boolean,
      default: true
    },
    nameColor: {
      type: String,
      default: '#999'
    },
    textColor: {
      type: String,
      default: '#999'
    },
    fontSize: {
      type: Number,
      default: 14
    }
  },
  data () {
    return {

    }
  },
  methods: {
    checkDynamicName (name) {
      if (name && name[0] === ',') {
        return name.substr(1, name.length)
      } else {
        return name
      }
    }
  }
}
</script>
<style lang="less">
  .node-list-con {
    font-size: 16px;
    color: #999;
    div {
      display: inline-block;
    }
  }
</style>
