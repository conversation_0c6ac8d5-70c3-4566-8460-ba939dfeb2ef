<template>
  <div>
    <Card>
      <search @searchResults='searchResults' @selectOnChanged='selectOnChanged' :setSearch='setSearchData' @keydown.enter.native='searchResults' @resetResults='resetResults'></search>
    </Card>
    <Card style="margin-top:10px;padding-top:6px;">
      <p slot='title' class="bold-font">用户管理</p>
      <div class="extra" slot="extra">
        <Button type="primary" icon="md-add-circle" @click="handleCreate">添加用户</Button>
        <formAction :setFormAction='setFormAction' @handleUpdateTable="getList" style="display: inline-block;"></formAction>
      </div>
      <div>
        <Table
          border
          :loading="loading"
          :columns="columns"
          :data="list"
          ref="selection"
          @on-select-all="tableSelectAll"
          @on-select-all-cancel="tableSelectCancel"
          @on-select="tableSelectAll"
          @on-select-cancel="tableSelectCancel"></Table>
        <div class="select_all">
          <Checkbox v-model="selectAll" @on-change="handleSelectAll(selectAll)">全选</Checkbox>
          <Button @click="handleDelete(selectionData, '1')">删除</Button>
        </div>
        <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
          :total="total" prev-text="< 上一页" next-text="下一页 >"
          @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
      </div>
    </Card>
    <addAccountModal :modalData="addAcountModalData" @callback="getList"></addAccountModal>
    <updateAccountModal :modalData="modalData" @callback="getList"></updateAccountModal>
  </div>
</template>

<script>
import search from '_c/search' // 查询组件
import formAction from '_c/formAction' // 表单操作组件
import API from '@/api/accountManagement'
import addAccountModal from './addAccountModal'
import updateAccountModal from './updateAccountModal'

export default {
  components: {
    search,
    formAction,
    addAccountModal,
    updateAccountModal
  },
  data () {
    return {
      selectAll: false,
      selectionData: [], // 存储已选中数据
      setSearchData: { // 查询设置，对象key值为回调参数
        full_name: {
          type: 'text',
          label: '姓名',
          value: '',
          width: 150
        },
        mobile: {
          type: 'text',
          label: '手机账号',
          value: '',
          width: 150
        },
        register_begin_date: {
          type: 'date',
          label: '注册时间',
          selected: '',
          width: 130,
          value: '',
          isdisable: false
        },
        register_end_date: {
          type: 'date_end',
          label: '-',
          selected: '',
          width: 130,
          value: '',
          isdisabled: false
        }
      },
      setFormAction: {
        operation: ['updateTable']
      },
      loading: false, // 表单列表loding状态
      columns: [
        {
          type: 'selection',
          width: 60,
          align: 'center'
        },
        {
          title: '手机账号',
          key: 'mobile',
          align: 'center'
        },
        {
          title: '姓名',
          key: 'full_name',
          align: 'center'
        },
        {
          title: '昵称',
          key: 'nickname',
          align: 'center'
        },
        {
          title: '注册时间',
          key: 'register_time',
          align: 'center'
        },
        {
          title: '上次登录时间',
          key: 'last_login_time',
          align: 'center'
        },
        {
          title: '操作',
          key: 'operation',
          align: 'center',
          width: 280,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                style: {
                  margin: '4px'
                },
                props: {
                  icon: 'md-paper',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleUpdate(params.row)
                  }
                }
              }, '查看'),
              h('Button', {
                style: {
                  margin: '4px'
                },
                props: {
                  icon: 'md-settings',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.resetPassword(params.row)
                  }
                }
              }, '重置密码'),
              h('Button', {
                style: {
                  margin: '4px'
                },
                props: {
                  icon: 'md-trash',
                  size: 'small',
                  disabled: params.row.admin_num !== '0'
                },
                on: {
                  click: () => {
                    this.handleDelete(params.row, '2')
                  }
                }
              }, '删除')
            ])
          }
        }
      ],
      list: [], // 表单列表数据
      total: null, // 列表数据条数
      listQuery: {// 列表请求参数
        pageSize: 10,
        pageIndex: 1,
        nickname: '',
        mobile: '',
        register_begin_date: '',
        register_end_date: ''
      },
      listCurrent: 1, // 当前页码
      addAcountModalData: {
        modal: false
      },
      modalData: {
        modal: false,
        data: undefined
      }
    }
  },
  created () {
    this.getList()
  },
  methods: {
    // 获取平台用户列表
    getList () {
      this.loading = true
      this.selectAll = false
      API.queryAccountPage(this.listQuery).then(response => {
        if (response.data.Code === 10000) {
          this.list = response.data.Result
          this.total = response.data.Total
        } else {
          this.$Message.error(response.data.Message)
        }
        setTimeout(() => {
          this.loading = false
        }, 1 * 800)
      }).catch(
        setTimeout(() => {
          this.loading = false
        }, 1 * 800)
      )
    },
    // 重置密码
    resetPassword (row) {
      this.$Modal.confirm({
        title: '重置密码',
        content: '<p>确认后会将账号' + row.mobile + '密码重置为hzx123456</p>',
        loading: true,
        onOk: () => {
          API.updatePwd({ id: row.id }).then(res => {
            if (res.data.Code === 10000) {
              this.$Message.success(res.data.Message)
              this.getList()
              this.$Modal.remove()
              this.loading = false
            } else {
              this.$Message.error(res.data.Message)
              this.$Modal.remove()
              this.loading = false
            }
          })
        }
      })
    },
    // 新增用户
    handleCreate () {
      this.addAcountModalData = {
        modal: true
      }
    },
    // 查看
    handleUpdate (row) {
      this.modalData = {
        modal: true,
        data: row
      }
    },
    // 删除
    handleDelete (row, type) {
      if (row.length < 1) {
        this.$Message.warning('请至少选中一条用户数据！')
        return
      }
      this.$Modal.confirm({
        title: '删除账号',
        content: type === '2' ? '<p>确认后会将账号' + row.mobile + '删除</p>' : '<p>确定删除选中项？</p>',
        loading: true,
        onOk: () => {
          let data = ''
          if (type === '1') {
            let listIds = row.map(item => {
              return item.id
            })
            data = listIds.join(',')
          } else {
            data = row.id
          }
          API.queryDeletePass({ ids: data }).then(res => {
            if (res.data.Code === 10000) {
              this.$Message.success(res.data.Message)
              this.getList()
              this.$Modal.remove()
              this.loading = false
            } else {
              this.$Message.error(res.data.Message)
              this.$Modal.remove()
              this.loading = false
            }
          })
        }
      })
    },
    // 全选
    handleSelectAll (status) {
      this.$refs.selection.selectAll(status)
    },
    // 取消勾选
    tableSelectCancel (selection, row) {
      this.selectAll = false
      this.selectionData = selection
    },
    // 选中勾选触发
    tableSelectAll (selection, row) {
      if (selection.length === this.list.length) this.selectAll = true
      this.selectionData = selection
    },
    // 查询
    searchResults (e) {
      if (e.target) delete e.target
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      Object.assign(this.listQuery, e)
      this.listQuery.register_begin_date = this.register_begin_date
      this.listQuery.register_end_date = this.register_end_date
      this.getList()
    },
    selectOnChanged (e) {
      if (e.flag === 'date_start') {
        this.register_begin_date = e.key
      } else if (e.flag === 'date_end') {
        this.register_end_date = e.key
      }
    },
    // 重置查询条件
    resetResults () {
      this.listQuery = { // 列表请求参数
        pageSize: 10,
        pageIndex: 1,
        nickname: '',
        mobile: '',
        register_begin_date: '',
        register_end_date: ''
      }
      this.listCurrent = 1
      this.setSearchData.full_name.value = ''
      this.setSearchData.mobile.value = ''
      this.setSearchData.register_begin_date.selected = ''
      this.setSearchData.register_end_date.selected = ''
      this.getList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    }
  }
}
</script>
<style lang="less">
  .extra {
    float: right;
    margin-top: 5px;
  }
  .ivu-table-wrapper {
    clear: both;
  }
  .select_all {
    margin: 15px 0 0 19px;
    button {
      color: white;
      border-color: #2d8cf0;
      background-color: #2d8cf0;
      margin-left: 10px;
    }
  }
</style>
