<template>
  <div style="padding: 20px; background: #fff;">
    <search @searchResults='searchResults' :setSearch='setSearchData' @keydown.enter.native='searchResults' @resetResults='resetResults'></search>
    <Row class="datalisthead">
      <Col span="6" style="text-align: left;">航次信息</Col>
      <Col span="10" style="text-align: left;">航线信息</Col>
      <Col span="5" style="text-align: left; padding-left: 20px;">功能</Col>
      <Col span="3" style="text-align: left;">操作</Col>
    </Row>
    <div v-for="(item, idx) in list" :key="idx" @click="handleDetail(item)">
      <Row class="voyage-area" justify="center" align="middle">
        <Col span="6">
          <div>{{ item.ship_name }} {{ item.voyage_no }}</div>
          <div v-for="(item, idx) in getGoods(item.cargoResult)" :key="idx" class="cargo_result">
            <div>{{ item }}</div>
          </div>
        </Col>
        <Col span="10" style="text-align: left;">
          <div>
            <Icon type="md-disc" color="#007DFF"/>
            {{ getPort(item.portResult, '1') }}
          </div>
          <div style="margin: 5px 5px;"><img :src="voyagePath" alt=""></div>
          <div>
            <Icon type="md-pin" color="#007DFF"/>
            {{ getPort(item.portResult, '2') }}
          </div>
        </Col>
        <Col span="5">
          <div v-for="(item, idx) in getGoodsNo(item.cargoResult)" :key="idx">
            <!-- <span>{{ item }}</span> -->
            <button :data-clipboard-text="item" @click="copyText(item)" class="copy_btn">复制订单号</button>
          </div>
        </Col>
        <Col span="3">
          <Tooltip placement="left" theme="light" :transfer="false">
            <Tag>...</Tag>
            <div slot="content">
              <div v-if="parseInt(item.status) !== 0 && parseInt(item.send_num) === 0 "><Button class="btn-area" type="text" @click.stop="sendVoyage(item)">发送</Button></div>
              <div v-if="parseInt(item.status) !== 0 && parseInt(item.send_num) !== 0 "><Button class="btn-area" type="text" @click.stop="sendAgainVoyage(item)">再次发送</Button></div>
              <div><Button class="btn-area" type="text" @click.stop="modifyVoyage(item)">修改</Button></div>
              <div><Button class="btn-area" type="text" @click.stop="delVoyage(item)">删除</Button></div>
              <div v-if="parseInt(item.send_num) != 0 "><Button class="btn-area" type="text" @click.stop="voyageHistory(item)">推送历史</Button></div>
              <div><Button class="btn-area" type="text" @click.stop="orderOutput(item)">航次命令</Button></div>
            </div>
          </Tooltip>
          
        </Col>
      </Row>
      <Spin fix v-if="spinShow">
        <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
        <div>Loading</div>
      </Spin>
    </div>
    <div v-if="list.length === 0"><Row class="no-list" justify="center" align="middle">暂无列表数据</Row></div>
    <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="pageSize"
          :current.sync="pageCur" :total="total" prev-text="< 上一页" next-text="下一页 >"
          @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
    <!-- 推送历史弹窗 -->
    <pushHistoryDrawer ref="pushHistoryDrawer"></pushHistoryDrawer>
  </div>
</template>
<script>
import search from '_c/search' // 查询组件
import voyagePath from '@/assets/images/voyage-path.png'
import API from '@/api/voyageManage/planVoyage'
import { previewVoyageWord } from '@/api/voyageManage/curVoyage'
import pushHistoryDrawer from '@/view/berthingPlan/pushHistoryDrawer'
import Clipboard from 'clipboard'

export default {
  props: {
    reset: Boolean
  },
  components: {
    search,
    pushHistoryDrawer
  },
  data () {
    return {
      spinShow: false, // loading
      isCopy: false, // 正在复制
      pageCur: 1, // 当前页
      pageSize: 5, // 每页个数
      total: 0, // 总页数
      queryParam: {
        pageSize: 5,
        pageIndex: 1
      },
      voyagePath,
      setSearchData: {// 查询设置，对象key值为回调参数
        ship_id: {
          type: 'select',
          label: '船名',
          selectData: [],
          selected: '',
          placeholder: '请选择船名',
          flag: 'ship_id',
          selectName: '',
          width: 210,
          value: ''
        }
      },
      list: []
    }
  },
  created () {
    if (window.localStorage.bussiShipList) {
      let shipList = JSON.parse(window.localStorage.bussiShipList)
      shipList.map(item => {
        this.setSearchData.ship_id.selectData.push({
          value: item.ship_id,
          label: item.ship_name
        })
      })
    } else {
      this.setSearchData.ship_id.selectData = []
    }
    this.getList()
  },
  computed: {
    getPort () { // 港口,码头解析
      return function (list, type) {
        if (!list || list.length <= 0) return ''
        // 按类型提取需要的港头数据列表,type = 1为装港, type = 2为卸港
        let curList = list.filter(item => item.port_type === type)
        let portList = curList.map(d => {
          return d.wharf_name === '' ? d.port_name : d.port_name + '/' + d.wharf_name
        })
        return portList.join(' ')
      }
    },
    getGoods () { // 货品,货量解析
      return function (list) {
        if (!list || list.length <= 0) return []
        let curList = list.map(item => {
          return item.goods_name + ' - ' + item.amounts
        })
        return curList
      }
    },
    getGoodsNo () { // 货品编号解析
      return function (list) {
        if (!list || list.length <= 0) return []
        let curList = list.map(item => {
          return item.cargo_no
        })
        return curList
      }
    }
  },
  methods: {
    // 获取列表
    getList () {
      this.spinShow = true
      API.queryVoyagePlanPage(this.queryParam).then(res => {
        this.spinShow = false
        if (res.data.Code === 10000) {
          this.list = res.data.Result
          this.total = res.data.Total
        }
      })
    },
    // 发送计划航次
    sendVoyage (row) {
      API.sendVoyage({ id: row.id }).then(res => {
        if (res.data.Code === 10000) {
          this.$Message.success(res.data.Message)
          this.getList()
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 再次发送计划航次
    sendAgainVoyage (row) {
      API.sendAgainVoyage({ id: row.id }).then(res => {
        if (res.data.Code === 10000) {
          this.$Message.success(res.data.Message)
          this.getList()
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 航次命令
    orderOutput (item) {
      previewVoyageWord({ id: item.id }).then(res => {
        if (res.data.Code === 10000) {
          sessionStorage.setItem('wpsUrl', res.data.wpsUrl)
          sessionStorage.setItem('token', res.data.token)
          const jump = this.$router.resolve({ name: 'viewFile' })
          window.open(jump.href, '_blank')
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 航次推送历史
    voyageHistory (item) {
      this.$refs.pushHistoryDrawer.formModal = true
      this.$refs.pushHistoryDrawer.title = '航次推送历史'
      this.$refs.pushHistoryDrawer.listQuery.key_type = 1
      this.$refs.pushHistoryDrawer.listQuery.key_id = item.id
    },
    // 修改计划航次
    modifyVoyage (row) {
      Object.assign(row, {
        type: 'modify'
      })
      this.$emit('planModify', row)
    },
    // 删除计划航次
    delVoyage (row) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确定删除该条记录？</p>',
        loading: true,
        onOk: () => {
          API.deleteVoyagePlan({ id: row.id }).then(res => {
            this.$Modal.remove()
            if (res.data.Code === 10000) {
              this.$Message.success(res.data.Message)
              this.getList()
            } else {
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
    },
    // 复制文本
    copyText (data) {
      this.isCopy = true
      let clipboard = new Clipboard('.copy_btn', {
        text: function () {
          return data
        }
      })
      clipboard.on('success', e => {
        this.$Message.success('复制单号成功！')
        clipboard.destroy()
        this.isCopy = false
      })
      clipboard.on('error', e => {
        this.$Message.error('该浏览器不支持自动复制')
        clipboard.destroy()
        this.isCopy = false
      })
    },
    // 航次详情
    handleDetail (row) {
      if (this.isCopy) return
      localStorage.setItem('detailType', 'planVoyage')
      localStorage.setItem('voyageObj', JSON.stringify(row))
      this.$router.push({
        name: 'voyageDetail'
      })
    },
    // 查询
    searchResults (e) {
      if (e.target) delete e.target
      this.pageCur = 1
      this.queryParam.pageIndex = 1
      Object.assign(this.queryParam, e)
      this.getList()
    },
    // 重置
    resetResults () {
      this.pageCur = 1
      this.queryParam = Object.assign(this.queryParam, { // 列表请求参数
        pageIndex: 1,
        ship_id: ''
      })
      this.setSearchData.ship_id.selected = ''
      this.getList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.queryParam.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.queryParam.pageIndex = val
      this.getList()
    }
  },
  watch: {
    reset () {
      this.getList()
    }
  }
}
</script>

<style lang="less">
  .voyage-area {
    cursor: pointer;
    &:hover {
      background: #ebf7ff;
    }
    display: flex;
    min-height: 112px;
    justify-content: center;
    justify-items: center;
    align-items: center;
    padding: 16px 20px;
    background:#fff;
    border-radius:4px;
    border:1px solid #D9D9D9;
    margin-top: 20px;
    color: #333;
    font-size: 14px;
    font-weight: 600;
    text-align: left;
    .cargo_result {
      font-weight: normal;
      margin-top: 10px;
    }
  }
  .btn-area {
    color: #007DFF;
  }
</style>
