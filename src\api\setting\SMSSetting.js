import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 开通短信通知
export function addSMSNotice (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/smsconfig/shipowner/config/addBatchMsgShipownerConfig',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取短信节点配置列表
export function queryNodeList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/smsconfig/shipowner/config/queryMsgShipownerConfigPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取节点模板内容
export function querySingleNode (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/smsconfig/template/detail/queryMsgTemplateDetailList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 保存短信模板配置内容
export function saveSingleNode (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/smsconfig/shipowner/config/updateMsgShipownerConfig',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  addSMSNotice,
  queryNodeList,
  querySingleNode,
  saveSingleNode
}
