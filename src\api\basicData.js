import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 查询船舶列表 带分页
export function queryBasicShipPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/ship/queryBasicShipPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询船舶列表 无分页
export function queryBasicShipList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/ship/queryBasicShipList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取指定船舶配置信息
export function queryBasicShipConfigs (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/ship/queryBasicShipConfigs',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 船舶配置 船舶排期时使用（新增修改统一用此接口）
export function updateBasicShipConfigs (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/ship/updateBasicShipConfigs',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export function queryUsCompanyList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/us/company/queryUsCompanyList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 用户列表(无分页)
export function queryCustomerList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/customer/company/queryBasicCustomerCompanyList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 用户列表(有分页)
export function queryCustomerPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/customer/company/queryBasicCustomerCompanyPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 港口列表(无分页)
export function queryPortList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/port/record/queryBasicPortList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 港口列表(有分页)
export function queryPortPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/port/record/queryBasicPortPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 码头列表(无分页)
export function queryWharfList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/wharf/record/queryBasicWharfList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 码头列表(有分页)
export function queryWharfPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/wharf/record/queryBasicWharfPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 泊位列表(无分页)
export function queryBerthList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/berth/record/queryBasicBerthList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 泊位列表(有分页)
export function queryBerthPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/berth/record/queryBasicBerthPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 货品列表(有分页)
export function queryBasicCargoPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/cargo/record/queryBasicCargoPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 货品列表(无分页)
export function queryBasicCargoList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/cargo/record/queryBasicCargoList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取成员信息列表 分页
export function queryCustomerMemberPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/customer/member/queryBasicCustomerMemberPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取成员信息列表 下拉
export function queryCustomerMemberList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/customer/member/queryBasicCustomerMemberList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取潮汐数据
export function queryTideList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/tide/info/queryTideInfo',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 系统时间
export function querySysDate (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/cargo/record/getSystemDate',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 新增时间区间设定数据
export function addStatTime (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/stat/single/addStatTime',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取时间区间设定数据
export function queryStatTime (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/stat/single/queryStatTime',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 执行航次查询无分页
export function queryVoyageList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/record/execute/queryVoyageList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 上传头像
export function avatarImage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/attachment/image',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 节点状态
export function queryNodeStatusList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/node/status/queryBasicNodeStatusList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 动态节点
export function queryNodeList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/node/queryBasicNodeList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询字典信息
export function dictEntryList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/smsconfig/node/config/getCustomerDictEntryList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}
