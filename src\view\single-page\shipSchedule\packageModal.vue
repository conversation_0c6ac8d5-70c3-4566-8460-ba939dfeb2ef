<template>
  <Modal
    v-model="modalData.modal"
    :data="modalData.data"
    :title="modalData.title"
    :width="1300"
    :mask-closable="false"
    @on-visible-change="visibleChange">
    <Table border stripe :columns="columns"
            row-key="id"
            :data="cargoList">
    </Table>
  </Modal>
</template>
<script>
import API from '@/api/shipSchedule'

export default ({
  props: {
    modalData: Object
  },
  data () {
    return {
      columns: [
        { title: '归属月份', key: 'belong_month', align: 'center'},
        { title: '船名', key: 'ship_name', align: 'center'},
        { title: '航次号', key: 'voyage_no', align: 'center'},
        // { title: '货主', key: 'shipper_name', align: 'center' },
        // { title: '货品', key: 'goods_name', align: 'center' },
        { title: '货量', key: 'amounts', align: 'center' },
        { title: '受载开始', key: 'start_plan_date', align: 'center' },
        { title: '受载结束', key: 'end_plan_date', align: 'center' },
        { title: '航次开始', key: 'empty_sail_start_day', align: 'center' },
        { title: '航次结束', key: 'estimated_over_day', align: 'center' },
        { title: '运价(元/吨)', key: 'freight_rate', align: 'center' },
        { title: '运费(元)', key: 'shipping_fee', align: 'center' },
        { title: '装港', key: 'load_port_name', align: 'center' },
        { title: '卸港', key: 'unload_port_name', align: 'center' }
      ],
      cargoList: []
    }
  },
  methods: {
    getCargoList() {
      API.queryVoyageMonthPlanList({ source_id: this.modalData.voyage_guarantee_plan_id, belong_month: this.modalData.belong_month }).then(res => {
        if(res.data.Code === 10000) {
          this.cargoList = res.data.Result
        } else {
          this.$Message.warning(res.data.Message)
        }
      })
    },
    visibleChange(val) {
      if(val) {
        this.getCargoList()
      }
    }
  }
})
</script>
