import {
  login,
  phoneLogin,
  superLogin,
  logout,
  getMessage,
  getContentByMsgId,
  hasRead,
  removeReaded,
  restoreTrash
} from '@/api/user'
import { setToken, getToken, getFullName, setAccess, setMenuShow, getAccess, setShipCompanyId, setCompanyName, getShipCompanyId, setFullName } from '@/libs/util'

export default {
  state: {
    full_name: getFullName(),
    userId: '',
    avatarImgPath: '',
    token: getToken(),
    conpanyId: getShipCompanyId(),
    canModify: true,
    access: getAccess(),
    menuShow: false,
    hasGetInfo: false,
    unreadCount: 0,
    messageUnreadList: [],
    messageReadedList: [],
    messageTrashList: [],
    messageContentStore: {}
  },
  mutations: {
    setAvatar (state, avatarPath) {
      state.avatarImgPath = avatarPath
    },
    setUserId (state, id) {
      state.userId = id
    },
    setUserName (state, name) {
      state.userName = name
    },
    setAccess (state, access) {
      state.access = access
      setAccess(access)
    },
    setMenuShow (state, menuShow) {
      state.menuShow = menuShow
      setMenuShow(menuShow)
    },
    setModify (state, modify) {
      state.canModify = modify
    },
    setToken (state, token) {
      state.token = token
      setToken(token)
    },
    setShipCompanyId (state, ship_company_id) {
      state.ship_company_id = ship_company_id
      setShipCompanyId(ship_company_id)
    },
    setCompanyName (state, company_name) {
      state.company_name = company_name
      setCompanyName(company_name)
    },
    setFullName (state, full_name) {
      state.full_name = full_name
      setFullName(full_name)
    },
    setHasGetInfo (state, status) {
      state.hasGetInfo = status
    },
    setMessageCount (state, count) {
      state.unreadCount = count
    },
    setMessageUnreadList (state, list) {
      state.messageUnreadList = list
    },
    setMessageReadedList (state, list) {
      state.messageReadedList = list
    },
    setMessageTrashList (state, list) {
      state.messageTrashList = list
    },
    updateMessageContentStore (state, { msg_id, content }) {
      state.messageContentStore[msg_id] = content
    },
    moveMsg (state, { from, to, msg_id }) {
      const index = state[from].findIndex(_ => _.msg_id === msg_id)
      const msgItem = state[from].splice(index, 1)[0]
      msgItem.loading = false
      state[to].unshift(msgItem)
    }
  },
  getters: {
    messageUnreadCount: state => state.messageUnreadList.length,
    messageReadedCount: state => state.messageReadedList.length,
    messageTrashCount: state => state.messageTrashList.length
  },
  actions: {
    // 登录
    handleLogin ({ commit }, { userName, password }) {
      userName = userName.trim()
      return new Promise((resolve, reject) => {
        login({
          userName,
          password
        }).then(res => {
          const data = res.data
          if (data.Code === 10000) {
            commit('setToken', data.Token)
            if (data.Role !== 1) {
              commit('setShipCompanyId', data.UserCompanyData[0].company_id)
              commit('setCompanyName', data.UserCompanyData[0].company_name)
              window.localStorage.userDataId = data.UserData.id // 取消***********账号所有操作权限
              window.localStorage.companyList = JSON.stringify(data.UserCompanyData)
              window.localStorage.shipNameList = data.UserCompanyData.length > 0 ? JSON.stringify(data.UserCompanyData[0].companyShipData) : ''
              window.localStorage.bussiShipList = data.UserCompanyData.length > 0 ? JSON.stringify(data.UserCompanyData[0].businessShipData) : ''
              let usrAccessList = ['vistor'] // vistor 浏览者权限，business 商务权限，companyAdmin 公司管理员权限
              if (data.UserCompanyData[0].company_user_role === 2) { // 公司管理员权限
                usrAccessList.push('companyAdmin')
              }
              if (data.UserCompanyData[0].company_user_type === 4 || data.UserData.id === '893') { // 商务权限 893秋月账号特殊处理  2025/02/24
                usrAccessList.push('business')
              }
              commit('setAccess', usrAccessList)
            } else {}
            commit('setFullName', data.UserData.user_name)
            commit('setHasGetInfo', true)
          }
          resolve(data)
        }).catch(err => {
          reject(err)
        })
      })
    },
    // 手机登录
    phoneLogin ({ commit }, { userName }) {
      userName = userName.trim()
      return new Promise((resolve, reject) => {
        phoneLogin({
          userName
        }).then(res => {
          const data = res.data
          if (data.Code === 10000) {
            commit('setToken', data.Token)
            if (data.Role !== 1) {
              commit('setShipCompanyId', data.UserCompanyData[0].company_id)
              commit('setCompanyName', data.UserCompanyData[0].company_name)
              window.localStorage.userDataId = data.UserData.id
              window.localStorage.companyList = JSON.stringify(data.UserCompanyData)
              window.localStorage.shipNameList = data.UserCompanyData.length > 0 ? JSON.stringify(data.UserCompanyData[0].companyShipData) : ''
              window.localStorage.bussiShipList = data.UserCompanyData.length > 0 ? JSON.stringify(data.UserCompanyData[0].businessShipData) : ''
              let usrAccessList = ['vistor'] // vistor 浏览者权限，business 商务权限，companyAdmin 公司管理员权限
              if (data.UserCompanyData[0].company_user_role === 2) { // 公司管理员权限
                usrAccessList.push('companyAdmin')
              }
              if (data.UserCompanyData[0].company_user_type === 4 || data.UserData.id === '893') { // 商务权限 893秋月账号特殊处理  2025/02/24
                usrAccessList.push('business')
              }
              commit('setAccess', usrAccessList)
              commit('setMenuShow', false)
            } else {}
            commit('setFullName', data.UserData.user_name)
            commit('setHasGetInfo', true)
          }
          resolve(data)
        }).catch(err => {
          reject(err)
        })
      })
    },
    // 超级管理登录
    handleSuperLogin ({ commit }, { userName, password }) {
      userName = userName.trim()
      return new Promise((resolve, reject) => {
        superLogin({
          userName,
          password
        }).then(res => {
          const data = res.data
          if (data.Code === 10000) {
            commit('setToken', data.Token)
            commit('setFullName', data.UserData.user_name)
            commit('setHasGetInfo', true)
            if (data.UserData.user_type && data.UserData.user_type === '3') {
              commit('setAccess', 'super_set') // 超级管理员账号***********权限
            } else {
              commit('setAccess', 'super_admin') // 超级管理员权限
            }
          }
          resolve(data)
        }).catch(err => {
          reject(err)
        })
      })
    },
    // 退出登录
    handleLogOut ({ state, commit }) {
      return new Promise((resolve, reject) => {
        logout(state.token).then(() => {
          commit('setToken', '')
          commit('setShipCompanyId', '')
          commit('setCompanyName', '')
          commit('setAccess', [])
          commit('setFullName', '')
          commit('setMenuShow', true)
          window.localStorage.clear()
          resolve()
        }).catch(err => {
          reject(err)
        })
      })
    },
    // 此方法用来获取未读消息条数，接口只返回数值，不返回消息列表
    getUnreadMessageCount ({ state, commit }) {
      // getUnreadCount().then(res => {
      //   const { data } = res
      //   commit('setMessageCount', data)
      // })
    },
    // 获取消息列表，其中包含未读、已读、回收站三个列表
    getMessageList ({ state, commit }) {
      return new Promise((resolve, reject) => {
        getMessage().then(res => {
          const { unread, readed, trash } = res.data
          commit('setMessageUnreadList', unread.sort((a, b) => new Date(b.create_time) - new Date(a.create_time)))
          commit('setMessageReadedList', readed.map(_ => {
            _.loading = false
            return _
          }).sort((a, b) => new Date(b.create_time) - new Date(a.create_time)))
          commit('setMessageTrashList', trash.map(_ => {
            _.loading = false
            return _
          }).sort((a, b) => new Date(b.create_time) - new Date(a.create_time)))
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 根据当前点击的消息的id获取内容
    getContentByMsgId ({ state, commit }, { msg_id }) {
      return new Promise((resolve, reject) => {
        let contentItem = state.messageContentStore[msg_id]
        if (contentItem) {
          resolve(contentItem)
        } else {
          getContentByMsgId(msg_id).then(res => {
            const content = res.data
            commit('updateMessageContentStore', { msg_id, content })
            resolve(content)
          })
        }
      })
    },
    // 把一个未读消息标记为已读
    hasRead ({ state, commit }, { msg_id }) {
      return new Promise((resolve, reject) => {
        hasRead(msg_id).then(() => {
          commit('moveMsg', {
            from: 'messageUnreadList',
            to: 'messageReadedList',
            msg_id
          })
          commit('setMessageCount', state.unreadCount - 1)
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 删除一个已读消息到回收站
    removeReaded ({ commit }, { msg_id }) {
      return new Promise((resolve, reject) => {
        removeReaded(msg_id).then(() => {
          commit('moveMsg', {
            from: 'messageReadedList',
            to: 'messageTrashList',
            msg_id
          })
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 还原一个已删除消息到已读消息
    restoreTrash ({ commit }, { msg_id }) {
      return new Promise((resolve, reject) => {
        restoreTrash(msg_id).then(() => {
          commit('moveMsg', {
            from: 'messageTrashList',
            to: 'messageReadedList',
            msg_id
          })
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    }
  }
}
