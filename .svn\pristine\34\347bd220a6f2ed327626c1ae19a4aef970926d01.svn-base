<template>
  <div>
    <Card>
      <search @searchResults='searchResults' @selectOnChanged='selectOnChanged' :setSearch='setSearchData' @keydown.enter.native='searchResults' @resetResults='resetResults'></search>
    </Card>
    <Card style="margin-top:10px;padding-top:6px;">
      <p slot='title' class="bold-font">公司管理</p>
      <div class="extra" slot="extra">
        <Button type="primary" icon="md-add-circle" @click="handleCreate">公司入驻</Button>
        <formAction :setFormAction='setFormAction' @handleUpdateTable="getList" style="display: inline-block;"></formAction>
      </div>
      <div>
        <Table
          border
          :loading="loading"
          :columns="columns"
          :data="list"
          ref="selection"
          @on-select-all="tableSelectAll"
          @on-select-all-cancel="tableSelectCancel"
          @on-select="tableSelectAll"
          @on-select-cancel="tableSelectCancel"></Table>
        <div class="select_all">
          <Checkbox v-model="selectAll" @on-change="handleSelectAll(selectAll)">全选</Checkbox>
          <Button @click="handleDelete(selectionData, '1')">删除</Button>
        </div>
        <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
          :total="total" prev-text="< 上一页" next-text="下一页 >"
          @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
      </div>
    </Card>
    <addCompanyModal :modalData="addCompanyModalData" @callback="getList"></addCompanyModal>
    <updateCompanyModal :modalData="modalData" @callback="getList"></updateCompanyModal>
  </div>
</template>

<script>
import search from '_c/search' // 查询组件
import formAction from '_c/formAction' // 表单操作组件
import API from '@/api/companyManagement'
import addCompanyModal from './addCompanyModal'
import updateCompanyModal from './updateCompanyModal'

export default {
  components: {
    search,
    formAction,
    addCompanyModal,
    updateCompanyModal
  },
  data () {
    return {
      selectAll: false,
      selectionData: [], // 存储已选中数据
      setSearchData: { // 查询设置，对象key值为回调参数
        name: {
          type: 'text',
          label: '企业名称',
          value: '',
          width: 150
        },
        company_type: {
          type: 'select',
          label: '企业类型',
          selectData: [],
          selected: '',
          filterable: true,
          placeholder: '请选择',
          selectName: '',
          width: 150,
          value: ''
        },
        insert_time_begin: {
          type: 'date',
          label: '注册时间',
          selected: '',
          width: 130,
          value: '',
          isdisable: false
        },
        insert_time_end: {
          type: 'date_end',
          label: '-',
          selected: '',
          width: 130,
          value: '',
          isdisabled: false
        }
      },
      setFormAction: {
        operation: ['updateTable']
      },
      loading: false, // 表单列表loding状态
      columns: [
        {
          type: 'selection',
          width: 60,
          align: 'center'
        },
        {
          title: '企业名称',
          key: 'name',
          align: 'center'
          // minWidth: 150
        },
        {
          title: '企业类型',
          key: 'company_type_name',
          align: 'center'
          // width: 70
        },
        // {
        //   title: '创建人',
        //   key: 'insert_user_name',
        //   align: 'center',
        //   minWidth: 100
        // },
        // {
        //   title: '创建时间',
        //   key: 'insert_time',
        //   align: 'center',
        //   width: 170
        // },
        {
          title: '管理员',
          key: 'full_name',
          align: 'center'
          // minWidth: 100
        },
        {
          title: '联系方式',
          key: 'insert_user_mobile',
          align: 'center'
          // width: 130
        },
        {
          title: '入驻状态',
          key: 'status_name',
          align: 'center'
          // width: 100
        },
        {
          title: '航次动态PC权限',
          key: 'is_open',
          align: 'center',
          // width: 90,
          render: (h, params) => {
            return h('i-switch', {
              props: {
                value: params.row.is_open === '1', // 1开启，0关闭
                disabled: params.row.company_type !== '1'
              },
              on: {
                'on-change': () => {
                  params.row.is_open = params.row.is_open === '0' ? '1' : '0'
                  this.changeIsopen(params.row)
                }
              }
            })
          }
        },
        {
          title: '操作',
          key: 'operation',
          align: 'center',
          width: 290,
          // minWidth: 150,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                style: {
                  margin: '4px'
                },
                props: {
                  icon: 'md-paper',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleUpdate(params.row)
                  }
                }
              }, '查看'),
              h('Button', {
                style: {
                  margin: '4px'
                },
                props: {
                  icon: 'md-trash',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleDelete(params.row, '2')
                  }
                }
              }, '注销'),
              h('Button', {
                style: {
                  margin: '4px',
                  display: params.row.company_type === '1' && params.row.status === '10' && params.row.need_sync === '1' ? 'inline-block' : 'none' // 1.未同步过，0.同步过
                },
                props: {
                  icon: 'md-settings',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleAdminConfig(params.row)
                  }
                }
              }, '同步管理员')
            ])
          }
        }
      ],
      list: [], // 表单列表数据
      total: null, // 列表数据条数
      listQuery: {// 列表请求参数
        pageSize: 10,
        pageIndex: 1,
        name: '',
        company_type: '',
        insert_time_begin: '',
        insert_time_end: ''
      },
      listCurrent: 1, // 当前页码
      addCompanyModalData: {
        modal: false
      },
      modalData: {
        modal: false,
        data: undefined
      }
    }
  },
  created () {
    API.queryCompanyTypeList().then(res => { // 获取公司类型
      if (res.data.Code === 10000) {
        this.setSearchData.company_type.selectData = res.data.Result.map(item => {
          return {
            value: item.id,
            label: item.company_type_name
          }
        })
      }
    })
    this.getList()
  },
  methods: {
    // 获取公司管理列表
    getList () {
      this.loading = true
      this.selectAll = false
      API.queryCompanyPage(this.listQuery).then(response => {
        if (response.data.Code === 10000) {
          this.list = response.data.Result
          this.total = response.data.Total
        } else {
          this.$Message.error(response.data.Message)
        }
        setTimeout(() => {
          this.loading = false
        }, 1 * 800)
      }).catch(
        setTimeout(() => {
          this.loading = false
        }, 1 * 800)
      )
    },
    // 权限配置
    changeIsopen (row) {
      let data = {
        ship_company_id: row.id,
        is_open: row.is_open
      }
      API.permissionConfig(data).then(res => {
        if (res.data.Code === 10000) {
          this.$Message.success(res.data.Message)
          this.getList()
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 公司入驻
    handleCreate () {
      this.addCompanyModalData = {
        modal: true
      }
    },
    // 查看
    handleUpdate (row) {
      this.modalData = {
        modal: true,
        data: row
      }
    },
    // 注销
    handleDelete (row, type) {
      if (row.length < 1) {
        this.$Message.warning('请至少选中一条公司数据！')
        return
      }
      this.$Modal.confirm({
        title: '提示',
        content: type === '2' ? '<p>确认后会将企业' + row.name + '注销</p>' : '<p>确定注销选中项？</p>',
        loading: true,
        onOk: () => {
          let data = ''
          if (type === '1') {
            let delIds = row.map(item => {
              return item.id
            })
            data = delIds.join(',')
          } else {
            data = row.id
          }
          API.delectCompany({ ids: data }).then(res => {
            if (res.data.Code === 10000) {
              this.$Message.success(res.data.Message)
              this.getList()
              this.$Modal.remove()
              this.loading = false
            } else {
              this.$Message.error(res.data.Message)
              this.$Modal.remove()
              this.loading = false
            }
          })
        }
      })
    },
    // 同步管理员
    handleAdminConfig (row) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>是否确认同步管理员？</p>',
        loading: true,
        onOk: () => {
          let data = {
            user_id: row.passport_id,
            user_type: '2', // 1:超级管理员 2：公司管理员。此处默认为2
            ship_company_id: row.id
          }
          API.adminConfig(data).then(res => {
            if (res.data.Code === 10000) {
              this.$Message.success(res.data.Message)
              this.getList()
              this.$Modal.remove()
              this.loading = false
            } else {
              this.$Message.error(res.data.Message)
              this.$Modal.remove()
              this.loading = false
            }
          })
        }
      })
    },
    // 全选
    handleSelectAll (status) {
      this.$refs.selection.selectAll(status)
    },
    // 取消勾选
    tableSelectCancel (selection, row) {
      this.selectAll = false
      this.selectionData = selection
    },
    // 选中勾选触发
    tableSelectAll (selection, row) {
      if (selection.length === this.list.length) this.selectAll = true
      this.selectionData = selection
    },
    // 查询
    searchResults (e) {
      if (e.target) delete e.target
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      Object.assign(this.listQuery, e)
      this.listQuery.insert_time_begin = this.insert_time_begin
      this.listQuery.insert_time_end = this.insert_time_end
      this.getList()
    },
    selectOnChanged (e) {
      if (e.flag === 'date_start') {
        this.insert_time_begin = e.key
      } else if (e.flag === 'date_end') {
        this.insert_time_end = e.key
      }
    },
    // 重置查询条件
    resetResults () {
      this.listQuery = { // 列表请求参数
        pageSize: 10,
        pageIndex: 1,
        name: '',
        company_type: '',
        insert_time_begin: '',
        insert_time_end: ''
      }
      this.listCurrent = 1
      this.setSearchData.name.value = ''
      this.setSearchData.company_type.selected = ''
      this.setSearchData.insert_time_begin.selected = ''
      this.setSearchData.insert_time_end.selected = ''
      this.getList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    }
  }
}
</script>
<style lang="less">
  .extra {
    float: right;
    margin-top: 5px;
  }
  .ivu-table-wrapper {
    clear: both;
  }
  .select_all {
    margin: 15px 0 0 19px;
    button {
      color: white;
      border-color: #2d8cf0;
      background-color: #2d8cf0;
      margin-left: 10px;
    }
  }
</style>
