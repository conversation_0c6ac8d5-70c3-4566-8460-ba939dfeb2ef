<template>
  <div>
    <Drawer
      v-model="drawerData.modal"
      :title="title"
      :width="900"
      :mask-closable="true"
      @on-visible-change="visibleChange">
      <Table border :loading="listLoading" ref="selection" :columns="columns" :data="dataList"></Table>
      <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="queryParam.pageSize" :current.sync="queryParam.pageIndex"
      :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
    </Drawer>
  </div>
</template>
<script>
import { queryEfficiencyReportDetail } from '@/api/statistics/operationEfficiency'

export default {
  props: {
    drawerData: Object
  },
  data () {
    return {
      title: '',
      listLoading: false,
      queryParam: {
        pageSize: 10,
        pageIndex: 1
      },
      columns: [
        {
          title: '船舶',
          key: 'ship_name',
          align: 'center'
        },
        {
          title: '航次',
          key: 'voyage_no',
          align: 'center'
        },
        {
          title: '航线',
          key: 'port_line',
          align: 'center'
        },
        {
          title: '状态',
          key: 'port_type_name',
          align: 'center'
        },
        {
          title: '作业时长',
          key: 'operation_time',
          align: 'center'
        },
        {
          title: '货量',
          key: 'unit_amount',
          align: 'center'
        },
        {
          title: '速率',
          key: 'operation_efficiency',
          align: 'center'
        }
      ],
      listCurrent: 1,
      total: 0,
      dataList: [] // 表格数据
    }
  },
  methods: {
    getList () {
      this.listLoading = true
      this.voyageRateList = []
      queryEfficiencyReportDetail(this.queryParam).then(res => {
        this.listLoading = false
        if (res.data.Code === 10000) {
          this.dataList = res.data.Result
          this.total = res.data.Total
        }
      })
    },
    // 页面跳转
    handleSizeChange (val) {
      this.queryParam.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.queryParam.pageIndex = val
      this.getList()
    },
    visibleChange (val) {
      if (val) {
        Object.assign(this.queryParam, this.drawerData.drawerParam)
        this.title = '作业效率详情 ' + this.drawerData.drawerParam.port_name + '-' + this.drawerData.drawerParam.wharf_name + '-' + this.drawerData.drawerParam.berth_name
        this.getList()
      } else {
        this.queryParam = {
          pageSize: 10,
          pageIndex: 1
        }
      }
    }
  }
}
</script>
