import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 获取船东公司人员配置列表
export function spComConfigUserPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/auth/company/user/querySpComConfigUserPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 人员姓名查询
export function spComConfigUserList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/auth/company/user/querySpComConfigUserList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 岸基权限开关
export function changeSpComConfigUserAuth (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/auth/company/user/changeSpComConfigUserShoreAuth',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}
