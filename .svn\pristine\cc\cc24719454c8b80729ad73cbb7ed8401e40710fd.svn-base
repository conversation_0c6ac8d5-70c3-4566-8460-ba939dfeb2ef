<template>
  <div>
    <canvas class="mask" ref="canvas"></canvas>
    <div style="left: 0; top: 0;">
      <portShowModal :modalData="portModal"></portShowModal>
      <wharfShowModal :modalData="wharfModal"></wharfShowModal>
      <berthShowModal :modalData="berthModal"></berthShowModal>
    </div>
  </div>
</template>

<script>
import API from '@/api/ais/mask'
import portShowModal from './modal/portShowModal'
import wharfShowModal from './modal/wharfShowModal'
import berthShowModal from './modal/berthShowModal'
import transLatlng from 'coordtransform'

export default {
  data () {
    return {
      lineOptions1: [], // 四条领海基线数据
      lineOptions2: [],
      lineOptions3: [],
      lineOptions4: [],
      portWharfShow: true,
      anchorageShow: false,
      portModal: {
        modal: false,
        name: '',
        detail: []
      },
      wharfModal: {
        modal: false,
        name: '',
        contact: '',
        detail: []
      },
      berthModal: {
        modal: false,
        name: '',
        detail: []
      },
      mapType: '1', // 地图类型
      items: [], // 存放港口码头所有数据，便于文本绘制（海图）
      items1: [], // 存放港口码头所有数据，便于文本绘制(卫星图)
      zoomLevel: 12, // 显示层级，默认当前层级
      markerLayer: null,
      detailShow: false, // 详情页面展示
      contactDetail: '', // 港口详情
      pdetailList: [
        {
          portTitle: '',
          portDetail: ''
        }
      ],
      bdetailList: [ // 码头泊位列表
        {
          name: '', // 泊位名称
          berthType: '', // 泊位类型
          berthLevel: '' // 靠泊条件
          // berthAbility: '' // 装卸能力
        }
      ],
      detailList: [ // 泊位详情列表
        {
          title: '',
          detail: ''
        }
      ], // 详情页数据
      markerPoint: [], // 标注点度坐标
      markIconList: [ // 标注图标列表
        {
          url: require('@/assets/images/port.png'),
          tip: '港口',
          type: 0
        },
        {
          url: require('@/assets/images/wharf.png'),
          tip: '码头',
          type: 1
        },
        {
          url: require('@/assets/images/berth.png'),
          tip: '泊位',
          type: 2
        },
        {
          url: require('@/assets/images/anchorage.png'),
          tip: '泊位',
          type: 3
        }
      ]
    }
  },
  components: {
    portShowModal,
    wharfShowModal,
    berthShowModal
  },
  props: {
    map: Object
  },
  watch: {
    map () {
      this.mapUpShow() // 港口码头层级展示
      this.map.on('baselayerchange', () => {
        this.removeAllLayer()
        if (this.map.mapType === 'MT_SEA') {
          this.mapType = '1'
        } else {
          this.mapType = '2'
        }
        this.draw()
      })
    }
  },
  methods: {
    mapLine () {
      // 如果有数据不再走接口
      if (this.lineOptions1.length > 0 && this.lineOptions2.length > 0 && this.lineOptions3.length > 0 && this.lineOptions4.length > 0) {
        this.allLineDraw()
        return
      }
      API.queryWatersBaselineList().then(res => {
        if (res.data.Code === 10000 && res.data.Result.length > 0) {
          res.data.Result.forEach(item => {
            if (item.area_type === '1') {
              this.lineOptions1.push([item.lng, item.lat])
            }
            if (item.area_type === '2') {
              this.lineOptions2.push([item.lng, item.lat])
            }
            if (item.area_type === '3') {
              this.lineOptions3.push([item.lng, item.lat])
            }
            if (item.area_type === '4') {
              this.lineOptions4.push([item.lng, item.lat])
            }
          })
          this.allLineDraw()
        }
      })
    },
    allLineDraw () {
      this.drawLine(this.lineOptions1)
      this.drawLine(this.lineOptions2)
      this.drawLine(this.lineOptions3)
      this.drawLine(this.lineOptions4)
    },
    // 绘制线段
    drawLine (options) {
      this.$nextTick(() => {
        // eslint-disable-next-line new-cap
        let myLayerGroupLine = new L.layerGroup()
        myLayerGroupLine.clearLayers()
        Object.assign(myLayerGroupLine, {
          type: 'baseLine'
        })
        let lineLayer = L.polyline(options, { color: '#e41120', weight: 1, dashArray: [5, 5] })
        myLayerGroupLine.addLayer(lineLayer)
        this.map.addLayer(myLayerGroupLine)
      })
    },
    mapUpShow () {
      let _that = this
      this.zoomLevel = this.map.getZoom()
      this.removeAllLayer()
      this.items = []
      this.items1 = []
      // 获取港口数据
      // API.queryOuterPortList({ map_type: 1 }).then(res => {
      //   // eslint-disable-next-line new-cap
      //   if (res.data.Code === 10000 && res.data.Result.length > 0) {
      //     let myIcon = L.icon({
      //       iconUrl: this.markIconList[0].url
      //     })
      //     res.data.Result.forEach((item, idx) => {
      //       // eslint-disable-next-line no-undef
      //       this.markerPoint = [ item.longitude, item.latitude ]
      //       this.items.push({
      //         id: item.id,
      //         type: 'port',
      //         point: this.markerPoint,
      //         icon: myIcon,
      //         name: item.port_name,
      //         zoom: item.port_level,
      //         detail: item.port_text_json
      //       })
      //     })
      //   }
      // })
      // // 获取码头数据
      // API.queryOuterWharfList({ map_type: 1 }).then(res => {
      //   if (res.data.Code === 10000 && res.data.Result.length > 0) {
      //     // eslint-disable-next-line no-undef
      //     let myIcon = L.icon({
      //       iconUrl: this.markIconList[1].url
      //     })
      //     res.data.Result.forEach((item, idx) => {
      //       this.markerPoint = [ item.longitude, item.latitude ]
      //       this.items.push({
      //         id: item.id,
      //         type: 'wharf',
      //         point: this.markerPoint,
      //         icon: myIcon,
      //         name: item.wharf_name,
      //         zoom: item.wharf_level,
      //         contact: item.phone_context,
      //         detail: item.wharf_text_json
      //       })
      //     })
      //   }
      // })
      // // 获取泊位信息
      // API.queryOuterBerthList({ map_type: 1 }).then(res => {
      //   if (res.data.Code === 10000 && res.data.Result.length > 0) {
      //     // eslint-disable-next-line no-undef
      //     let myIcon = L.icon({
      //       iconUrl: this.markIconList[2].url
      //     })
      //     res.data.Result.forEach((item, idx) => {
      //       this.markerPoint = [ item.longitude, item.latitude ]
      //       this.items.push({
      //         id: item.id,
      //         type: 'berth',
      //         point: this.markerPoint,
      //         icon: myIcon,
      //         name: item.berth_name,
      //         zoom: item.berth_level,
      //         detail: item.berth_text_json
      //       })
      //     })
      //   }
      // })
      // API.queryPiispAuchorageList({ map_type: 1 }).then(res => {
      //   if (res.data.Code === 10000 && res.data.Result.length > 0) {
      //     // eslint-disable-next-line no-undef
      //     let myIcon = L.icon({
      //       iconUrl: this.markIconList[3].url
      //     })
      //     res.data.Result.forEach((item, idx) => {
      //       this.markerPoint = [ item.longitude, item.latitude ]
      //       this.items.push({
      //         id: item.id,
      //         type: 'anchorage',
      //         point: this.markerPoint,
      //         icon: myIcon,
      //         name: item.auchorage_name,
      //         zoom: item.auchorage_level,
      //         detail: item.auchorage_text_json
      //       })
      //     })
      //   }
      // })
      _that.$nextTick(() => {
        _that.draw()
        _that.map.on('drag', (ev) => {
          _that.draw()
        })
        _that.map.on('zoom', (ev) => {
          _that.draw()
        })
      })
    },
    defaultModal () {
      this.portModal.modal = false
      this.wharfModal.modal = false
      this.berthModal.modal = false
    },
    removeLineLayer () {
      let _that = this
      this.map.eachLayer(function (layer) {
        if (layer && layer.type === 'baseLine') {
          _that.map.removeLayer(layer)
        }
      })
    },
    removeAllLayer () {
      let _that = this
      this.map.eachLayer(function (layer) {
        if (layer && layer.id && layer.type === 'anchorage') {
          _that.map.removeLayer(layer)
        }
      })
    },
    clear () {
      const canvas = this.$refs.canvas
      const ctx = canvas.getContext('2d')
      ctx.clearRect(0, 0, canvas.width, canvas.height)
    },
    // 判断是否在范围内
    isInView (center) {
      return this.map.getBounds().pad(0.1).contains(center)
    },
    // 获取四个顶点位置
    getRectPoint () {
      // let leftdown = this.map.getBounds().getSouthWest().lng + ',' + this.map.getBounds().getSouthWest().lat
      // let rightdown = this.map.getBounds().getSouthEast().lng + ',' + this.map.getBounds().getSouthEast().lat
      // let leftup = this.map.getBounds().getNorthWest().lng + ',' + this.map.getBounds().getNorthWest().lat
      // let rightup = this.map.getBounds().getNorthEast().lng + ',' + this.map.getBounds().getNorthEast().lat
      let minLng = this.map.getBounds().getSouthWest().lng // 左经度
      let maxLng = this.map.getBounds().getNorthEast().lng // 右经度
      let minLat = this.map.getBounds().getSouthWest().lat // 下纬度
      let maxLat = this.map.getBounds().getNorthEast().lat // 上纬度
      return { minLng: minLng, maxLng: maxLng, minLat: minLat, maxLat: maxLat }
    },
    isInPoint (layer, point) {
      if (layer.point[0] > (point.minLat - 1) && layer.point[0] < point.maxLat && layer.point[1] > (point.minLng - 1) && layer.point[1] < point.maxLng) return true
      return false
    },
    gcj2wgslat (lat, lng) {
      let PI = 3.14159265358979324 // 圆周率
      let a = 6378245.0 // 克拉索夫斯基椭球参数长半轴a
      let ee = 0.00669342162296594323 // 克拉索夫斯基椭球参数第一偏心率平方
      let dLat = this.transformLat(lng - 105.0, lat - 35.0)
      let radLat = lat / 180.0 * PI
      let magic = Math.sin(radLat)
      magic = 1 - ee * magic * magic
      let sqrtMagic = Math.sqrt(magic)
      dLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * PI)
      return (lat - dLat)
    },
    gcj2wgslng (lat, lng) {
      let PI = 3.14159265358979324// 圆周率
      let a = 6378245.0// 克拉索夫斯基椭球参数长半轴a
      let ee = 0.00669342162296594323// 克拉索夫斯基椭球参数第一偏心率平方
      let dLng = this.transformLon(lng - 105.0, lat - 35.0)
      let radLat = lat / 180.0 * PI
      let magic = Math.sin(radLat)
      magic = 1 - ee * magic * magic
      let sqrtMagic = Math.sqrt(magic)
      dLng = (dLng * 180.0) / (a / sqrtMagic * Math.cos(radLat) * PI)
      return (lng - dLng)
    },
    transformLon (x, y) {
      let PI = 3.14159265358979324 // 圆周率
      let ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * Math.sqrt(Math.abs(x))
      ret += (20.0 * Math.sin(6.0 * x * PI) + 20.0 * Math.sin(2.0 * x * PI)) * 2.0 / 3.0
      ret += (20.0 * Math.sin(x * PI) + 40.0 * Math.sin(x / 3.0 * PI)) * 2.0 / 3.0
      ret += (150.0 * Math.sin(x / 12.0 * PI) + 300.0 * Math.sin(x / 30.0 * PI)) * 2.0 / 3.0
      return ret
    },
    transformLat (x, y) {
      let PI = 3.14159265358979324 // 圆周率
      let ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * Math.sqrt(Math.abs(x))
      ret += (20.0 * Math.sin(6.0 * x * PI) + 20.0 * Math.sin(2.0 * x * PI)) * 2.0 / 3.0
      ret += (20.0 * Math.sin(y * PI) + 40.0 * Math.sin(y / 3.0 * PI)) * 2.0 / 3.0
      ret += (160.0 * Math.sin(y / 12.0 * PI) + 320 * Math.sin(y * PI / 30.0)) * 2.0 / 3.0
      return ret
    },
    draw () {
      this.removeAllLayer()
      let pointObj = this.getRectPoint()
      let _that = this
      this.zoomLevel = this.map.getZoom()
      // 图层显隐
      this.items.forEach(layer => {
        let _point = layer.point
        if (_that.map.mapType === 'MT_SATELLITE') {
          let transPoint = transLatlng.wgs84togcj02(parseFloat(_point[1]), parseFloat(_point[0]))
          _point = [transPoint[1], transPoint[0]]
        }
        if (layer && layer.id && layer.id !== 'baseLine') {
          if (_that.zoomLevel >= parseInt(layer.zoom) && _that.isInPoint(layer, pointObj)) {
            if (_that.portWharfShow && (layer.type === 'port' || layer.type === 'wharf' || layer.type === 'berth')) {
              let markerLayer = L.marker(_point, { icon: layer.icon, riseOnHover: true, keyboard: true }).bindTooltip(layer.name, {
                direction: 'bottom',
                offset: [6, 12],
                permanent: true,
                opacity: '1',
                className: 'div_tip'
              }).openTooltip()
              Object.assign(markerLayer, {
                id: layer.id,
                type: layer.type,
                name: layer.name,
                zoom: layer.zoom,
                contact: layer.contact ? layer.contact : '',
                detail: layer.detail
              })
              if (markerLayer.zoom <= _that.zoomLevel) {
                this.map.addLayer(markerLayer)
              }
              markerLayer.on('click', item => {
                this.$emit('hideOtherArea')
                this.defaultModal()
                if (layer.type === 'port') {
                  this.portModal = {
                    modal: true,
                    name: item.target.name,
                    detail: item.target.detail
                  }
                }
                if (layer.type === 'wharf') {
                  this.wharfModal = {
                    modal: true,
                    name: item.target.name,
                    contact: item.target.phone_context,
                    detail: item.target.detail
                  }
                }
                if (layer.type === 'berth') {
                  this.berthModal = {
                    modal: true,
                    name: item.target.name,
                    detail: item.target.detail
                  }
                }
              })
            }
            if (_that.anchorageShow && layer.type === 'anchorage') {
              let markerLayer = L.marker(_point, { icon: layer.icon, riseOnHover: true, keyboard: true }).bindTooltip(layer.name, {
                direction: 'bottom',
                offset: [6, 12],
                permanent: true,
                opacity: '1',
                className: 'div_tip'
              }).openTooltip()
              Object.assign(markerLayer, {
                id: layer.id,
                type: layer.type,
                name: layer.name,
                zoom: layer.zoom,
                detail: layer.detail
              })
              if (markerLayer.zoom <= _that.zoomLevel) {
                this.map.addLayer(markerLayer)
              }
              markerLayer.on('click', item => {
                this.$emit('hideOtherArea')
                this.defaultModal()
                this.portModal = {
                  modal: true,
                  name: item.target.name,
                  detail: item.target.detail
                }
              })
            }
          }
        }
      })
    }
  }
}
</script>

<style>
.mask {
  position: absolute;
  height: 100%;
  left: 0;
  top: 0;
  pointer-events: none;
  z-index: 10;
}
.div_tip {
  background: rgba(255, 255, 255,0.8);
  padding: 4px 5px;
  color: #333;
  font-size: 12px;
  /* font-weight: bold; */
  line-height: 14px;
  /* border: 1px solid #333; */
  z-index: 1;
}
.div_tip_none {
  display: none;
}
.leaflet-tooltip-bottom:before {
  border: none;
}
</style>
