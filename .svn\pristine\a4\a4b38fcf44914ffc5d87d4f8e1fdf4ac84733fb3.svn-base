<template>
  <div class="loader-mask" v-if="visible">
    <div class="loader-content">
      <div class="loader-spinner"></div>
      <div class="loader-message">{{ currentMessage }}</div>
      <div class="loader-progress">
        <div class="loader-progress-bar" :style="{ width: progress + '%' }"></div>
      </div>
      <div class="loader-status">
        已完成 {{ Math.round(progress) }}%
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DataLoader',
  props: {
    visible: <PERSON><PERSON><PERSON>,
    currentMessage: String,
    progress: Number
  }
}
</script>

<style scoped>
.loader-mask {
  position: fixed;
  z-index: 2000;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(20, 24, 31, 0.75); /* 更深色遮罩 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.loader-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(34, 39, 50, 0.98);
  border-radius: 12px;
  padding: 40px 48px 32px 48px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.25);
  min-width: 320px;
}

.loader-spinner {
  width: 56px;
  height: 56px;
  border: 6px solid #39c5bb33;
  border-top: 6px solid #3981c5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 24px;
}

@keyframes spin {
  0% { transform: rotate(0deg);}
  100% { transform: rotate(360deg);}
}

.loader-message {
  color: #3981c5;
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 18px;
  text-align: center;
  letter-spacing: 1px;
  text-shadow: 0 2px 8px #0004;
}

.loader-progress {
  width: 220px;
  height: 8px;
  background: #222b36;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 12px;
}

.loader-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #3981c5 0%, #3981c5 100%);
  transition: width 0.3s;
}

.loader-status {
  color: #e6edf3;
  font-size: 15px;
  margin-top: 2px;
  letter-spacing: 1px;
}
</style>