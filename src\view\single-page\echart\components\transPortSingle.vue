<!-- 货运量单船对比 -->
<template>
  <div>
    <Card>
      <div>
        时间：<DatePicker v-model="start_date" format="yyyy" @on-change="data=>queryParam.base_year=start_date=data" type="year" placeholder="请选择年度" style="width: 150px"></DatePicker>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        对比时间：<DatePicker v-model="end_date" format="yyyy"  @on-change="data=>queryParam.contrast_year=end_date=data" type="year" placeholder="请选择年度" style="width: 150px"></DatePicker>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        船名：<Select label-in-value size="small" class="select-ship-content" :transfer="false" v-model="queryParam.ship_id" placeholder="请选择船舶"
                  clearable @on-change="shipSelect">
                <Option v-for="item in shipList" :value="item.value" :key="item.value">{{ item.label }}</Option>
        </Select>
        <Button style="margin-left: 20px;" type="primary" size="small" @click="searchData">查询</Button>
        <Button style="margin-left: 10px;" size="small" @click="searchReset">重置</Button>
      </div>
      <Row>
        <Col span="14">
          <ChartLine class="box-chart" :legendShow="true" :value="lineData1" xAxisUnit="月" unit="吨" :color="lineColor" :showOnemarkLine="true" :gridRight="100" :text="`${ship_name}货运量`" />
          <ChartLine class="box-chart" :legendShow="true" :value="lineData2" xAxisUnit="月" unit="万吨公里" :color="lineColor" :showOnemarkLine="true" :gridRight="100" :text="`${ship_name}周转量`" />
          <ChartLine class="box-chart" :legendShow="true" :value="lineData3" xAxisUnit="月" unit="次" :color="lineColor" :showOnemarkLine="true" :gridRight="100" :text="`${ship_name}航次`" />
        </Col>
        <Col span="10">
          <Table class="table_box" border :columns="columns" :data="data"></Table>
        </Col>
      </Row>
    </Card>
  </div>
</template>
<script>
import { querySysDate } from '@/api/basicData'
import API from '@/api/statistics/transPortView'
import MonthSelect from '@/components/monthSelect'
import { ChartLine } from '_c/charts'

export default {
  components: {
    MonthSelect,
    ChartLine
  },
  data () {
    return {
      start_date: '',
      end_date: '',
      default_ship: '',
      shipList: [],
      ship_name: '',
      queryParam: {
        ship_id: '', // 指定船舶id(必填)
        base_year: '', // 开始年（yyyy，必填）
        contrast_year: '' // 对比年（yyyy，必填）
      },
      startPlace: 'bottom-start',
      lineColor: ['#5B8FF9', '#73DEB3'],
      lineData1: {
        // xAxis: ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'],
        xAxis: [],
        legend: ['时间', '对比时间'],
        data: [[], []],
        symbol: ['circle', 'triangle']
      },
      lineData2: {
        // xAxis: ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'],
        xAxis: [],
        legend: ['时间', '对比时间'],
        data: [[], []],
        symbol: ['circle', 'triangle']
      },
      lineData3: {
        // xAxis: ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'],
        xAxis: [],
        legend: ['时间', '对比时间'],
        data: [[], []],
        symbol: ['circle', 'triangle']
      },
      columns: [
        {
          title: '月份',
          key: 'voyage_over_month',
          align: 'center',
          width: 110
        },
        {
          title: '货运量(吨)',
          key: 'load_amount',
          align: 'center'
        },
        {
          title: '周转量(万吨公里)',
          key: 'turnover_volume_sum',
          align: 'center'
        },
        {
          title: '航次(次)',
          key: 'voyage_total',
          align: 'center',
          width: 85
        }
      ],
      data: [] // 表格数据
    }
  },
  methods: {
    getCurShipId () {
      if (window.localStorage.shipNameList) {
        let shipList = JSON.parse(window.localStorage.shipNameList)
        if (shipList.length > 0) {
          this.queryParam.ship_id = shipList[0].ship_id
          this.default_ship = this.ship_name = shipList[0].ship_name
        }
        shipList.map(item => {
          if ((item.business_model === '1' || item.business_model === '2') && !item.ship_name.includes('善')) { // 只要自营和船期船舶且不需要万邦船舶  2024/09/09调整
            this.shipList.push({
              value: item.ship_id,
              label: item.ship_name
            })
          }
        })
      } else {
        this.setSearchData.ship_id.selectData = []
      }
    },
    getSysDate () {
      querySysDate().then(res => {
        if (res.data.Code === 10000) {
          this.sysDate = res.data.systemDate
          this.queryParam.base_year = this.start_date = this.sysDate.split(' ')[0].split('-')[0]
          this.queryParam.contrast_year = this.end_date = parseInt(this.start_date) - 1 + ''
          this.getList()
        }
      })
    },
    getList () {
      this.resetData()
      this.lineData1.legend = [this.start_date, this.end_date]
      this.lineData2.legend = [this.start_date, this.end_date]
      this.lineData3.legend = [this.start_date, this.end_date]
      API.queryStatShipMonthInfo(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          // 拆线数据
          if (res.data.shipMonthInfoArray.length === 12 && res.data.contrastshipMonthInfoArray.length === 12) {
            res.data.shipMonthInfoArray.forEach(item => {
              this.lineData1.xAxis.push(item.voyage_over_month.substring(5, 7) < 10 ? item.voyage_over_month.substring(6) : item.voyage_over_month.substring(5, 7))
              this.lineData2.xAxis.push(item.voyage_over_month.substring(5, 7) < 10 ? item.voyage_over_month.substring(6) : item.voyage_over_month.substring(5, 7))
              this.lineData3.xAxis.push(item.voyage_over_month.substring(5, 7) < 10 ? item.voyage_over_month.substring(6) : item.voyage_over_month.substring(5, 7))
              this.lineData1.data[0].push(item.load_amount || 0)
              this.lineData2.data[0].push(item.turnover_volume_sum || 0)
              this.lineData3.data[0].push(item.voyage_total || 0)
              // 表格数据
              this.data.push({
                voyage_over_month: item.voyage_over_month,
                load_amount: item.load_amount,
                turnover_volume_sum: item.turnover_volume_sum,
                voyage_total: item.voyage_total
              })
            })
            res.data.contrastshipMonthInfoArray.forEach(item => {
              this.lineData1.data[1].push(item.load_amount || 0)
              this.lineData2.data[1].push(item.turnover_volume_sum || 0)
              this.lineData3.data[1].push(item.voyage_total || 0)
            })
          } else {
            this.lineCheck(res.data.shipMonthInfoArray, this.lineData1, 'load_amount', 0)
            this.lineCheck(res.data.contrastshipMonthInfoArray, this.lineData1, 'load_amount', 1)
            this.lineCheck(res.data.shipMonthInfoArray, this.lineData2, 'turnover_volume_sum', 0)
            this.lineCheck(res.data.contrastshipMonthInfoArray, this.lineData2, 'turnover_volume_sum', 1)
            this.lineCheck(res.data.shipMonthInfoArray, this.lineData3, 'voyage_total', 0)
            this.lineCheck(res.data.contrastshipMonthInfoArray, this.lineData3, 'voyage_total', 1)
          }
        }
      })
    },
    lineCheck (arr, data, str, index) {
      let getDataNameList = arr.map(item => item.voyage_over_month.split('-')[1])
      data.xAxis.forEach(item => {
        if (getDataNameList.includes(item)) {
          let _curIdx = getDataNameList.findIndex(list => list === item)
          data.data[index].push(arr[_curIdx][str] || 0)
        } else {
          data.data[index].push(0)
        }
      })
    },
    shipSelect (obj) {
      this.ship_name = obj.label
    },
    resetData () {
      this.lineData1.xAxis = []
      this.lineData2.xAxis = []
      this.lineData3.xAxis = []
      this.lineData1.data = [[], []]
      this.lineData2.data = [[], []]
      this.lineData3.data = [[], []]
      this.data = []
    },
    searchData () {
      this.start_date = this.queryParam.base_year
      this.end_date = this.queryParam.contrast_year
      this.getList()
    },
    searchReset () {
      this.start_date = this.sysDate.split(' ')[0].split('-')[0]
      this.end_date = parseInt(this.start_date) - 1 + ''
      if (this.shipList.length > 0) {
        this.queryParam.ship_id = this.shipList[0].value
        this.ship_name = this.default_ship
      }
      this.getList()
    }
  },
  created () {
    this.getCurShipId()
    this.getSysDate()
  }
}
</script>
<style scoped>
  .box-chart {
    height: 250px;
    margin-top: 20px;
  }
  .select-ship-content {
    width: 110px;
    margin-left:12px !important;
  }
  .table_box {
    margin-top: 60px;
  }
</style>
