<template>
  <Drawer v-model="modalData.modal" :title="modalData.title" width="500" @on-visible-change="visibleChange">
    <Table border show-summary :columns="columns" :data="list" @on-row-dblclick="feeListShow">
    </Table>
    <Modal v-model="feeShow" :title="feeListTitle" width="1300">
      <Table border :columns="feeColumns" :data="feeList">
      </Table>
    </Modal>
  </Drawer>
</template>

<script>
import API from '@/api/shipSchedule'

export default ({
  props: {
    modalData: Object
  },
  data () {
    return {
      feeShow: false,
      feeListTitle: '',
      list: [],
      columns: [
        { key: 'ship_name', title: '船名', align: 'center'},
        { key: 'ship_amounts', title: '货量(吨)', align: 'center'},
        { key: 'ship_shipping_fee', title: '运费(元)', align: 'center'}
      ],
      feeColumns: [
        { title: '归属月份', key: 'belong_month', align: 'center'},
        { title: '船名', key: 'ship_name', align: 'center'},
        { title: '航次号', key: 'voyage_no', align: 'center'},
        { title: '货量', key: 'amounts', align: 'center' },
        { title: '受载开始', key: 'start_plan_date', align: 'center' },
        { title: '受载结束', key: 'end_plan_date', align: 'center' },
        { title: '航次开始', key: 'empty_sail_start_day', align: 'center' },
        { title: '航次结束', key: 'estimated_over_day', align: 'center' },
        { title: '运价(元/吨)', key: 'freight_rate', align: 'center' },
        { title: '运费(元)', key: 'shipping_fee', align: 'center' },
        { title: '装港', key: 'load_port_name', align: 'center' },
        { title: '卸港', key: 'unload_port_name', align: 'center' }
      ],
      feeList: []
    }
  },
  methods: {
    getList() {
      this.list = []
      API.queryVoyageMonthPlanListAndStat( { belong_month: this.modalData.belong_month }).then(res => {
        if (res.data.Code === 10000) {
          this.list = res.data.Result
        } else {
          this.$Message.warning(res.data.Message)
        }
      })
    },
    feeListShow(row) {
      this.feeShow = true
      this.feeList = row.planList
      this.feeListTitle = row.ship_name + '运费预收入详情 - ' + this.modalData.belong_month
    },
    visibleChange(val) {
      if (val) {
        this.getList()
      }
    }
  }
})
</script>
