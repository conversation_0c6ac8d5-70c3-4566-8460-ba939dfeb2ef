/**
 * @description: iView表单验证规则
 * 验证规则有需要才引用
 */
// 验证中文格式
export const validateRealName = (rule, value, callback) => {
  if (value !== '' && (!/^[\u4E00-\u9FA5]+$/.test(value))) {
    callback(new Error('请输入中文！'))
  }
  callback()
}
// 验证英文格式
export const validateEnglish = (rule, value, callback) => {
  if (value !== '' && (!/^[[a-zA-Z]+$/.test(value))) {
    callback(new Error('请输入英文！'))
  }
  callback()
}
// 验证英文名（英文和空格）格式
export const validateEnglishSpace = (rule, value, callback) => {
  if (value !== '' && (!/^[A-Za-z][A-Za-z\s]*[A-Za-z]$/.test(value))) {
    callback(new Error('请输入英文名！'))
  }
  callback()
}
// 验证数字格式
export const validateNumber = (rule, value, callback) => {
  if (value !== '' && (!/^[0-9]+$/.test(value))) {
    callback(new Error('请输入数字！'))
  }
  callback()
}
// 验证身份证格式 /^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{4}$/
export const validateIdCard = (rule, value, callback) => {
  if (value !== '' && (!/^(^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$)|(^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])((\d{4})|\d{3}[Xx])$)$/.test(value))) {
    callback(new Error('请输入正确的身份证号码！'))
  }
  callback()
}
// 验证银行账号
export const validateBankAccount = (rule, value, callback) => {
  if (value !== '' && (!/^[0-9]{16,19}$/.test(value))) {
    callback(new Error('请输入正确银行账号！'))
  }
  callback()
}

// 验证船员适任证书编号
export const validateSailorCertificateNo = (rule, value, callback) => {
  if (value !== '' && (!/^[A|B|Y|D][A-Z]{2}[1|2|3][1|2|3][1|2|3|4|5]\d{9}$/.test(value))) {
    callback(new Error('请输入正确的证书编号! '))
  }
  callback()
}

// 验证手机号码格式 /^(13[0-9]|14[5|7]|15[0|1|2|3|5|6|7|8|9]|18[0|1|2|3|5|6|7|8|9])\d{8}$/
export const validateMobilePhone = (rule, value, callback) => {
  if (value !== '' && (!/^1[0-9]{10}$/.test(value))) {
    callback(new Error('请输入正确的手机号码！'))
  }
  callback()
}

// 验证家庭电话格式  ^(\(\d{3,4}-)|\d{3.4}-)?\d{7,8}$
export const validateHomePhone = (rule, value, callback) => {
  if (value !== '' && (!/^0\d{2,3}-\d{7,8}(-\d{1,6})?$/.test(value))) {
    callback(new Error('请输入正确的家庭电话！'))
  }
  callback()
}

// 不能输入特殊字符
export const validateNormal = (rule, value, callback) => {
  if (value !== '' && (!/^[A-Za-z0-9\u4e00-\u9fa5]+$/.test(value))) {
    callback(new Error('禁止输入特殊字符！'))
  }
  callback()
}

// 只能输入英文和数字
export const validateEnglishNumber = (rule, value, callback) => {
  if (value !== '' && (!/^[A-Za-z0-9]+$/.test(value))) {
    callback(new Error('请输入英文或数字！'))
  }
  callback()
}

// 只能输入正整数
export const validatePositiveInt = (rule, value, callback) => {
  if (value !== '' && (!/^[1-9]\d*$/.test(value))) {
    callback(new Error('请输入正整数！'))
  }
  callback()
}

// 只能输入整数或小数
export const validateIntDecimal = (rule, value, callback) => {
  if (value !== '' && (!/^[0-9]+([.]{1}[0-9]+){0,1}$/.test(value))) {
    callback(new Error('请输入整数或小数'))
  }
  callback()
}

// 保留至少一位小数点
export const validateDecimal = (rule, value, callback) => {
  if (value !== '' && (!/^[0-9]+([.]{1}[0-9])+$/.test(value))) {
    callback(new Error('请保留至少一位小数点！'))
  }
  callback()
}

// 邮箱验证
export const validateEmail = (rule, value, callback) => {
  if (value !== '' && (!/^(\w+\.?)*\w+@(?:\w+\.)\w+$/.test(value))) {
    callback(new Error('请输入正确的邮箱!'))
  }
  callback()
}
