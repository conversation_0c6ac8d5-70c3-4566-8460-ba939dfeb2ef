<template>
  <div>
    <div class="transportCard">
      <Row>
        <Col span="4">
          <Collapse
            simple
            accordion
            v-model="curYear"
            v-for="(item, index) in curDatesList"
            :key="index"
            @on-change="getList(item.report_year, item.reportMonthList[0].report_month)">
            <Panel :name="item.report_year"> {{ item.report_year }}年
              <p slot="content"
                v-for="(itx, idx) in item.reportMonthList"
                :key="idx"
                @click="getList(item.report_year, itx.report_month, idx)"
                class="content"
                :class="{'curidx': curidx === idx}">{{ parseInt(itx.report_month) }}月</p>
            </Panel>
          </Collapse>
        </Col>
        <Col span="16" offset="1" class="tableCard">
          <div class="handleBtn">
            <Button type="primary" @click="handleAdd">新增</Button>
            <!-- <Button type="primary" @click="handleDown('all')">全部下载</Button> -->
          </div>
          <Table border :loading="loading" :columns="columns" :data="listData"></Table>
          <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
              :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange'/>
        </Col>
      </Row>
    </div>
    <transportReportEdit ref="transportReportEdit" @addSuccess="getList"></transportReportEdit>
  </div>
</template>

<script>
import API from '@/api/monthlyReport/transportReport'
import transportReportEdit from './transportReportEdit'

export default {
  components: {
    transportReportEdit
  },
  data () {
    return {
      curYear: '',
      curidx: 0,
      curDatesList: [],
      total: null, // 列表数据条数
      listData: [],
      loading: false, // 表单列表loding状态
      columns: [
        {
          title: '船舶',
          key: 'ship_names',
          // width: 280,
          align: 'center'
        },
        {
          title: '制表人',
          key: 'insert_name',
          align: 'center',
          width: 90
        },
        {
          title: '制表时间',
          key: 'insert_time',
          align: 'center'
        },
        {
          title: '表格时间段',
          key: 'insert_time',
          align: 'center',
          render: (h, params) => {
            return h('div', params.row.start_date + ' ~ ' + params.row.end_date)
          }
        },
        {
          title: '操作',
          align: 'center',
          width: 260,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: {
                  icon: 'md-paper',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handlePreview(params.row)
                  }
                }
              }, '预览'),
              h('Button', {
                props: {
                  icon: 'md-download',
                  size: 'small'
                },
                style: {
                  margin: '0 5px'
                },
                on: {
                  click: () => {
                    this.handleDown(params.row, 'one')
                  }
                }
              }, '下载'),
              h('Button', {
                props: {
                  icon: 'md-trash',
                  size: 'small'
                },
                style: {
                  margin: '0 5px'
                },
                on: {
                  click: () => {
                    this.handleDelete(params.row)
                  }
                }
              }, '删除')
            ])
          }
        }
      ],
      listQuery: { // 列表请求参数
        id: '',
        report_year: '',
        report_month: '',
        end_date: '',
        start_date: '',
        pageSize: 10,
        pageIndex: 1
      },
      listCurrent: 1 // 当前页码
    }
  },
  created () {
    this.getDataList()
  },
  methods: {
    // 获取报表日期
    getDataList () {
      API.queryReportDates().then(res => {
        if (res.data.Code === 10000) {
          res.data.Result.map(e => {
            this.curDatesList.push({
              report_year: e.report_year,
              reportMonthList: e.report_months
            })
          })
          this.curYear = this.curDatesList.length > 0 ? this.curDatesList[0].report_year : ''
          this.getList(this.curDatesList[0].report_year, this.curDatesList[0].reportMonthList[0].report_month)
        }
      })
    },
    // 获取月度运输表列表
    getList (year, month, idx) {
      this.loading = true
      this.listQuery.report_year = year
      this.listQuery.report_month = month
      this.curidx = idx || 0
      delete this.listQuery.curIdx
      API.queryCarriageMPage(this.listQuery).then(response => {
        if (response.data.Code === 10000) {
          this.listData = response.data.Result
          this.total = response.data.Total
          this.listQuery.start_date = response.data.Result[0].start_date
          this.listQuery.end_date = response.data.Result[0].end_date
        } else {
          this.$Message.error(response.data.Message)
        }
        setTimeout(() => {
          this.loading = false
        }, 1 * 800)
      }).catch(
        setTimeout(() => {
          this.loading = false
        }, 1 * 800)
      )
    },
    // 新增
    handleAdd () {
      this.$refs.transportReportEdit.modal = true
      this.$refs.transportReportEdit.modalData = Object.assign(this.listQuery, { curIdx: this.curidx })
    },
    // 预览
    handlePreview (row) {
      sessionStorage.setItem('wpsUrl', row.wps_url)
      sessionStorage.setItem('token', this.$store.state.user.token)
      const jump = this.$router.resolve({ name: 'viewFile' })
      window.open(jump.href, '_blank')
    },
    // 下载
    handleDown (row) {
      window.open(row.download_url, '_blank')
    },
    // 删除
    handleDelete (row) {
      this.$Modal.confirm({
        title: '删除',
        content: '<p>确定删除改月度运输表？</p>',
        loading: true,
        onOk: () => {
          API.delCarriageReport({ id: row.id }).then(res => {
            if (res.data.Code === 10000) {
              this.$Message.success(res.data.Message)
              this.getList(row.report_year, row.report_month, this.curidx)
              this.$Modal.remove()
              this.loading = false
            } else {
              this.$Message.error(res.data.Message)
              this.$Modal.remove()
              this.loading = false
            }
          })
        }
      })
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList(this.listQuery.report_year, this.listQuery.report_month, this.curidx)
    }
  }
}
</script>
<style lang="less">
.transportCard {
  margin-top: 20px;
  border: none;
  background: transparent;
  .ivu-collapse {
    margin-bottom: 1px;
    border: none;
  }
  .ivu-collapse-item.ivu-collapse-item-active {
    .ivu-collapse-header {
      color: #007DFF;
    }
  }
  .ivu-collapse-header {
    color: #4A4A4A;
    background-color: white;
  }
  .ivu-collapse-content {
    padding: 0;
    background-color: #E6E8EA;
    .ivu-collapse-content-box {
      padding: 0;
    }
  }
  .content {
    cursor: pointer;
    color: #4A4A4A;
    line-height: 47px;
    text-align: center;
    border-top: 0.5px solid #fff;
  }
  .content.curidx {
    color: #007DFF;
  }
}
.tableCard {
  padding: 20px 10px;
  background-color: white;
  .handleBtn {
    text-align: right;
    margin-bottom: 15px;
  }
}
</style>
