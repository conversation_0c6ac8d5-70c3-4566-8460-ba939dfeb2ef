<template>
  <Drawer
    v-model="modalData.modal"
    title="公司入驻"
    width="750"
    :mask-closable="false"
    @on-visible-change="modalShow">
      <Form ref="formData" :model="formData" :rules="ruleForm" :label-width="85">
        <Row>
          <Col span="11">
            <FormItem label="公司名称" prop="name">
              <Input v-model="formData.name"></Input>
            </FormItem>
            <FormItem label="公司类型" prop="company_type">
              <Select v-model="formData.company_type" filterable>
                <Option v-for="(item1, idx) in companyTypeList" :key="idx" :value="item1.value">{{ item1.label }}</Option>
              </Select>
            </FormItem>
            <FormItem label="创建人" prop="insert_user_name">
              <Select v-model="formData.insert_user_name" filterable @on-change="getMobile">
                <Option v-for="(item1, idx) in insertUserList" :key="idx" :value="item1.value">{{ item1.label }}</Option>
              </Select>
            </FormItem>
            <FormItem label="联系方式" prop="insert_user_mobile">
              <Input v-model="formData.insert_user_mobile" readonly></Input>
            </FormItem>
            <FormItem label="备注" prop="comments">
              <Input type="textarea" :autosize="true" v-model="formData.comments"></Input>
            </FormItem>
          </Col>
          <Col span="8" offset="5">
            <FormItem style="display:none;">
              <Input type="text" v-model='formData.business_licence_image'></Input>
            </FormItem>
            <div class="userPic" style="clear:both;">
              <img src="@/assets/images/business.png" alt="" v-if="imgBaseUrl === ''">
              <img :src="imgBaseUrl" alt="" v-else>
            </div>
            <Upload action=''
              :show-upload-list='false'
              accept=".jpg, .jpeg, .png"
              :format="['jpg','jpeg','png']"
              :max-size="2048"
              :before-upload="handleImgUpload"
              style="margin:10px 40px 0 20px;">
                <Button>营业执照</Button>
            </Upload>
          </Col>
        </Row>
      </Form>
    <div class="demo-drawer-footer">
      <Button @click="clearData" style="margin-right:10px;">取消</Button>
      <Button type="primary" @click="createData">保存</Button>
    </div>
  </Drawer>
</template>
<script>
import API from '@/api/companyManagement'
import { avatarImage } from '@/api/basicData'
import { validateMobilePhone } from '@/libs/iViewValidate'

export default {
  props: {
    modalData: Object
  },
  data () {
    return {
      companyTypeList: [],
      insertUserList: [],
      formData: {
        name: '',
        company_type: '',
        insert_user_name: '',
        insert_user_mobile: '',
        comments: '',
        business_licence_image: ''
      },
      ruleForm: {
        insert_user_mobile: [
          { required: false, validator: validateMobilePhone, trigger: 'blur' }
        ]
      },
      imgBaseUrl: '',
      uploadData: ''
    }
  },
  methods: {
    // 保存用户
    createData () {
      this.$refs['formData'].validate((valid) => {
        if (valid) {
          this.$Modal.confirm({
            title: '提示',
            content: '<p>是否确认公司入驻？</p>',
            loading: true,
            onOk: () => {
              const userModifyFn = () => {
                API.addUsCompany(this.formData).then(res => {
                  if (res.data.Code === 10000) {
                    this.$Message.success(res.data.Message)
                    this.$Modal.remove()
                    this.$emit('callback')
                    this.modalData.modal = false
                  } else {
                    this.modalData.modal = true
                    this.$Message.error(res.data.Message)
                    this.$Modal.remove()
                  }
                }).catch()
              }
              if (this.uploadData === '') {
                userModifyFn()
              } else {
                avatarImage({ base64File: this.imgBaseUrl }).then(e => {
                  this.formData.business_licence_image = e.data.fileUrl
                  tempData = Object.assign({}, this.formData)
                  if (e.data.Code === -10000) {
                    this.$Message.warning(response.data.Message)
                  } else {
                    userModifyFn()
                  }
                })
              }
            }
          })
        }
      })
    },
    // 取消
    clearData () {
      this.modalData.modal = false
    },
    // 模态框展示
    modalShow (val) {
      if (val) {
        API.queryCompanyTypeList().then(res => { // 获取公司类型
          if (res.data.Code === 10000) {
            this.companyTypeList = res.data.Result.map(item => {
              return {
                label: item.company_type_name,
                value: item.id
              }
            })
          }
        })
        API.accountList().then(res => {
          if (res.data.Code === 10000) {
            this.insertUserList = res.data.Result.map(item => {
              return {
                label: item.full_name,
                value: item.mobile
              }
            })
          }
        })
      } else {
        this.imgBaseUrl = ''
        this.$nextTick(() => {
          this.formData = {}
          this.$refs['formData'].resetFields()
        })
      }
    },
    // 获取创建人联系方式
    getMobile (d) {
      this.formData.insert_user_mobile = d
    },
    // 上传图片
    handleImgUpload (file) {
      const isLt2M = file.size / 1024 / 1024 < 2
      const fileExt = file.name.split('.').pop().toLocaleLowerCase()
      if (!isLt2M) {
        this.$Message.warning('附件过大，附件最大2M')
      } else {
        if (fileExt === 'jpg' || fileExt === 'jpeg' || fileExt === 'png') {
          let that = this
          let reader = new FileReader()
          reader.readAsDataURL(file)
          reader.onload = function (e) {
            that.imgBaseUrl = e.target.result
          }
          this.uploadData = new FormData()
          this.uploadData.append('file', file)
          return false
        } else {
          this.$Message.warning(`${file.name}格式错误`)
          return false
        }
      }
    }
  }
}
</script>
