<template>
  <div>
    <Drawer
      v-model="modalData.modal"
      title="详情"
      width="750"
      :mask-closable="false"
      @on-visible-change="modalShow">
      <Tabs type="card" v-model="tabName" @on-click="getJoinList">
        <TabPane label="用户信息" name="userInfo">
          <Form ref="formData" :model="formData" :label-width="115">
            <Row>
              <Col span="6">
                <Button @click="handleUpdate">编辑</Button>
                <FormItem style="display:none">
                  <Input type="text" v-model='formData.avatar'></Input>
                </FormItem>
                <div class="userPic" style="margin:35px 0 0 100px;">
                  <img src="@/assets/images/picture.png" alt="" v-if="imgBaseUrl === ''">
                  <img :src="imgBaseUrl" alt="" v-else>
                </div>
                <Upload action=''
                  :show-upload-list='false'
                  accept=".jpg, .jpeg, .png"
                  :format="['jpg','jpeg','png']"
                  :max-size="2048"
                  :before-upload="handleImgUpload"
                  style="margin:20px 40px 0 115px;">
                    <Button v-if="!readonly">更换头像</Button>
                </Upload>
              </Col>
              <Col span="12" offset="4">
                <FormItem label="昵称" prop="nickname">
                  <Input v-model="formData.nickname" :readonly="readonly"></Input>
                </FormItem>
                <FormItem label="账号" prop="mobile">
                  <Input v-model="formData.mobile" readonly></Input>
                </FormItem>
                <FormItem label="姓名" prop="full_name">
                  <Input v-model="formData.full_name" :readonly="readonly"></Input>
                </FormItem>
                <FormItem label="性别" prop="gender">
                  <Select v-model="formData.gender" :disabled="readonly">
                    <Option value="1">女</Option>
                    <Option value="0">男</Option>
                  </Select>
                </FormItem>
                <FormItem label="生日" prop="birthday">
                  <DatePicker type="date" v-model="formData.birthday" format="yyyy-MM-dd" @on-change="data=>formData.birthday=data" :readonly="readonly" style="width: 100%"></DatePicker>
                </FormItem>
                <FormItem label="所在地" prop="detail_address">
                  <Input v-model="formData.detail_address" :readonly="readonly"></Input>
                </FormItem>
                <FormItem label="注册时间" prop="register_time">
                  <Input v-model="formData.register_time" readonly></Input>
                </FormItem>
                <FormItem label="最后登录时间" prop="last_login_time">
                  <Input v-model="formData.last_login_time" readonly></Input>
                </FormItem>
              </Col>
            </Row>
          </Form>
        </TabPane>
        <TabPane label="加入公司" name="joinCompany">
          <div class="join_div">
            <span>昵称：{{ nickname }} &nbsp;&nbsp;&nbsp;&nbsp;账号：{{ mobile }}</span>
            <Button @click="handleJoinAdd">加入公司</Button>
          </div>
          <Table border :loading="joinLoading" :columns="joinColumns" :data="joinList" ref="selection"></Table>
          <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
          prev-text="< 上一页" next-text="下一页 >"
            @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
        </TabPane>
      </Tabs>
      <div class="demo-drawer-footer">
        <Button @click="clearData" style="margin-right:10px;">取消</Button>
        <Button type="primary" v-if="!readonly" @click="updateData">保存</Button>
      </div>
    </Drawer>
    <!-- 加入公司弹窗内容 -->
    <Modal
      v-model="addCompanyModel"
      title="加入公司"
      @on-ok="ok"
      @on-cancel="cancel"
      class="form_addcompany">
      <div class="join_div">昵称：{{ nickname }} &nbsp;&nbsp;&nbsp;&nbsp;账号：{{ mobile }}</div>
      <Form ref="addCompanyData" :model="addCompanyData" :label-width="80">
        <Row>
          <Col>
            <FormItem label="公司名称：">
              <Select v-model="addCompanyData.join_company_id" filterable @on-select="getCompanyType">
                <Option v-for="(item1, idx) in joinCompanyList" :key="idx" :value="item1.value">{{ item1.label }}</Option>
              </Select>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col>
            <FormItem label="公司类型：">
              <Input v-model="addCompanyData.company_type" disabled></Input>
            </FormItem>
          </Col>
        </Row>
      </Form>
    </Modal>
  </div>
</template>
<script>
import API from '@/api/accountManagement'
import { avatarImage } from '@/api/basicData'
import { queryAllCompanyList } from '@/api/customerManagement/customerManagement' // 获取公司名称下拉

export default {
  props: {
    modalData: Object
  },
  data () {
    return {
      readonly: true,
      imgBaseUrl: '',
      uploadData: '',
      tabName: 'userInfo',
      formData: {
        avatar: '',
        nickname: '',
        mobile: '',
        full_name: '',
        gender: '',
        birthday: '',
        detail_address: '',
        register_time: '',
        last_login_time: ''
      },
      joinLoading: false, // 表单列表loding状态
      joinColumns: [
        {
          title: '入驻公司',
          key: 'company_name',
          align: 'center'
        },
        {
          title: '公司类型',
          key: 'company_type_name',
          align: 'center',
          width: 90
        },
        {
          title: '状态',
          key: 'company_status_name',
          align: 'center',
          width: 95
        },
        {
          title: '岸基权限',
          key: 'shore_auth',
          align: 'center',
          width: 90,
          render: (h, params) => {
            return h('i-switch', {
              props: {
                value: params.row.shore_auth === '1',
                disabled: params.row.company_type !== '1'
              },
              on: {
                'on-change': () => {
                  params.row.shore_auth = params.row.shore_auth === '0' ? '1' : '0'
                  this.changeShoreAuth(params.row)
                }
              }
            })
          }
        },
        {
          title: '操作',
          key: 'operation',
          align: 'center',
          width: 240,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                style: {
                  margin: '4px',
                  display: params.row.company_status === '10' ? 'none' : 'block'
                },
                props: {
                  icon: 'md-paper',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleAgreeJoin(params.row)
                  }
                }
              }, '通过'),
              h('Button', {
                style: {
                  margin: '4px',
                  display: params.row.company_status === '0' ? 'block' : 'none'
                },
                props: {
                  icon: 'md-paper',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleRefuseJoin(params.row)
                  }
                }
              }, '拒绝'),
              h('Button', {
                style: {
                  margin: '4px'
                },
                props: {
                  icon: 'md-settings',
                  size: 'small',
                  disabled: params.row.role === '0'
                },
                on: {
                  click: () => {
                    this.handoverManage(params.row)
                  }
                }
              }, '移交管理员'),
              h('Button', {
                style: {
                  margin: '4px'
                },
                props: {
                  icon: 'md-trash',
                  size: 'small',
                  disabled: params.row.role === '0' // role:0管理员，1默认角色
                },
                on: {
                  click: () => {
                    this.handleDelete(params.row)
                  }
                }
              }, '移除公司')
            ])
          }
        }
      ],
      joinList: [], // 表单列表数据
      listQuery: {// 列表请求参数
        pageSize: 10,
        pageIndex: 1,
        id: ''
      },
      listCurrent: 1, // 当前页码
      nickname: '',
      mobile: '',
      addCompanyModel: false, // 加入公司弹窗默认关闭
      addCompanyData: {
        join_company_id: '',
        company_type: ''
      },
      joinCompanyList: [],
      selectAll: false,
      selectionData: {} // 存储已选中数据
    }
  },
  methods: {
    // 编辑个人信息
    handleUpdate () {
      this.readonly = false
    },
    // 修改
    updateData () {
      this.$refs['formData'].validate((valid) => {
        if (valid) {
          this.$Modal.confirm({
            title: '提示',
            content: '<p>是否修改信息？</p>',
            loading: true,
            onOk: () => {
              const userModifyFn = () => {
                let param = {
                  id: this.formData.id,
                  full_name: this.formData.full_name,
                  nickname: this.formData.nickname,
                  gender: this.formData.gender,
                  birthday: this.formData.birthday,
                  region_id: this.formData.region_id,
                  avatar: this.formData.avatar
                }
                API.updateAccount(param).then(res => {
                  if (res.data.Code === 10000) {
                    this.$Message.success(res.data.Message)
                    this.$Modal.remove()
                    this.$emit('callback')
                    this.modalData.modal = false
                  } else {
                    this.modalData.modal = true
                    this.$Message.error(res.data.Message)
                    this.$Modal.remove()
                  }
                }).catch()
              }
              if (this.uploadData === '') {
                userModifyFn()
              } else {
                avatarImage({ base64File: this.imgBaseUrl }).then(e => {
                  this.formData.avatar = e.data.fileUrl
                  if (e.data.Code === -10000) {
                    this.$Message.warning(response.data.Message)
                  } else {
                    userModifyFn()
                  }
                })
              }
            }
          })
        }
      })
    },
    // 取消
    clearData () {
      this.modalData.modal = false
    },
    // 上传图片
    handleImgUpload (file) {
      const isLt2M = file.size / 1024 / 1024 < 2
      const fileExt = file.name.split('.').pop().toLocaleLowerCase()
      if (!isLt2M) {
        this.$Message.warning('附件过大，附件最大2M')
      } else {
        if (fileExt === 'jpg' || fileExt === 'jpeg' || fileExt === 'png') {
          let that = this
          let reader = new FileReader()
          reader.readAsDataURL(file)
          reader.onload = function (e) {
            that.imgBaseUrl = e.target.result
          }
          this.uploadData = new FormData()
          this.uploadData.append('file', file)
          return false
        } else {
          this.$Message.warning(`${file.name}格式错误`)
          return false
        }
      }
    },
    // 模态框展示
    modalShow (val) {
      if (val) {
        this.tabName = 'userInfo'
        this.formData = this.modalData.data
        this.nickname = this.modalData.data.nickname
        this.mobile = this.modalData.data.mobile
        this.imgBaseUrl = this.formData.avatar !== 'picture.png' ? this.modalData.data.avatar : picture
      } else {
        this.readonly = true
        this.imgBaseUrl = ''
      }
    },
    // 加入公司，获取所有公司下拉数据
    handleJoinAdd () {
      this.addCompanyModel = true
      queryAllCompanyList({ passport_id: this.modalData.data.id, find_all: 0 }).then(res => {
        if (res.data.Code === 10000) {
          this.joinCompanyList = res.data.Result.map(item => {
            return {
              value: item.id,
              label: item.name,
              companyType: item.company_type_name
            }
          })
        }
      })
    },
    // 获取公司类型
    getCompanyType (val) {
      let curIndex = this.joinCompanyList.findIndex(item => {
        return val.value === item.value
      })
      this.addCompanyData.company_type = this.joinCompanyList[curIndex].companyType
    },
    // 获取已申请入驻公司的列表
    getJoinList (name) {
      if (name === '0') return
      this.loading = true
      this.selectAll = false
      this.listQuery.id = this.modalData.data.id
      API.queryAccountDetail(this.listQuery).then(res => {
        if (res.data.Code === 10000) {
          this.joinList = res.data.Companys
        } else {
          this.$Message.error(res.data.Message)
        }
        setTimeout(() => {
          this.loading = false
        }, 1 * 800)
      }).catch(
        setTimeout(() => {
          this.loading = false
        }, 1 * 800)
      )
    },
    // 岸基权限
    changeShoreAuth (row) {
      let data = {
        ship_company_config_auth_id: row.ship_company_config_auth_id,
        ship_company_id: row.company_id,
        ship_user_id: this.modalData.data.id,
        shore_auth: row.shore_auth
      }
      API.configUser(data).then(res => {
        if (res.data.Code === 10000) {
          this.$Message.success(res.data.Message)
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 保存加入公司
    ok () {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>是否确认加入公司？</p>',
        loading: true,
        onOk: () => {
          let data = {
            passport_id: this.modalData.data.id,
            join_company_id: this.addCompanyData.join_company_id
          }
          API.joinCompany(data).then(res => {
            if (res.data.Code === 10000) {
              this.$Message.success(res.data.Message)
              this.$Modal.remove()
              this.getJoinList()
              this.clearJoinData()
            } else {
              this.addCompanyModel = true
              this.$Message.error(res.data.Message)
              this.$Modal.remove()
            }
          }).catch()
        }
      })
    },
    // 取消加入公司弹窗
    cancel () {
      this.clearJoinData()
    },
    // 清除加入公司弹窗内容
    clearJoinData () {
      this.addCompanyModel = false
      this.joinCompanyList = []
      this.addCompanyData = {
        join_company_id: '',
        company_type: ''
      }
    },
    // 移交管理员
    handoverManage (row) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>是否确认移交管理员？</p>',
        loading: true,
        onOk: () => {
          let data = {
            company_id: row.company_id,
            new_admin_id: this.modalData.data.id
          }
          API.handoverManage(data).then(res => {
            if (res.data.Code === 10000) {
              this.$Modal.remove()
              this.getJoinList()
              this.$Message.success(res.data.Message)
            } else {
              this.$Modal.remove()
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
    },
    // 移除公司
    handleDelete (row) {
      this.$Modal.confirm({
        title: '移除公司',
        content: '<p>是否确认移除公司？</p>',
        loading: true,
        onOk: () => {
          let data = {
            company_id: row.company_id,
            member_id: this.modalData.data.id
          }
          API.removeMember(data).then(res => {
            if (res.data.Code === 10000) {
              this.$Modal.remove()
              this.getJoinList()
              this.$Message.success(res.data.Message)
            } else {
              this.$Modal.remove()
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
    },
    // 通过加入公司审核
    handleAgreeJoin (row) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>是否确认加入公司？</p>',
        loading: true,
        onOk: () => {
          API.agreeJoinCompany({ user_company_id: row.id }).then(res => {
            if (res.data.Code === 10000) {
              this.$Modal.remove()
              this.getJoinList()
              this.$Message.success(res.data.Message)
            } else {
              this.$Modal.remove()
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
    },
    // 拒绝加入公司审核
    handleRefuseJoin (row) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>是否确认拒绝加入公司？</p>',
        loading: true,
        onOk: () => {
          API.refuseJoinCompany({ user_company_id: row.id }).then(res => {
            if (res.data.Code === 10000) {
              this.$Modal.remove()
              this.getJoinList()
              this.$Message.success(res.data.Message)
            } else {
              this.$Modal.remove()
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    }
  }
}
</script>
<style scoped lang="less">
  .join_div {
    overflow: hidden;
    line-height: 32px;
    font-size: 14px;
    margin-bottom: 10px;
    button {
      float: right;
    }
  }
  .form_addcompany {
    text-align: center;
  }
  .userPic {
    overflow: hidden;
    width: 120px;
    margin-top: 20px;
    height: 160px;
    display: flex;
    justify-content: center;
    align-items: center;
    img {
      width: 100%;
    }
  }
</style>
