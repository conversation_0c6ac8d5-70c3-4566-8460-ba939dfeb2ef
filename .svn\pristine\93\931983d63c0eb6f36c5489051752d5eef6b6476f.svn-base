import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 查询港口信息
export function queryOuterPortList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/piisp/outer/port/queryOuterPortList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询码头信息
export function queryOuterWharfList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/piisp/outer/wharf/queryOuterWharfList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询泊位信息
export function queryOuterBerthList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/piisp/outer/berth/queryOuterBerthList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询锚地信息
export function queryPiispAuchorageList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/piisp/inner/auchorage/queryPiispAuchorageList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 十二海里领海基线列表
export function queryWatersBaselineList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/port/record/queryWatersBaselineList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  queryOuterPortList,
  queryOuterWharfList,
  queryOuterBerthList,
  queryPiispAuchorageList,
  queryWatersBaselineList
}
