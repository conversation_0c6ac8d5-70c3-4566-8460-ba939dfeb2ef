<template>
  <div>
    <Drawer
      v-model="modalData.modal"
      :title="modalData.title"
      :width="800"
      :mask-closable="false"
      @on-visible-change="visibleChange">
      <div>
        时间：<month-select class="month-select" @on-change="dateSelect" v-model="curDate"></month-select>&nbsp;&nbsp;&nbsp;&nbsp;
        船名：<Select size="small" class="select-ship-content" :transfer="false" v-model="queryParam.ship_id" placeholder="请选择船舶"
                  clearable @on-change="shipSelect">
                <Option v-for="item in shipList" :value="item.value" :key="item.value">{{ item.label }}</Option>
        </Select>
        <Button style="margin-left: 20px;" type="primary" size="small" @click="searchData">查询</Button>
        <Button style="margin-left: 10px;" size="small" @click="searchReset">重置</Button>
      </div>
      <div v-if="modalData.modal" style="margin-top: 20px;">
        <chart-pie style="height: 200px;" :legendShow="isLegend" :value="voyageRateList" text="南上北下占比" :color="pieColor" unit="航次"></chart-pie>
      </div>
      <Tabs type="card" v-model="queryParam.direction_type" @on-click="tabChange">
        <TabPane v-for="(tab, index) in tabList" :key="index" :label="tab.label" :name="tab.value">
          <Table border :loading="listLoading" ref="selection" :columns="columns" :data="dataList"></Table>
          <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="queryParam.pageSize" :current.sync="queryParam.pageIndex"
          :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
        </TabPane>
      </Tabs>
    </Drawer>
  </div>
</template>
<script>
import API from '@/api/statistics/voyageAnalysis'
import MonthSelect from '@/components/monthSelect'
import { ChartPie } from '_c/charts'

export default {
  props: {
    modalData: Object,
    startDate: String,
    endDate: String,
    defaultStartDate: String,
    defaultEndDate: String
  },
  components: {
    MonthSelect,
    ChartPie
  },
  data () {
    return {
      isLegend: false, // 不显示图例
      listLoading: false, // 表格加载
      pieColor: ['#5D7092', '#5B8FF9', '#5AD8A6'],
      shipList: [], // 公司船舶列表
      queryParam: {
        ship_id: '',
        start_month: '',
        end_month: '',
        direction_type: '1', // 1南上；-1北下
        pageSize: 10,
        pageIndex: 1
      },
      tabList: [
        { label: '南上', value: '1' },
        { label: '北下', value: '-1' }
      ],
      columns: [
        {
          title: '航次',
          key: 'voyage_no',
          align: 'center'
        },
        {
          title: '航线',
          key: 'port_line',
          align: 'center'
        },
        {
          title: '货品',
          key: 'goods_name',
          align: 'center'
        },
        {
          title: '实际货量',
          key: 'load_amount',
          align: 'center'
        }
      ],
      listCurrent: 1,
      total: 0,
      dataList: [], // 表格数据
      voyageRateList: [],
      voyageNumList: [
        { value: 14, name: '南上' },
        { value: 39, name: '北下' }
      ]
    }
  },
  computed: {
    curDate () {
      if (this.queryParam.start_month !== '' && this.queryParam.end_month !== '') {
        return this.queryParam.start_month + '~' + this.queryParam.end_month
      }
      if (this.queryParam.start_month !== '' && this.queryParam.end_month === '') {
        return this.queryParam.start_month
      }
      return ''
    }
  },
  created () {
    if (window.localStorage.shipNameList) {
      let shipList = JSON.parse(window.localStorage.shipNameList)
      shipList.map(item => {
        if ((item.business_model === '1' || item.business_model === '2') && !item.ship_name.includes('善')) { // 只要自营和船期船舶且不需要万邦船舶  2024/09/09调整
          this.shipList.push({
            value: item.ship_id,
            label: item.ship_name
          })
        }
      })
    } else {
      this.setSearchData.ship_id.selectData = []
    }
  },
  methods: {
    tabChange (id) {
      this.queryParam.pageIndex = 1
      this.queryParam.direction_type = id
      this.getList()
    },
    // 日期改变触发
    dateSelect (dateObj) {
      this.queryParam.start_month = dateObj[0] ? dateObj[0] : ''
      this.queryParam.end_month = dateObj[1] ? dateObj[1] : ''
    },
    // 船舶数据改变触发
    shipSelect (value) {
      this.queryParam.ship_id = value
    },
    // 开始查询
    searchData () {
      this.queryParam.pageIndex = 1
      this.getList()
    },
    // 重置按钮点击
    searchReset () {
      this.queryParam = {
        ship_id: '',
        start_month: this.defaultStartDate,
        end_month: this.defaultEndDate,
        direction_type: this.queryParam.direction_type, // 1南上；-1北下
        pageSize: 10,
        pageIndex: 1
      }
      this.getList()
    },
    getList () {
      this.listLoading = true
      this.voyageRateList = []
      API.queryVoyageDirectionDetail(this.queryParam).then(res => {
        this.listLoading = false
        if (res.data.Code === 10000) {
          res.data.directionRatioArray.forEach(item => {
            this.voyageRateList.push({
              value: parseInt(item.direction_count),
              name: item.direction_name
            })
          })
          this.dataList = res.data.directionReportObj.Result
          this.total = res.data.directionReportObj.Total
        }
      })
    },
    // 页面跳转
    handleSizeChange (val) {
      this.queryParam.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.queryParam.pageIndex = val
      this.getList()
    },
    visibleChange (val) {
      if (val) {
        this.queryParam.start_month = this.startDate
        this.queryParam.end_month = this.endDate
        this.getList()
      } else {
        this.queryParam = {
          ship_id: '',
          start_month: '',
          end_month: '',
          direction_type: '1', // 1南上；-1北下
          pageSize: 10,
          pageIndex: 1
        }
      }
    }
  }
}
</script>
<style scoped>
  .select-ship-content {
    width: 110px;
    margin-left:12px !important;
  }
</style>
