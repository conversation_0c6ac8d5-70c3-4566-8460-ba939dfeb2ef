<template>
  <div style="padding: 20px; background: #fff;">
    <search @searchResults='searchResults' :setSearch='setSearchData' @keydown.enter.native='searchResults' @resetResults='resetResults'></search>
    <div>
      <Row class="datalisthead">
        <Col span="4" style="text-align: center;">航次信息</Col>
        <Col span="5" style="text-align: center;">航线信息</Col>
        <Col span="8">动态信息</Col>
        <Col span="3" style="text-align: center;">功能</Col>
        <Col span="2" style="text-align: center;">操作</Col>
      </Row>
      <div v-for="(item, idx) in list" :key="idx" class="voyage-list" @click="handleDetail(item)">
        <Row class="voyage-area" justify="center" align="middle">
          <Col span="4" style="text-align: left;">
            <div>{{ item.ship_name }} {{ item.voyage_no }}</div>
            <div v-for="(item, idx) in getGoods(item.cargoResult)" :key="idx" class="cargo_result">
              <div>{{ item }}</div>
            </div>
          </Col>
          <Col span="5" style="text-align: left;">
            <div>
              <Icon type="md-disc" color="#007DFF"/>
              {{ getPort(item.portResult, '1') }}
            </div>
            <div style="margin: 5px 5px;"><img :src="voyagePath" alt=""></div>
            <div>
              <Icon type="md-pin" color="#007DFF"/>
              {{ getPort(item.portResult, '2') }}
            </div>
          </Col>
          <Col span="8">
            <nodeList :nodeObj="item" :fontSize="14" :nameColor="'#007DFF'" :textColor="'#333'" :dateShow="true"></nodeList>
          </Col>
          <Col span="3">
            <div v-for="(item, idx) in getGoodsNo(item.cargoResult)" :key="idx">
              <button :data-clipboard-text="item" @click="copyText(item)" class="copy_btn">复制订单号</button>
            </div>
          </Col>
          <Col span="2">
            <Tooltip placement="left" theme="light" :transfer="false">
              <Tag>...</Tag>
              <div slot="content">
                <div v-if="getCompanyAdmin"><Button class="btn-area" @click.stop="voyageChange(item)">航次变更</Button></div>
                <div><Button class="btn-area" @click.stop="voyageSend(item)">再次发送</Button></div>
                <div><Button class="btn-area" @click.stop="voyageHistory(item)">推送历史</Button></div>
                <div><Button class="btn-area" @click.stop="berthList(item)">靠泊计划</Button></div>
                <div><Button class="btn-area" @click.stop="orderOutput(item)">航次命令</Button></div>
                <div v-if="getCompanyAdmin"><Button class="btn-area" @click.stop="generateVoyage(item)">航次完结</Button></div>
                <div><Button class="btn-area" @click.stop="sofVoyage(item)">作业时间表</Button></div>
              </div>
            </Tooltip>
          </Col>
        </Row>
      </div>
      <Spin fix v-if="spinShow">
        <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
        <div>Loading</div>
      </Spin>
    </div>
    <div v-if="list.length === 0"><Row class="no-list" justify="center" align="middle">暂无列表数据</Row></div>
    <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="pageSize" :current.sync="pageCur"
          :total="total" prev-text="< 上一页" next-text="下一页 >"
          @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
    <!-- 推送历史弹窗 -->
    <pushHistoryDrawer ref="pushHistoryDrawer"></pushHistoryDrawer>
    <!-- 靠泊计划列表弹窗 -->
    <berthListDraw ref="berthListDrawer"></berthListDraw>
    <!-- 装卸货作业SOF -->
    <voyageSOFDrawer ref="voyageManagementDrawer"></voyageSOFDrawer>
  </div>
</template>
<script>
import search from '_c/search' // 查询组件
import nodeList from '@/components/nodeList/nodeNList'
import voyagePath from '@/assets/images/voyage-path.png'
import API from '@/api/voyageManage/curVoyage'
import pushHistoryDrawer from '@/view/berthingPlan/pushHistoryDrawer'
import berthListDraw from '@/view/berthingPlan/berthListDrawer'
import voyageSOFDrawer from './drawer/voyageSOFDrawer'
import Clipboard from 'clipboard'

export default {
  props: {
    reset: Boolean
  },
  components: {
    search,
    nodeList,
    pushHistoryDrawer,
    berthListDraw,
    voyageSOFDrawer
  },
  data () {
    return {
      spinShow: false, // loading
      isCopy: false, // 正在复制
      pageCur: 1, // 当前页
      pageSize: 5, // 每页个数
      total: 0, // 总页数
      queryParam: {
        pageSize: 5,
        pageIndex: 1
      },
      voyagePath,
      setSearchData: {// 查询设置，对象key值为回调参数
        ship_id: {
          type: 'select',
          label: '船名',
          selectData: [],
          selected: '',
          placeholder: '请选择船名',
          flag: 'ship_id',
          selectName: '',
          width: 210,
          value: ''
        }
      },
      list: []
    }
  },
  created () {
    if (localStorage.bussiShipList) {
      let shipList = JSON.parse(window.localStorage.bussiShipList)
      shipList.map(item => {
        this.setSearchData.ship_id.selectData.push({
          value: item.ship_id,
          label: item.ship_name
        })
      })
    } else {
      this.setSearchData.ship_id.selectData = []
    }
    this.getList()
  },
  computed: {
    getCompanyAdmin () {
      return this.$store.state.user.access.includes('companyAdmin')
    },
    getPort () { // 港口,码头解析
      return function (list, type) {
        if (!list || list.length <= 0) return ''
        // 按类型提取需要的港头数据列表,type = 1为装港, type = 2为卸港
        let curList = list.filter(item => item.port_type === type)
        let portList = curList.map(d => {
          return d.wharf_name === '' ? d.port_name : d.port_name + '/' + d.wharf_name
        })
        return portList.join(' ')
      }
    },
    getGoods () { // 货品,货量解析
      return function (list) {
        if (!list || list.length <= 0) return []
        let curList = list.map(item => {
          return item.goods_name + ' - ' + item.amounts
        })
        return curList
      }
    },
    getGoodsNo () { // 货品编号解析
      return function (list) {
        if (!list || list.length <= 0) return []
        let curList = list.map(item => {
          return item.cargo_no
        })
        return curList
      }
    }
  },
  methods: {
    // 获取列表
    getList () {
      this.spinShow = true
      API.queryVoyagePage(this.queryParam).then(res => {
        this.spinShow = false
        if (res.data.Code === 10000) {
          this.list = res.data.Result
          this.total = res.data.Total
        }
      })
    },
    // 航次变更
    voyageChange (row) {
      Object.assign(row, {
        type: 'reset'
      })
      this.$emit('planModify', row)
    },
    // 航次发送
    voyageSend (item) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>是否发送到船端，发送后将无法修改航次命令内容！</p>',
        loading: true,
        onOk: () => {
          API.sendChangeVoyage({
            id: item.id,
            ship_id: item.ship_id
          }).then(res => {
            if (res.data.Code === 10000) {
              this.$Message.success(res.data.Message)
              this.$Modal.remove()
              this.getList()
            } else {
              this.$Message.error(res.data.Message)
              this.$Modal.remove()
            }
          })
        }
      })
    },
    // 航次推送历史
    voyageHistory (item) {
      this.$refs.pushHistoryDrawer.formModal = true
      this.$refs.pushHistoryDrawer.title = '航次推送历史'
      this.$refs.pushHistoryDrawer.listQuery.key_type = 1
      this.$refs.pushHistoryDrawer.listQuery.key_id = item.id
    },
    // 靠泊计划列表
    berthList (item) {
      this.$refs.berthListDrawer.formModal = true
      this.$refs.berthListDrawer.title = '靠泊计划列表'
      this.$refs.berthListDrawer.listQuery.voyage_id = item.id
    },
    // 航次命令
    orderOutput (item) {
      API.previewVoyageWord({ id: item.id }).then(res => {
        if (res.data.Code === 10000) {
          sessionStorage.setItem('wpsUrl', res.data.wpsUrl)
          sessionStorage.setItem('token', res.data.token)
          const jump = this.$router.resolve({ name: 'viewFile' })
          window.open(jump.href, '_blank')
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 航次完结
    generateVoyage (item) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>船名：' + item.ship_name + item.voyage_no + '航次是否确认已完结？</p>' + '<p>确认后该航次归属于历史航次。</p>',
        loading: true,
        onOk: () => {
          API.generateVoyage({ id: item.id }).then(res => {
            if (res.data.Code === 10000) {
              this.$Message.success(res.data.Message)
              this.$Modal.remove()
              this.getList()
            } else {
              this.$Message.error(res.data.Message)
              this.$Modal.remove()
            }
          })
        }
      })
    },
    // SOF
    sofVoyage (row) {
      localStorage.setItem('voyageObj', JSON.stringify(row))
      API.createSOF({ id: row.id }).then(res => {
        if (res.data.Code === 10000) {
          this.$refs.voyageManagementDrawer.formModal = true
          this.$refs.voyageManagementDrawer.dialogType = 'loadingOperation'
          this.$refs.voyageManagementDrawer.modalData = Object.assign({}, row)
          this.$refs.voyageManagementDrawer.title = '装货作业'
          localStorage.setItem('voyageId', row.id)
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 复制文本
    copyText (data) {
      this.isCopy = true
      let clipboard = new Clipboard('.copy_btn', {
        text: function () {
          return data
        }
      })
      clipboard.on('success', e => {
        this.$Message.success('复制单号成功！')
        clipboard.destroy()
        this.isCopy = false
      })
      clipboard.on('error', e => {
        this.$Message.error('该浏览器不支持自动复制')
        clipboard.destroy()
        this.isCopy = false
      })
    },
    // 航次详情
    handleDetail (row) {
      if (this.isCopy) return
      localStorage.setItem('detailType', 'curVoyage')
      localStorage.setItem('voyageObj', JSON.stringify(row))
      this.$router.push({
        name: 'voyageDetail'
      })
    },
    // 查询
    searchResults (e) {
      if (e.target) delete e.target
      this.pageCur = 1
      this.queryParam.pageIndex = 1
      Object.assign(this.queryParam, e)
      this.getList()
    },
    // 重置
    resetResults () {
      this.pageCur = 1
      this.queryParam = Object.assign(this.queryParam, { // 列表请求参数
        pageIndex: 1,
        ship_id: ''
      })
      this.setSearchData.ship_id.selected = ''
      this.getList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.queryParam.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.queryParam.pageIndex = val
      this.getList()
    }
  },
  watch: {
    reset () {
      this.getList()
    }
  }
}
</script>

<style lang="less">
  .voyage-area {
    cursor: pointer;
    &:hover {
      background: #ebf7ff;
    }
    display: flex;
    min-height: 112px;
    justify-content: center;
    justify-items: center;
    align-items: center;
    padding: 16px 20px;
    background:#fff;
    border-radius:4px;
    border:1px solid #D9D9D9;
    margin-top: 20px;
    color: #333;
    font-weight: 600;
    text-align: center;
    .copy_btn {
      padding: 0 8px;
      margin-left: 12px;
      font-size: 12px;
      transform: scale(0.833,0.833);
      *font-size: 10px;
      color: #007DFF;
      background:#fff;
      border-radius:12px;
      border:1px solid #007DFF;
      cursor: pointer;
    }
    .btn-area {
      border: none;
      color: #333;
      &:hover {
        color: #007DFF;
      }
    }
  }
  .datalisthead {
    margin: 10px 0 -10px 0;
    padding: 0 15px;
    font-size: 14px;
    font-weight: bold;
    text-align: center;
    .ivu-col {
      padding: 0 5px;
    }
  }
</style>
