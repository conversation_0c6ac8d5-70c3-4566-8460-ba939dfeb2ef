<template>
  <div>
    <!-- 整体对比 -->
    <Card>
      <div class="btn_area">
        <span>时间：</span>
        <month-select class="month-select" v-model="baseDate" @on-change="(val) =>{dateSelect(val, 'base')}"></month-select>
        <span> &nbsp; 对比时间：</span>
        <month-select class="month-select" v-model="contrastDate" @on-change="(val) =>{dateSelect(val, 'contrast')}"></month-select>
        <Button size="small" type="primary" @click="clearData" style="padding: 3px 7px">重置</Button>
      </div>
      <Row>
        <Col span="9">
          <div class="line-area">
            <ChartBar style="height: 300px;" subtext="抛锚时长/小时" unit="小时" :value="anchorTimeData" @clickBack="lineBack" :clickable="true" :color="lineColor" rotate="45" legendTop="25" legendPosition="10" text="船舶抛锚时长"/>
          </div>
          <div class="line-area">
            <ChartBar style="height: 300px;" subtext="抛锚72h+次数" unit="小时" :value="anchorMoreTimeData" @clickBack="lineBackBig" :clickable="true" :color="lineColor" rotate="45" legendTop="25" legendPosition="10" text="抛锚72小时以上次数"/>
          </div>
          <Spin size="large" fix v-if="spinShow">
            <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
            <div>Loading</div>
          </Spin>
        </Col>
        <Col span="15">
          <search @searchResults='searchResults' :setSearch='setSearchData' @keydown.enter.native='searchResults' @resetResults='resetResults'></search>
          <Table :data="listData" border :loading="loading" :columns="columns" class="list_table"></Table>
          <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.queryParam.pageSize" :current.sync="listCurrent"
          :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
        </Col>
      </Row>
    </Card>
  </div>
</template>
<script>
import { ChartBar } from '_c/charts'
import search from '_c/search' // 查询组件
import MonthSelect from '@/components/monthSelect'
import { queryAnchorOverall, queryAnchorReport } from '@/api/statistics/anchorStatistics'
import { queryStatTime, queryPortList, queryWharfList } from '@/api/basicData'

export default {
  components: {
    search,
    ChartBar,
    MonthSelect
  },
  data () {
    return {
      spinShow: false,
      chartParam: {
        start_month: '',
        end_month: '',
        start_contrast_month: '',
        end_contrast_month: ''
      },
      lineColor: ['#6699FF', '#E74823'],
      anchorShipList: [], // 抛锚船舶列表
      anchorMoreShipList: [], // 抛锚超72小时船舶列表
      anchorTimeData: {
        xAxis: [],
        legend: ['时间', '对比时间'],
        data: [[], []]
      },
      anchorMoreTimeData: {
        xAxis: [],
        legend: ['时间', '对比时间'],
        data: [[], []],
        symbol: ['emptyCircle', 'circle']
      },
      setSearchData: {
        ship_id: {
          type: 'select',
          label: '船名',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 110,
          value: '',
          filterable: true
        },
        voyage_no: {
          type: 'text',
          label: '航次',
          value: '',
          width: 90
        },
        port_id: {
          type: 'select',
          label: '港口',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 110,
          value: '',
          filterable: true,
          change: this.getWharf
        },
        wharf_id: {
          type: 'select',
          label: '码头',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 130,
          value: '',
          filterable: true,
          isdisabled: true
        },
        is_seventy_two: {
          type: 'select',
          label: '是否超72小时',
          selectData: [
            {
              value: '1',
              label: '是'
            },
            {
              value: '0',
              label: '否'
            }
          ],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 60,
          value: ''
        }
        // is_seventy_two: {
        //   type: 'button',
        //   label: '',
        //   value: '72小时以上',
        //   width: 90,
        //   buttonType: 'default',
        //   click: this.isSeventy
        // }
      },
      queryParam: {
        ship_id: '',
        start_month: '',
        end_month: '',
        pageSize: 20,
        pageIndex: 1,
        voyage_no: '',
        port_id: '',
        wharf_id: '',
        is_seventy_two: '',
        port_type: '',
        base_year: ''
        // is_seventy_two: ''
      },
      loading: false,
      total: null,
      listCurrent: 1,
      listData: [],
      columns: [
        {
          title: '船名',
          key: 'ship_name',
          align: 'center'
        },
        {
          title: '航次',
          key: 'voyage_no',
          align: 'center',
          width: 80
        },
        {
          title: '航线',
          key: 'port_line',
          align: 'center'
        },
        {
          title: '港口',
          key: 'port_name',
          align: 'center'
        },
        {
          title: '码头',
          key: 'wharf_name',
          align: 'center'
        },
        {
          title: '状态',
          key: 'port_type_name',
          align: 'center',
          width: 65
        },
        {
          title: '抛锚时长',
          key: 'anchor_time',
          align: 'center',
          width: 100
        },
        {
          title: '备注',
          key: '',
          align: 'center'
        }
      ]
    }
  },
  created () {
    // 获取船名
    if (window.localStorage.shipNameList) {
      let shipList = JSON.parse(window.localStorage.shipNameList)
      shipList.map(item => {
        if ((item.business_model === '1' || item.business_model === '2') && !item.ship_name.includes('善')) { // 只要自营和船期船舶且不需要万邦船舶  2024/09/09调整
          this.setSearchData.ship_id.selectData.push({
            value: item.ship_id,
            label: item.ship_name
          })
        }
      })
    } else {
      this.setSearchData.ship_id.selectData = []
    }
    queryPortList().then(res => {
      if (res.data.Code === 10000) {
        res.data.Result.map(item => {
          this.setSearchData.port_id.selectData.push({
            value: item.id,
            label: item.port_name
          })
        })
      }
    })
    this.getData()
  },
  methods: {
    // 抛锚时长未超过72小时
    lineBack (val) {
      let _curIdx = val
      if (this.anchorShipList.length > 0) {
        this.queryParam.ship_id = this.anchorShipList[_curIdx].ship_id
        this.setSearchData.ship_id.selected = this.anchorShipList[_curIdx].ship_id
        this.queryParam.is_seventy_two = ''
        this.setSearchData.is_seventy_two.selected = ''
        this.getList()
      }
    },
    // 抛锚时长超过72小时
    lineBackBig (val) {
      let _curIdx = val
      if (this.anchorMoreShipList) {
        this.queryParam.ship_id = this.anchorMoreShipList[_curIdx].ship_id
        this.queryParam.is_seventy_two = '1'
        this.setSearchData.is_seventy_two.selected = '1'
        this.setSearchData.ship_id.selected = this.anchorMoreShipList[_curIdx].ship_id
        this.getList()
      }
    },
    // 系统年月
    getData () {
      queryStatTime().then(res => {
        if (res.data.Code === 10000) {
          if (res.data.start_month && res.data.end_month) {
            this.chartParam.start_month = res.data.start_month
            this.chartParam.end_month = res.data.end_month
            this.chartParam.start_contrast_month = res.data.start_month.substring(0, 4) - 1 + res.data.start_month.substring(4, 7)
            this.chartParam.end_contrast_month = res.data.start_month.substring(0, 4) - 1 + res.data.end_month.substring(4, 7)
            this.getChartList()
            this.getList()
          }
        }
      })
      // querySysDate().then(res => {
      //   if (res.data.Code === 10000 && res.data.systemDate !== '') {
      //     let _year = res.data.systemDate.substring(0, 4)
      //     this.chartParam.start_month = _year + '-01'
      //     this.chartParam.end_month = res.data.systemDate.substring(0, 7)
      //     this.baseDate = this.chartParam.start_month + '~' + this.chartParam.end_month
      //     this.chartParam.start_contrast_month = _year - 1 + '-01'
      //     this.chartParam.end_contrast_month = _year - 1 + res.data.systemDate.substring(4, 7)
      //     this.contrastDate = this.chartParam.start_contrast_month + '~' + this.chartParam.end_contrast_month
      //     this.getChartList()
      //     this.getList()
      //   }
      // })
    },
    // 获取统计折线图
    getChartList () {
      this.resetChart()
      this.spinShow = true
      this.anchorTimeData.legend = [this.baseDate, this.contrastDate]
      this.anchorMoreTimeData.legend = [this.baseDate, this.contrastDate]
      queryAnchorOverall(this.chartParam).then(res => {
        if (res.data.Code === 10000) {
          this.spinShow = false
          if (res.data.baseLineArray.length > 0) { // 指定月份区间--船舶抛锚时长
            res.data.baseLineArray.forEach((item, idx) => {
              if (!item.ship_name.includes('万华') && (item.anchor_time_sum !== '0' || res.data.contrastLineLine[idx].anchor_time_sum !== '0')) { // 剔除万华8
                this.anchorShipList.push({
                  ship_id: item.ship_id,
                  ship_name: item.ship_name
                })
                this.anchorTimeData.xAxis.push(item.ship_name)
                this.anchorTimeData.data[0].push(item.anchor_time_sum)
                this.anchorTimeData.data[1].push(res.data.contrastLineLine[idx].anchor_time_sum)
              }
            })
          }
          if (res.data.baseRedLineArray.length > 0) { // 指定月份区间--船舶抛锚时长72次数
            res.data.baseRedLineArray.forEach((item, idx) => {
              if (!item.ship_name.includes('万华') && (item.count_num !== '0' || res.data.contrastRedLineLine[idx].count_num !== '0')) { // 剔除万华8
                this.anchorMoreShipList.push({
                  ship_id: item.ship_id,
                  ship_name: item.ship_name
                })
                this.anchorMoreTimeData.xAxis.push(item.ship_name)
                this.anchorMoreTimeData.data[0].push(item.count_num)
                this.anchorMoreTimeData.data[1].push(res.data.contrastRedLineLine[idx].count_num)
              }
            })
          }
        } else {
          this.spinShow = false
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 获取报表
    getList () {
      this.loading = true
      this.queryParam.start_month = this.chartParam.start_month
      this.queryParam.end_month = this.chartParam.end_month
      queryAnchorReport(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          this.loading = false
          this.listData = res.data.Result
          this.total = res.data.Total
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 获取码头
    getWharf () {
      this.setSearchData.wharf_id.isdisabled = this.setSearchData.port_id.selected === undefined
      this.setSearchData.wharf_id.selectData = []
      this.setSearchData.wharf_id.selected = ''
      if (this.setSearchData.port_id.selected === undefined) return
      queryWharfList({ port_id: this.setSearchData.port_id.selected }).then(res => {
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.setSearchData.wharf_id.selectData.push({
              value: item.id,
              label: item.terminal_name
            })
          })
        }
      })
    },
    // 数据清空
    clearData () {
      this.chartParam = {
        start_month: '',
        end_month: '',
        start_contrast_month: '',
        end_contrast_month: ''
      }
      this.resetChart()
      this.getData()
      this.resetResults()
    },
    // 重置统计图数据
    resetChart () {
      this.anchorTimeData.xAxis = []
      this.anchorMoreTimeData.xAxis = []
      this.anchorTimeData.data = [[], []]
      this.anchorMoreTimeData.data = [[], []]
    },
    // 重置报表
    resetResults () {
      this.queryParam = {
        ship_id: '',
        start_month: '',
        end_month: '',
        pageSize: 20,
        pageIndex: 1,
        voyage_no: '',
        port_id: '',
        wharf_id: '',
        port_type: '',
        base_year: ''
      }
      this.listCurrent = 1
      // this.queryParam.is_seventy_two = '0'
      // this.setSearchData.is_seventy_two.buttonType = 'default'
      this.setSearchData.ship_id.selected = ''
      this.setSearchData.voyage_no.value = ''
      this.setSearchData.port_id.selected = ''
      this.setSearchData.is_seventy_two.selected = ''
      this.getList()
    },
    // 报表查询
    searchResults () {
      this.listCurrent = 1
      this.queryParam.ship_id = this.setSearchData.ship_id.selected
      this.queryParam.voyage_no = this.setSearchData.voyage_no.value
      this.queryParam.port_id = this.setSearchData.port_id.selected
      this.queryParam.wharf_id = this.setSearchData.wharf_id.selected
      this.queryParam.is_seventy_two = this.setSearchData.is_seventy_two.selected
      this.queryParam.start_month = this.chartParam.start_month
      this.queryParam.end_month = this.chartParam.end_month
      this.getList()
    },
    // 72小时以上
    // isSeventy () {
    //   this.setSearchData.is_seventy_two.buttonType = 'primary'
    //   this.queryParam.is_seventy_two = '1'
    //   this.getList()
    // },
    // 日期变化触发
    dateSelect (val, type) {
      if (type === 'base') {
        this.chartParam.start_month = val[0]
        this.chartParam.end_month = val[1]
      } else {
        this.chartParam.start_contrast_month = val[0]
        this.chartParam.end_contrast_month = val[1]
      }
      this.getChartList()
      this.getList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.queryParam.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.queryParam.pageIndex = val
      this.getList()
    }
  },
  computed: {
    baseDate () {
      if (this.chartParam.start_month !== '' && this.chartParam.end_month !== '') {
        return this.chartParam.start_month + '~' + this.chartParam.end_month
      }
      if (this.chartParam.start_month !== '' && this.chartParam.end_month === '') {
        return this.chartParam.start_month
      }
      return ''
    },
    contrastDate () {
      if (this.chartParam.start_contrast_month !== '' && this.chartParam.end_contrast_month !== '') {
        return this.chartParam.start_contrast_month + '~' + this.chartParam.end_contrast_month
      }
      if (this.chartParam.start_contrast_month !== '' && this.chartParam.end_contrast_month === '') {
        return this.chartParam.start_contrast_month
      }
      return ''
    }
  }
}
</script>
<style lang="less" scoped>
.btn_area {
  margin-bottom: 15px;
   button {
    margin-left: 12px;
  }
}
.line-area,
.list_table {
  margin-top: 20px;
}
</style>
<style lang="less">
.btn_area {
  .month-select-input .ivu-input {
    color: #515a6e;
    line-height: 30px;
    height: 30px;
    background-color: #fff;
    border: 1px solid #dcdee2;
  }
}
.ivu-poptip-popper {
  z-index: 9999 !important;
}
</style>
