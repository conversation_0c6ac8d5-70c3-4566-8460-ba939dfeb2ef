import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 船舶月度计划列表
export function queryVoyageMonthPlanPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/month/plan/queryVoyageMonthPlanPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 船舶月度计划列表
export function queryVoyageMonthPlanList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/month/plan/queryVoyageMonthPlanList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

//  船舶月度计划列表——船舶分组后
export function queryVoyageMonthPlanListByShipGroup (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/month/plan/queryVoyageMonthPlanListByShipGroup',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

//  船舶月度计划列表——船舶分组后
export function queryVoyageMonthPlanListAndVoyagesByShipGroup (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/month/plan/queryVoyageMonthPlanListAndVoyagesByShipGroup',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

//  添加货源
export function addVoyageMonthPlan (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/month/plan/addVoyageMonthPlan',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

//  修改货源
export function updateVoyageMonthPlan (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/month/plan/updateVoyageMonthPlan',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

//  删除货源
export function delVoyageMonthPlan (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/month/plan/delVoyageMonthPlan',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 计算承运结束时间
export function queryStatLinePortGroupList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/month/plan/queryStatLinePortGroupList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 计算承运结束时间
export function correctStatPortToPortCarrierTime (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/stat/port/carrier/correctStatPortToPortCarrierTime',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 批量分配船舶和航次号
export function batchAssignVoyageMonthPlanShips (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/month/plan/batchAssignVoyageMonthPlanShips',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取船舶当前信息
export function queryVoyageMonthPlanShipsInfo (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/month/plan/queryVoyageMonthPlanShipsInfo',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 船舶包运计划列表 分页
export function queryVoyageGuaranteePlanPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/guarantee/plan/queryVoyageGuaranteePlanPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 船舶包运计划列表 未分页
export function queryVoyageGuaranteePlanList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/guarantee/plan/queryVoyageGuaranteePlanList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 船舶包运计划添加
export function addVoyageGuaranteePlan (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/guarantee/plan/addVoyageGuaranteePlan',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 船舶包运计划修改
export function updateVoyageGuaranteePlan (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/guarantee/plan/updateVoyageGuaranteePlan',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 船舶包运计划删除
export function delVoyageGuaranteePlan (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/guarantee/plan/delVoyageGuaranteePlan',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 包量拆分到货盘
export function autoDecompositionGuaranteePlanByShips (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/month/plan/autoDecompositionGuaranteePlanByShips',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 包量拆分到货盘
export function queryVoyageMonthPlanListAndStat (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/month/plan/queryVoyageMonthPlanListAndStat',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 包量拆分到货盘
export function updateVoyageMonthPlanVoyageNo (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/month/plan/updateVoyageMonthPlanVoyageNo',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 导入货盘文件
export function importMonthPlan (data) {
  return axios.request({
    url: '/voyage/month/plan/importMonthPlan',
    method: 'post',
    headers: config.ajaxHeader,
    data
  })
}

export default {
  queryVoyageMonthPlanPage,
  queryVoyageMonthPlanList,
  queryVoyageMonthPlanListByShipGroup,
  queryVoyageMonthPlanListAndVoyagesByShipGroup,
  addVoyageMonthPlan,
  updateVoyageMonthPlan,
  delVoyageMonthPlan,
  queryStatLinePortGroupList,
  correctStatPortToPortCarrierTime,
  batchAssignVoyageMonthPlanShips,
  queryVoyageMonthPlanShipsInfo,
  queryVoyageGuaranteePlanPage,
  queryVoyageGuaranteePlanList,
  addVoyageGuaranteePlan,
  updateVoyageGuaranteePlan,
  delVoyageGuaranteePlan,
  autoDecompositionGuaranteePlanByShips,
  queryVoyageMonthPlanListAndStat,
  updateVoyageMonthPlanVoyageNo,
  importMonthPlan
}
