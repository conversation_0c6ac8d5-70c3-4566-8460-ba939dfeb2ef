import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'
const baseUrl = process.env.NODE_ENV === 'development' ? config.baseUrl.dev : config.baseUrl.pro

// 获取历史导出列表
export function exportHistoryList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/template/history/queryDynamicTemplateHistoryPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 删除历史导出记录
export function exportHistoryDelete (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/template/history/delDynamicTemplateHistory',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 下载历史导出记录
export function getDownFile (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/template/history/previewDynamicTemplateHistory',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}
// export function getDownFile (data) {
//   let qsData = Qs.stringify(data)
//   window.location.href = baseUrl + '/sys/down/downFile?' + qsData
// }

export default {
  exportHistoryList,
  exportHistoryDelete,
  getDownFile
}
