import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 获取港口管理列表,分页
export function queryPortPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/port/record/queryBasicPortManagePage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取港口管理列表,无分页
export function queryPortList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/port/record/queryBasicPortManageList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取港口所属省份/区域/时区
export function queryDicListByCode (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/port/record/queryDicListByCode',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 关联潮汐港口
export function queryTidePorts (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/port/record/queryBasicTidePorts',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 附近港口
export function queryNearbyPorts (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/port/record/queryBasicPortManageByName',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 删除
export function queryDeletePort (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/port/record/delBasicPort',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 新增
export function addPort (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/port/record/addBasicPort',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 修改
export function updatePort (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/port/record/updateBasicPort',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  queryPortPage,
  queryPortList,
  queryDicListByCode,
  queryTidePorts,
  queryNearbyPorts,
  queryDeletePort,
  addPort,
  updatePort
}