<template>
  <div class="tce_content">
    <!-- <div class="content_title">TCE预算</div> -->
    <Affix class="tab_area">
      <div @click="btn_click">{{ curTab === 0 ? '国内' : '国际'}}</div>
      <!-- <Button :type="curTab === 0 ? 'primary' : ''" shape="circle" size="small" @click="innerClick">国内</Button>
      <Button :type="curTab === 1 ? 'primary' : ''" style="margin-left: 2px;" size="small" @click="outClick">国际</Button> -->
    </Affix>
    <!-- 国际tce 开始 -->
    <Form ref="formInline" :model="formData" :label-width="160">
      <FormItem prop="freight_income" label="运费收入">
        <!-- <span class="voyage_input" @click="voyageDaySelect">切换</span> -->
        <InputNumber v-if="curTab === 1"
            v-model="formData.freight_income"
            @on-change="feeChange"
            :disabled="curTab === 1"
            :formatter="value => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
            :parser="value => value.replace(/\$\s?|(,*)/g, '')"></InputNumber>

        <InputNumber v-else
            v-model="formData.freight_income"
            @on-change="feeChange"
            :disabled="curTab === 1"
            :formatter="value => `￥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
            :parser="value => value.replace(/\￥\s?|(,*)/g, '')"></InputNumber>
      </FormItem>
      <Row class="sub_income_area">
        <Col span="12">
          <FormItem prop="goods_quantity" label="货量(MT)" :label-width="80">
            <InputNumber
            v-model="formData.goods_quantity"
            @on-change="feeChange"
            :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
            :parser="value => value.replace(/[,\sMT]/g, '')"></InputNumber>
            <!-- <Input type="number" v-model="formData.goods_quantity" @on-change="feeChange" placeholder="请输入货量" /> -->
          </FormItem>
        </Col>
        <Col span="12">
          <FormItem prop="unit_price" label="单价" :label-width="80">
            <InputNumber v-if="curTab === 1"
            v-model="formData.unit_price"
            @on-change="feeChange"
            :formatter="value => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
            :parser="value => value.replace(/\$\s?|(,*)/g, '')"></InputNumber>

            <InputNumber v-else
            v-model="formData.unit_price"
            @on-change="feeChange"
            :formatter="value => `￥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
            :parser="value => value.replace(/\￥\s?|(,*)/g, '')"></InputNumber>
          </FormItem>
        </Col>
      </Row>
      <Row class="sub_income_area">
        <Col span="12">
          <FormItem prop="extra_price" label="额外价格" :label-width="80">
            <InputNumber v-if="curTab === 1"
            v-model="formData.extra_price"
            @on-change="feeChange"
            :formatter="value => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
            :parser="value => value.replace(/\$\s?|(,*)/g, '')"></InputNumber>

            <InputNumber v-else
            v-model="formData.extra_price"
            @on-change="feeChange"
            :formatter="value => `￥${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
            :parser="value => value.replace(/\￥\s?|(,*)/g, '')"></InputNumber>
            <!-- <Input type="number" v-model="formData.extra_price" @on-change="feeChange" placeholder="请输入价格" /> -->
          </FormItem>
        </Col>
        <Col span="12">
          <FormItem prop="deduction_ratio" label="扣减比例(%)" :label-width="85">
            <InputNumber
            v-model="formData.deduction_ratio"
            :max="100"
            @on-change="feeChange"></InputNumber>
            <!-- <Input type="number" v-model="formData.deduction_ratio" @on-change="feeChange" placeholder="请输入比例" /> -->
          </FormItem>
        </Col>
      </Row>
      <FormItem prop="brokerage_fees_ratio" :label="curTab === 1 ? 'Broker佣金比例(%)' : 'Broker佣金'">
        <InputNumber v-if="curTab === 1"
            v-model="formData.brokerage_fees_ratio"
            :max="100"
            @on-change="feeChange"></InputNumber>
        
        <InputNumber v-else
            v-model="formData.brokerage_fees_ratio"
            @on-change="feeChange"></InputNumber>
        <!-- <Input type="number" v-model="formData.brokerage_fees" @on-change="feeChange" placeholder="请输入Broker佣金比例" /> -->
      </FormItem>
      <FormItem prop="fuel_proportion" :label="curTab === 1 ? '燃油费占收入比(%)' : '燃油费'">
        <InputNumber v-if="curTab === 1"
            v-model="formData.fuel_proportion"
            :max="100"
            @on-change="feeChange"></InputNumber>

        <InputNumber v-else
            v-model="formData.fuel_proportion"
            @on-change="feeChange"></InputNumber>
        <!-- <Input type="number" v-model="formData.fuel_cost" @on-change="feeChange" placeholder="请输入燃油费占收入比" /> -->
      </FormItem>
      <FormItem prop="port_charges_ratio" :label="curTab === 1 ? '港口费占收入比(%)' : '港口费'">
        <InputNumber v-if="curTab === 1"
            v-model="formData.port_charges_ratio"
            :max="100"
            @on-change="feeChange"></InputNumber>

        <InputNumber v-else
            v-model="formData.port_charges_ratio"
            @on-change="feeChange"></InputNumber>
        <!-- <Input type="number" v-model="formData.port_charges" @on-change="feeChange" placeholder="请输入港口费占收入比" /> -->
      </FormItem>
      <FormItem v-if="curTab === 1" prop="carbon_tax" label="EU-ETS费用">
        <InputNumber
            v-model="formData.carbon_tax"
            @on-change="feeChange"
            :formatter="value => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
            :parser="value => value.replace(/\$\s?|(,*)/g, '')"></InputNumber>
        <!-- <Input type="number" v-model="formData.carbon_tax" @on-change="feeChange" placeholder="请输入碳税" /> -->
      </FormItem>
      <FormItem prop="voyage_days" label="航次天数">
        <span class="voyage_input" @click="voyageDaySelect">录入</span>
        <InputNumber
            v-model="formData.voyage_days"
            @on-change="feeChange"
            :formatter="value => `${value}天`"
            :parser="value => value.replace('天', '')"></InputNumber>
        <!-- <Input type="number" v-model="formData.voyage_days" @on-change="feeChange" placeholder="请输入航次天数" /> -->
      </FormItem>
      <FormItem class="tce_area" prop="voyage_days" :label="curTab === 1 ? 'TCE(美元/天)' : 'TCE(人民币/天)'">
        <span class="tce_input">{{ formData.tce }}</span>
      </FormItem>
    </Form>
    <!-- 国际tce 结束 -->
    <div class="bottom_btn" :style="'margin-top:' + buttonTop">
      <Button @click="saveTce" type="primary" size="large" long>保存</Button>
      <Button @click="showList" style="margin-top: 0.8rem;" size="large" long>记录</Button>
    </div>
    <!-- 详情弹窗 -->
    <Drawer class="drawer_area" width="100" v-model="isAddShow" title="TCE预算历史记录" :mask-closable="false">
      <div v-for="(item, idx) in tceHisList" :key="'tceHis' + idx" class="list_area">
        <div class="tce_title">
          <span v-if="curTab === 1"><span style="font-weight: bold;">TCE: </span>${{ item.tce }}/天</span>
          <span v-else><span style="font-weight: bold;">TCE: </span>￥{{ item.tce }}/天</span>
          <span style="float: right;"><span style="font-weight: bold;">日期：</span>{{ item.insert_time.split(' ')[0] }}</span>
        </div>
        <Row class="list_line">
          <Col span="8">
            <div class="line_title">
              运费收入
              <Poptip class="pop_tip" placement="top-start">
                <Icon type="ios-help-circle" size="12" />
                <Row slot="content" style=" background: #f7f7f7; padding: 0.5rem 0.5rem 0.5rem 1rem;">
                  <Col span="6">
                    <div class="line_title">货量:</div>
                    <div>{{ item.goods_quantity | formatNumber }}MT</div>
                  </Col>
                  <Col span="6">
                    <div class="line_title">单价:</div>
                    <div v-if="curTab === 1">${{ item.unit_price | formatNumber }}</div>
                    <div v-else>￥{{ item.unit_price | formatNumber }}</div>
                  </Col>
                  <Col span="6">
                    <div class="line_title">额外价格:</div>
                    <div v-if="curTab === 1">${{ item.extra_price | formatNumber }}</div>
                    <div v-else>￥{{ item.extra_price | formatNumber }}</div>
                  </Col>
                  <Col span="6">
                    <div class="line_title">扣减比例:</div>
                    <div>{{ item.deduction_ratio }}%</div>
                  </Col>
                </Row>
              </Poptip>:
            </div>
            <div v-if="curTab === 1">${{ item.freight_income | formatNumber }}</div>
            <div v-else>￥{{ item.freight_income | formatNumber }}</div>
          </Col>
          <Col span="8">
            <div class="line_title">单价:</div>
            <div v-if="curTab === 1">${{ item.unit_price | formatNumber }}</div>
            <div v-else>￥{{ item.unit_price | formatNumber }}</div>
          </Col>
          <Col span="8">
            <div class="line_title">航次天数:</div>
            <div>{{ item.voyage_days }}天</div>
          </Col>
        </Row>
        
        <Row class="list_line">
          <Col span="8">
            <div class="line_title">Broker经纪费:</div>
            <div v-if="curTab === 1">${{ item.brokerage_fees | formatNumber }}({{ item.brokerage_fees_ratio }}%)</div>
            <div v-else>￥{{ item.brokerage_fees_ratio | formatNumber }}</div>
          </Col>
          <Col span="8">
            <div class="line_title">港口使费:</div>
            <div v-if="curTab === 1">${{ item.port_charges | formatNumber }}({{ item.port_charges_ratio }}%)</div>
            <div v-else>￥{{ item.port_charges_ratio | formatNumber }}</div>
          </Col>
          <Col span="8">
            <div class="line_title">燃油成本:</div>
            <div v-if="curTab === 1">${{ item.fuel_cost | formatNumber }}({{ item.fuel_proportion }}%)</div>
            <div v-else>￥{{ item.fuel_proportion | formatNumber }}</div>
          </Col>
          <!-- <Col span="8">
            <div class="line_title">碳税:</div>
            <div>${{ item.carbon_tax | formatNumber }}</div>
          </Col> -->
        </Row>
      </div>
      <div class="drawer_footer">
        <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="queryParam.pageSize" :current.sync="queryParam.pageIndex"
          :total="total" prev-text="< 上一页" next-text="下一页 >"
          @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
      </div>
    </Drawer>
    <!-- 航次天数下拉弹窗 -->
    <Modal v-model="voyageDrawerShow" :title="voyageTitle" :mask-closable="false" placement="bottom" width="80" height="300" @on-ok="voyageDaysCheck">
      <Row>
        <Col span="11">
          <Input type="number" v-model="dayObj.voyage_day" @on-change="voyageDayChange" placeholder="航行时间">
            <span slot="prepend">航行时间：</span>
            <span slot="append">天</span>
          </Input>
        </Col>
        <Col span="11" offset="1">
          <Input type="number" v-model="dayObj.load_day" @on-change="voyageDayChange" placeholder="装货时间">
            <span slot="prepend">装货时间：</span>
            <span slot="append">天</span>
          </Input>
        </Col>
      </Row>
      <Row style="margin-top: 1rem;">
        <Col span="11">
          <Input type="number" v-model="dayObj.unload_day" @on-change="voyageDayChange" placeholder="卸货时间">
            <span slot="prepend">卸货时间：</span>
            <span slot="append">天</span>
          </Input>
        </Col>
        <Col span="11" offset="1">
          <Input type="number" v-model="dayObj.other_day" @on-change="voyageDayChange" placeholder="其他时间">
            <span slot="prepend">其他时间：</span>
            <span slot="append">天</span>
          </Input>
        </Col>
      </Row>
    </Modal>
  </div>
</template>
<script>
import API from '@/api/unLoginPage/tce.js'

export default {
  data () {
    return {
      curTab: 1, // 当前页面 0: 国内 1：国际
      isAddShow: false,
      voyageDrawerShow: false,
      buttonTop: '5rem',
      total: 0,
      voyageTitle: '航次天数',
      queryParam: {
        tce_type: 1,
        pageSize: 10,
        pageIndex: 1,
        mobile: ''
      },
      tceHisList: [],
      dayObj: {
        voyage_day: null,
        load_day: null,
        unload_day: null,
        other_day: null
      },
      formData: {
        freight_income: null,
        unit_price: null,
        goods_quantity:null,
        extra_price: null,
        deduction_ratio: null,
        brokerage_fees_ratio: 2.5,
        brokerage_fees: null,
        fuel_cost: null,
        fuel_proportion: null,
        port_charges: null,
        port_charges_ratio: null,
        carbon_tax: null,
        voyage_days: null,
        tce: null
      }
    }
  },
  filters: {
    formatNumber(value) {
      // 将数字转换为字符串，并以三位一组用逗号隔开
      return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")
    }
  },
  mounted () {
    let _that = this
    qing.call('getPersonInfo', { // 针对云之家小程序限定
      success: function (res) {
        if (res.data.openId && res.data.openId !== '') {
          _that.formData.mobile = res.data.openId
          localStorage.setItem('mobile', res.data.openId)
        }
      }
    })
    this.handleResize()
    this.getFirstList()
  },
  methods: {
    getList () { // 获取列表
      this.queryParam.mobile = localStorage.getItem('mobile')
      this.queryParam.tce_type = this.curTab
      API.queryTcePage(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          this.tceHisList = [...this.tceHisList, ...res.data.Result]
          this.total = res.data.Total
        }
      })
    },
    btn_click () {
      console.log(this.curTab)
      this.curTab = this.curTab === 1 ? 0 : 1
      this.getFirstList()
    },
    innerClick () { // 国内按钮点击
      // this.curTab = 0
      this.getFirstList()
    },
    outClick () { // 国际按钮点击
      // this.curTab = 1
      this.getFirstList()
    },
    getFirstList () { // 获取第一条数据
      let _param = {
        mobile: localStorage.getItem('mobile'),
        tce_type: this.curTab,
        pageIndex: 1,
        pageSize: 1
      }
      API.queryTcePage(_param).then(res => {
        if (res.data.Code === 10000 && res.data.Result.length > 0) {
          this.formData = res.data.Result[0]
        } else {
          this.formData = {
            freight_income: null,
            unit_price: null,
            goods_quantity: null,
            extra_price: null,
            deduction_ratio: null,
            brokerage_fees_ratio: this.curTab === 1 ? 2.5 : null,
            brokerage_fees: null,
            fuel_cost: null,
            fuel_proportion: null,
            port_charges: null,
            port_charges_ratio: null,
            carbon_tax: null,
            voyage_days: null,
            tce: null
          }
        }
      })
    },
    showList () { // 打开新增弹窗
      this.isAddShow = true
      this.tceHisList = []
      this.queryParam.pageIndex = 1
      this.getList()
    },
    voyageDaySelect () { // 航次天数下拉弹窗
      this.voyageDrawerShow = true
    },
    voyageDayChange () {
      let voyage_day = !this.dayObj.voyage_day ? 0 : this.dayObj.voyage_day
      let load_day = !this.dayObj.load_day ? 0 : this.dayObj.load_day
      let unload_day = !this.dayObj.unload_day ? 0 : this.dayObj.unload_day
      let other_day = !this.dayObj.other_day ? 0 : this.dayObj.other_day
      let voyage_days = parseFloat(voyage_day) + parseFloat(load_day) + parseFloat(unload_day) + parseFloat(other_day)
      this.voyageTitle = '航次天数：' + voyage_days + '天'
    },
    voyageDaysCheck () { // 航次天数计算
      let voyage_day = !this.dayObj.voyage_day ? 0 : this.dayObj.voyage_day
      let load_day = !this.dayObj.load_day ? 0 : this.dayObj.load_day
      let unload_day = !this.dayObj.unload_day ? 0 : this.dayObj.unload_day
      let other_day = !this.dayObj.other_day ? 0 : this.dayObj.other_day
      this.formData.voyage_days = parseFloat(voyage_day) + parseFloat(load_day) + parseFloat(unload_day) + parseFloat(other_day)
      this.feeChange()
    },
    saveTce () { // 计算并保存tce
      this.formData.mobile = localStorage.getItem('mobile')
      if (!this.formData.mobile || this.formData.mobile === '') {
        this.$Message.error('未能获取用户信息，请联系管理员！')
        return
      }
      if (!this.formData.tce || this.formData.tce === 0) {
        this.$Message.error('请检查数据是否完整！')
      } else {
        Object.assign(this.formData, {
          tce_type: this.curTab
        })
        API.addTce(this.formData).then(res => {
          if(res.data.Code === 10000) {
            this.$Message.success(res.data.Message)
            // this.clearData()  要保留就不清除
          } else {
            this.$Message.error(res.data.Message)
          }
        })
      }
    },
    clearData () {
      this.formData = {
        freight_income: null,
        unit_price: null,
        goods_quantity: null,
        extra_price: null,
        deduction_ratio: null,
        brokerage_fees_ratio: 2.5,
        brokerage_fees: null,
        fuel_cost: null,
        fuel_proportion: null,
        port_charges: null,
        port_charges_ratio: null,
        carbon_tax: null,
        voyage_days: null,
        tce: null
      }
      this.dayObj = {
        voyage_day: null,
        load_day: null,
        unload_day: null,
        other_day: null
      }
    },
    handleResize () {
      this.$nextTick(() => {
        let formHeight = this.$refs.formInline.$el.clientHeight
        let windowHeight = window.innerHeight
        this.buttonTop = windowHeight - formHeight - 120 + 'px'
      })
    },
    feeChange () { // tce费用计算
      let unit_price = !this.formData.unit_price ? 0 : this.formData.unit_price
      let goods_quantity = !this.formData.goods_quantity ? 0 : this.formData.goods_quantity
      let extra_price = !this.formData.extra_price ? 0 : this.formData.extra_price
      let deduction_ratio = !this.formData.deduction_ratio ? 0 :this.formData.deduction_ratio
      this.formData.freight_income = this.formData.freight_income ? this.formData.freight_income : (parseFloat(((parseFloat(unit_price) * parseFloat(goods_quantity) + parseFloat(extra_price)) * (1 - deduction_ratio / 100)).toFixed(2)))
      if (!this.formData.voyage_days || this.formData.voyage_days === 0) {
        this.formData.tce = 0
        return
      }
      console.log('1:' + this.formData.freight_income)
      let freight_income = !this.formData.freight_income ? 0 : this.formData.freight_income
      console.log('2:' + this.formData.freight_income)
      let brokerage_fees_ratio = !this.formData.brokerage_fees_ratio ? 2.5 : this.formData.brokerage_fees_ratio
      let brokerage_fees = ((parseFloat(unit_price) * parseFloat(goods_quantity) + parseFloat(extra_price)) * brokerage_fees_ratio / 100).toFixed(2)
      this.formData.brokerage_fees = brokerage_fees
      let fuel_proportion = !this.formData.fuel_proportion ? 0 : this.formData.fuel_proportion
      let fuel_cost = freight_income * fuel_proportion / 100
      this.formData.fuel_cost = fuel_cost
      let port_charges_ratio = !this.formData.port_charges_ratio ? 0 : this.formData.port_charges_ratio
      let port_charges = freight_income * port_charges_ratio / 100
      this.formData.port_charges = port_charges
      let carbon_tax = !this.formData.carbon_tax ? 0 : this.formData.carbon_tax
      let voyage_days = !this.formData.voyage_days ? 0 : this.formData.voyage_days

      if (this.curTab === 0) { // 国内算法
        console.log(freight_income, brokerage_fees_ratio, fuel_proportion, port_charges_ratio, voyage_days)
        this.formData.tce = ((parseFloat(freight_income) - parseFloat(brokerage_fees_ratio) - parseFloat(fuel_proportion) - parseFloat(port_charges_ratio)) / parseFloat(voyage_days)).toFixed(2)
      }
      if (this.curTab === 1) { // 国际算法
        this.formData.tce = ((parseFloat(freight_income) - parseFloat(brokerage_fees) - parseFloat(fuel_cost) - parseFloat(port_charges) - parseFloat(carbon_tax)) / parseFloat(voyage_days)).toFixed(2)
      }
    },
    // 页面跳转
    handleSizeChange (val) {
      this.queryParam.pageSize = val
      this.tceHisList = []
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.queryParam.pageIndex = val
      this.tceHisList = []
      this.getList()
    },
    cancel () { // 隐藏弹窗
      this.isAddShow = false
    }
  }
}
</script>
<style lang="less">
  .tab_area {
    position: absolute;
    background: #2d8cf0ee;
    width: 3rem;
    height: 3rem;
    text-align: center;
    line-height: 3rem;
    color: #fff;
    border-radius: 50%;
    bottom: 20%;
    right: 1rem;
    z-index: 999999;
  }
  .tce_content {
    width: 100%;
    height: 100%;
    color: #333;
    padding: 2.5rem 1.5rem 1.2rem;
    background: url('../../assets/images/tce_bg.png');
    background-size: cover;
    background-repeat: no-repeat;
    backdrop-filter: blur(10rem);
  }
  .tce_content .ivu-form .ivu-form-item-label {
    width: 50% !important;
    background: #eb7631;
    border-radius: 0.3rem 0 0 0.3rem;
    border: 1px solid #ccc;
    border-right: none;
    font-size: 1.3rem;
    color: #fff;
  }
  .tce_content .ivu-form-item-content {
    margin-left: 50% !important;
  }
  .tce_content .ivu-input-number {
    width: 100%;
    height: 3.1rem;
    line-height: 3.1rem;
    border-radius: 0 4px 4px 0;
  }
  .tce_content .ivu-input {
    border-radius: 0 0.3rem 0.3rem 0;
    height:3.1rem;
    font-size: 1.2rem;
  }
  .tce_area .ivu-form-item-label {
    background: #2d8cf0 !important;
    border: none !important;
    color: #fff;
    font-weight: bold;
  }
  .sub_income_area {
    margin-top: -1.2rem;
  }
  .sub_income_area .ivu-form-item-label {
    font-size: 1rem !important;
    background: #a0a0a0 !important;
  }
  .sub_income_area .ivu-input-number {
    width: 100%;
    height: 2.8rem;
    line-height: 2.8rem;
    border-radius: 0 4px 4px 0;
    font-size: 1rem;
  }
  .sub_income_area .ivu-input {
    border-radius: 0 0.3rem 0.3rem 0;
    height: 2.8rem;
    font-size: 1rem;
  }
  .sub_income_area .ivu-form-item {
    margin-bottom: 1rem !important;
  }
  .drawer_area .ivu-drawer-header {
    background: #2d8cf0 !important;
    .ivu-drawer-header-inner {
      color: #fff;
    }
  }
  .drawer_area {
    .ivu-drawer-close .ivu-icon-ios-close, .ivu-drawer-close .ivu-icon-ios-close:hover {
      color: #fff !important;
    }
  }
  .tce_input {
    padding-left: 0.8rem;
    display: inline-block;
    background: #e6e6e7;
    width: 100%;
    height: 3rem;
    line-height: 3rem;
    font-size: 1.6rem;
    border-radius: 0 0.3rem 0.3rem 0;
  }
  .voyage_input {
    position: absolute;
    background: #a0a0a0;
    border: 1px solid #a0a0a0;
    right: 0;
    display: inline-block;
    text-align: center;
    width: 3rem;
    height: 3rem;
    line-height: 3rem;
    font-size: 1rem;
    color: #fff;
    z-index: 10;
  }
  .content_title {
    color: #fff;
    font-size: 1.5rem;
    font-weight: bold;
    text-align: center;
    margin-bottom: 0.2rem;
  }
  .bottom_btn {
    width: 100%;
    text-align: center;
  }
  .drawer_footer {
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    border-top: 1px solid #e8e8e8;
    padding: 0.2rem 1.2rem 1rem;
    text-align: right;
    background: #fff;
  }
  .list_area {
    min-height: 9rem;
    background: #fff;
    margin: 0.9rem 0;
    padding: 0.9rem;
    border-radius: 0.4rem;
    box-shadow: 0px 0px 0.5rem 0.1rem rgba(0, 0, 0, 0.18);
  }
  .tce_title {
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #ccc;
    font-size: 1.1rem;
  }
  .list_line {
    margin-top: 0.9rem;
  }
  .line_title {
    color: #a0a0a0;
  }
  .ivu-poptip-popper {
    min-width: 80% !important;
  }
  .add_btn_area {
    width: 100%;
    position: fixed;
    bottom: 0;
  }
</style>
