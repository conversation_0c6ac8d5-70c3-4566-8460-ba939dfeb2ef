<template>
  <div>
    <!-- 中海油船舶模板 -->
    <Table border :columns="cnoocColumns" :data="templateData" :max-height="`${winHeight}`" class="alignTable"></Table>
  </div>
</template>
<script>
export default {
  props: {
    templateData: Array
  },
  data () {
    return {
      winHeight: 500,
      cnoocColumns: [
        {
          title: '序号',
          type: 'index',
          align: 'center',
          width: 65
        },
        {
          title: '船名',
          key: 'ship_name',
          align: 'center',
          width: 100
        },
        {
          title: '航次',
          key: 'voyage_no',
          align: 'center',
          width: 85
        },
        {
          title: '货品',
          key: 'goods_name',
          align: 'center',
          width: 125
        },
        {
          title: '货量',
          key: 'amounts',
          align: 'center',
          width: 80
        },
        {
          title: '卸货港',
          key: 'port_name',
          align: 'center',
          width: 110
        },
        {
          title: '预抵装货港时间',
          key: 'export_load_data',
          align: 'center'
        },
        {
          title: '预抵卸货港时间',
          key: 'export_unload_data',
          align: 'center'
        }
      ]
    }
  },
  mounted () {
    // 挂载浏览器高度获取方法
    const that = this
    that.winHeight = window.innerHeight - 280
    window.onresize = () => {
      return (() => {
        that.winHeight = window.innerHeight - 280
      })()
    }
  }
}
</script>
