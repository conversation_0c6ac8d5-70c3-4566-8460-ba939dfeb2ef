<template>
  <div class="ship-route-config">
    <div class="layout-container">
      <!-- 左侧船舶列表 -->
      <div class="left-panel">
        <Card>
          <p slot="title">船舶列表</p>
          <div class="ship-list">
            <div v-for="(ship, index) in ships" 
                 :key="index" 
                 class="ship-item"
                 :class="{ active: currentShip.ship_id === ship.ship_id }"
                 @click="selectShip(ship)">
              <div class="ship-name">{{ ship.ship_name }}</div>
              <div class="ship-code">{{ ship.mmsi }}</div>
            </div>
          </div>
        </Card>
      </div>
      
      <!-- 右侧内容区 -->
      <div class="right-panel">
        <!-- 船舶基础信息和航线限制 -->
        <Card>
          <p slot="title">船舶信息</p>
          <div class="ship-info" v-if="currentShip.ship_id">
            <div class="info-row">
              <div class="info-item">
                <span class="label">船舶名称：</span>
                <span class="value">{{ currentShip.ship_name }}</span>
              </div>
              <div class="info-item">
                <span class="label">MMSI：</span>
                <span class="value">{{ currentShip.mmsi }}</span>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item">
                <span class="label">航线限制：</span>
                <Select v-model="currentShip.limit_level" style="width: 300px">
                  <Option v-for="item in routeOptions" :value="item.value" :key="item.value">
                    {{ item.label }}
                  </Option>
                </Select>
              </div>
            </div>
          </div>
          <div v-else class="no-ship-selected">
            请选择左侧船舶
          </div>
        </Card>

        <!-- 化学品限制 -->
        <Card style="margin-top: 16px">
          <p slot="title">化学品限制</p>
          <div v-if="currentShip.ship_id">
            <Button type="primary" @click="showChemicalModal">添加化学品限制</Button>
            <Table :columns="chemicalColumns" :data="currentShip.chemicalList" style="margin-top: 16px">
              <template slot-scope="{ row }" slot="action">
                <Button type="primary" size="small" style="margin-right: 5px" @click="editChemical(row)">编辑</Button>
                <Button type="error" size="small" @click="deleteChemical(row)">删除</Button>
              </template>
            </Table>
          </div>
        </Card>

        <!-- 客户限制 -->
        <Card style="margin-top: 16px">
          <p slot="title">客户限制</p>
          <div v-if="currentShip.ship_id">
            <Button type="primary" @click="showCustomerModal">添加客户限制</Button>
            <Table :columns="customerColumns" :data="currentShip.customerList" style="margin-top: 16px">
              <template slot-scope="{ row }" slot="action">
                <Button type="primary" size="small" style="margin-right: 5px" @click="editCustomer(row)">编辑</Button>
                <Button type="error" size="small" @click="deleteCustomer(row)">删除</Button>
              </template>
            </Table>
          </div>
        </Card>

        <!-- 底部保存按钮 -->
        <div class="bottom-actions" v-if="currentShip.ship_id">
          <Button type="primary" size="large" @click="saveSettings">保存设置</Button>
        </div>
      </div>
    </div>

    <!-- 化学品限制弹窗 -->
    <Modal v-model="chemicalModalVisible" :title="chemicalModalTitle">
      <Form ref="chemicalForm" :model="chemicalForm" :rules="chemicalRules" :label-width="100">
        <FormItem label="化学品" prop="goods_id">
          <Select filterable label-in-value v-model="chemicalForm.goods_id" placeholder="请选择化学品" @on-change="chemicalSelect" :disabled="chemicalModalTitle === '编辑化学品限制'">
            <Option v-for="item in chemicalOptions" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
        </FormItem>
        <FormItem label="最大载重吨" prop="maximum_load">
          <Input v-model="chemicalForm.maximum_load"></Input>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button @click="closeChemicalModal">取消</Button>
        <Button type="primary" @click="saveChemical">确定</Button>
      </div>
    </Modal>

    <!-- 客户限制弹窗 -->
    <Modal v-model="customerModalVisible" :title="customerModalTitle">
      <Form ref="customerForm" :model="customerForm" :rules="customerRules" :label-width="100">
        <FormItem label="客户名称" prop="company_id">
          <Select filterable label-in-value v-model="customerForm.company_id" placeholder="请选择客户" @on-change="customerSelect" :disabled="customerModalTitle === '编辑客户限制'">
            <Option v-for="item in customerOptions" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
        </FormItem>
        <FormItem label="限制类型" prop="limit_type">
          <Select v-model="customerForm.limit_type" placeholder="请选择限制类型" @on-change="customerLimitTypeChange">
            <Option v-for="item in limitTypeOptions" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
        </FormItem>
        <FormItem v-if="customerForm.limit_type === '1'" label="限制化学品" prop="limit_goods_ids">
          <Select filterable multiple v-model="customerForm.limit_goods_ids" placeholder="请选择化学品" @on-change="customerGoodsChange">
            <Option v-for="item in chemicalOptions" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
        </FormItem>
        <FormItem label="备注" prop="remarks">
          <Input v-model="customerForm.remarks" type="textarea" :rows="4" placeholder="请输入备注"></Input>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button @click="closeCustomerModal">取消</Button>
        <Button type="primary" @click="saveCustomer">确定</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import { queryBasicShipList, queryBasicCargoList, queryUsCompanyList, queryBasicShipConfigs, updateBasicShipConfigs } from '@/api/basicData.js'

export default {
  name: 'ShipRouteConfig',
  data() {
    return {
      // 船舶列表数据
      ships: [],
      currentShip: {},
      
      // 航线选项
      routeOptions: [
        { label: '不限航线', value: '0' },
        { label: '省内航线', value: '1' }
      ],
      
      // 化学品配置
      chemicalColumns: [
        { title: '化学品', key: 'goods_name' },
        { title: '最大载重吨', key: 'maximum_load' },
        { title: '操作', slot: 'action', width: 150 }
      ],
      chemicalModalVisible: false,
      chemicalModalTitle: '添加化学品限制',
      chemicalForm: {
        goods_id: '',
        goods_name: '',
        maximum_load: 0
      },
      chemicalRules: {
        goods_id: [{ required: true, message: '请选择化学品', trigger: 'change' }],
        maximum_load: [
          { required: true, message: '请输入最大载重吨', trigger: 'change' }
        ]
      },
      chemicalOptions: [],

      // 客户限制
      customerColumns: [
        { title: '客户名称', key: 'company_name' },
        { title: '限制类型', key: 'limit_type', render: (h, params) => {
            const type = params.row.limit_type === '0' ? '禁止所有货物' : '特定化学品限制'
            return h('span', type)
          }
        },
        { title: '备注', key: 'remarks' },
        { title: '操作', slot: 'action', width: 150 }
      ],
      customerModalVisible: false,
      customerModalTitle: '添加客户限制',
      customerForm: {
        company_id: '',
        company_name: '',
        limit_type: '',
        limit_goods_ids: [],
        remarks: ''
      },
      customerRules: {
        company_id: [{ required: true, message: '请选择客户', trigger: 'change' }],
        limit_type: [{ required: true, message: '请选择限制类型', trigger: 'change' }],
        limit_goods_ids: [{ required: true, type: 'array', min: 1, message: '请至少选择一种限制化学品', trigger: 'change' }]
      },
      customerOptions: [],
      limitTypeOptions: [
        { label: '禁止所有货物', value: '0' },
        { label: '特定化学品限制', value: '1' }
      ]
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      // 获取船舶数据
      queryBasicShipList({business_model: 1, is_wanbang: 0}).then(res => {
        if(res.data.Code === 10000) {
          this.ships = res.data.Result
        } else {
          this.$Message.error(res.data.Message)
        }
      })
      // 获取货品数据
      queryBasicCargoList().then(res => {
        if(res.data.Code === 10000) {
         this.chemicalOptions = res.data.Result.map(item => ({
            label: item.cargo_name,
            value: item.id
          })) 
        }
      })
      // 获取客户列表数据
      queryUsCompanyList({ ship_company_id: 2929, company_type: 2 }).then(res => {
        if(res.data.Code === 10000) {
          this.customerOptions = res.data.Result.map(item => ({
            label: item.name,
            value: item.id
          }))
        }
      })
    },
    getCurShipBaseInfo() { // 获取当前船舶基础信息
      queryBasicShipConfigs({ ship_id: this.currentShip.ship_id }).then(res => {
        if(res.data.Code === 10000) {
          this.currentShip = {
            ship_id: res.data.Result.ship_id,
            ship_name: res.data.Result.ship_name,
            mmsi: res.data.Result.mmsi,
            limit_level: res.data.Result.limit_level || '0', // 默认值为 '0'
            chemicalList: res.data.Result.shipCargoConfigs || [], 
            customerList: res.data.Result.shipCompanyLimits || []
          }
        }
      })
    },
    // 化学品限制变化
    chemicalSelect(obj) {
      if (obj) {
        this.chemicalForm.goods_name = obj.label
      }
    },
    // 客户限制类型变化
    customerLimitTypeChange() {
      if (this.customerForm.limit_type === '0') {
        this.customerForm.limit_goods_ids = []
      }
    },
    // 客户限制货物变化
    customerGoodsChange(obj) {
      this.customerForm.limit_goods_ids = obj
    },
    // 客户限制变化
    customerSelect(obj) {
      if (obj) {
        this.customerForm.company_name = obj.label
      } 
    },
    // 选择船舶
    selectShip(ship) {
      this.currentShip = {
        ...ship,
        chemicalList: ship.chemicalList || [],
        customerList: ship.customerList || [],
        limit_level: ship.limit_level || '0'
      }
      this.getCurShipBaseInfo()
    },

    // 化学品相关方法
    showChemicalModal() {
      this.chemicalModalTitle = '添加化学品限制'
      this.resetChemicalForm()
      this.chemicalModalVisible = true
    },
    editChemical(row) {
      this.chemicalModalTitle = '编辑化学品限制'
      this.chemicalForm = { ...row }
      this.chemicalModalVisible = true
    },
    resetChemicalForm() {
      this.chemicalForm = {
        goods_id: '',
        goods_name: '',
        maximum_load: 0
      }
      // 清空表单校验
      if (this.$refs.chemicalForm) {
        this.$refs.chemicalForm.resetFields()
      }
    },
    closeChemicalModal() {
      this.chemicalModalVisible = false
      this.resetChemicalForm()
    },
    deleteChemical(row) {
      this.$Modal.confirm({
        title: '确认删除',
        content: '确定要删除这条化学品限制吗？',
        onOk: () => {
          this.currentShip.chemicalList.splice(row._index, 1)
        }
      })
    },
    saveChemical() {
      this.$refs.chemicalForm.validate(valid => {
        if (valid) {
          if (this.chemicalModalTitle === '添加化学品限制') {
            this.currentShip.chemicalList.push({ ...this.chemicalForm })
          } else {
            const index = this.currentShip.chemicalList.findIndex(item => item.goods_id === this.chemicalForm.goods_id)
            this.currentShip.chemicalList.splice(index, 1, { ...this.chemicalForm })
          }
          this.closeChemicalModal()
        }
      })
    },

    // 客户限制相关方法
    showCustomerModal() {
      this.customerModalTitle = '添加客户限制'
      this.resetCustomerForm()
      this.customerModalVisible = true
    },
    editCustomer(row) {
      this.customerModalTitle = '编辑客户限制'
      // let _limit_goods_id = (row.goodsArray && row.goodsArray.length > 0) ? row.goodsArray.map(item => item.limit_goods_id) : row.limit_goods_ids ? row.limit_goods_ids.join(',') : ''
      let _limit_goods_id = row.limit_goods_ids ? row.limit_goods_ids : (row.goodsArray && row.goodsArray.length > 0) ? row.goodsArray.map(item => item.limit_goods_id) :  ''
      this.customerForm = { ...row, limit_goods_ids: _limit_goods_id }
      this.customerModalVisible = true
    },
    resetCustomerForm() {
      this.customerForm = {
        company_id: '',
        company_name: '',
        limit_type: '',
        limit_goods_ids: [],
        remarks: ''
      }
      // 清空表单校验
      if (this.$refs.customerForm) {
        this.$refs.customerForm.resetFields()
      }
    },
    closeCustomerModal() {
      this.customerModalVisible = false
      this.resetCustomerForm()
    },
    deleteCustomer(row) {
      this.$Modal.confirm({
        title: '确认删除',
        content: '确定要删除这条客户限制吗？',
        onOk: () => {
          this.currentShip.customerList.splice(row._index, 1)
        }
      })
    },
    saveCustomer() {
      this.$refs.customerForm.validate(valid => {
        if (valid) {
          if (this.customerModalTitle === '添加客户限制') {
            this.currentShip.customerList.push({ ...this.customerForm })
          } else {
            const index = this.currentShip.customerList.findIndex(item => item.company_id === this.customerForm.company_id)
            this.currentShip.customerList.splice(index, 1, { ...this.customerForm })
          }
          this.closeCustomerModal()
        }
      })
    },

    // 保存所有设置
    saveSettings() {
      let _param = {
        ship_id: this.currentShip.ship_id,
        limit_level: this.currentShip.limit_level,
        cargoConfigJsonStr: JSON.stringify(this.currentShip.chemicalList),
        companyLimitJsonStr: JSON.stringify(this.currentShip.customerList.map(item => ({
          company_id: item.company_id,
          limit_type: item.limit_type,
          limit_goods_ids: item.limit_goods_ids ? item.limit_goods_ids.join(',') : (item.goodsArray && item.goodsArray.length > 0) ? item.goodsArray.map(goods => goods.limit_goods_id).join(',') : '',
          remarks: item.remarks
        })))
      }
      updateBasicShipConfigs(_param).then(res => {
        if(res.data.Code === 10000) {
          this.$Message.success('保存成功')
          this.getCurShipBaseInfo() 
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    }
  }
}
</script>

<style scoped>
.ship-route-config {
  padding: 16px;
  min-width: 500px;
  overflow-x: auto;
}

.layout-container {
  display: flex;
  gap: 16px;
  min-width: 500px;
}

.left-panel {
  width: 280px;
  min-width: 280px;
  flex-shrink: 0;
}

.right-panel {
  flex: 1;
  min-width: 200px;
}

.ship-list {
  max-height: 500px;
  overflow-y: auto;
}

.ship-item {
  padding: 12px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  transition: all 0.3s;
}

.ship-item:hover {
  background-color: #f5f5f5;
}

.ship-item.active {
  background-color: #e6f7ff;
  border-right: 3px solid #1890ff;
}

.ship-name {
  font-weight: bold;
  margin-bottom: 4px;
}

.ship-code {
  color: #666;
  font-size: 12px;
}

.ship-info {
  padding: 16px;
}

.info-row {
  display: flex;
  margin-bottom: 16px;
}

.info-item {
  margin-right: 32px;
}

.label {
  color: #666;
  margin-right: 8px;
}

.no-ship-selected {
  text-align: center;
  color: #999;
  padding: 32px;
}

.bottom-actions {
  margin-top: 24px;
  text-align: center;
}
</style> 