<template>
  <Drawer
    v-model="formModal"
    :title="title"
    width="900"
    :mask-closable="showType !== 'create' && showType !== 'modify' && showType !== 'reset'"
    @on-visible-change="visibleChange"
    class-name="drawerCon"
  >
  <div>
    <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="105" inline class="ivu-form-inline">
      <Row>
        <h3 class="bold-font title-font" style="display:inline-block;">基础信息</h3>
        <Button v-if="showType !== 'reset'" class="import-btn" type="primary" @click="importVoyage">导入</Button>
      </Row>
      <Card>
        <Row>
          <Col span="7">
            <FormItem label="船名" prop="ship_id">
              <Select v-model="formValidate.ship_id" filterable :disabled="showType === 'reset'">
                <Option v-for="(item, index) in shipNameList" :key="index" :value="item.value">{{ item.label }}</Option>
              </Select>
            </FormItem>
          </Col>
          <Col span="7">
            <FormItem label="航次" prop="voyage_no">
              <Input v-model="formValidate.voyage_no" :disabled="showType === 'reset'">
                <span slot="prepend">V.</span>
              </Input>
            </FormItem>
          </Col>
          <Col span="7">
            <FormItem label="船上联系方式" prop="shipper_phone">
              <Input v-model="formValidate.shipper_phone"></Input>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="7">
            <FormItem label="日期" prop="voyage_date">
              <DatePicker type="date" :disabled="showType === 'reset'" :value="formValidate.voyage_date" @on-change="data=>formValidate.voyage_date=data" placeholder="请选择日期" v-if="showType!=='detail' && showType !== 'detail_plan'" style="width:100%;"></DatePicker>
              <Input v-model="formValidate.voyage_date" disabled v-else></Input>
            </FormItem>
          </Col>
          <Col span="7">
            <FormItem label="发票抬头" prop="invoice">
              <Input v-model="formValidate.invoice" :disabled="showType === 'detail' || showType === 'detail_plan'"></Input>
            </FormItem>
          </Col>
          <Col span="7">
            <FormItem label="里程" prop="mile">
              <Input v-model="formValidate.mile" :disabled="showType === 'detail' || showType === 'detail_plan'"></Input>
            </FormItem>
          </Col>
        </Row>
      </Card>
      <div class="m-top15">
        <h3 class="bold-font title-font" style="display: inline;">航线信息</h3>
        <Button class="add-btn" icon="md-add-circle" @click="handleTabsAdd" type="text" slot="extra" v-if="showType !== 'detail' && showType !== 'detail_plan' && showType !== 'reset'">新增货主</Button>
      </div>
      <Card class="m-top15" v-for="(tab, index) in formValidate.cargoParam" :key="index">
        <div class="m-b9">
          <h4 class="title-font" style="display: inline;">货品<span class="voyage-num">{{(index + 1)}}</span></h4>
          <Button v-if="index > 0" class="close-btn" @click="beforeRemove(index)" icon="md-close" size="large" type="text"></Button>
        </div>
        <Row>
          <Col span="7">
            <FormItem label="货主" :prop="'cargoParam.' + index  + '.cargo_company_id'" :rules="{required: true, message: '此处不能为空', trigger: 'change'}">
              <Select v-model="tab.cargo_company_id" label-in-value filterable> <!-- :disabled="showType === 'reset'" 2022.04.11不再限制 -->
                <Option v-for="(item, index) in customerList" :key="index" :value="item.customer_company_id">{{ item.customer_company_name }}</Option>
              </Select>
              <Icon class="refresh_btn" :class="isLoadCustomer ? 'spin-icon-load' : ''" type="md-refresh" @click="queryCustomerList" />
            </FormItem>
          </Col>
          <Col span="7">
            <FormItem label="货品" :prop="'cargoParam.' + index + '.goods_id'" :rules="{required: true, message: '此处不能为空', trigger: 'change'}">
              <Select v-model="tab.goods_id" label-in-value filterable> <!-- :disabled="showType === 'reset'" 2022.04.11不再限制 -->
                <Option v-for="(item, index) in goodsList" :key="index" :value="item.id">{{ item.cargo_name }}</Option>
              </Select>
            </FormItem>
          </Col>
          <Col span="7">
            <FormItem class="my-item-required"  label="允许损耗(‰)" :prop="'cargoParam.' + index + '.allowable_loss'" :rules="{required: false, message: '请填写数字', pattern: /^[0-9]+([.]{1}[0-9]+){0,1}$/, trigger: 'blur'}">
              <Input v-model="tab.allowable_loss" :disabled="showType === 'detail' || showType === 'detail_plan'"></Input>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="7">
            <FormItem label="计划受载日期"  class="my-item-required" :prop="'cargoParam.' + index + '.start_plan_date'"> <!-- :rules="{required: true, message: '此处不能为空', trigger: 'change'}" -->
              <DatePicker type='date' format="yyyy-MM-dd" placeholder="选择日期" @on-change="data=>tab.start_plan_date=data"
                          :value="tab.start_plan_date" placement="top"></DatePicker>
            </FormItem>
          </Col>
          <Col span="5">
            <FormItem class="end-date-before" label="-" :label-width="25" :prop="'cargoParam.' + index + '.end_plan_date'"> <!-- :rules="{required: true, message: '此处不能为空', trigger: 'change'}" -->
              <DatePicker type='date' format="yyyy-MM-dd" placeholder="选择日期" @on-change="data=>tab.end_plan_date=data"
                          :value="tab.end_plan_date" placement="top"></DatePicker>
            </FormItem>
          </Col>
          <Col span="7">
            <FormItem label="约定滞期时间（h）" :label-width="155" :prop="'cargoParam.' + index + '.promise_delayed_time'"> <!-- :rules="{required: true, message: '此处不能为空', trigger: 'change'}"-->
              <Input type='number' v-model='tab.promise_delayed_time'></Input>
            </FormItem>
          </Col>
        </Row>
        <!-- 装港开始 -->
        <div v-for="(loadPort, idx) in tab.loadPortArr" :key="'load' + idx">
          <div class="port-title">
            <span>装港 <em class="port-title-name">{{ idx + 1 }}</em></span>
            <Button v-if="idx === 0 && showType !== 'reset'" type="text" icon="md-add-circle" class="add-btn" @click="addPort(index, 'load')">新增装港</Button>
            <Button v-if="idx > 0 && showType !== 'reset'" type="text" icon="md-remove-circle" class="remove-btn" @click="removePort(index, idx, 'load')">删除装港</Button>
          </div>
          <Row>
            <Col span="7">
              <FormItem label="装货港" :prop="'cargoParam.' + index + '.loadPortArr.' + idx + '.port_id'" :rules="{required: true, message: '此处不能为空', trigger: 'change'}">
                <Select v-model="loadPort.port_id" :disabled="loadPort.is_edit === '0'" filterable @on-change="portChange(index, idx, 'load')">
                  <Option v-for="(item, index) in loadPortList" :key="index" :value="item.id">{{ item.port_name }}</Option>
                </Select>
              </FormItem>
            </Col>
            <Col span="7">
              <FormItem class="my-item-required" label="码头" :prop="'cargoParam.' + index + '.loadPortArr.' + idx + '.wharf_id'"> <!-- :rules="{required: true, message: '此处不能为空', trigger: 'change'}" -->
                <Select v-model="loadPort.wharf_id" :disabled="loadPort.is_edit === '0'" filterable placeholder="装货码头">
                  <Option v-for="(item, idex) in loadWharfList[index][idx]" :key="idex" :value="item.id">{{ item.terminal_name }}</Option>
                </Select>
                <Icon class="refresh_btn" :class="isLoadPort ? 'spin-icon-load' : ''" type="md-refresh" @click="getWharfList(index, idx, 'load')"/>
              </FormItem>
            </Col>
            <!-- <Col span="7">
              <FormItem label="计划装港日期" :prop="'cargoParam.' + index + '.loadPortArr.' + idx + '.start_plan_date'" :rules="{required: true, message: '此处不能为空', trigger: 'change'}">
                <DatePicker type="date" :value="loadPort.start_plan_date" @on-change="data=>loadPort.start_plan_date=data" placeholder="请选择日期" style="width:100%;"></DatePicker>
              </FormItem>
            </Col> -->
          </Row>
          <Row>
            <Col span="7">
              <FormItem label="代理公司" :prop="'cargoParam.' + index + '.loadPortArr.' + idx + '.agency_company_id'">
                <Select v-model="loadPort.agency_company_id" filterable @on-change="agentChange(index, idx, 'load')">
                  <Option v-for="(item, index) in agentList" :key="index" :value="item.customer_company_id">{{ item.customer_company_name }}</Option>
                </Select>
              </FormItem>
            </Col>
            <Col span="7">
              <FormItem label="代理联系人" :prop="'cargoParam.' + index + '.loadPortArr.' + idx + '.agency_contact_id'">
                <Select v-model="loadPort.agency_contact_id" filterable @on-change="agentMemChange(loadPort.agency_contact_id, index, idx, 'load')">
                  <Option v-for="(item, idex) in loadAgentMemberList[index][idx]" :key="idex" :value="item.id">{{ item.customer_name }}</Option>
                </Select>
              </FormItem>
            </Col>
            <Col span="7">
              <FormItem label="代理联系电话" :prop="'cargoParam.' + index + '.loadPortArr.' + idx + '.agency_contact_tel'">
                <span>{{ loadAgentMemberList[index][idx] ? loadAgentMemberList[index][idx].agency_contact_tel : '' }}</span>
                <Input type='text' v-model="loadPort.agency_contact_tel" readonly></Input>
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="7">
              <FormItem class="my-item-required" label="计量方式" :prop="'cargoParam.' + index + '.loadPortArr.' + idx + '.unit'"> <!-- :rules="{required: true, message: '此处不能为空', trigger: 'change'}" -->
                <Select v-model="loadPort.unit"> <!-- :disabled="showType === 'reset'" 2022.04.11不再限制 -->
                  <Option value="1" key="1">装港流量计</Option>
                  <Option value="2" key="2">装港商检船板量</Option>
                  <Option value="3" key="3">装港岸罐量</Option>
                </Select>
              </FormItem>
            </Col>
            <Col span="7">
              <FormItem class="my-item-required" label="货量(T)" :prop="'cargoParam.' + index + '.loadPortArr.' + idx + '.amount'"> <!-- :rules="{required: true, message: '此处不能为空', trigger: 'change'}" -->
                <Input type='number' v-model='loadPort.amount'></Input>
              </FormItem>
            </Col>
          </Row>
        </div>
        <!-- 装港结束 -->

        <!-- 卸港开始 -->
        <div v-for="(unloadPort, idx) in tab.unloadPortArr" :key="'unload' + idx">
          <div class="port-title">
            <span>卸港 <em class="port-title-name">{{ idx + 1 }}</em></span>
            <Button v-if="idx === 0 && showType !== 'reset'" type="text" icon="md-add-circle" class="add-btn" @click="addPort(index, 'unload')">新增卸港</Button>
            <Button v-if="idx > 0 && showType !== 'reset'" type="text" icon="md-remove-circle" class="remove-btn" @click="removePort(index, idx, 'unload')">删除卸港</Button>
          </div>
          <Row>
            <Col span="7">
              <FormItem label="卸货港" :prop="'cargoParam.' + index + '.unloadPortArr.' + idx + '.port_id'" :rules="{required: true, message: '此处不能为空', trigger: 'change'}">
                <Select v-model="unloadPort.port_id" :disabled="unloadPort.is_edit === '0'" filterable @on-change="portChange(index, idx, 'unload')">
                  <Option v-for="(item, index) in unloadPortList" :key="'unloadport' + index" :value="item.id">{{ item.port_name }}</Option>
                </Select>
              </FormItem>
            </Col>
            <Col span="7">
              <FormItem class="my-item-required" label="码头" :prop="'cargoParam.' + index + '.unloadPortArr.' + idx + '.wharf_id'"> <!-- :rules="{required: true, message: '此处不能为空', trigger: 'change'}" -->
                <Select v-model="unloadPort.wharf_id" :disabled="unloadPort.is_edit === '0'" filterable placeholder="卸货码头">
                  <Option v-for="(item, idex) in unloadWharfList[index][idx]" :key="'unloadwharf' + idex" :value="item.id">{{ item.terminal_name }}</Option>
                </Select>
                <Icon class="refresh_btn" :class="isLoadPort ? 'spin-icon-load' : ''" type="md-refresh"  @click="getWharfList(index, idx, 'unload')" />
              </FormItem>
            </Col>
            <!-- <Col span="7">
              <FormItem label="计划卸港日期" :prop="'cargoParam.' + index + '.unloadPortArr.' + idx + '.end_plan_date'" :rules="{required: true, message: '此处不能为空', trigger: 'change'}">
                 :value="unloadPort.end_plan_date" @on-change="data=>unloadPort.end_plan_date=data" placeholder="请选择日期" style="width:100%;"></DatePicker>
              </FormItem>
            </Col> -->
          </Row>
          <Row>
            <Col span="7">
              <FormItem label="代理公司" :prop="'cargoParam.' + index + '.unloadPortArr.' + idx + '.agency_company_id'">
                <Select v-model="unloadPort.agency_company_id" filterable @on-change="agentChange(index, idx, 'unload')">
                  <Option v-for="(item, index) in agentList" :key="'agent' + index" :value="item.customer_company_id">{{ item.customer_company_name }}</Option>
                </Select>
              </FormItem>
            </Col>
            <Col span="7">
              <FormItem label="代理联系人" :prop="'cargoParam.' + index + '.unloadPortArr.' + idx + '.agency_contact_id'">
                <Select v-model="unloadPort.agency_contact_id" filterable @on-change="agentMemChange(unloadPort.agency_contact_id, index, idx, 'unload')">
                  <Option v-for="(item, idex) in unloadAgentMemberList[index][idx]" :key="'member' + idex" :value="item.id">{{ item.customer_name }}</Option>
                </Select>
              </FormItem>
            </Col>
            <Col span="7">
              <FormItem label="代理联系电话" :prop="'cargoParam.' + index + '.unloadPortArr.' + idx + '.agency_contact_tel'">
                <Input type='text' v-model='unloadPort.agency_contact_tel' readonly></Input>
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="7">
              <FormItem class="my-item-required" label="计量方式" :prop="'cargoParam.' + index + '.unloadPortArr.' + idx + '.unit'"> <!-- :rules="{required: true, message: '此处不能为空', trigger: 'change'}" -->
                <Select v-model="unloadPort.unit"><!-- :disabled="showType === 'reset'" 2022.04.11不再限制 -->
                  <Option value="4" key="4">卸港商检船板量</Option>
                  <Option value="5" key="5">卸港岸罐量</Option>
                </Select>
              </FormItem>
            </Col>
            <Col span="7">
              <FormItem class="my-item-required" label="货量(T)" :prop="'cargoParam.' + index + '.unloadPortArr.' + idx + '.amount'"> <!--  :rules="{required: true, message: '此处不能为空', trigger: 'change'}" -->
                <Input type='number' v-model='unloadPort.amount'></Input>
              </FormItem>
            </Col>
          </Row>
        </div>
        <!-- 卸港结束 -->
      </Card>
      <div class="nodatainfo" v-show="showNoData">暂无数据</div><br>
      <h3 class="bold-font title-font">其他信息</h3>
      <Card class="textareacaed">
        <Row>
          <Col>
            <FormItem label="洗舱的具体要求">
              <Input type="textarea" v-model="formValidate.cabin_wash_context" :autosize="true" :disabled="showType === 'detail' || showType === 'detail_plan'"></Input>
            </FormItem>
          </Col>
          <Col>
            <FormItem label="港口调度/发货人/收货人/代理人的相关资料、联系方式">
              <Input type="textarea" v-model="formValidate.port_context" :autosize="true" :disabled="showType === 'detail' || showType === 'detail_plan'"></Input>
            </FormItem>
          </Col>
          <Col>
            <FormItem label="航次港口、货物等应注意的问题">
              <Input type="textarea" v-model="formValidate.voyage_context" :autosize="true" :disabled="showType === 'detail' || showType === 'detail_plan'"></Input>
            </FormItem>
          </Col>
          <Col>
            <FormItem label="航次合同相关事宜">
              <Input type="textarea" v-model="formValidate.contract_context" :autosize="true" :disabled="showType === 'detail' || showType === 'detail_plan'"></Input>
            </FormItem>
          </Col>
        </Row>
      </Card>
    </Form>
    <div class="demo-drawer-footer">
      <Button type="primary" @click="handleCancel">取消</Button>
      <Button v-if="showType==='create'" type="primary" @click="saveModalData">保存</Button>
      <Button v-if="showType==='modify' || showType==='reset'" type="primary" @click="updateData">{{ showType === 'reset' ? '变更并发送' : '保存' }}</Button>
    </div>
  </div>
  <!-- 导入弹窗 -->
  <voyageViewModal :modalData="voyageModalData" @callback="voyageViewBack"></voyageViewModal>
  <!-- 新增确认发送弹窗 -->
  <Modal
    v-model="addModal"
    width="350"
    title="提示">
    <span>是否发送到船端，发送后将无法修改航次命令内容！</span>
    <div slot="footer">
      <Button type="primary" @click="createData(1)">发送</Button>
      <Button type="primary" @click="createData(2)">不发送</Button>
    </div>
  </Modal>
  </Drawer>
</template>
<script>
import { validatePositiveInt, validateMobilePhone } from '@/libs/iViewValidate'
import { queryCustomerList, queryCustomerMemberList, queryPortList, queryBasicCargoList, queryWharfList } from '@/api/basicData'
import voyageViewModal from './voyageViewModal'
import API from '@/api/voyageManage/planVoyage'
import curAPI from '@/api/voyageManage/curVoyage'

export default {
  components: {
    voyageViewModal
  },
  data () {
    return {
      isLoadCustomer: false, // 货主数据加载状态
      isLoadPort: false, // 港口数据加载状态
      showType: 'create', // 新增或者修改类型
      formModal: false, // 模态框显示状态
      addModal: false, // 新增保存发送弹窗显示状态
      dialogType: null,
      detailId: '', // 查询详情id
      title: '', // 模态框标题
      ship_name: '', // 船名
      showNoData: false, // 没有相关信息
      activeTab: '货品1', // 航线信息默认显示
      tabAddNumber: 1,
      voyageModalData: { // 导入航次列表入参
        modal: false,
        title: ''
      },
      shipNameList: [], // 船名
      loadPortList: [], // 装货港
      loadWharfList: [[]], // 装货码头
      unloadPortList: [], // 卸货港
      unloadWharfList: [[]], // 卸货码头
      customerList: [], // 货主
      goodsList: [], // 货品
      agentList: [], // 代理公司
      loadAgentMemberList: [[]], // 装港代理公司联系人
      unloadAgentMemberList: [[]], // 卸港代理公司联系人
      formValidate: {
        ship_id: '', // 船名id
        voyage_no: '', // 航次
        shipper_phone: '', // 船上联系方式
        voyage_date: '', // 航次日期
        cabin_wash_context: '',
        contract_context: '',
        port_context: '',
        voyage_context: '',
        cargoParam: [{ // 航线信息
          cargo_company_id: '', // 货主id
          goods_id: '', // 货品id
          amounts: '', // 货品总数量
          allowable_loss: '', // 允许损耗值
          start_plan_date: '', // 计划受载开始时间
          end_plan_date: '', // 计划受载结束时间
          promise_delayed_time: '', // 滞期时间
          loadPortArr: [
            { // 默认一条装港数据
              port_type: 1, // 港口类型（1：装 2：卸）
              port_id: '', // 港口id
              wharf_id: '', // 码头id
              agency_company_id: '', // 代理公司id
              agency_contact_id: '', // 代理公司联系人id
              agency_contact_tel: '', // 代理公司联系电话
              amount: '', // 数量
              unit: '' // 计量单位（1.装港流量计2.装港商检船板3.装港岸罐量 4.卸货商检船板量、5.卸货港岸罐量）
            }
          ],
          unloadPortArr: [
            { // 默认一条装卸数据
              port_type: 2, // 港口类型（1：装 2：卸）
              port_id: '', // 港口id
              wharf_id: '', // 码头id
              agency_company_id: '', // 代理公司id
              agency_contact_id: '', // 代理公司联系人id
              agency_contact_tel: '', // 代理公司联系电话
              amount: '', // 数量
              unit: '' // 计量单位（1.装港流量计2.装港商检船板3.装港岸罐量 4.卸货商检船板量、5.卸货港岸罐量）
            }
          ]
        }]
      },
      ruleValidate: {
        ship_id: [
          { required: true, message: '此处不能为空', trigger: 'change' }
        ],
        voyage_no: [
          { required: true, message: '此处不能为空', trigger: 'change' },
          { required: false, validator: validatePositiveInt, trigger: 'blur' }
        ],
        voyage_date: [
          { required: true, message: '此处不能为空', trigger: 'change' }
        ],
        shipper_phone: [
          { required: false, validator: validateMobilePhone, trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    // 显隐操作
    visibleChange (val) {
      if (val) {
        if (localStorage.menuSort && JSON.parse(localStorage.menuSort).length !== 0) {
          JSON.parse(localStorage.menuSort).map(item => {
            this.subMenuDisabled.push(item.menu_sort)
          })
          this.subMenuDisabled = this.subMenuDisabled.join(',')
        }
        // 获取船名
        if (window.localStorage.shipNameList) {
          this.shipNameList = []
          let shipList = JSON.parse(window.localStorage.bussiShipList)
          shipList.map(item => {
            this.shipNameList.push({
              value: item.ship_id,
              label: item.ship_name
            })
          })
        } else {
          this.shipNameList = []
        }
        this.queryCustomerList()
        this.queryPortList()
        // 获取代理公司 公司类型（1、船东；2、货主；3、代理）
        queryCustomerList({ company_type: 3 }).then(res => {
          this.agentList = []
          if (res.data.Code === 10000) {
            res.data.Result.map(item => {
              this.agentList.push(item)
            })
          }
        })
        // 获取货品
        queryBasicCargoList(this.goodsListQuery).then(res => {
          if (res.data.Code === 10000) {
            res.data.Result.map(item => {
              this.goodsList.push(item)
            })
          }
        })
        if (this.showType !== 'create' && this.detailId !== '') {
          this.resetDetailObj()
        }
      } else {
        this.handleCancel()
      }
    },
    // 获取货主 公司类型（1、船东；2、货主；3、代理）
    queryCustomerList () {
      this.isLoadCustomer = true
      queryCustomerList({ company_type: 2 }).then(res => {
        if (res.data.Code === 10000) {
          this.isLoadCustomer = false
          res.data.Result.map(item => {
            this.customerList.push(item)
          })
        }
      })
    },
    // 获取港口
    queryPortList () {
      this.isLoadPort = true
      queryPortList(this.loadPortListQuery).then(res => {
        if (res.data.Code === 10000) {
          this.isLoadPort = false
          res.data.Result.map(item => {
            this.loadPortList.push(item)
            this.unloadPortList.push(item)
          })
        }
      })
    },
    // 导入航次信息
    importVoyage () {
      this.voyageModalData = {
        modal: true,
        title: '导入航次信息'
      }
    },
    // 导入航次回显
    voyageViewBack (d) {
      this.echoVoyageDetail(d)
    },
    // 取消
    handleCancel () {
      this.formModal = false
      this.resetFormValid()
    },
    // 港头改变调用
    portChange (index, idx, str) { // index最外层index, 装/卸港index,str装/卸港区别字符
      if (!this.formModal) return
      this.getWharfList(index, idx, str)
    },
    // 代理公司改变调用
    agentChange (index, idx, str) { // index最外层index, 装/卸港index,str装/卸港区别字符
      if (!this.formModal) return
      this.getAgentList(index, idx, str)
    },
    // 代理人改变调用并显示电话
    agentMemChange (val, index, idx, str) {
      if (!this.formModal) return
      if (str === 'load') {
        if (this.loadAgentMemberList[index][idx].length > 0) {
          this.loadAgentMemberList[index][idx].forEach(item => {
            if (item.id === val) {
              this.formValidate.cargoParam[index].loadPortArr[idx].agency_contact_tel = item.company_tel
            }
          })
        }
      } else {
        if (this.unloadAgentMemberList[index][idx].length > 0) {
          this.unloadAgentMemberList[index][idx].forEach(item => {
            if (item.id === val) {
              this.formValidate.cargoParam[index].unloadPortArr[idx].agency_contact_tel = item.company_tel
            }
          })
        }
      }
    },
    resetDetailObj () { // 引入数据重组(接口调用)
      // 计划航次修改获取详情
      if (this.showType === 'modify') {
        API.getVoyagePlanById({ id: this.detailId }).then(res => {
          if (res.data.Code === 10000) {
            this.echoVoyageDetail(res.data.Result[0])
          }
        })
      }
      // 航次变更获取详情
      if (this.showType === 'reset') {
        curAPI.getVoyageDetailById({ id: this.detailId }).then(res => {
          if (res.data.Code === 10000) {
            this.echoVoyageDetail(res.data.Result[0])
          }
        })
      }
    },
    // 回显数据整合
    echoVoyageDetail (list) {
      this.formValidate = list
      if (this.showType !== 'modify' && this.showType !== 'reset') {
        this.formValidate.ship_id = ''
        this.formValidate.ship_name = '' // 不导入船名
      }
      if (list.voyage_no && list.voyage_no.indexOf('V.') === 0) { // 航次号截取
        this.formValidate.voyage_no = list.voyage_no.split('.')[1]
      }
      this.formValidate.cargoParam = list.cargoParam
      if (list.cargoParam.length > 0) {
        list.cargoParam.forEach((item, index) => {
          let loadIdx = 0 // 初始化装港idx
          let unloadIdx = 0 // 初始化卸港idx
          this.formValidate.cargoParam[index].loadPortArr = []
          this.formValidate.cargoParam[index].unloadPortArr = []
          item.portParam.forEach((list, idx) => {
            if (list.port_type === '1') {
              if (index > 0) {
                this.loadWharfList.push([]) // 码头列表初始化,否则遍历不了报错
                this.loadAgentMemberList.push([]) // 代理成员列表初始化,否则遍历不了报错
              }
              this.formValidate.cargoParam[index].loadPortArr.push(list)
              this.getAgentList(index, loadIdx, 'load')
              this.getWharfList(index, loadIdx, 'load')
              loadIdx += 1
            } else {
              if (index > 0) {
                this.unloadWharfList.push([]) // 码头列表初始化,否则遍历不了报错
                this.unloadAgentMemberList.push([]) // 代理成员列表初始化,否则遍历不了报错
              }
              this.formValidate.cargoParam[index].unloadPortArr.push(list)
              this.getAgentList(index, unloadIdx, 'unload')
              this.getWharfList(index, unloadIdx, 'unload')
              unloadIdx += 1
            }
          })
        })
      }
    },
    // 获取代理成员列表
    getAgentList (index, idx, str) { // index最外层index, 装/卸港index,str装/卸港区别字符
      if (!this.formModal) return
      let curAgentId
      if (str === 'load') {
        curAgentId = this.formValidate.cargoParam[index].loadPortArr[idx].agency_company_id ? this.formValidate.cargoParam[index].loadPortArr[idx].agency_company_id : ''
      } else {
        curAgentId = this.formValidate.cargoParam[index].unloadPortArr[idx].agency_company_id ? this.formValidate.cargoParam[index].unloadPortArr[idx].agency_company_id : ''
      }
      if (!curAgentId || curAgentId === '') return
      queryCustomerMemberList({ customer_company_id: curAgentId }).then(res => {
        if (res.data.Code === 10000) {
          if (str === 'load') {
            if (index > 0) {
              this.loadAgentMemberList.push([]) // 如果超过两个货主，再加一个空数组存放
            }
            this.$set(this.loadAgentMemberList[index], idx, res.data.Result)
          } else {
            if (index > 0) {
              this.unloadAgentMemberList.push([]) // 如果超过两个货主，再加一个空数组存放
            }
            this.$set(this.unloadAgentMemberList[index], idx, res.data.Result)
          }
        }
      })
    },
    // 获取对应港口码头数据
    getWharfList (index, idx, str) { // index最外层index, 装/卸港index,str装/卸港区别字符
      if (!this.formModal) return
      let curPortId = str === 'load' ? this.formValidate.cargoParam[index].loadPortArr[idx].port_id : this.formValidate.cargoParam[index].unloadPortArr[idx].port_id
      if (!curPortId || curPortId === '') return
      queryWharfList({ port_id: curPortId }).then(res => {
        if (res.data.Code === 10000) {
          if (str === 'load') {
            if (index > 0) {
              this.loadWharfList.push([]) // 如果超过两个货主，再加一个空数组存放
            }
            this.$set(this.loadWharfList[index], idx, res.data.Result)
          } else {
            if (index > 0) {
              this.unloadWharfList.push([]) // 如果超过两个货主，再加一个空数组存放
            }
            this.$set(this.unloadWharfList[index], idx, res.data.Result)
          }
        }
      })
    },
    // 新增港口 index 位置  str 装/卸港标识
    addPort (index, str) {
      if (str === 'load') {
        let curObj = {
          port_type: 1, // 港口类型（1：装 2：卸）
          port_id: '', // 港口id
          wharf_id: '', // 码头id
          agency_company_id: '', // 代理公司id
          agency_contact_id: '', // 代理公司联系人id
          agency_contact_tel: '', // 代理公司联系电话
          amount: '', // 数量
          unit: '' // 计量单位（1.装港流量计2.装港商检船板3.装港岸罐量 4.卸货商检船板量、5.卸货港岸罐量）
        }
        this.formValidate.cargoParam[index].loadPortArr.push(curObj)
        this.formValidate.cargoParam[index].portParam.push(curObj) // 双向绑定，便于绑定校验
        this.loadWharfList[index].push([]) // 码头列表初始化,便于码头操作
        this.loadAgentMemberList[index].push([]) // 代理成员列表初始化,便于代理成员操作
      } else {
        let curObj = {
          port_type: 2, // 港口类型（1：装 2：卸）
          port_id: '', // 港口id
          wharf_id: '', // 码头id
          agency_company_id: '', // 代理公司id
          agency_contact_id: '', // 代理公司联系人id
          agency_contact_tel: '', // 代理公司联系电话
          amount: '', // 数量
          unit: '' // 计量单位（1.装港流量计2.装港商检船板3.装港岸罐量 4.卸货商检船板量、5.卸货港岸罐量）
        }
        this.formValidate.cargoParam[index].unloadPortArr.push(curObj)
        this.formValidate.cargoParam[index].portParam.push(curObj) // 双向绑定，便于绑定校验
        this.unloadWharfList[index].push([]) // 码头列表初始化,便于码头操作
        this.unloadAgentMemberList[index].push([]) // 代理成员列表初始化,便于代理成员操作
      }
    },
    // 移除港口
    removePort (index, idx, str) {
      if (str === 'load') {
        this.formValidate.cargoParam[index].loadPortArr.splice(idx, 1)
        this.loadWharfList[index].splice(idx, 1) // 码头同步数据,便于码头操作
      } else {
        this.formValidate.cargoParam[index].unloadPortArr.splice(idx, 1)
        this.unloadWharfList[index].splice(idx, 1) // 码头同步数据,便于码头操作
      }
    },
    // 添加货主货品
    handleTabsAdd () {
      let detailJsonData = {
        cargo_company_id: '', // 货主id
        goods_id: '', // 货品id
        amounts: '', // 货品总数量
        allowable_loss: '', // 允许损耗值
        start_plan_date: '', // 计划受载开始时间
        end_plan_date: '', // 计划受载结束时间
        promise_delayed_time: '', // 滞期时间
        loadPortArr: [
          { // 默认一条装港数据
            port_type: 1, // 港口类型（1：装 2：卸）
            port_id: '', // 港口id
            wharf_id: '', // 码头id
            agency_company_id: '', // 代理公司id
            agency_contact_id: '', // 代理公司联系人id
            agency_contact_tel: '', // 代理公司联系电话
            amount: '', // 数量
            unit: '' // 计量单位（1.装港流量计2.装港商检船板3.装港岸罐量 4.卸货商检船板量、5.卸货港岸罐量）
          }
        ],
        unloadPortArr: [
          { // 默认一条装卸数据
            port_type: 2, // 港口类型（1：装 2：卸）
            port_id: '', // 港口id
            wharf_id: '', // 码头id
            agency_company_id: '', // 代理公司id
            agency_contact_id: '', // 代理公司联系人id
            agency_contact_tel: '', // 代理公司联系电话
            amount: '', // 数量
            unit: '' // 计量单位（1.装港流量计2.装港商检船板3.装港岸罐量 4.卸货商检船板量、5.卸货港岸罐量）
          }
        ]
      }
      this.tabAddNumber = this.tabAddNumber + 1
      this.activeTab = `货品${this.tabAddNumber}`
      this.loadWharfList.push([]) // 码头列表初始化,否则遍历不了报错
      this.loadAgentMemberList.push([]) // 代理成员列表初始化,否则遍历不了报错
      this.unloadWharfList.push([]) // 码头列表初始化,否则遍历不了报错
      this.unloadAgentMemberList.push([]) // 代理成员列表初始化,否则遍历不了报错
      this.formValidate.cargoParam.push(detailJsonData)
    },
    // 移除货品
    beforeRemove (index) {
      if (this.formValidate.cargoParam.length > 1) {
        this.$Modal.confirm({
          title: '提示',
          content: '<p>是否删除本条信息,删除后将无法恢复？</p>',
          loading: true,
          onOk: () => {
            this.tabAddNumber = this.tabAddNumber - 1
            this.activeTab = `货品${this.tabAddNumber}`
            this.$Modal.remove()
            this.formValidate.cargoParam.splice(index, 1)
          }
        })
      } else {
        this.$Message.warning('请保留至少一条航线信息')
      }
    },
    // 弹起发送与不发送窗口
    saveModalData () {
      this.$refs['formValidate'].validate((valid) => {
        if (valid) {
          this.addModal = true
        }
      })
    },
    // 新增保存
    createData (type) {
      // 计算装港总货量
      this.formValidate.cargoParam.map(item => {
        item.amounts = item.loadPortArr.reduce((acc, cur) => {
          return acc + parseFloat(cur.amount)
        }, 0) || 0
        item.portParam = [...item.loadPortArr, ...item.unloadPortArr]
      })
      let data = { ...this.formValidate }
      let testData = data.cargoParam.map(item => {
        item.portParam = JSON.stringify(item.portParam)
        let backItem = {
          'cargo_company_id': item.cargo_company_id || '',
          'goods_id': item.goods_id || '',
          'amounts': item.amounts || '',
          'allowable_loss': item.allowable_loss || '',
          'start_plan_date': item.start_plan_date || '',
          'end_plan_date': item.end_plan_date || '',
          'promise_delayed_time': item.promise_delayed_time || '',
          'portParam': item.portParam || ''
        }
        return JSON.stringify(backItem)
      })
      data.send_type = type
      Object.assign(data, { 'cargoParam': `[${testData}]` })
      data.voyage_no = 'V.' + data.voyage_no
      API.addVoyagePlan(data).then(res => {
        this.addModal = false
        if (res.data.Code === 10000) {
          this.handleCancel()
          this.$emit('addBack', 'planVoyage')
          this.$Message.success(res.data.Message)
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 编辑保存
    updateData () {
      this.$refs['formValidate'].validate((valid) => {
        if (valid) {
          // 计算装港总货量
          this.formValidate.cargoParam.map(item => {
            item.amounts = item.loadPortArr.reduce((acc, cur) => {
              return acc + parseFloat(cur.amount)
            }, 0) || 0
            item.portParam = [...item.loadPortArr, ...item.unloadPortArr]
          })
          let data = { ...this.formValidate }
          data.send_type = 2 // 发送类型（1：发送，2不发送）
          let testData = data.cargoParam.map(item => {
            item.portParam = JSON.stringify(item.portParam)
            let backItem = {
              'cargo_company_id': item.cargo_company_id || '',
              'goods_id': item.goods_id || '',
              'amounts': item.amounts || '',
              'allowable_loss': item.allowable_loss || '',
              'start_plan_date': item.start_plan_date || '',
              'end_plan_date': item.end_plan_date || '',
              'promise_delayed_time': item.promise_delayed_time || '', // 滞期时间
              'id': item.id || '',
              'portParam': item.portParam || ''
            }
            return JSON.stringify(backItem)
          })
          Object.assign(data, { 'cargoParam': `[${testData}]` })
          data.voyage_no = 'V.' + data.voyage_no
          if (this.showType === 'modify') {
            API.updateVoyagePlan(data).then(res => {
              if (res.data.Code === 10000) {
                this.handleCancel()
                this.$emit('addBack', 'planVoyage')
                this.$Message.success(res.data.Message)
              } else {
                this.$Message.error(res.data.Message)
              }
            })
          }
          if (this.showType === 'reset') {
            curAPI.changeVoyage(data).then(res => {
              if (res.data.Code === 10000) {
                this.handleCancel()
                this.$emit('addBack', 'curVoyage')
                this.$Message.success(res.data.Message)
              } else {
                this.$Message.error(res.data.Message)
              }
            })
          }
        }
      })
    },
    resetFormValid () {
      this.formValidate = {
        ship_id: '', // 船名id
        voyage_no: '', // 航次
        shipper_phone: '', // 船上联系方式
        voyage_date: '', // 航次日期
        cabin_wash_context: '',
        contract_context: '',
        port_context: '',
        voyage_context: '',
        cargoParam: [{ // 航线信息
          cargo_company_id: '', // 货主id
          goods_id: '', // 货品id
          amounts: '', // 货品总数量
          allowable_loss: '', // 允许损耗值
          start_plan_date: '', // 计划受载开始时间
          end_plan_date: '', // 计划受载结束时间
          promise_delayed_time: '', // 滞期时间
          loadPortArr: [
            { // 默认一条装港数据
              port_type: 1, // 港口类型（1：装 2：卸）
              port_id: '', // 港口id
              wharf_id: '', // 码头id
              agency_company_id: '', // 代理公司id
              agency_contact_id: '', // 代理公司联系人id
              agency_contact_tel: '', // 代理公司联系电话
              amount: '', // 数量
              unit: '' // 计量单位（1.装港流量计2.装港商检船板3.装港岸罐量 4.卸货商检船板量、5.卸货港岸罐量）
            }
          ],
          unloadPortArr: [
            { // 默认一条装卸数据
              port_type: 2, // 港口类型（1：装 2：卸）
              port_id: '', // 港口id
              wharf_id: '', // 码头id
              agency_company_id: '', // 代理公司id
              agency_contact_id: '', // 代理公司联系人id
              agency_contact_tel: '', // 代理公司联系电话
              amount: '', // 数量
              unit: '' // 计量单位（1.装港流量计2.装港商检船板3.装港岸罐量 4.卸货商检船板量、5.卸货港岸罐量）
            }
          ]
        }]
      }
      this.$refs['formValidate'].resetFields()
    }
  }
}
</script>
<style scoped lang="less">
.demo-drawer-footer {
  padding: 10px 16px;
  margin-top: 10px;
  text-align: right;
  background: #fff;
  button {
    margin-left: 10px;
  }
}
.m-top15 {
  margin-top: 15px;
}
.m-b9 {
  margin-bottom: 9px;
}
.add-btn {
  float: right;
  color: #185BDD;
}
.import-btn {
  float: right;
}
.remove-btn {
  float: right;
  color: #f16643;
}
.close-btn {
  font-size: 28px;
  right: 2px;
  position: absolute;
  top: 0;
  color: #185BDD;
}
.voyage-num {
  width: 16px;
  height: 16px;
  margin-left: 1px;
  border-radius: 50%;
  background: #185BDD;
  color: #fff;
  font-size: 12px;
  text-align: center;
  display: inline-block;
}
.nodatainfo {
  padding: 40px;
  margin-top: 15px;
  font-size: 14px;
  text-align: center;
  background-color: #fff;
}
.ivu-form-inline .ivu-form-item {
  width: 100%;
}
.port-title {
  margin: 0 0 20px 50px;
  font-size: 14px;
  font-weight: bold;
  color: #4A4A4A;
  &-name {
    border: 1px solid #4A4A4A;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    line-height: 18px;
    display: inline-block;
    text-align: center;
  }
}
</style>
<style lang="less">
.textareacaed {
   label {
    width: 100% !important;
    text-align: left !important;
   }
  .ivu-form-item-content {
    margin-left: 0 !important;
  }
}
.nostarticon.ivu-form-item.ivu-form-item-required .ivu-form-item-label:before {
  display: none;
}
.my-item-required .ivu-form-item-label:before {
  content: '*';
  display: inline-block;
  margin-right: 4px;
  line-height: 1;
  font-family: SimSun;
  font-size: 12px;
  color: #ed4014;
}
.end-date-before .ivu-form-item-label::before {
  content: none !important;
}
.refresh_btn {
    position: absolute;
    margin-top: 10px;
    margin-left: 10px;
    cursor: pointer;
    z-index: 9999;
}
.spin-icon-load {
  animation: ani-spin 1s linear infinite;
}
@keyframes ani-spin {
  from { transform: rotate(0deg);}
  50%  { transform: rotate(180deg);}
  to   { transform: rotate(360deg);}
}
</style>
