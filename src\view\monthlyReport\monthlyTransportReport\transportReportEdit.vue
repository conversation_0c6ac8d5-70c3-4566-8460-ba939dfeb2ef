<template>
  <div>
    <Drawer
      v-model="modal"
      :data="modalData"
      :title="title"
      :width="800"
      :mask-closable="false"
      @on-visible-change="visibleChange">
      <search
        @searchResults='searchResults'
        @selectOnChanged='selectOnChanged'
        :setSearch='setSearchData'
        @keydown.enter.native='searchResults'></search>
      <div class="table_Drawer">
        <div class="table_header">
          <span class="ship_name">船舶</span>
          <span class="be_selectd">统计数量</span>
        </div>
        <div class="table_header">
          <span class="ship_name">船舶</span>
          <span class="be_selectd">统计数量</span>
        </div>
        <div v-for="(item, index) in shipList" :key="index" class="table_body">
          <CheckboxGroup v-model="isSelected" @on-change="getShipId">
            <Checkbox :label="item.ship_id" class="checkdiv" :disabled="item.being_selected === '1' || item.voyage_num === '0'">
              <span class="ship_name">{{ item.ship_name }}</span>
              <span class="be_selectd">{{ item.voyage_num }}</span>
            </Checkbox>
          </CheckboxGroup>
        </div>
      </div>
      <div class="demo-drawer-footer">
        <Button @click="modal = false" style="margin-right: 10px;">取消</Button>
        <Button type="primary" @click="createData">生成表格</Button>
      </div>
    </Drawer>
  </div>
</template>
<script>
import search from '_c/search' // 查询组件
import { queryReportShips, addCarriageReport } from '@/api/monthlyReport/transportReport'
export default {
  components: { search },
  data () {
    return {
      modal: false,
      title: '',
      modalData: {},
      shipList: [],
      isSelected: [],
      shipIdSelecteds: [], // 存储已选择船舶
      setSearchData: {// 查询设置，对象key值为回调参数
        start_date: {
          type: 'date',
          label: '时间段：',
          selected: '',
          width: 130,
          value: '',
          isdisable: false
        },
        end_date: {
          type: 'date_end',
          label: '-',
          selected: '',
          width: 130,
          value: '',
          isdisable: false
        }
      }
    }
  },
  methods: {
    visibleChange (val) {
      if (val === true) {
        this.title = '时间：' + this.modalData.report_year + '年' + parseInt(this.modalData.report_month) + '月'
        if (this.modalData.start_date !== '' && this.modalData.end_date !== '') {
          this.start_date = this.modalData.start_date
          this.end_date = this.modalData.end_date
          this.setSearchData.start_date.selected = this.modalData.start_date
          this.setSearchData.end_date.selected = this.modalData.end_date
          this.searchResults()
        }
      } else {
        this.isSelected = []
        this.shipList = []
        this.setSearchData.start_date.selected = ''
        this.setSearchData.end_date.selected = ''
      }
    },
    // 查询
    searchResults () {
      this.setSearchData.start_date.value = this.start_date
      this.setSearchData.end_date.value = this.end_date
      if (this.start_date === undefined || this.end_date === undefined) return this.$Message.error('请选择完整时间段！')
      this.getShip()
    },
    // 时间格式
    selectOnChanged (e) {
      if (e.flag === 'date_start') {
        this.start_date = e.key
      } else if (e.flag === 'date_end') {
        this.end_date = e.key
      }
    },
    // 获取船舶列表
    getShip () {
      let data = {
        start_date: this.setSearchData.start_date.value,
        end_date: this.setSearchData.end_date.value,
        report_year: this.modalData.report_year,
        report_month: parseInt(this.modalData.report_month)
      }
      queryReportShips(data).then(res => {
        if (res.data.Code === 10000) {
          this.shipList = res.data.Result
        }
      })
    },
    // 获取船舶ids
    getShipId (data) {
      this.shipIdSelecteds = data
    },
    // 生成表格
    createData () {
      if (this.shipIdSelecteds.length < 1) {
        this.$Message.error('请至少选择一条船舶！')
        return
      }
      let data = {
        ship_ids: this.shipIdSelecteds.toString(),
        start_date: this.setSearchData.start_date.value,
        end_date: this.setSearchData.end_date.value,
        report_year: this.modalData.report_year,
        report_month: parseInt(this.modalData.report_month)
      }
      addCarriageReport(data).then(res => {
        if (res.data.Code === 10000) {
          this.$emit('addSuccess', this.modalData.report_year, this.modalData.report_month, this.modalData.curIdx)
          this.modal = false
          this.$Message.success(res.data.Message)
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.table_Drawer {
  border: 1px solid #D9D9D9;
}
.table_header {
  height: 70px;
  line-height: 70px;
  background-color: #F5F7FA;
  .ship_name {
    margin-left: 11%;
  }
}
.table_header, .table_body {
  width: 50%;
  text-align: center;
  display: inline-block;
  margin-bottom: -1px;
  border-bottom: 1px solid #D9D9D9;
}
.table_body {
  height: 50px;
  line-height: 50px;
  .checkdiv {
    width: 100%;
  }
}
.table_body:nth-child(odd), .table_header:nth-child(1) {
  border-right: 1px solid #D9D9D9;
}
.table_header span, .table_body span {
  display: inline-block;
  border-left: 1px solid #D9D9D9;
}
.ship_name {
  width: 45%;
}
.be_selectd {
  width: 41%;
}
.filter-container {
  top: 3px;
  left: 160px;
  position: absolute;
}
</style>
<style lang="less">
.table_Drawer {
  .ivu-checkbox {
    width: 10%;
  }
  .ivu-checkbox-wrapper + span, .ivu-checkbox + span {
    margin: 0;
  }
}
.filter-container {
  .ivu-form .ivu-form-item-label {
    font-size: 14px;
    color: #17233d;
    font-weight: bold;
  }
  .ivu-btn-primary:last-child {display: none;}
}
</style>
