<template>
  <div>
    <Drawer
      v-model="modalData.modal"
      :data="modalData.data"
      title="新增"
      width="800"
      :mask-closable="false"
      @on-visible-change="visibleChange">
      <Card>
        <Form ref="formInline" :model="formInline" :rules="ruleValidate" :label-width="65" inline>
          <Row>
            <Col span="7">
              <FormItem label="船名">
                <Input v-model="formInline.ship_name" disabled></Input>
              </FormItem>
            </Col>
            <Col span="7">
              <FormItem label="航次">
                <Input v-model="formInline.voyage_no" disabled></Input>
              </FormItem>
            </Col>
            <Col span="10">
              <FormItem label="港口码头" :label-width="85" prop="dynamic_port_id">
                <Cascader :data="portResultList" trigger="hover" v-model="portResultData" :render-format="format" @on-change="changePortWharf" style="display: inline-block;margin-right: 10px;width: calc(100% - 58px);"></Cascader>
                <span class="con_text con_load" v-if="curPortType === '1'">装</span>
                <span class="con_text con_unload" v-if="curPortType === '2'">卸</span>
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="7">
              <FormItem label="状态" prop="status_id">
                <Select v-model="formInline.status_id" @on-change="changeStatus" clearable :disabled="statusDisabled">
                  <Option v-for="(item, index) in statusList" :key="index" :value="item.value">{{ item.label }}</Option>
                </Select>
              </FormItem>
            </Col>
            <Col span="7">
              <FormItem label="节点" prop="node_id">
                <Select v-model="formInline.node_id" @on-change="changeNode" ref="nodeRef" clearable :disabled="nodeDisabled">
                  <Option v-for="(item, index) in nodeList" :key="index" :value="item.value">{{ item.label }}</Option>
                </Select>
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="节点时间" prop="node_date" :label-width="85">
                <DatePicker
                  type="datetime"
                  format="yyyy-MM-dd HH:mm"
                  :value='formInline.node_date'
                  @on-ok="handleok('1')"
                  @on-change="data=>formInline.node_date=data"></DatePicker>
              </FormItem>
            </Col>
          </Row>
          <!-- 起锚、第一条缆绳上岸、船舶系泊 -->
          <Row v-if="showPort">
            <Col span="8">
              <FormItem prop="port_id" label="港口">
                <Select v-model="formInline.port_id" @on-change="changePort" clearable>
                  <Option v-for="(item, index) in allPortList" :key="index" :value="item.value">{{ item.label }}</Option>
                </Select>
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem prop="wharf_id" label="码头">
                <Select v-model="formInline.wharf_id" @on-change="changeWharf" ref="wharfRef" clearable :disabled="wharfDisabled">
                  <Option v-for="(item, index) in allWharfList" :key="index" :value="item.value">{{ item.label }}</Option>
                </Select>
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="泊位">
                <Select v-model="formInline.berth_id" ref="berthRef" clearable :disabled="berthDisabled">
                  <Option v-for="(item, index) in berthList" :key="index" :value="item.value">{{ item.label }}</Option>
                </Select>
              </FormItem>
            </Col>
          </Row>
          <!-- 作业状态 -->
          <Row v-if="showCargo">
            <Col span="10">
              <FormItem label="货主货品">
                <Cascader :data="cargoCompanyList" trigger="hover" v-model="cargoResultData" :render-format="format" @on-change="changeCargoData"></Cascader>
              </FormItem>
            </Col>
              <!-- <FormItem label="货主">
                <Select v-model="formInline.cargo_company_id" @on-change="changeCargo" clearable>
                  <Option v-for="(item, index) in cargoCompanyList" :key="index" :value="item.value">{{ item.label }}</Option>
                </Select>
              </FormItem>
            <Col span="7">
              <FormItem label="货品">
                <Input v-model="formInline.goods_name" disabled></Input>
              </FormItem>
            </Col> -->
          </Row>
          <!-- 有预计节点的 -->
          <Row v-if="showExpectNode">
            <Col span="8">
              <FormItem label="预计节点" :label-width="80">
                <Select v-model="formInline.expect_node_id">
                  <Option v-for="(item, index) in expectNodeList" :key="index" :value="item.value">{{ item.label }}</Option>
                </Select>
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="预计时间" :label-width="80">
                <DatePicker
                  type="datetime"
                  confirm
                  format="yyyy-MM-dd HH:mm"
                  :value='formInline.expect_date'
                  @on-ok="handleok('2')"
                  @on-change="data=>formInline.expect_date=data"></DatePicker>
              </FormItem>
            </Col>
          </Row>
          <!-- 计量结束 -->
          <Row v-if="showOther">
            <Row v-if="curPortType === '1'">
              <Col span="8" v-if="isShowLF">
                <FormItem label="装港流量计" :label-width="115">
                  <Input v-model='formInline.load_flowmeter_amount'></Input>
                </FormItem>
              </Col>
              <Col span="8" v-if="isShowLT">
                <FormItem label="装港岸罐量" :label-width="115">
                  <Input v-model='formInline.load_tank_amount'></Input>
                </FormItem>
              </Col>
              <Col span="8" v-if="isShowLS">
                <FormItem label="装港商检船板量" :label-width="115">
                  <Input v-model='formInline.load_ship_amount'></Input>
                </FormItem>
              </Col>
            </Row>
            <Row v-if="curPortType === '2'">
              <Col span="8" v-if="isShowLlT">
                <FormItem label="卸港岸罐量" :label-width="115">
                  <Input v-model='formInline.unload_tank_amount'></Input>
                </FormItem>
              </Col>
              <Col span="8" v-if="isShowUlS">
                <FormItem label="卸港商检船板量" :label-width="115">
                  <Input v-model='formInline.unload_ship_amount'></Input>
                </FormItem>
              </Col>
            </Row>
          </Row>
          <Row>
            <Col span="24">
              <FormItem label="备注" prop="remark">
                <Input type="textarea" :autosize="true" v-model="formInline.remark" :rows="4"></Input>
              </FormItem>
            </Col>
          </Row>
        </Form>
      </Card>
      <div class="demo-drawer-footer">
        <Button @click="modalData.modal = false" style="margin-right: 10px;">取消</Button>
        <Button type="primary" @click="createData">保存</Button>
      </div>
    </Drawer>
  </div>
</template>

<script>
import { queryNodeStatusList, queryNodeList, queryPortList, queryWharfList, queryBerthList } from '@/api/basicData'
import { addHistoryNode, addCheckNode } from '@/api/voyageHistory'
export default {
  props: {
    modalData: Object
  },
  data () {
    return {
      portResultList: [], // 存储归属港口码头list
      portResultData: [], // 归属港口码头
      cargoResultData: [], // 储存货主货品
      statusList: [],
      nodeList: [],
      showExpectNode: false, // 有预计节点的显示内容
      expectNodeList: [], // 储存所有节点数据
      showPort: false,
      allPortList: [],
      allWharfList: [],
      berthList: [],
      showCargo: false,
      cargoCompanyList: [],
      statusDisabled: true,
      nodeDisabled: true,
      wharfDisabled: true,
      berthDisabled: true,
      showOther: false,
      curPortType: '',
      isShowLF: false,
      isShowLS: false,
      isShowLT: false,
      isShowUlS: false,
      isShowLlT: false,
      curUnit: '',
      curWharfId: '',
      formInline: {
        voyage_id: '',
        port_mode: '',
        dynamic_port_id: '',
        dynamic_wharf_id: '',
        status_id: '',
        node_id: '',
        node_date: '',
        port_id: '',
        wharf_id: '',
        berth_id: '',
        expect_node_id: '',
        expect_date: '',
        remark: '',
        cargo_company_id: '',
        goods_id: '',
        goods_name: '',
        load_flowmeter_amount: '',
        load_ship_amount: '',
        load_tank_amount: '',
        unload_ship_amount: '',
        unload_tank_amount: '',
        unit: ''
      },
      ruleValidate: {
        dynamic_port_id: [
          { required: true, message: '港口码头不能为空', trigger: 'change' }
        ],
        status_id: [
          { required: true, message: '状态不能为空', trigger: 'change' }
        ],
        node_id: [
          { required: true, message: '节点不能为空', trigger: 'change' }
        ],
        node_date: [
          { required: true, message: '节点时间不能为空', trigger: 'change' }
        ],
        port_id: [
          { required: true, message: '港口不能为空', trigger: 'change' }
        ],
        wharf_id: [
          { required: true, message: '码头不能为空', trigger: 'change' }
        ],
        berth_id: [
          { required: true, message: '泊位不能为空', trigger: 'change' }
        ],
        cargo_company_id: [
          { required: true, message: '货主不能为空', trigger: 'change' }
        ],
        goods_id: [
          { required: true, message: '货品不能为空', trigger: 'change' }
        ]
      },
      port_detail_id: ''
    }
  },
  methods: {
    // 开启模态框
    visibleChange (d) {
      if (d === true) {
        this.formInline.ship_name = this.modalData.data.ship_name
        this.formInline.voyage_no = this.modalData.data.voyage_no
        let curList = []
        this.modalData.data.portResult.map(item => { // 获取归属港口码头
          if (curList.includes(item.port_id)) {
            let _curIdx = this.portResultList.findIndex(list => list.value === item.port_id)
            this.portResultList[_curIdx].children.push({
              label: item.wharf_name,
              value: item.wharf_id + item.port_type,
              portType: item.port_type,
              unit: item.unit
            })
          } else {
            this.portResultList.push({
              label: item.port_name,
              value: item.port_id,
              children: [{
                label: item.wharf_name,
                value: item.wharf_id + item.port_type,
                portType: item.port_type,
                unit: item.unit
              }]
            })
          }
          curList.push(item.port_id)
        })
        queryNodeStatusList().then(res => { // 获取状态
          if (res.data.Code === 10000) {
            res.data.Result.map(item => {
              this.statusList.push({
                value: item.id,
                label: item.node_status_name
              })
            })
          }
        })
      } else {
        this.clearData()
      }
    },
    // 选择归属港口码头
    changePortWharf (value, selectedData) {
      if (value.length < 1) {
        this.formInline.dynamic_port_id = ''
        return
      }
      this.formInline.port_id = this.formInline.dynamic_port_id = value[0]
      this.formInline.wharf_id = this.formInline.dynamic_wharf_id = value[1].substr(0, value[1].length - 1)
      this.portResultData = [value[0], value[1]]
      if (this.showPort) {
        this.changePort(this.formInline.port_id)
        this.changeWharf(this.formInline.wharf_id)
      }
      this.curPortType = selectedData[1].portType
      this.curUnit = selectedData[1].unit
      this.statusDisabled = false
      // 获取货主、货品
      this.cargoCompanyList = []
      this.cargoResultData = []
      let curCargoList = []
      console.log(this.modalData.data.cargoResult)
      this.modalData.data.cargoResult.map(e => {
        if (this.formInline.wharf_id === e.wharf_id) {
          if (curCargoList.includes(e.cargo_company_id)) {
            let _curIdx = this.cargoCompanyList.findIndex(list => list.value === e.cargo_company_id)
            this.cargoCompanyList[_curIdx].children.push({
              label: e.goods_name + ' ' + e.amount + '吨',
              value: e.id,
              unit: e.unit,
              goods_id: e.goods_id
            })
          } else {
            this.cargoCompanyList.push({
              label: e.cargo_company_name,
              value: e.cargo_company_id,
              children: [{
                label: e.goods_name + ' ' + e.amount + '吨',
                value: e.id,
                unit: e.unit,
                goods_id: e.goods_id
              }]
            })
          }
          curCargoList.push(e.cargo_company_id)
        }
      })
    },
    // 编辑保存
    createData () {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          this.$Modal.confirm({
            title: '提示',
            content: '<p>是否确认添加节点？</p>',
            loading: true,
            onOk: () => {
              let data = {
                voyage_id: this.modalData.data.id,
                port_mode: this.curPortType,
                dynamic_port_id: this.formInline.dynamic_port_id,
                dynamic_wharf_id: this.formInline.dynamic_wharf_id,
                status_id: this.formInline.status_id,
                node_id: this.formInline.node_id,
                node_date: this.formInline.node_date,
                port_id: this.formInline.port_id,
                wharf_id: this.formInline.wharf_id,
                berth_id: this.formInline.berth_id,
                expect_node_id: this.formInline.expect_node_id,
                expect_date: this.formInline.expect_date,
                remark: this.formInline.remark,
                cargo_company_id: this.formInline.cargo_company_id,
                goods_id: this.formInline.goods_id,
                load_flowmeter_amount: this.formInline.load_flowmeter_amount,
                load_ship_amount: this.formInline.load_ship_amount,
                load_tank_amount: this.formInline.load_tank_amount,
                unload_ship_amount: this.formInline.unload_ship_amount,
                unload_tank_amount: this.formInline.unload_tank_amount,
                unit: this.curUnit,
                port_detail_id: this.port_detail_id
              }
              if (this.modalData.isCheck) {
                addCheckNode({ detailJson: JSON.stringify(data) }).then(response => {
                  if (response.data.Code === 10000) {
                    this.$Message.success(response.data.Message)
                    this.loading = false
                    this.$Modal.remove()
                    this.modalData.modal = false
                    this.$emit('addSuccess')
                  } else {
                    this.$Message.error(response.data.Message)
                    this.modalData.modal = false
                    this.$Modal.remove()
                    this.loading = false
                  }
                }).catch(function () {
                  this.$Message.warning('系统异常，请联系管理员')
                })
              } else {
                addHistoryNode({ detailJson: JSON.stringify(data) }).then(response => {
                  if (response.data.Code === 10000) {
                    this.$Message.success(response.data.Message)
                    this.loading = false
                    this.$Modal.remove()
                    this.modalData.modal = false
                    this.$emit('addSuccess')
                  } else {
                    this.$Message.error(response.data.Message)
                    this.modalData.modal = false
                    this.$Modal.remove()
                    this.loading = false
                  }
                }).catch(function () {
                  this.$Message.warning('系统异常，请联系管理员')
                })
              }
            }
          })
        }
      })
    },
    // 级联格式
    format (labels, selectedData) {
      if (labels.length === 0) return
      return labels[labels.length - 2] + ' - ' + labels[labels.length - 1]
    },
    // 通过节点显示相应节点内容
    changeNode (row) {
      // this.clearDataNode()
      let curIdx = this.nodeList.findIndex(item => { // 获取当前选中的节点index
        return item.value === row
      })
      if (this.nodeList[curIdx] === undefined) return
      // 显示预计节点、预计节点时间:起航/航行中/到港/抛锚/ 输油管接妥/开始装卸货/暂停装卸货/恢复装卸货/结束装卸货
      let expectNodeCodeList = ['AA', 'AB', 'AC', 'AG', 'AO', 'AP', 'AQ', 'AR', 'AS', 'AT', 'AU', 'AV', 'AW']
      this.showExpectNode = !!expectNodeCodeList.includes(this.nodeList[curIdx].curNodeCode)
      // 显示港口、码头、泊位：起锚、第一条缆绳上岸、船舶系泊
      let showPortList = ['AI', 'AJ', 'AK']
      this.showPort = !!showPortList.includes(this.nodeList[curIdx].curNodeCode)
      // 显示货主：作业状态
      this.showCargo = this.nodeList[curIdx].curStatusCode === 'ZY'
      // 显示计量方式：计量结束
      this.showOther = this.nodeList[curIdx].curNodeCode === 'BA'
      this.getUnit()
      queryNodeList().then(res => { // 获取预计节点（所有节点）
        if (res.data.Code === 10000) {
          res.data.Result.map(e => {
            this.expectNodeList.push({
              label: e.node_name,
              value: e.id
            })
          })
        }
      })
      queryPortList().then(res => { // 获取所有港口
        if (res.data.Code === 10000) {
          res.data.Result.map(e => {
            this.allPortList.push({
              label: e.port_name,
              value: e.id
            })
          })
          if (this.showPort) {
            this.changePort(this.formInline.port_id)
            this.changeWharf(this.formInline.wharf_id)
          }
        }
      })
      if (this.modalData.data.cargoResult.length === 1) {
        this.formInline.cargo_company_id = this.modalData.data.cargoResult[0].cargo_company_id
        this.formInline.goods_name = this.modalData.data.cargoResult[0].goods_name
        this.formInline.goods_id = this.modalData.data.cargoResult[0].goods_id
      }
      // 获取货主、货品
      // let curCargoList = []
      // this.modalData.data.cargoResult.map(e => {
      //   if (this.formInline.dynamic_port_id === e.port_id) {
      //     if (curCargoList.includes(e.cargo_company_id)) {
      //       let _curIdx = this.cargoCompanyList.findIndex(list => list.value === e.cargo_company_id)
      //       this.cargoCompanyList[_curIdx].children.push({
      //         label: e.goods_name + ' ' + e.amount + '吨',
      //         value: e.goods_id,
      //         unit: e.unit
      //       })
      //       curCargoList.push(e.cargo_company_id)
      //     } else {
      //       this.cargoCompanyList.push({
      //         label: e.cargo_company_name,
      //         value: e.cargo_company_id,
      //         children: [{
      //           label: e.goods_name + ' ' + e.amount + '吨',
      //           value: e.goods_id,
      //           unit: e.unit
      //         }]
      //       })
      //       curCargoList.push(e.cargo_company_id)
      //     }
      //   }
      // })
    },
    // 选择货主货品
    changeCargoData (value, selectedData) {
      this.formInline.cargo_company_id = value[0]
      this.formInline.goods_id = selectedData[1].goods_id
      this.cargoResultData = [value[0], value[1]]
      let _curIdx = this.modalData.data.cargoResult.findIndex(e => e.id === value[1])
      this.port_detail_id = this.modalData.data.cargoResult[_curIdx].port_detail_id
      this.curUnit = selectedData[1].unit
      this.getUnit()
    },
    // 获取货品
    changeCargo (row) {
      if (row === undefined) return
      let curIdx = this.modalData.data.cargoResult.findIndex(item => {
        return item.cargo_company_id === row
      })
      this.formInline.goods_name = this.modalData.data.cargoResult[curIdx].goods_name
      this.formInline.goods_id = this.modalData.data.cargoResult[curIdx].goods_id
    },
    // 获取计量方式
    getUnit () {
      if (!this.showOther) return
      switch (this.curUnit) { // 计量方式：1.装港流量计 2.装港商检船板量 3.装港岸罐量 4.卸港商检船板量 5.卸港岸罐量
        case '1': // isShowLF:装港流量计, isShowLT:装港岸罐量, isShowLS:装港商检船板量, isShowLlT:卸港岸罐量, isShowUlS: 卸港商检船板量
          this.isShowLF = true
          this.isShowLS = true
          this.isShowLT = false
          break
        case '2':
          this.isShowLS = true
          this.isShowLF = false
          this.isShowLT = false
          break
        case '3':
          this.isShowLF = false
          this.isShowLT = true
          this.isShowLS = true
          break
        case '4':
          this.isShowUlS = true
          this.isShowLlT = false
          break
        case '5':
          this.isShowLlT = true
          this.isShowUlS = true
          break
        default:
          break
      }
    },
    // 获取节点
    changeStatus (d) {
      if (d === undefined) {
        this.nodeDisabled = true
        this.formInline.node_id = ''
        return
      }
      this.nodeList = []
      this.nodeDisabled = false
      this.$refs.nodeRef.clearSingleSelect()
      queryNodeList({ node_status_id: d }).then(res => {
        if (res.data.Code === 10000) {
          res.data.Result.map(e => {
            this.nodeList.push({
              label: e.node_name,
              value: e.id,
              curNodeCode: e.node_code,
              curStatusCode: e.node_status_code
            })
          })
        }
      })
    },
    // 切换节点后重置数据
    clearDataNode () {
      this.cargoCompanyList = []
    },
    // 关闭弹窗，重置数据
    clearData () {
      this.showCargo = false
      this.showOther = false
      this.curPortType = ''
      this.showExpectNode = false
      this.showPort = false
      this.expectNodeList = []
      this.portResultData = []
      this.cargoResultData = []
      this.statusDisabled = true
      this.portResultList = []
      this.statusList = []
      this.nodeList = []
      this.allPortList = []
      this.allWharfList = []
      this.berthList = []
      this.$refs.formInline.resetFields()
      this.clearDataNode()
      this.$nextTick(() => {
        this.formInline = {
          voyage_id: '',
          port_mode: '',
          dynamic_port_id: '',
          dynamic_wharf_id: '',
          status_id: '',
          node_id: '',
          node_date: '',
          port_id: '',
          wharf_id: '',
          berth_id: '',
          expect_node_id: '',
          expect_date: '',
          remark: '',
          cargo_company_id: '',
          goods_id: '',
          goods_name: '',
          load_flowmeter_amount: '',
          load_ship_amount: '',
          load_tank_amount: '',
          unload_ship_amount: '',
          unload_tank_amount: '',
          unit: ''
        }
      })
    },
    // 获取码头
    changePort (d) {
      this.berthDisabled = true
      if (d === undefined) {
        this.wharfDisabled = true
        this.formInline.wharf_id = ''
        this.formInline.berth_id = ''
      } else {
        this.allWharfList = []
        this.berthList = []
        this.wharfDisabled = false
        this.curWharfId = this.formInline.wharf_id
        this.$refs.wharfRef.clearSingleSelect()
        queryWharfList({ port_id: d }).then(res => {
          if (res.data.Code === 10000) {
            this.formInline.wharf_id = this.curWharfId
            res.data.Result.map(item => {
              this.allWharfList.push({
                value: item.id,
                label: item.terminal_name
              })
            })
          }
        })
      }
    },
    // 获取泊位
    changeWharf (d) {
      this.berthList = []
      this.$refs.berthRef.clearSingleSelect()
      this.berthDisabled = false
      let curTerminalId = d === '' || d === undefined ? this.curWharfId : d
      queryBerthList({ terminal_id: curTerminalId }).then(res => {
        if (res.data.Code === 10000) {
          res.data.Result.map(e => {
            this.berthList.push({
              label: e.berth_name,
              value: e.id
            })
          })
        }
      })
    },
    handleok (index) {
      let d = new Date()
      let curMonth = d.getMonth() + 1 < 10 ? '0' + (d.getMonth() + 1) : (d.getMonth() + 1)
      let curDate = d.getDate() < 10 ? '0' + d.getDate() : d.getDate()
      let curHours = d.getHours() < 10 ? '0' + d.getHours() : d.getHours()
      let curMinutes = d.getMinutes() < 10 ? '0' + d.getMinutes() : d.getMinutes()
      let dData = d.getFullYear() + '-' + curMonth + '-' + curDate + ' ' + curHours + ':' + curMinutes
      if (index === '1' && this.formInline.node_date === '') { // 节点时间
        this.formInline.node_date = dData
      } else if (index === '2' && this.formInline.expect_date === '') { // 预计节点时间
        this.formInline.expect_date = dData
      }
    }
  }
}
</script>
<style scoped>
.demo-drawer-footer {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  background: #fff;
}
.con_text {
  width: 25px;
  height: 25px;
  line-height: 25px;
  text-align: center;
  display: inline-block;
  border-radius: 50%;
  -moz-border-radius: 50%;
  -webkit-border-radius: 50%;
}
.con_load {
  color: #017db4;
  background-color: #facd91;
}
.con_unload {
  color: #facd91;
  background-color: #017db4;
}
.formModalTitle {
  margin: 0 0 9px;
}
.ivu-form-inline .ivu-form-item {
  width: 100%;
}
.span-ivu {
  width: 100%;
  height: 32px;
  line-height: 1.5;
  padding: 4px 7px;
  font-size: 12px;
  display: inline-block;
  border-radius: 4px;
  border: 1px solid #dcdee2;
}
.span-ivu.bgclass {
  background-color: #f3f3f3;
  color: #ccc;
}
</style>
