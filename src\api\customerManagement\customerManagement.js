import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 获取所有公司 下拉
export function queryAllCompanyList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/us/company/queryUsCompanyList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 新增客户公司信息
export function addCustomerCompany (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/customer/company/addBasicCustomerCompany',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 修改客户公司信息
export function updateCustomerCompany (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/customer/company/updateBasicCustomerCompany',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 删除公司
export function deleteCustomerCompany (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/customer/company/delBasicCustomerCompany',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 删除成员信息
export function customerInfoDelete (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/customer/member/delBasicCustomerMember',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 新增成员信息
export function customerInfoAdd (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/customer/member/addBasicCustomerMember',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 修改成员信息
export function customerInfoUpdate (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/customer/member/updateBasicCustomerMember',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 货主公司开启/关闭短信状态
export function addBatchMsgShipowner (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/smsconfig/company/config/changeMsgCompanyConfig',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 货主成员开启/关闭短信状态
export function changeMsgUserConfig (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/smsconfig/user/config/changeMsgUserConfig',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取货主公司推送节点配置
export function queryFullMsgNodeConfigList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/smsconfig/node/config/queryFullMsgNodeConfigList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 修改&保存 货主公司推送节点配置
export function updateBatchMsgNodeConfig (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/smsconfig/node/config/updateBatchMsgNodeConfig',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}


export default {
  queryAllCompanyList,
  addCustomerCompany,
  updateCustomerCompany,
  deleteCustomerCompany,
  customerInfoDelete,
  customerInfoAdd,
  customerInfoUpdate,
  addBatchMsgShipowner,
  changeMsgUserConfig,
  queryFullMsgNodeConfigList,
  updateBatchMsgNodeConfig
}
