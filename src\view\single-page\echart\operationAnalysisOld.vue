<template>
  <div>
    <card>
      <div class="btn-area">
        <Select size="small" class="select-ship-content" :transfer="false" v-model="queryParam.ship_id" placeholder="请选择船舶"
                  clearable @on-change="shipSelect">
        </Select>
        <month-select class="month-select" @on-change="dateSelect"></month-select>
        <Button size="small" type="primary" icon="md-eye" @click="handleReport('view')">预览报表</Button>
        <Button size="small" type="primary" icon="md-download" @click="handleReport('down')">下载报表</Button>
        <Row style="margin-top: 13px;">
          <Col span="16">
            <chart-bar style="height: 300px;" unit="" :value="analysisData" text="船舶营运总览"/>
          </Col>
          <Col span="8">
            <chart-pie style="height: 300px;" :value="transData" text="运输货品占比" :color="transColor"></chart-pie>
          </Col>
        </Row>
      </div>
    </card>

    <card style="margin-top: 20px;">
      <Row>
        <h4 style="margin-bottom: 23px; width: 60%; display: inline-block;">2020年运输货品组成</h4>
        <Col span="15">
          <Row>
            <Col span="5">
              <div class="num-title">执行航次数</div>
              <count-to class="num-chart" :end="chartVolumeNum" unitClass="num-unit"/>
              <span>次</span>
            </Col>
            <Col span="5">
              <div class="num-title">航程</div>
              <count-to class="num-chart" :end="chartVoyageNum" unitClass="num-unit" :decimals="decimals2"/>
              <span>万海里</span>
            </Col>
            <Col span="5">
              <div class="num-title">货运量</div>
              <count-to class="num-chart" :end="chartGoodsNum" unitClass="num-unit" :decimals="decimals2"/>
              <span>万吨</span>
            </Col>
            <Col span="5">
              <div class="num-title">周转量</div>
              <count-to class="num-chart" :end="chartTurnoverNum" unitClass="num-unit" :decimals="decimals2"/>
              <span>万吨公里</span>
            </Col>
            <Col span="4">
              <div class="num-title">油品</div>
              <count-to class="num-chart" :end="oilTon" unitClass="num-unit" :decimals="decimals2"/>
              <span>万吨公里</span>
            </Col>
          </Row>
          <Row style="margin-top: 30px;">
            <Col span="5">
              <div class="num-title">待泊时长</div>
              <count-to class="num-chart" :end="chartWaitingTime" unitClass="num-unit"/>
              <span>小时</span>
            </Col>
            <Col span="5">
              <div class="num-title">平均待泊</div>
              <count-to class="num-chart" :end="chartAverageWaiting" unitClass="num-unit" :decimals="decimals2"/>
              <span>小时</span>
            </Col>
            <Col span="5">
              <div class="num-title">总损耗</div>
              <count-to class="num-chart" :end="chartTotalLoss" unitClass="num-unit" :decimals="decimals4"/>
              <span>万吨</span>
            </Col>
            <Col span="5">
              <div class="num-title">平均损耗率</div>
              <count-to class="num-chart" :end="chartAverageLossRate" unitClass="num-unit" :decimals="decimals2"/>
              <span>‰</span>
            </Col>
            <Col span="4">
              <div class="num-title">化学品</div>
              <count-to class="num-chart" :end="chemicalTon" unitClass="num-unit" :decimals="decimals2"/>
              <span>万吨公里</span>
            </Col>
          </Row>
        </Col>
        <Col span="9">
          <chart-pie style="height: 200px; margin-top: -50px;" :value="yearPieData" text="" :color="transColor" :legendShow="false" :radius="90" :center="yearPieCenter"></chart-pie>
        </Col>
      </Row>
    </card>

    <card>
      <Row>
        <Col span="10">
          <chart-line style="height: 300px;" unit="满载率" :value="lossLineData" :color="transColor" text="各船损耗率统计"/>
        </Col>
        <Col span="14">
          <Table border :data="lossList" :columns="lossColumn"></Table>
        </Col>
      </Row>
    </card>

    <card>
      <Row>
        <Col span="12">
          <chart-line style="height: 300px;" unit="" :value="shipAuchorData" :color="transColor" text="各船抛锚时长分析"/>
        </Col>
        <Col span="12">
          <chart-line style="height: 300px;" unit="" :value="portAuchorData" :color="transColor" text="港口抛锚时长分析"/>
        </Col>
      </Row>
    </card>
  </div>
</template>
<script>
import { ChartPie, ChartLine, ChartBar } from '_c/charts'
import MonthSelect from '@/components/monthSelect'
import CountTo from '_c/count-to'

export default {
  components: {
    ChartPie,
    ChartLine,
    ChartBar,
    MonthSelect,
    CountTo
  },
  data () {
    return {
      decimals2: 2, // 保留两位小数
      decimals4: 4, // 保留四位小数
      queryParam: {
        ship_id: '',
        start_month: '',
        end_month: ''
      },
      analysisData: {
        xAxis: ['兴通6', '兴通739', '兴通789', '兴通719'],
        data: [45, 56, 88, 99]
      },
      transColor: ['#5B8FF9', '#5AD8A6', '#5D7092', '#F6BD16', '#E8684A', '#6DC8EC', '#9270CA', '#FF9D4D', '#269A99', '#FF99C3'],
      transData: [
        { value: 88, name: '对二甲苯(PX)' },
        { value: 65, name: '乙二醇' },
        { value: 60, name: '航煤' },
        { value: 47, name: '柴油' }
      ],
      yearPieData: [ // 年度运输货品数据
        { value: 88, name: '油品' },
        { value: 65, name: '化学品' }
      ],
      lossLineData: {
        xAxis: ['兴通9', '兴通10', '兴通66'],
        legend: ['满载率'],
        smooth: 0,
        data: [30, 20, 40],
        symbol: ['circle']
      },
      lossColumn: [
        {
          title: '船名',
          width: 60,
          align: 'center'
        },
        {
          title: '兴通66',
          key: '',
          align: 'center'
        },
        {
          title: '兴通719',
          key: '',
          align: 'center'
        },
        {
          title: '兴通739',
          key: '',
          align: 'center'
        },
        {
          title: '兴通789',
          key: '',
          align: 'center'
        }
      ],
      lossList: [],
      shipAuchorData: {
        xAxis: ['兴通9', '兴通10', '兴通66'],
        legend: ['抛锚时长/小时', '抛锚72小时/次'],
        smooth: 0,
        data: [[30, 20, 40], [40, 30, 30]],
        symbol: ['circle', 'triangle']
      },
      portAuchorData: {
        xAxis: ['兴通9', '兴通10', '兴通66'],
        legend: ['抛锚时长/小时', '抛锚72小时/次'],
        smooth: 0,
        data: [[30, 50, 10], [20, 30, 50]],
        symbol: ['circle', 'triangle']
      },
      yearPieCenter: ['50%', '50%'],
      chartGoodsNum: 0, // 货运量
      chartTurnoverNum: 0, // 周转量
      chartVolumeNum: 0, // 航次数
      chartVoyageNum: 0, // 航程
      chartWaitingTime: 0, // 待泊时长
      chartAverageWaiting: 0, // 平均待泊
      chartTotalLoss: 0, // 总损耗
      chartAverageLossRate: 0, // 平均损耗率
      oilTon: 0, // 油品
      chemicalTon: 0 // 化学品
    }
  },
  methods: {
    // 获取信息
    getList () {

    },
    // 日期变化触发
    dateSelect (dateObj) {
      this.queryParam.start_month = dateObj[0] ? dateObj[0] : ''
      this.queryParam.end_month = dateObj[1] ? dateObj[1] : ''
      this.getList()
    },
    // 船舶选择触发
    shipSelect () {
      this.getList()
    },
    // 显示更多
    showModelBtn () {

    },
    handleReport (type) {

    }
  }
}
</script>
<style lang="less" scoped>
  .btn-area {
    text-align: right;
    margin-bottom: 10px;
    button {
      margin-left: 12px;
    }
    .morebtn {
      right: 25px;
      top: 20px;
      position: absolute;
      font-size: 12px;
      color: #666;
      cursor: pointer;
      font-family: Arial, Helvetica, sans-serif;
    }
  }
  .btn-area .select-ship-content {
    width: 110px;
    margin-left:12px !important;
    height: 25px !important;
  }
  .btn-area .select-ship-content .ivu-select-selection {
    height: 25px !important;
    background: #007DFF;
  }
  .btn-area .select-ship-content .ivu-select-selection .ivu-select-selected-value {
    color: #fff;
  }
  .btn-area .select-ship-content .ivu-select-selection .ivu-select-arrow {
    color: #fff;
  }
  .num-chart {
    display: inline-block;
    font-size: 16px;
    font-weight: bold;
    margin-right: 5px;
  }
</style>
