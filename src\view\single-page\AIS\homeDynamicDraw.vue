<template>
  <div> <!-- 动态信息 -->
    <Drawer v-model="drawerShow" :transfer="false" inner width="800"
            @on-visible-change="visibleChange" :title="currentVoyageInfo">
      <div class="no-data-con" v-if="timeLineData.length === 0">暂无动态数据</div>
      <Timeline class="timelinestyleAis">
        <Menu style="width: 100%;" :open-names="[`${index}`]" v-for="(item, index) in timeLineData" :key="index">
          <Submenu :name="`${index}`" >
            <template slot="title">{{ item.port_name }}{{ item.wharf_name !== '' ? ' — ' + item.wharf_name : '' }}</template>
            <MenuItem v-for="(item1, index1) in item.dynamicResult" :name="`${index1}-1`" :key="index1">
              <TimelineItem>
                <div class="enterdata">{{ item1.node_date_s }}</div> <!-- 节点时间 -->
                <div class="timelinecontent">
                  <div class="text-content-left">
                    <strong>{{ item1.node_name }}</strong>
                    <div>{{ item1.remark }}</div>
                    <div>
                      <span v-if="dynamicNodeData.includes(item1.dynamic_node)" style="white-space: pre">{{ item1.dynamic_context }}</span><!-- 3到港,7抛锚,11船舶系泊,17输油管接妥,18开始装/卸货,19暂停装/卸货,20恢复装/卸货,21结束装/卸货 -->
                      <span v-if="item1.dynamic_node === '1' && item1.PortName !== ''">前往{{ item1.PortName }}港{{ item1.wharfName !== '' ? item1.wharfName + '码头' : '' }}{{ item1.expect_date !== '' ? ',预计' + item1.expect_date + '到达' : '' }}</span><!-- 起航 -->
                      <span v-if="item1.dynamic_node === '2' && item1.PortName !== '' && item1.expect_date !== ''">预计{{ item1.expect_date }}到达{{ item1.PortName }}港{{ item1.wharfName !== '' ? item1.wharfName + '码头' : '' }}</span><!-- 航行中 -->
                      <span v-if="item1.dynamic_node === '9' && item1.wharfName !== ''">前往{{ item1.wharfName }}码头{{ item1.berthName !== '' ? item1.berthName + '泊位':'' }}</span><!-- 起锚 -->
                      <span v-if="item1.dynamic_node === '10' && item1.wharfName !== ''">靠泊{{ item1.wharfName }}码头{{item1.berthName !== '' ? item1.berthName + '泊位' : ''}}</span><!-- 第一条缆绳上岸 -->
                      <span v-if="item1.dynamic_node === '33' && item1.PortName !== '' && item1.expect_date !== ''">预计{{item1.expect_date}}抵达{{ item1.PortName }}港</span><!-- 离港 -->
                    </div>
                  </div>
                </div>
                <div class="enterdatas">
                  <div>{{ (item1.enter_date).substring(5, 10) }}<br>{{ (item1.enter_date).split(' ')[1] }}</div> <!-- 录入时间 -->
                </div>
              </TimelineItem>
            </MenuItem>
          </Submenu>
        </Menu>
      </Timeline>
    </Drawer>
  </div>
</template>

<script>
import { getVoyageDynamicById } from '@/api/voyageManage/voyageDetail'

export default {
  props: {},
  data () {
    return {
      drawerShow: false, // 显示状态
      currentVoyageInfo: '', // 当前航次
      voyage_id: '', // 航次id
      timeLineData: [], // 动态数据列表
      dynamicNodeData: ['3', '7', '11', '15', '16', '17', '18', '19', '23'] // 特殊节点
    }
  },
  methods: {
    visibleChange (val) {
      if (val) {
        this.getList()
      }
    },
    // 获取动态信息时间轴列表
    getList () {
      getVoyageDynamicById({ voyage_id: this.voyage_id }).then(response => {
        if (response.data.Code === 10000) {
          this.timeLineData = response.data.Result
          let _curNodeDate = []
          let _dynamicList = []
          this.timeLineData.filter((item, index) => {
            _dynamicList = [..._dynamicList, ...item.dynamicResult]
          })
          _dynamicList.map((item, index) => {
            if (!_curNodeDate.includes(item.node_date.substring(0, 10))) {
              _curNodeDate.push(item.node_date.substring(0, 10))
              item.node_date_s = item.node_date.substring(5, 10) + '/' + item.node_date_week + ' ' + item.node_date.split(' ')[1]
            } else {
              item.node_date_s = item.node_date.split(' ')[1]
            }
          })
          _dynamicList.map((list, idx) => {
            this.timeLineData.forEach(item => {
              item.dynamicResult.map((item1, idx1) => {
                if (item1.id === list.id) {
                  item1.node_date_s = list.node_date_s
                }
              })
            })
          })
        }
      }).catch()
    }
  }
}
</script>
<style scoped>
.no-data-con {
  text-align: center;
  color: #918f8f69;
  font-size: 20px;
}
.enterdata {
  top: 0;
  left: -135px;
  position: absolute;
  color: #333;
  font-size: 14px;
  min-width: 125px;
  text-align: right;
}
.timelinecontent {
  width: calc(100% - 55px);
  max-width: 500px;
}
.ivu-menu-vertical.ivu-menu-light:after {
  left: 7px;
  width: 2px;
  background-color: #D7E3F1;
  display: none;
}
.ivu-form-inline .ivu-form-item {
  width: 100%;
}
</style>
<style lang="less">
.timelinestyleAis {
  margin-left: 145px;
  .ivu-menu-light {
    background-color: transparent;
    .ivu-timeline-item {
      padding: 0;
      margin: 0 0 0 -8px !important;
      .ivu-timeline-item-head {
        width: 16px;
        height: 16px;
        border-width: 0;
        background-color: #D7E3F1;
      }
      .timelinecontent {
        padding: 13px;
        color: #515151;
        background: #E6E9EF;
        display: inline-block;
        font-size: 14px;
        border-radius: 5px;
        position: relative;
        strong {
          color: #666;
          font-size: 18px;
        }
      }
      .enterdatas {
        position: absolute;
        left: 535px;
        top: 31%;
        div {
          color: #999;
          line-height: normal;
        }
      }
    }
    &:first-child .ivu-menu-item:first-child {
      .ivu-timeline-item-head {
        border: 5px solid #195BDD;
      }
      .timelinecontent {
        background: #D9E6F9;
        strong {
          color: #000;
        }
        .ivu-btn-primary {
          color: #87A9EB;
        }
      }
    }
    li.ivu-menu-submenu {
      .ivu-menu-submenu-title::after {
        content: '';
        display: block;
        border-left: 2px dashed #D7E3F1;
        height: 100%;
        width: 2px;
        position: absolute;
        top: 27px;
        left: -20px;
      }
    }
    .ivu-menu-submenu.ivu-menu-opened .ivu-menu-submenu-title::after {
      border-left: 2px solid #D7E3F1;
      top: 15px;
      height: 54px;
      .ivu-menu-submenu-title-icon::before {
        background-image: url(../../../assets/images/arrow-dropdown.png);
      }
    }
    &:last-child {
      li.ivu-menu-submenu:last-child .ivu-menu-submenu-title:after {
        display: none;
      }
      .ivu-menu-submenu.ivu-menu-opened:last-child .ivu-menu-submenu-title::after {
        display: block;
      }
    }
  }
  .ivu-menu-vertical .ivu-menu-submenu-title {
    width: 227px;
    height: 44px;
    line-height: 44px;
    padding: 0 0 0 15px;
    font-size: 16px;
    color: #333;
    font-weight: bold;
    background: rgba(215,227,241,1);
    border-radius: 22px;
    margin-left: 20px;
    margin-bottom: 10px;
    &::before {
      content: '';
      display: block;
      width: 8px;
      height: 8px;
      background-color: #B8D1FF;
      border-radius: 50%;
      position: absolute;
      left: -23px;
      top: 15px;
      z-index: 9;
    }
    .ivu-menu-submenu-title-icon {
      right: 8px;
      &::before {
        content: '';
        width: 14px;
        height: 14px;
        display: block;
        background-size: 14px;
        background-repeat: no-repeat;
        background-image: url(../../../assets/images/arrow-dropup.png);
      }
    }
  }
  .ivu-menu-vertical .ivu-menu-submenu .ivu-menu::before {
    content: '';
    width: 2px;
    height: 110%;
    position: absolute;
    left: 0;
    top: 0;
    border-left: 2px solid #D7E3F1;
  }
  .ivu-menu-vertical .ivu-menu-submenu .ivu-menu-item {
    cursor: default;
    padding: 0 !important;
    background-color: transparent;
  }
  .ivu-menu-light.ivu-menu-vertical .ivu-menu-item-active:not(.ivu-menu-submenu):after {
    display: none;
  }
  .text-content-left {
    width: 80%;
    float: left;
  }
  .ivu-btn-primary {
    width: 30px;
    height: 30px;
    margin-top: -5px;
    padding: 0;
    border-width: 0;
    color: #999;
    font-size: 16px;
    background-color: transparent !important;
    position: absolute;
    right: 10px;
    top: 35%;
  }
}
</style>
