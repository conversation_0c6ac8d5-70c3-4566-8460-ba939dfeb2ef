import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 查询营运核对数据列表
export function voyageOperationCheckInfo (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/check/info/voyageOperationCheckInfo',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 动态节点列表 查询
export function queryDynamicList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/check/info/queryDynamicList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 新增历史动态节点
export function addDynamic (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/check/info/addDynamic',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 更新单个动态节点
export function updateDynamic (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/check/info/updateDynamic',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 更新单个动态节点
export function delDynamic (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/check/info/delDynamic',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 批量更新动态节点
export function updateDynamicBatch (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/check/info/updateDynamicBatch',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询营运核对数据核对
export function addVoyageCheckInfo (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/check/info/addVoyageCheckInfo',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 修改航程
export function updateMonthReportDetail (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/check/info/updateMonthReportDetail',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 修改备注
export function updateStatCargo (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/voyage/stat/cargo/updateStatCargo',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  voyageOperationCheckInfo,
  queryDynamicList,
  addDynamic,
  updateDynamic,
  delDynamic,
  updateDynamicBatch,
  addVoyageCheckInfo,
  updateMonthReportDetail,
  updateStatCargo
}
