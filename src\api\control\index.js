import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 商务专用，查询航次包括计划，在航
export function queryBusinessVoyageConsolePage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/console/home/<USER>/queryBusinessVoyageConsolePage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 商务专用:靠泊计划
export function queryBusinessBerthPlanConsolePage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/console/home/<USER>/queryBusinessBerthPlanConsolePage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询在航航次
export function queryVoyageConsolePage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/console/home/<USER>/queryVoyageConsolePage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询计划航次
export function queryVoyagePlanConsolePage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/console/home/<USER>/queryVoyagePlanConsolePage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  queryBusinessVoyageConsolePage,
  queryBusinessBerthPlanConsolePage,
  queryVoyageConsolePage,
  queryVoyagePlanConsolePage
}
