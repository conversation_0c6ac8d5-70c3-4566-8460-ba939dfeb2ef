<template>
  <Modal
    v-model="modalData.modal"
    @on-visible-change="modalShow"
    :title="modalData.title"
    :closable="false"
    :mask-closable="false"
    width="500">
    <Form ref="formPrice" :model="formPrice" :label-width="65">
      <div v-for="(item, index) in formPrice.detailJson" :key="index">
        <Row>
          <Col span="10">
            <FormItem label="时间" :prop="'detailJson.' + index + '.cargo_date'" :rules="{required: true, message: '此处不能为空', trigger: 'change'}">
              <DatePicker type="date" v-model="item.cargo_date" @on-change="data=>item.cargo_date=data" placeholder="选择时间"></DatePicker>
            </FormItem>
          </Col>
          <Col span="10">
            <FormItem label="价格" :prop="'detailJson.' + index + '.cargo_price'" :rules="{required: true, message: '此处不能为空', trigger: 'change'}">
              <Input type='number' v-model='item.cargo_price' placeholder="请填写货品价格"></Input>
            </FormItem>
          </Col>
          <Col span="2">
            <Button type="text" @click="handleRemove(index)" icon="md-remove-circle" class="formbtn"></Button>
          </Col>
          <Col span="2">
            <Button v-if="index === (formPrice.detailJson.length - 1)" type="text" @click="handleAdd()" icon="md-add-circle" class="formbtn addbtn"></Button>
          </Col>
        </Row>
      </div>
    </Form>
    <div slot="footer">
      <Button @click="clearData" style="margin-right:10px;">取消</Button>
      <Button type="primary" @click="saveGoodsPrice">保存</Button>
    </div>
  </Modal>
</template>
<script>
import API from '@/api/basicConfig/goodsConfig'

export default {
  props: {
    modalData: Object
  },
  data () {
    return {
      formPrice: {
        ship_company_id: '',
        detailJson: [
          {
            cargo_id: this.modalData.cargo_id,
            cargo_date: '',
            cargo_price: ''
          }
        ]
      }
    }
  },
  methods: {
    // 保存今日调价数据
    saveGoodsPrice () {
      this.$refs['formPrice'].validate((valid) => {
        if (valid) {
          let curJson = this.formPrice.detailJson.map(item => {
            return {
              cargo_id: this.modalData.cargo_id,
              cargo_date: item.cargo_date,
              cargo_price: item.cargo_price
            }
          })
          let param = {
            ship_company_id: this.formPrice.ship_company_id,
            detailJson: JSON.stringify(curJson)
          }
          API.addBasicGoodsPrice(param).then(res => {
            if (res.data.Code === 10000) {
              this.modalData.modal = false
              this.$Message.success(res.data.Message)
              this.$emit('callback')
            } else {
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
    },
    // 日期变化
    dateChange (val) {
      this.curDate = val
    },
    // 添加货品信息内容
    handleAdd () {
      this.formPrice.detailJson.push({
        cargo_id: this.modalData.cargo_id,
        cargo_date: '',
        cargo_price: ''
      })
    },
    // 移除货品信息内容
    handleRemove (index) {
      if (this.formPrice.detailJson.length > 1) {
        this.formPrice.detailJson.splice(index, 1)
      } else {
        this.$Message.warning('请至少保留一条货品数据！')
      }
    },
    clearData () {
      this.formPrice = {
        ship_company_id: '',
        detailJson: [
          {
            cargo_id: '',
            cargo_date: '',
            cargo_price: ''
          }
        ]
      }
      this.$refs['formPrice'].resetFields()
      this.modalData.modal = false
    },
    // 显隐操作
    modalShow (val) {
      if (!val) {
        this.clearData()
      }
    }
  }
}
</script>
<style scoped>
  .header-title {
    font-size: 16px;
    font-weight: bold;
  }
  .header-time {
    width: 130px;
    float: right;
    margin-top: -8px;
  }
  .header-btn {
    float: right;
    margin-top: -8px;
    margin-right: 10px;
    height: 32px;
    line-height: 12px;
  }
  .formbtn {
    font-size: 24px;
    padding: 0 3px;
  }
  .formbtn.addbtn {
    right: 0;
    bottom: 18px;
  }
</style>
