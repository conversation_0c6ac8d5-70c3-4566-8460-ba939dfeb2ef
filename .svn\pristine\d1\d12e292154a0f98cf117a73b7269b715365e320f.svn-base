<template>
  <div>
    <Card>
      <div class="btn-area">
        <Button size="small" type="primary" icon="ios-arrow-back" @click="$emit('callback')">返回</Button>
        <Select size="small" class="select-ship-content" :transfer="false" v-model="queryParam.ship_id" placeholder="请选择船舶"
                clearable @on-change="shipSelect">
          <Option v-for="(item, index) in selfShipList" :value="item.ship_id" :key="index">{{ item.ship_name }}</Option>
        </Select>
        <month-select @on-change="dateSelect"></month-select>
        <Button size="small" type="primary" icon="md-eye" @click="handleReport('view')">预览报表</Button>
        <Button size="small" type="primary" icon="md-download" @click="handleReport('down')">下载报表</Button>
      </div>
      <div class="line-area">
        <Spin size="large" fix v-if="spinShow1">
          <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
          <div>Loading</div>
        </Spin>
        <chart-bar style="height: 300px;" unit="小时" :value="anchorTimeData" text="抛锚时长"/>
      </div>
      <div class="line-area">
        <Spin size="large" fix v-if="spinShow2">
          <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
          <div>Loading</div>
        </Spin>
        <chart-line style="height: 400px;" unit="吨(小时)" :value="loadrateNumData" text="装卸速率"/>
      </div>
    </Card>
  </div>
</template>
<script>
import { ChartLine, ChartBar } from '_c/charts'
import MonthSelect from '@/components/monthSelect'
import CountTo from '_c/count-to'
import API from '@/api/statistics'

export default {
  components: {
    ChartLine,
    ChartBar,
    MonthSelect,
    CountTo
  },
  data () {
    return {
      spinShow1: false,
      spinShow2: false,
      selfShipList: [], // 船舶列表
      queryParam: {
        ship_id: '',
        start_month: '',
        end_month: ''
      },
      anchorTimeData: {
        xAxis: [],
        legend: [],
        data: []
      },
      loadrateNumData: {
        xAxis: [],
        legend: ['装货速率', '卸货速率'],
        symbol: ['circle', 'triangle'],
        data: [
          [],
          []
        ]
      }
    }
  },
  methods: {
    async getList () {
      this.resetData()
      this.spinShow1 = true
      this.spinShow2 = true
      // 抛锚时长
      API.queryStatisticWaitBerthMonthSummary(this.queryParam).then(res => {
        this.spinShow1 = false
        if (res.data.Code === 10000) {
          if (!res.data.Result || res.data.Result.length <= 0) return
          res.data.Result.forEach(item => {
            this.anchorTimeData.xAxis.push(item.voyage_over_month)
            this.anchorTimeData.data.push(item.month_wait_berth_sum)
          })
        }
      })
      // 装卸速率
      API.queryStatisticRateMonthSummary(this.queryParam).then(res => {
        this.spinShow2 = false
        if (res.data.Code === 10000) {
          res.data.monthRate.forEach(item => {
            this.loadrateNumData.xAxis.push(item.voyage_over_month)
            this.loadrateNumData.data[0].push(item.month_load_rate)
            this.loadrateNumData.data[1].push(item.month_unload_rate)
          })
        }
      })
    },
    // 数据清空 重置
    resetData () {
      this.anchorTimeData.xAxis = []
      this.anchorTimeData.data = []
      this.loadrateNumData.xAxis = []
      this.loadrateNumData.data[0] = []
      this.loadrateNumData.data[1] = []
    },
    // 船舶选择触发
    shipSelect () {
      this.getList()
    },
    // 日期变化触发
    dateSelect (dateObj) {
      this.queryParam.start_month = dateObj[0] ? dateObj[0] : ''
      this.queryParam.end_month = dateObj[1] ? dateObj[1] : ''
      this.getList()
    },
    // 报表预览 下载
    handleReport (str) {
      API.voyagePmsczxslReportTeplate(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          if (str === 'view') { // 预览
            sessionStorage.setItem('wpsUrl', res.data.wpsUrl)
            sessionStorage.setItem('token', res.data.token)
            const jump = this.$router.resolve({ name: 'viewFile' })
            window.open(jump.href, '_blank')
          } else { // 下载
            window.open(res.data.fileUrl, '_blank')
          }
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    }
  },
  created () {
    this.selfShipList = JSON.parse(localStorage.shipNameList)
    this.getList()
  }
}
</script>

<style lang="less" scoped>
  .btn-area {
    text-align: right;
    margin-bottom: 10px;
    button {
      margin-left: 12px;
    }
  }
  .total-num {
    background: #DAE8FF;
    padding: 32px 25px;
    .num-title {
      font-size: 14px;
      font-weight: bold;
    }
    .num-date {
      font-size: 10px;
      color: #7C8093;
    }
    .num-data {
      margin-top: 13px;
      font-size: 24px;
      font-weight: bold;
    }
  }
  .line-area {
    position: relative;
    margin-top: 40px;
  }
</style>
<style>
  .num-unit {
    font-size: 10px;
    font-weight: 100;
    margin-left: 6px;
  }
  .btn-area .select-ship-content {
   width: 110px;
   margin-left:12px !important;
   height: 32px !important;
 }
 .btn-area .select-ship-content .ivu-select-selection {
   height: 32px !important;
   background: #007DFF;
 }
 .btn-area .select-ship-content .ivu-select-selection .ivu-select-selected-value {
   color: #fff;
 }
 .btn-area .select-ship-content .ivu-select-selection .ivu-select-arrow {
   color: #fff;
 }
</style>
