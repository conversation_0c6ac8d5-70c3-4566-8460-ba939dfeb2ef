<template>
  <div>
    <!-- 单船对比 -->
    <Card>
      <div class="btn_area">
        <span>时间：</span>
        <DatePicker type="year" style="width: 120px" :value="chartParam.base_year" @on-change="(val) =>{changeContrastDate(val, 'base_year')}"></DatePicker>
        <span> &nbsp; 对比时间：</span>
        <DatePicker type="year" style="width: 120px" :value="chartParam.contrast_year" @on-change="(val) =>{changeContrastDate(val, 'contrast_year')}"></DatePicker>
        <span> &nbsp; 船名：</span>
        <Select v-model="chartParam.ship_id" style="width:150px">
          <Option v-for="item in shipList" :value="item.value" :key="item.value">{{ item.label }}</Option>
        </Select>
        <Button size="small" type="primary" @click="searchData" style="padding: 3px 7px">查询</Button>
        <Button size="small" type="primary" @click="clearData" style="padding: 3px 7px">重置</Button>
      </div>
      <Row>
        <Col span="10">
          <div class="line-area">
            <chart-line style="height: 300px;" xAxisUnit="月" subtext="抛锚时长/小时" :value="anchorTimeData" :showOnemarkLine="true" :gridRight="90" @clickBack="lineBack" :clickable="true" :color="lineColor" text="船舶抛锚时长"/>
          </div>
          <div class="line-area">
            <chart-line style="height: 300px;" xAxisUnit="月" subtext="抛锚72h+次数" :value="anchorMoreTimeData" :showOnemarkLine="true" :gridRight="90" @clickBack="lineBackBig" :clickable="true" :color="lineColor" text="抛锚72小时以上次数"/>
          </div>
          <Spin size="large" fix v-if="spinShow">
            <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
            <div>Loading</div>
          </Spin>
        </Col>
        <Col span="14">
          <search @searchResults='searchResults' :setSearch='setSearchData' @keydown.enter.native='searchResults' @resetResults='resetResults'></search>
          <Table :data="listData" border :loading="loading" :columns="columns" class="list_table"></Table>
          <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.queryParam.pageSize" :current.sync="listCurrent"
          :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
        </Col>
      </Row>
    </Card>
  </div>
</template>
<script>
import { ChartLine } from '_c/charts'
import search from '_c/search' // 查询组件
import { querySysDate, queryPortList, queryWharfList } from '@/api/basicData'
import { queryShipAnchor, queryAnchorReport } from '@/api/statistics/anchorStatistics'

export default {
  components: {
    search,
    ChartLine
  },
  data () {
    return {
      shipList: [],
      spinShow: false,
      baseDate: '',
      contrastDate: '',
      chartParam: {
        ship_id: '',
        base_year: '',
        contrast_year: ''
      },
      lineColor: ['#6699FF', '#E74823'],
      anchorTimeData: {
        xAxis: [],
        legend: ['时间', '对比时间'],
        data: [[], []],
        symbol: ['emptyCircle', 'circle']
      },
      anchorMoreTimeData: {
        xAxis: [],
        legend: ['时间', '对比时间'],
        data: [[], []],
        symbol: ['emptyCircle', 'circle']
      },
      setSearchData: {
        port_id: {
          type: 'select',
          label: '港口',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 150,
          value: '',
          filterable: true,
          change: this.getWharf
        },
        wharf_id: {
          type: 'select',
          label: '码头',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 150,
          value: '',
          filterable: true,
          isdisabled: true
        },
        voyage_over_month: {
          type: 'select',
          label: '月份',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 150,
          value: ''
        },
        is_seventy_two: {
          type: 'select',
          label: '是否超72小时',
          selectData: [
            {
              value: '1',
              label: '是'
            },
            {
              value: '0',
              label: '否'
            }
          ],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 60,
          value: ''
        }
      },
      queryParam: {
        ship_id: '',
        start_month: '',
        end_month: '',
        pageSize: 20,
        pageIndex: 1,
        voyage_no: '',
        port_id: '',
        wharf_id: '',
        voyage_over_month: '',
        is_seventy_two: '',
        port_type: '',
        base_year: ''
      },
      loading: false,
      total: null,
      listCurrent: 1,
      listData: [],
      columns: [
        {
          title: '船名',
          key: 'ship_name',
          align: 'center'
        },
        {
          title: '航次',
          key: 'voyage_no',
          align: 'center',
          width: 80
        },
        {
          title: '航线',
          key: 'port_line',
          align: 'center'
        },
        {
          title: '港口',
          key: 'port_name',
          align: 'center'
        },
        {
          title: '码头',
          key: 'wharf_name',
          align: 'center'
        },
        {
          title: '状态',
          key: 'port_type_name',
          align: 'center',
          width: 65
        },
        {
          title: '抛锚时长',
          key: 'anchor_time',
          align: 'center',
          width: 100
        },
        {
          title: '备注',
          key: '',
          align: 'center'
        }
      ]
    }
  },
  created () {
    if (window.localStorage.shipNameList) {
      let shipList = JSON.parse(window.localStorage.shipNameList)
      shipList.map(item => {
          if ((item.business_model === '1' || item.business_model === '2') && !item.ship_name.includes('善')) { // 只要自营和船期船舶且不需要万邦船舶  2024/09/09调整
          this.shipList.push({
            value: item.ship_id,
            label: item.ship_name
          })
        }
      })
      this.chartParam.ship_id = this.shipList[0].value
    } else {
      this.shipList = []
    }
    queryPortList().then(res => {
      if (res.data.Code === 10000) {
        res.data.Result.map(item => {
          this.setSearchData.port_id.selectData.push({
            value: item.id,
            label: item.port_name
          })
        })
      }
    })
    this.getSysDate()
  },
  methods: {
    // 抛锚时长未超过72小时
    lineBack (val) {
      let dateName =  val.name.length > 1 ? val.name : '0' + val.name
      this.setSearchData.voyage_over_month.selected = val.seriesName + '-' + dateName
      this.queryParam.voyage_over_month = val.seriesName + '-' + dateName
      this.queryParam.is_seventy_two = ''
      this.setSearchData.is_seventy_two.selected = ''
      this.getList()
    },
    // 抛锚时长超过72小时
    lineBackBig (val) {
      let dateName =  val.name.length > 1 ? val.name : '0' + val.name
      this.setSearchData.voyage_over_month.selected = val.seriesName + '-' + dateName
      this.queryParam.voyage_over_month = val.seriesName + '-' + dateName
      this.queryParam.is_seventy_two = '1'
      this.setSearchData.is_seventy_two.selected = '1'
      this.getList()
    },
    // 获取统计折线图
    getChartList () {
      this.resetChart()
      this.spinShow = true
      this.anchorTimeData.legend = [this.chartParam.base_year, this.chartParam.contrast_year]
      this.anchorMoreTimeData.legend = [this.chartParam.base_year, this.chartParam.contrast_year]
      queryShipAnchor(this.chartParam).then(res => {
        if (res.data.Code === 10000) {
          this.spinShow = false
          if (res.data.monthAnchorTimeArray.length > 0) { // 锚泊时长指定年份
            res.data.monthAnchorTimeArray.forEach(item => {
              this.anchorTimeData.xAxis.push(item.voyage_over_month.substring(5, 7) < 10 ? item.voyage_over_month.substring(6) : item.voyage_over_month.substring(5, 7))
              this.anchorTimeData.data[0].push(item.anchor_time_sum)
              this.setSearchData.voyage_over_month.selectData.push({
                value: item.voyage_over_month,
                label: item.voyage_over_month
              })
            })
          }
          if (res.data.contrastMonthAnchorTimeArray.length > 0) { // 锚泊时长对比年份
            res.data.contrastMonthAnchorTimeArray.forEach(item => {
              this.anchorTimeData.data[1].push(item.anchor_time_sum)
            })
          }
          if (res.data.monthAnchorRedLineArray.length > 0) { // 72小时次数指定年份
            res.data.monthAnchorRedLineArray.forEach(item => {
              this.anchorMoreTimeData.xAxis.push(item.voyage_over_month.substring(5, 7) < 10 ? item.voyage_over_month.substring(6) : item.voyage_over_month.substring(5, 7))
              this.anchorMoreTimeData.data[0].push(item.count_num)
            })
          }
          if (res.data.contrastMonthAnchorRedLineArray.length > 0) { // 72小时次数对比年份
            res.data.contrastMonthAnchorRedLineArray.forEach(item => {
              this.anchorMoreTimeData.data[1].push(item.count_num)
            })
          }
        } else {
          this.spinShow = false
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 获取报表
    getList () {
      this.loading = true
      let data = Object.assign(this.queryParam, this.chartParam)
      queryAnchorReport(data).then(res => {
        if (res.data.Code === 10000) {
          this.loading = false
          this.listData = res.data.Result
          this.total = res.data.Total
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 数据清空
    resetChart () {
      this.anchorTimeData.xAxis = []
      this.anchorMoreTimeData.xAxis = []
      this.anchorTimeData.data = [[], []]
      this.anchorMoreTimeData.data = [[], []]
    },
    clearData () {
      this.chartParam.ship_id = this.shipList[0].value
      this.getSysDate()
      this.resetResults()
    },
    // 重置报表
    resetResults () {
      this.queryParam = {
        ship_id: '',
        start_month: '',
        end_month: '',
        pageSize: 20,
        pageIndex: 1,
        voyage_no: '',
        port_id: '',
        wharf_id: '',
        voyage_over_month: '',
        is_seventy_two: '',
        port_type: '',
        base_year: ''
      }
      this.listCurrent = 1
      this.setSearchData.port_id.selected = ''
      this.setSearchData.voyage_over_month.selected = ''
      this.setSearchData.is_seventy_two.selected = ''
      this.getList()
    },
    // 全局查询
    searchData () {
      this.queryParam.ship_id = this.chartParam.ship_id
      this.queryParam.base_year = this.chartParam.base_year
      this.getList()
      this.getChartList()
    },
    // 报表查询
    searchResults () {
      this.listCurrent = 1
      this.queryParam.port_id = this.setSearchData.port_id.selected
      this.queryParam.wharf_id = this.setSearchData.wharf_id.selected
      this.queryParam.voyage_over_month = this.setSearchData.voyage_over_month.selected
      this.queryParam.is_seventy_two = this.setSearchData.is_seventy_two.selected
      this.getList()
    },
    // 系统年月
    getSysDate () {
      querySysDate().then(res => {
        if (res.data.Code === 10000 && res.data.systemDate !== '') {
          let _year = res.data.systemDate.substring(0, 4)
          this.chartParam.base_year = _year
          this.chartParam.contrast_year = JSON.stringify(_year - 1)
          this.getChartList()
          this.getList()
        }
      })
    },
    // 获取码头
    getWharf () {
      this.setSearchData.wharf_id.isdisabled = this.setSearchData.port_id.selected === undefined
      this.setSearchData.wharf_id.selectData = []
      this.setSearchData.wharf_id.selected = ''
      if (this.setSearchData.port_id.selected === undefined) return
      queryWharfList({ port_id: this.setSearchData.port_id.selected }).then(res => {
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.setSearchData.wharf_id.selectData.push({
              value: item.id,
              label: item.terminal_name
            })
          })
        }
      })
    },
    // 选择时间
    changeContrastDate (val, d) {
      if (d === 'base_year') {
        this.chartParam.base_year = val
      } else if (d === 'contrast_year') {
        this.chartParam.contrast_year = val
      }
    },
    // 页面跳转
    handleSizeChange (val) {
      this.queryParam.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.queryParam.pageIndex = val
      this.getList()
    }
  }
}
</script>
<style lang="less" scoped>
.btn_area {
  margin-bottom: 15px;
   button {
    margin-left: 12px;
  }
}
.line-area,
.list_table {
  margin-top: 20px;
}
</style>
<style lang="less">
.btn_area {
  .month-select-input .ivu-input {
    color: #515a6e;
    line-height: 30px;
    height: 30px;
    background-color: #fff;
    border: 1px solid #dcdee2;
  }
}
.ivu-poptip-popper {
  z-index: 9999 !important;
}
</style>
