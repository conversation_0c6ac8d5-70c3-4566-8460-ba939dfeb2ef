<template>
  <div>
    <Tabs type="card" :animated="false" v-model="tabId">
      <TabPane label="整体对比">
        <TransPortAll v-if="tabId === 0" :startDate="start_month" :endDate="end_month"></TransPortAll>
      </TabPane>
      <TabPane label="单船对比">
        <TransPortSingle v-if="tabId === 1" :startDate="start_month" :endDate="end_month"></TransPortSingle>
      </TabPane>
      <TabPane label="港口货流量">
        <TransPortData v-if="tabId === 2" :startDate="start_month" :endDate="end_month"></TransPortData>
      </TabPane>
    </Tabs>
  </div>
</template>
<script>
import TransPortAll from './components/transPortAll.vue'
import TransPortSingle from './components/transPortSingle.vue'
import TransPortData from './components/transPortData.vue'

export default {
  components: {
    TransPortAll,
    TransPortSingle,
    TransPortData
  },
  data () {
    return {
      tabId: 0,
      start_month: '',
      end_month: ''
    }
  },
  methods: {
    tabChange (id) {
    }
  }
}
</script>
