import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 获取平台用户列表,分页
export function queryAccountPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/auth/pass/config/queryAccountPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询平台用户详情：获取指定用户的相关信息（个人信息及加入公司列表）ok
export function queryAccountDetail (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/auth/pass/config/queryAccountDetail',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 添加平台用户ok
export function addAccount (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/auth/pass/config/addPassportAndJoinCompany',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 修改平台用户
export function updateAccount (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/auth/pass/config/updatePassport',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 重置密码ok
export function updatePwd (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/auth/pass/config/updatePwd',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 删除平台用户ok
export function queryDeletePass (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/auth/pass/config/deletePassport',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 指定用户申请加入公司OK
export function joinCompany (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/us/company/designeeJoinCompany',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 移交管理员
export function handoverManage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/us/company/handoverManage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 移除公司
export function removeMember (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/us/company/removeMember',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 拒绝加入公司
export function refuseJoinCompany (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/us/company/refuseJoinCompany',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 通过加入公司申请
export function agreeJoinCompany (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/us/company/agreeJoinCompany',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 岸基权限ok
export function configUser (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/auth/company/user/changeSpComConfigUserShoreAuth',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  queryAccountPage,
  queryAccountDetail,
  addAccount,
  updateAccount,
  updatePwd,
  queryDeletePass,
  joinCompany,
  handoverManage,
  removeMember,
  refuseJoinCompany,
  agreeJoinCompany,
  configUser
}