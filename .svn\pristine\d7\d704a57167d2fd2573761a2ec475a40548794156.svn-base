<template>
  <Drawer
    v-model="modalData.modal"
    :title="modalData.title"
    width="750"
    :mask-closable="false"
    @on-visible-change="modalShow">
    <Form ref="formData" :model="formData" :rules="ruleForm" :label-width="125">
      <Row>
        <Col span="11">
          <FormItem label="省份/港口" prop="port_province_key">
            <Select v-model="formData.port_province_key" filterable @on-change="getPort">
              <Option v-for="(item1, idx) in portProvinceList" :key="idx" :value="item1.value">{{ item1.label }}</Option>
            </Select>
          </FormItem>
        </Col>
        <Col span="8">
          <FormItem prop="port_id" :label-width="25">
            <Select v-model="formData.port_id" filterable @on-change="getWharf">
              <Option v-for="(item1, idx) in portList" :key="idx" :value="item1.value">{{ item1.label }}</Option>
            </Select>
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span="11">
          <FormItem label="码头名称" prop="wharf_id">
            <Select v-model="formData.wharf_id" filterable>
              <Option v-for="(item1, idx) in wharfList" :key="idx" :value="item1.value">{{ item1.label }}</Option>
            </Select>
          </FormItem>
        </Col>
        <Col span="11">
          <FormItem label="泊位名称" prop="berth_name">
            <Input v-model="formData.berth_name"></Input>
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span="11">
          <FormItem label="泊位名称全拼" prop="full_spelling">
            <Input v-model="formData.full_spelling"></Input>
          </FormItem>
        </Col>
        <Col span="11">
          <FormItem label="泊位名称首字母" prop="initial">
            <Input v-model="formData.initial"></Input>
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span="11">
          <FormItem label="排序号">
            <Input v-model="formData.order_num"></Input>
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span="11">
          <FormItem label="经度">
            <Input v-model="formData.longtitude"></Input>
          </FormItem>
        </Col>
        <Col span="10" offset="1">
          <FormItem label="纬度" :label-width="65">
            <Input v-model="formData.latitude"></Input>
          </FormItem>
        </Col>
      </Row>
    </Form>
    <div class="demo-drawer-footer">
      <Button @click="clearData" style="margin-right:10px;">取消</Button>
      <Button type="primary" v-if="modalData.dialogType==='create'" @click="createData">保存</Button>
      <Button type="primary" v-if="modalData.dialogType==='update'" @click="updateData">保存</Button>
    </div>
  </Drawer>
</template>
<script>
import { queryPortList, queryDicListByCode } from '@/api/portManagement'
import { queryWharfList } from '@/api/wharfManagement'
import { addBerth, updateBerth } from '@/api/berthManagement'

export default {
  props: {
    modalData: Object
  },
  data () {
    return {
      portProvinceList: [],
      portList: [],
      wharfList: [],
      formData: {
        port_province_key: '',
        port_id: '',
        wharf_id: '',
        berth_name: '',
        full_spelling: '',
        initial: '',
        longtitude: '',
        latitude: '',
        order_num: ''
      },
      ruleForm: {
        port_province_key: [
          { required: true, message: '省份不能为空', trigger: 'change' }
        ],
        port_id: [
          { required: true, message: '港口名称不能为空', trigger: 'change' }
        ],
        wharf_id: [
          { required: true, message: '港口名称不能为空', trigger: 'change' }
        ],
        berth_name: [
          { required: true, message: '码头名称不能为空', trigger: 'blur' }
        ],
        full_spelling: [
          { required: true, message: '码头名称全拼不能为空', trigger: 'blur' }
        ],
        initial: [
          { required: true, message: '码头名称首字母不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    // 获取港口
    getPort () {
      queryPortList({ port_province_key: this.formData.port_province_key }).then(res => {
        if (res.data.Code === 10000) {
          this.portList = res.data.Result.map(item => {
            return {
              value: item.port_id,
              label: item.port_name
            }
          })
        }
      })
    },
    // 获取码头
    getWharf () {
      queryWharfList({ port_id: this.formData.port_id }).then(res => {
        if (res.data.Code === 10000) {
          this.wharfList = res.data.Result.map(item => {
            return {
              value: item.wharf_id,
              label: item.wharf_name
            }
          })
        }
      })
    },
    // 新增
    createData () {
      this.$refs['formData'].validate((valid) => {
        if (valid) {
          this.$Modal.confirm({
            title: '提示',
            content: '<p>是否确认新增港口？</p>',
            loading: true,
            onOk: () => {
              addBerth(this.formData).then(res => {
                if (res.data.Code === 10000) {
                  this.$Message.success(res.data.Message)
                  this.$Modal.remove()
                  this.$emit('callback')
                  this.modalData.modal = false
                } else {
                  this.modalData.modal = true
                  this.$Message.error(res.data.Message)
                  this.$Modal.remove()
                }
              }).catch()
            }
          })
        }
      })
    },
    // 修改
    updateData () {
      this.$refs['formData'].validate((valid) => {
        if (valid) {
          this.$Modal.confirm({
            title: '提示',
            content: '<p>是否修改港口信息？</p>',
            loading: true,
            onOk: () => {
              let data = {
                port_province_key: this.formData.port_province_key,
                port_id: this.formData.port_id,
                wharf_id: this.formData.wharf_id,
                berth_name: this.formData.berth_name,
                berth_id: this.formData.berth_id,
                initial: this.formData.initial,
                full_spelling: this.formData.full_spelling,
                order_num: this.formData.order_num,
                longtitude: this.formData.longtitude,
                latitude: this.formData.latitude
              }
              updateBerth(data).then(res => {
                if (res.data.Code === 10000) {
                  this.$Message.success(res.data.Message)
                  this.$Modal.remove()
                  this.$emit('callback')
                  this.modalData.modal = false
                } else {
                  this.modalData.modal = true
                  this.$Message.error(res.data.Message)
                  this.$Modal.remove()
                }
              }).catch()
            }
          })
        }
      })
    },
    // 取消
    clearData () {
      this.modalData.modal = false
    },
    // 模态框展示
    modalShow (val) {
      if (val) {
        queryDicListByCode ({ dic_code: 'portProvince' }).then(res => { // 选择省份
          if (res.data.Code === 10000) {
            this.portProvinceList = res.data.Result.map(item => {
              return {
                value: item.port_province_key,
                label: item.port_province_name
              }
            })
          }
        })
        if (this.modalData.dialogType === 'update') {
          this.formData = {...{}, ...this.modalData.data}
          this.getPort()
          this.getWharf()
        }
      } else {
        this.$nextTick(() => {
          this.formData = {}
          this.$refs['formData'].resetFields()
        })
      }
    }
  }
}
</script>
