<template>
  <div class="port_area_con">
    <Modal v-if="!isMobile" :mask="false" v-model="portModalShow" @on-visible-change="modalShowHide" :styles="{ position: 'absolute', top: '100px', right: '10px'}" class-name="vertical-right-modal" :transfer="false" scrollable :title="isTerminalSingalClick ? '码头动态' : portTitle" width="400">
      <div class="port_list_area">
        <Tabs v-model="curTabName" v-if="isPortTabShow">
          <TabPane :label="estimateLabel" name="预抵">
            <Table :columns="portEtaColumns" max-height="600" :loading="portLoading" :data="portEtaList"></Table>
          </TabPane>
          <TabPane :label="waitLabel" name="等泊">
            <Table :columns="portAtaColumns" max-height="600" :loading="portLoading" :data="portAtaList"></Table>
          </TabPane>
          <TabPane :label="workLabel" name="作业">
            <Table :columns="portAtbColumns" max-height="600" :loading="portLoading" :data="portAtbList"></Table>
          </TabPane>
        </Tabs>
        <Tabs v-if="isTerminalTabShow" v-model="curTerminalName">
          <TabPane :label="wharfLabel" name="码头">
            <Table :columns="wharfColumn" :loading="wharfLoading" max-height="550" :data="wharfList"></Table>
          </TabPane>
          <TabPane label="泊位" name="泊位" class="berth_contain">
            <Card v-for="(item, index) in allBerthList" :key="index" class="berth_card">
              <Spin size="large" fix v-if="wharfLoading"></Spin>
              <Row>
                <Col span="9">
                  <div class="berth_top_title">{{ item.name }}泊位</div>
                </Col>
                <Col span="5">
                  <div class="berth_title">作业</div>
                  <div class="berth_title">{{ item.berthStatusInfo.working }}</div>
                </Col>
                <Col span="5">
                  <div class="berth_title">等泊</div>
                  <div class="berth_title">{{ item.berthStatusInfo.waiting }}</div>
                </Col>
                <Col span="5">
                  <div class="berth_title">在航</div>
                  <div class="berth_title">{{ item.berthStatusInfo.sailing }}</div>
                </Col>
              </Row>
              <Table v-if="item.isTableShow" :columns="wharfColumn" max-height="450" :data="item.tableData"></Table>
              <div>
                <Button class="berth_more_btn" :type="item.isTableShow ? 'default' : 'primary'" long @click="berthDetail(index)">{{ item.isTableShow ? '收起' : '展开' }}</Button>
              </div>
              </Spin>
            </Card>
          </TabPane>
        </Tabs>
      </div>
      <div slot="footer">
        <Button @click="updateList" type="success">更新</Button>
        <Button @click="modalDownClick" type="primary">{{ (isTerminalTabShow && !isTerminalSingalClick) ? '返回' : '关闭' }}</Button>
      </div>
    </Modal>
    <van-popup v-else closeable round v-model="portModalShow" :overlay="false" position="bottom" @close="modalDownClick">
      <van-button class="reload_btn_mobile" @click="updateList" type="primary" icon="replay" size="mini" plain></van-button>
      <Tabs v-model="curTabName" v-if="isPortTabShow">
        <TabPane :label="estimateLabel" name="预抵">
          <Table :columns="portEtaColumns" max-height="200" :data="portEtaList"></Table>
        </TabPane>
        <TabPane :label="waitLabel" name="等泊">
          <Table :columns="portAtaColumns" max-height="200" :data="portAtaList"></Table>
        </TabPane>
        <TabPane :label="workLabel" name="作业">
          <Table :columns="portAtbColumns" max-height="200" :data="portAtbList"></Table>
        </TabPane>
      </Tabs>
      <Tabs v-if="isTerminalTabShow" v-model="curTerminalName">
        <TabPane :label="wharfLabel" name="码头">
          <Table :columns="wharfColumn" max-height="200" :data="wharfList"></Table>
        </TabPane>
        <TabPane label="泊位" name="泊位" class="berth_contain_mobile">
          <Card v-for="(item, index) in allBerthList" :key="index" class="berth_card">
            <Row>
              <Col span="9">
                <div class="berth_top_title">{{ item.name }}泊位</div>
              </Col>
              <Col span="5">
                <div class="berth_title">作业</div>
                <div class="berth_title">{{ item.berthStatusInfo.working }}</div>
              </Col>
              <Col span="5">
                <div class="berth_title">等泊</div>
                <div class="berth_title">{{ item.berthStatusInfo.waiting }}</div>
              </Col>
              <Col span="5">
                <div class="berth_title">在航</div>
                <div class="berth_title">{{ item.berthStatusInfo.sailing }}</div>
              </Col>
            </Row>
            <Table v-if="item.isTableShow" :columns="wharfColumn" max-height="200" :data="item.tableData"></Table>
            <div>
              <Button class="berth_more_btn" :type="item.isTableShow ? 'default' : 'primary'" long @click="berthDetail(index)">{{ item.isTableShow ? '收起' : '展开' }}</Button>
            </div>
          </Card>
        </TabPane>
      </Tabs>
    </van-popup>
  </div>
</template>
<script>
import axios from 'axios'
import API from '@/api/shipManagement'
import transLatlng from 'coordtransform'

export default {
  props: {
    map: Object
  },
  data () {
    return {
      portLoading: false, // 表格港口数据loading状态
      wharfLoading: false, // 表格码头数据loading状态
      docWidth: document.documentElement.clientWidth,
      isShow: true, // 判断是否显示
      portTitle: '港区动态',
      curTabName: '预抵',
      curTerminalName: '码头',
      estimateLabel: '预抵',
      waitLabel: '等泊',
      workLabel: '作业',
      wharfLabel: '码头',
      isTerminalSingalClick: false, // 码头直接点击触发
      portModalShow: false, // 港区动态显隐
      isPortTabShow: false, // 港区tab显隐
      isTerminalTabShow: false, // 码头tab显隐
      portEtaColumns: [ // 港口预抵内容
        {
          title: '船舶',
          key: 'mapbase_vessel_info_vessel_name',
          render: (h, params) => {
            return h('div', [
              h('span', {
                style: {
                  margin: '0 5px',
                  cursor: 'pointer'
                },
                props: {
                  icon: 'md-eye',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.map.shipsService.locationShip(params.row.mapbase_vessel_info_vessel_id, true)
                    this.map.setZoom(12)
                  }
                }
              }, params.row.mapbase_vessel_info_vessel_name)
            ])
          }
        },
        {
          title: 'ETA',
          key: 'mapbase_vessel_realtime_estimate_eta',
          render: (h, params) => {
            return h('div', {}, params.row.mapbase_vessel_realtime_estimate_eta.substring(0, 10))
          }
        },
        {
          title: '预测码头',
          key: 'mapbase_terminals_name',
          render: (h, params) => {
            return h('div', [
              h('span', {
                style: {
                  margin: '0 5px',
                  cursor: 'pointer'
                },
                props: {
                  icon: 'md-eye',
                  size: 'small'
                },
                on: {
                  click: () => {
                    if (params.row.mapbase_terminals_name !== '') {
                      this.getTerminalList(params.row.mapbase_terminals_id, params.row.mapbase_terminals_name)
                      this.getAllBerthList(params.row.mapbase_terminals_id)
                    }
                  }
                }
              }, params.row.mapbase_terminals_name)
            ])
          }
        }
      ],
      portAtaColumns: [ // 港口等泊内容
        {
          title: '船舶',
          key: 'mapbase_vessel_info_vessel_name',
          render: (h, params) => {
            return h('div', [
              h('span', {
                style: {
                  margin: '0 5px',
                  cursor: 'pointer'
                },
                props: {
                  icon: 'md-eye',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.map.shipsService.locationShip(params.row.mapbase_vessel_info_vessel_id, true)
                    this.map.setZoom(14)
                  }
                }
              }, params.row.mapbase_vessel_info_vessel_name)
            ])
          }
        },
        {
          title: 'ATA',
          key: 'mapbase_vessel_track_ata',
          render: (h, params) => {
            return h('div', {}, params.row.mapbase_vessel_track_ata.substring(0, 10))
          }
        },
        {
          title: '预测码头',
          key: 'mapbase_terminals_name',
          render: (h, params) => {
            return h('div', [
              h('span', {
                style: {
                  margin: '0 5px',
                  cursor: 'pointer'
                },
                props: {
                  icon: 'md-eye',
                  size: 'small'
                },
                on: {
                  click: () => {
                    if (params.row.mapbase_terminals_name !== '') {
                      this.getTerminalList(params.row.mapbase_terminals_id, params.row.mapbase_terminals_name)
                      this.getAllBerthList(params.row.mapbase_terminals_id)
                    }
                  }
                }
              }, params.row.mapbase_terminals_name)
            ])
          }
        }
      ],
      portAtbColumns: [ // 港口作业内容
        {
          title: '船舶',
          key: 'mapbase_vessel_info_vessel_name',
          render: (h, params) => {
            return h('div', [
              h('span', {
                style: {
                  margin: '0 5px',
                  cursor: 'pointer'
                },
                props: {
                  icon: 'md-eye',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.map.setZoom(16)
                    this.map.shipsService.locationShip(params.row.mapbase_vessel_info_vessel_id, true)
                  }
                }
              }, params.row.mapbase_vessel_info_vessel_name)
            ])
          }
        },
        {
          title: 'ATB',
          key: 'mapbase_vessel_track_atb',
          render: (h, params) => {
            return h('div', {}, params.row.mapbase_vessel_track_atb.substring(0, 10))
          }
        },
        {
          title: '作业码头',
          key: 'mapbase_terminals_name',
          render: (h, params) => {
            return h('div', [
              h('span', {
                style: {
                  margin: '0 5px',
                  cursor: 'pointer'
                },
                props: {
                  icon: 'md-eye',
                  size: 'small'
                },
                on: {
                  click: () => {
                    if (params.row.mapbase_terminals_name !== '') {
                      this.getTerminalList(params.row.mapbase_terminals_id, params.row.mapbase_terminals_name)
                      this.getAllBerthList(params.row.mapbase_terminals_id)
                    }
                  }
                }
              }, params.row.mapbase_terminals_name)
            ])
          }
        }
      ],
      wharfColumn: [ // 码头列表信息表头
        {
          title: '船舶',
          key: 'vesselName',
          render: (h, params) => {
            let _str1 = ''
            let _str2 = ''
            if (params.row.status === 'WORKING') {
              _str1 = 'ATA:' + this.getDate(params.row.ata)
              _str2 = 'ETD:' + this.getDate(params.row.etd)
            }
            if (params.row.status === 'WAITING') {
              _str1 = 'ATA:' + this.getDate(params.row.ata)
              _str2 = 'ETD:' + this.getDate(params.row.etd)
            }
            if (params.row.status === 'SAILING') {
              _str1 = 'ETA:' + this.getDate(params.row.eta)
              _str2 = 'ETD:' + this.getDate(params.row.etd)
            }
            return h('div', [
              h('div', {
                style: {
                  fontSize: '18px',
                  cursor: 'pointer'
                },
                on: {
                  click: () => {
                    if (params.row.status === 'WORKING') this.map.setZoom(16)
                    if (params.row.status === 'WAITING') this.map.setZoom(14)
                    if (params.row.status === 'SAILING') this.map.setZoom(12)
                    this.map.shipsService.locationShip(params.row.mmsi, true)
                  }
                }
              }, params.row.vesselName),
              h('div', {
                style: {
                  fontSize: '12px'
                }
              }, _str1),
              h('div', {
                style: {
                  fontSize: '12px'
                }
              }, _str2)
            ])
          }
        },
        {
          title: '泊位',
          key: 'berthName',
          render: (h, params) => {
            let _str = ''
            if (params.row.status === 'WORKING') {
              _str = 'ATB:' + this.getDate(params.row.atb)
            }
            if (params.row.status === 'WAITING') {
              _str = 'ETB:' + this.getDate(params.row.etb)
            }
            if (params.row.status === 'SAILING') {
              _str = 'ETB:' + this.getDate(params.row.etb)
            }
            return h('div', [
              h('div', {
                style: {
                  fontSize: '18px'
                }
              }, params.row.berthName),
              h('div', {
                style: {
                  fontSize: '12px'
                }
              }, _str)
            ])
          }
        },
        {
          title: '状态',
          key: 'status',
          width: 70,
          render: (h, params) => {
            let _str = ''
            let _color = ''
            if (params.row.status === 'WORKING') {
              _str = '作业'
              _color = '#F22728'
            }
            if (params.row.status === 'WAITING') {
              _str = '等泊'
              _color = '#FF9500'
            }
            if (params.row.status === 'SAILING') {
              _str = '在航'
              _color = '#52C22B'
            }
            return h('div', {
              style: {
                color: _color
              }
            }, _str)
          }
        }
      ],
      curTerminalId: '', // 当前选中港口id
      curSelectTerminalName: '', // 当前选中港口名称
      portEtaList: [], // 港口预抵列表
      portAtaList: [], // 港口靠泊列表
      portAtbList: [], // 港口作业列表
      wharfList: [], // 码头作业列表
      allBerthList: [], // 码头下所有泊位信息
      baseData: {}, // 地图数据
      portPointMarkers: null,
      terminalMarkers: null,
      berthMarkers: null,
      originCenter: null, // 原始地图中心位置
      originRadius: null, // 原始半径范围
      zoomLevel: 12,
      markIconList: [ // 标注图标列表
        {
          url: require('@/assets/images/port.png'),
          tip: '港口',
          type: 0
        },
        {
          url: require('@/assets/images/wharf.png'),
          tip: '码头',
          type: 1
        },
        {
          url: require('@/assets/images/berth.png'),
          tip: '泊位',
          type: 2
        },
        {
          url: require('@/assets/images/anchorage.png'),
          tip: '锚地',
          type: 3
        }
      ]
    }
  },
  computed: {
    isMobile () {
      return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Mobi/i.test(navigator.userAgent) ||
            window.innerWidth < 500
    }
  },
  methods: {
    // 初始化数据
    async initAreaData () {
      /**
       * 三方数据引入入口
       */
      /**
      const center = this.map.getCenter()
      const northWest = this.map.getBounds().getNorthWest()
      this.zoomLevel = this.map.getZoom()
      let radius = Math.round((this.map.distance(center, northWest) * 2) / 1000, 2)
      if (radius > 500) {
        radius = 500
      }
      if ((this.originCenter === null && this.originRadius === null) || Math.round(this.map.distance(center, this.originCenter) / 1000, 2) + radius / 2 > this.originRadius) {
        await this.getBaseData(center.lat, center.lng, radius)
        this.originCenter = center
        this.originRadius = radius
      }
      */

      /**
       * 本地数据库引入入口
       * 1：港口，2：码头，3：泊位，4：船舶，5：港口组
       */
      let _that = this
      await API.queryMapBaseList({ base_type: 1 }).then(res => {
        if (res.data.Code === 10000) {
          Object.assign(this.baseData, {
            ports: res.data.Result
          })
          this.drawPort()
          API.queryMapBaseList({ base_type: 2 }).then(res => {
            if (res.data.Code === 10000) {
              Object.assign(this.baseData, {
                terminals: res.data.Result
              })
              _that.drawTerminal()
              API.queryMapBaseList({ base_type: 3 }).then(res => {
                if (res.data.Code === 10000) {
                  Object.assign(this.baseData, {
                    berths: res.data.Result
                  })
                  _that.drawBerth()
                  _that.$nextTick(() => {
                    _that.map.on('drag', (ev) => {
                      _that.drawPort()
                      _that.drawTerminal()
                      _that.drawBerth()
                    })
                    _that.map.on('zoom', (ev) => {
                      _that.drawPort()
                      _that.drawTerminal()
                      _that.drawBerth()
                    })
                  })
                }
              })
            }
          })
        }
      })
    },
    modalShowHide (val) {
      if (val) {
        this.curTabName = '预抵'
        this.curTerminalName = '码头'
        window.onresize = () => {
          this.docWidth = document.documentElement.clientWidth
        }
      }
    },
    // 获取四个顶点位置
    getRectPoint () {
      // let leftdown = this.map.getBounds().getSouthWest().lng + ',' + this.map.getBounds().getSouthWest().lat
      // let rightdown = this.map.getBounds().getSouthEast().lng + ',' + this.map.getBounds().getSouthEast().lat
      // let leftup = this.map.getBounds().getNorthWest().lng + ',' + this.map.getBounds().getNorthWest().lat
      // let rightup = this.map.getBounds().getNorthEast().lng + ',' + this.map.getBounds().getNorthEast().lat
      let minLng = this.map.getBounds().getSouthWest().lng // 左经度
      let maxLng = this.map.getBounds().getNorthEast().lng // 右经度
      let minLat = this.map.getBounds().getSouthWest().lat // 下纬度
      let maxLat = this.map.getBounds().getNorthEast().lat // 上纬度
      return { minLng: minLng, maxLng: maxLng, minLat: minLat, maxLat: maxLat }
    },
    isInPoint (latlng, point) {
      if (latlng[0] > (point.minLat - 1) && latlng[0] < point.maxLat && latlng[1] > (point.minLng - 1) && latlng[1] < point.maxLng) return true
      return false
    },
    // 日期返回
    getDate (str) {
      let _date = new Date(str)
      let _month = _date.getMonth() + 1
      let _day = _date.getDate()
      let _h = _date.getHours()
      let _m = _date.getMinutes()
      return this.getPreNum(_month) + '-' + this.getPreNum(_day) + '  ' + this.getPreNum(_h) + ':' + this.getPreNum(_m)
    },
    modalDownClick () {
      if (this.isPortTabShow || this.isTerminalSingalClick) {
        this.isPortTabShow = false
        this.portModalShow = false
        this.curPortId = ''
        this.curPortName = ''
        this.curTerminalId = ''
        this.curSelectTerminalName = ''
      }
      if (this.isTerminalTabShow) {
        this.isTerminalTabShow = false
        this.isPortTabShow = true
        this.curTabName = '预抵'
      }
    },
    // 列表更新
    updateList () {
      if (this.isPortTabShow) {
        this.getPortList(this.curPortId, this.curPortName)
      }
      if (this.isTerminalTabShow) {
        this.getTerminalList(this.curTerminalId, this.curSelectTerminalName)
        this.getAllBerthList(this.curTerminalId)
      }
    },
    // 日期前缀0
    getPreNum (str) {
      let _backStr = parseInt(str) > 9 ? str : '0' + str
      return _backStr
    },
    // 获取港口泊位船泊基础信息
    getBaseData (lat, lon, dis) {
      axios.post('https://www.shipformula.com/v1/api/node/map/MapBaseControl/getDynamicShowData', {
        lat: lat,
        lon: lon,
        distance: dis
      }, {
        headers: {
          Authorization: 'WEB_87d5a2f0eee411e99934277e1c26e6e8_056652_1699689064016_3da9a468c1eb83fa02b95b648d0be77640f46150'
        }
      }).then(res => {
        this.baseData = res.data.info
        API.batchAddOrUpdateMapBase({ detailJson: JSON.stringify({ info: this.baseData }) }).then(res => {
          console.log(res)
        })
        this.drawPort()
        this.drawTerminal()
        this.drawBerth()
      })
    },
    // tab切换回港口显示
    tabBackToPort () {
      this.isPortTabShow = true
      this.isTerminalTabShow = false
      this.curTabName = '预抵'
    },
    // 获取码头信息
    getTerminalList (terminalId, terminalName) {
      this.curTerminalId = terminalId
      this.curSelectTerminalName = terminalName
      this.wharfLabel = terminalName
      this.portModalShow = true
      this.isPortTabShow = false
      this.isTerminalTabShow = true
      // this.curTerminalName = '码头'
      let _param = {
        terminalsId: terminalId
      }
      API.queryShipFormulaInfo({
        url: 'https://mobile.shipformula.com/v1/api/backend/toolbox/TerminalLine/refreshMyShipStatus',
        paramMap: JSON.stringify(_param)
      }).then(res => {
        if (res.data.Code === 10000) {
          this.wharfList = res.data.Result.info
        }
      })
    },
    getPortList (portId, portName) {
      this.curPortId = portId
      this.curPortName = portName
      this.isPortTabShow = true
      // this.curTabName = '预抵'
      this.isTerminalSingalClick = false
      this.isTerminalTabShow = false
      this.portTitle = '港区动态 - ' + portName
      let _param = {
        mapbase_ports_id: portId
      }
      this.portModalShow = true
      this.portLoading = true
      // 获取预抵船舶数据
      API.queryShipFormulaInfo({
        url: 'https://mobile.shipformula.com/v1/api/node/map/MapBaseControl/getPortTrafficEstimate',
        paramMap: JSON.stringify(_param)
      }).then(res => {
        this.portLoading = false
        if (res.data.Code === 10000) {
          this.portEtaList = res.data.Result.info.rows
          this.estimateLabel = (h) => {
            return h('div', [
              h('span', '预抵'),
              h('Badge', {
                props: {
                  offset: [0, 20],
                  count: this.portEtaList.length,
                  type: 'primary'
                }
              })
            ])
          }
        }
      })
      // 获取等泊船舶数据
      API.queryShipFormulaInfo({
        url: 'https://mobile.shipformula.com/v1/api/node/map/MapBaseControl/getPortTrafficWait',
        paramMap: JSON.stringify(_param)
      }).then(res => {
        if (res.data.Code === 10000) {
          this.portAtaList = res.data.Result.info.rows
          this.waitLabel = (h) => {
            return h('div', [
              h('span', '等泊'),
              h('Badge', {
                props: {
                  count: this.portAtaList.length,
                  type: 'warning'
                }
              })
            ])
          }
        }
      })
      // 获取作业船舶数据
      API.queryShipFormulaInfo({
        url: 'https://mobile.shipformula.com/v1/api/node/map/MapBaseControl/getPortTrafficWork',
        paramMap: JSON.stringify(_param)
      }).then(res => {
        if (res.data.Code === 10000) {
          this.portAtbList = res.data.Result.info.rows
          this.workLabel = (h) => {
            return h('div', [
              h('span', '作业'),
              h('Badge', {
                props: {
                  count: this.portAtbList.length,
                  type: 'success'
                }
              })
            ])
          }
        }
      })
    },
    // 获取泊位详情数据
    berthDetail (idx) {
      this.allBerthList[idx].isTableShow = !this.allBerthList[idx].isTableShow
      this.$forceUpdate()
      if (this.allBerthList[idx].isTableShow) {
        let _param = {
          berthId: this.allBerthList[idx].berthId,
          berthName: this.allBerthList[idx].name
        }
        API.queryShipFormulaInfo({
          url: 'https://mobile.shipformula.com/v1/api/backend/toolbox/TerminalLine/myBerthStatus',
          paramMap: JSON.stringify(_param)
        }).then(res => {
          if (res.data.Code === 10000) {
            this.allBerthList[idx].tableData = res.data.Result.info
            this.$forceUpdate()
          }
        })
      }
    },
    // 获取当前码头所有泊位信息
    getAllBerthList (terminalId) {
      let _param = {
        terminalsId: terminalId
      }
      this.wharfLoading = true
      API.queryShipFormulaInfo({
        url: 'https://mobile.shipformula.com/v1/api/backend/toolbox/TerminalLine/myAllBerthStatus',
        paramMap: JSON.stringify(_param)
      }).then(res => {
        this.wharfLoading = false
        if (res.data.Code === 10000) {
          this.allBerthList = res.data.Result.info
          this.allBerthList.forEach(item => {
            Object.assign(item, {
              isTableShow: false,
              tableData: []
            })
          })
        }
      })
    },
    // 绘制港口
    drawPort () {
      this.removeAllLayer()
      if (!this.isShow) return
      let pointObj = this.getRectPoint()
      for (let p of this.baseData.ports) {
        let loc = {
          lat: Number(p.latitude),
          lng: Number(p.longitude)
        }
        let _point = [loc.lat, loc.lng]
        // eslint-disable-next-line no-undef
        let myIcon = L.icon({
          iconUrl: this.markIconList[0].url
        })
        if (this.map.getZoom() >= 8 && this.isInPoint(_point, pointObj)) {
          // eslint-disable-next-line no-undef
          let portMarker = L.marker(_point, { icon: myIcon, riseOnHover: true }).bindTooltip(p.name, {
            direction: 'bottom',
            offset: [6, 12],
            permanent: true,
            opacity: '1',
            className: 'div_tip'
          }).openTooltip()
          Object.assign(portMarker, {
            id: p.id,
            name: p.name,
            type: 'port',
            zoom: 8
          })
          portMarker.on('click', item => {
            this.getPortList(item.target.id, item.target.name)
          })
          this.map.addLayer(portMarker)
        }
      }
    },
    // 绘制码头
    drawTerminal () {
      if (!this.isShow) return
      let pointObj = this.getRectPoint()
      for (let p of this.baseData.terminals) {
        let loc = {
          lat: Number(p.latitude),
          lng: Number(p.longitude)
        }
        let _point = [loc.lat, loc.lng]
        // eslint-disable-next-line no-undef
        let myIcon = L.icon({
          iconUrl: this.markIconList[1].url
        })
        if (this.map.getZoom() >= 12 && this.isInPoint(_point, pointObj)) {
          // eslint-disable-next-line no-undef
          let terminalMarker = L.marker(_point, { icon: myIcon, riseOnHover: true }).bindTooltip(p.name, {
            direction: 'bottom',
            offset: [6, 12],
            permanent: true,
            opacity: '1',
            className: 'div_tip'
          }).openTooltip()
          Object.assign(terminalMarker, {
            id: p.id,
            name: p.name,
            type: 'terminal',
            zoom: 12
          })
          terminalMarker.on('click', item => {
            this.isTerminalSingalClick = true
            this.getTerminalList(item.target.id, item.target.name)
            this.getAllBerthList(item.target.id)
          })
          this.map.addLayer(terminalMarker)
        }
      }
    },
    // 绘制泊位
    drawBerth () {
      if (!this.isShow) return
      let pointObj = this.getRectPoint()
      for (let p of this.baseData.berths) {
        let loc = {
          lat: Number(p.latitude),
          lng: Number(p.longitude)
        }
        let _point = [loc.lat, loc.lng]
        if (this.map.mapType === 'MT_SATELLITE') {
          let transPoint = transLatlng.wgs84togcj02(parseFloat(_point[1]), parseFloat(_point[0]))
          _point = [transPoint[1], transPoint[0]]
        }
        // eslint-disable-next-line no-undef
        let myIcon = L.icon({
          iconUrl: this.markIconList[2].url
        })
        if (this.map.getZoom() >= 16 && this.isInPoint(_point, pointObj)) {
          // eslint-disable-next-line no-undef
          let berthMarker = L.marker(_point, { icon: myIcon, interactive: false }).bindTooltip(p.name, {
            direction: 'bottom',
            offset: [6, 12],
            permanent: true,
            opacity: '1',
            className: 'div_tip'
          }).openTooltip()
          Object.assign(berthMarker, {
            id: p.id,
            type: 'berth',
            zoom: 16
          })
          this.map.addLayer(berthMarker)
        }
      }
    },
    // 移除所有图层
    removeAllLayer () {
      let _that = this
      this.map.eachLayer(function (layer) {
        if (layer && layer.id && layer.type !== 'anchorage') {
          _that.map.removeLayer(layer)
          layer.closeTooltip()
        }
      })
    }
  },
  created () {
    setTimeout(() => {
      // eslint-disable-next-line no-undef
      this.portPointMarkers = L.layerGroup()
      // eslint-disable-next-line no-undef
      this.terminalMarkers = L.layerGroup()
      // eslint-disable-next-line no-undef
      this.berthMarkers = L.layerGroup()
      this.initAreaData()
    }, 100)
  }
}
</script>
<style>
.vertical-right-modal .ivu-modal .ivu-modal-content {
  left: var(--docWidth);
}
.div_tip {
  background: rgba(255, 255, 255,0.8);
  padding: 4px 5px;
  color: #333;
  font-size: 12px;
  /* font-weight: bold; */
  line-height: 14px;
  /* border: 1px solid #333; */
  z-index: 1;
}
.leaflet-tooltip-bottom:before {
  border: none;
}
.port_area_con .ivu-modal-header {
  background: #4880FF;
  color: #fff;
}
.port_area_con .ivu-modal-header .ivu-modal-header-inner {
  color: #fff;
}
.port_area_con .ivu-modal-close .ivu-icon-ios-close {
  color:#fff;
}
.port_list_area .ivu-badge {
  margin-left: 5px !important;
}
.berth_contain {
  max-height: 450px;
  overflow: auto;
}
.berth_contain_mobile {
  max-height: 200px;
  overflow: auto;
}
.berth_card {
  margin-bottom: 10px;
}
.berth_top_title {
  height: 40px;
  line-height: 40px;
}
.berth_contain .berth_title {
  text-align: center;
}
.berth_more_btn {
  margin-top: 10px;
}
.reload_btn_mobile {
  position: absolute !important;
  right: 40px;
  top: 11px;
  z-index: 8888;
}
</style>
