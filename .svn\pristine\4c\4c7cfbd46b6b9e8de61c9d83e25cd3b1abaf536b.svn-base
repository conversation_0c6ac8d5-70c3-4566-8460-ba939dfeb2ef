<template>
  <Modal
    v-model="modalData.modal"
    @on-visible-change="modalShow"
    :closable="false"
    :mask-closable="false"
    width="500">
    <div slot="header">
      <span class="header-title">{{ modalData.title }}</span>
      <DatePicker class="header-time" type="date" @on-change="dateChange" placeholder="选择日期"></DatePicker>
      <Button class="header-btn" @click="importData">导入上次数据</Button>
    </div>
    <Form ref="formPrice" :model="formPrice" :label-width="65" class="formdiv">
      <div v-for="(item, index) in formPrice.detailJson" :key="index">
        <Row>
          <Col span="10">
            <FormItem label="货品" :prop="'detailJson.' + index + '.cargo_id'" :rules="{required: true, message: '此处不能为空', trigger: 'change'}">
              <Select v-model="item.cargo_id" filterable @on-open-change="goodsIdOpen(index)" @on-change="goodsIdSelected">
                <Option v-for="(item1, idx) in goodsList" :key="idx" :value="item1.id" v-show="!goodsIdList.includes(item1.id)">{{ item1.cargo_name }}</Option>
              </Select>
            </FormItem>
          </Col>
          <Col span="10">
            <FormItem label="价格" :prop="'detailJson.' + index + '.cargo_price'" :rules="{required: true, message: '此处不能为空', trigger: 'change'}">
              <Input type='number' v-model='item.cargo_price' placeholder="请填写货品价格"></Input>
            </FormItem>
          </Col>
          <Col span="2">
            <Button type="text" @click="handleRemove(index)" icon="md-remove-circle" class="formbtn"></Button>
          </Col>
          <Col span="2">
            <Button v-if="index === (formPrice.detailJson.length - 1)" type="text" @click="handleAdd()" icon="md-add-circle" class="formbtn addbtn"></Button>
          </Col>
        </Row>
      </div>
    </Form>
    <div slot="footer">
      <Button @click="clearData" style="margin-right:10px;">取消</Button>
      <Button type="primary" @click="saveGoodsPrice">保存</Button>
    </div>
  </Modal>
</template>
<script>
import { queryBasicCargoList, querySysDate } from '@/api/basicData'
import API from '@/api/basicConfig/goodsConfig'

export default {
  props: {
    modalData: Object
  },
  data () {
    return {
      sysDate: '', // 系统时间
      curDate: '', // 当前时间
      goodsList: [], // 获品列表
      curGoodsId: '', // 存储当前展开的货品id
      goodsIdList: [], // 获品id去重存放数组
      formPrice: {
        ship_company_id: '',
        detailJson: [
          {
            cargo_id: '',
            cargo_date: '',
            cargo_price: ''
          }
        ]
      }
    }
  },
  methods: {
    // 获取货品列表
    getGoodsList () {
      queryBasicCargoList({ cargo_name: '' }).then(res => {
        if (res.data.Code === 10000) {
          this.goodsList = res.data.Result
        }
      })
    },
    // 保存今日调价数据
    saveGoodsPrice () {
      this.$refs['formPrice'].validate((valid) => {
        if (valid) {
          let curJson = this.formPrice.detailJson.map(item => {
            return {
              cargo_id: item.cargo_id,
              cargo_date: this.curDate === '' ? this.sysDate : this.curDate,
              cargo_price: item.cargo_price
            }
          })
          let param = {
            ship_company_id: this.formPrice.ship_company_id,
            detailJson: JSON.stringify(curJson)
          }
          API.addBasicGoodsPrice(param).then(res => {
            if (res.data.Code === 10000) {
              this.modalData.modal = false
              this.$Message.success(res.data.Message)
              this.$emit('callback')
            } else {
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
    },
    importData () {
      API.queryBasicGoodsLatelyPriceList().then(res => {
        if (res.data.Code === 10000) {
          let newArr = res.data.Result.map(item => {
            return {
              cargo_id: item.cargo_id,
              cargo_price: item.cargo_price
            }
          })
          this.formPrice.detailJson = newArr
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 日期变化
    dateChange (val) {
      this.curDate = val
    },
    // 获品展开
    goodsIdOpen (index) {
      this.curGoodsId = this.formPrice.detailJson[index].id
    },
    // 存储已选择的数据
    goodsIdSelected (d) {
      if (this.goodsIdList.includes(this.curGoodsId)) {
        let idIndex = this.goodsIdList.indexOf(this.curGoodsId)
        this.goodsIdList.splice(idIndex, 1)
      }
      this.goodsIdList.push(d)
    },
    // 添加货品信息内容
    handleAdd () {
      this.formPrice.detailJson.push({
        cargo_id: '',
        cargo_date: '',
        cargo_price: ''
      })
    },
    // 移除货品信息内容
    handleRemove (index) {
      if (this.formPrice.detailJson.length > 1) {
        this.goodsIdList.splice(index, 1)
        this.formPrice.detailJson.splice(index, 1)
      } else {
        this.$Message.warning('请至少保留一条货品数据！')
      }
    },
    clearData () {
      this.curGoodsId = ''
      this.goodsIdList = []
      this.formPrice = {
        ship_company_id: '',
        detailJson: [
          {
            cargo_id: '',
            cargo_date: '',
            cargo_price: ''
          }
        ]
      }
      this.$refs['formPrice'].resetFields()
      this.modalData.modal = false
    },
    getSysDate () {
      querySysDate().then(res => {
        if (res.data.Code === 10000) {
          this.sysDate = res.data.systemDate.split(' ')[0]
        }
      })
    },
    // 显隐操作
    modalShow (val) {
      if (val) {
        this.getGoodsList()
        this.getSysDate()
      } else {
        this.clearData()
      }
    }
  }
}
</script>
<style scoped>
  .header-title {
    font-size: 16px;
    font-weight: bold;
  }
  .header-time {
    width: 130px;
    float: right;
    margin-top: -8px;
  }
  .header-btn {
    float: right;
    margin-top: -8px;
    margin-right: 10px;
    height: 32px;
    line-height: 12px;
  }
  .formbtn {
    font-size: 24px;
    padding: 0 3px;
  }
  .formbtn.addbtn {
    right: 0;
    bottom: 18px;
  }
</style>
