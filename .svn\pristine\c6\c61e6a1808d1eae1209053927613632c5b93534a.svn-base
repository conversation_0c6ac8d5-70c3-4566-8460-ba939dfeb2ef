<template>
  <Drawer
    v-model="modalData.modal"
    :title="modalData.title"
    :data="modalData"
    width="850"
    @on-visible-change="modalShow">
    <Form ref="formData" :model="formData" :rules="ruleForm" :label-width="85">
      <h3 class="bold-font title-font">基础信息</h3>
      <Row>
        <Col span="6">
          <FormItem label="船名中文" prop="ship_name">
            <Input v-model="formData.ship_name" :readonly="modalData.dialogType === 'detail'"></Input>
          </FormItem>
        </Col>
        <Col span="6">
          <FormItem label="mmsi" prop="mmsi">
            <Input v-model="formData.mmsi" :readonly="modalData.dialogType === 'detail'"></Input>
          </FormItem>
        </Col>
        <Col span="6">
          <FormItem label="船长(米)" prop="length">
            <Input v-model="formData.length" :readonly="modalData.dialogType === 'detail'"></Input>
          </FormItem>
        </Col>
        <Col span="6">
          <FormItem label="最大吃水" prop="max_draught">
            <Input v-model="formData.max_draught" :readonly="modalData.dialogType === 'detail'"></Input>
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span="6">
          <FormItem label="船名英文" prop="ship_name_en">
            <Input v-model="formData.ship_name_en" :readonly="modalData.dialogType === 'detail'"></Input>
          </FormItem>
        </Col>
        <Col span="6">
          <FormItem label="呼号" prop="call_sign">
            <Input v-model="formData.call_sign" :readonly="modalData.dialogType === 'detail'"></Input>
          </FormItem>
        </Col>
        <Col span="6">
          <FormItem label="船宽(米)" prop="breadth">
            <Input v-model="formData.breadth" :readonly="modalData.dialogType === 'detail'"></Input>
          </FormItem>
        </Col>
        <Col span="6">
          <FormItem label="满载吃水" prop="load_draught">
            <Input v-model="formData.load_draught" :readonly="modalData.dialogType === 'detail'"></Input>
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span="6">
          <FormItem label="船舶类型" prop="ship_type">
            <Select v-model="formData.ship_type" filterable :disabled="modalData.dialogType === 'detail'">
              <Option v-for="(item, index) in shipTypeList" :key="index" :value="item.entry_key">{{ item.entry_name }}</Option>
            </Select>
          </FormItem>
        </Col>
        <Col span="6">
          <FormItem label="IMO" prop="imo_number">
            <Input v-model="formData.imo_number" :readonly="modalData.dialogType === 'detail'"></Input>
          </FormItem>
        </Col>
        <Col span="6">
          <FormItem label="船容" prop="capacity">
            <Input v-model="formData.capacity" :readonly="modalData.dialogType === 'detail'"></Input>
          </FormItem>
        </Col>
        <Col span="6">
          <FormItem label="空载吃水" prop="light_draught">
            <Input v-model="formData.light_draught" :readonly="modalData.dialogType === 'detail'"></Input>
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span="6">
          <FormItem label="曾用名">
            <Input v-model="formData.used_name" :readonly="modalData.dialogType === 'detail'"></Input>
          </FormItem>
        </Col>
        <Col span="6">
          <!-- <FormItem label="交易类型">
            <Select v-model="formData.transaction_type" filterable :disabled="modalData.dialogType === 'detail'">
              <Option value="CRUDE_FOREIGN">CRUDE_FOREIGN</Option>
              <Option value="DOMESTIC">DOMESTIC</Option>
              <Option value="INLAND">INLAND</Option>
              <Option value="other">other</Option>
            </Select>
          </FormItem> -->
          <FormItem label="营运模式">
            <Select v-model="formData.business_model" filterable :disabled="modalData.dialogType === 'detail'">
              <Option value="">无</Option>
              <Option value="1">自营</Option>
              <Option value="2">期租</Option>
              <Option value="3">国际</Option>
            </Select>
          </FormItem>
        </Col>
        <Col span="6">
          <FormItem label="总吨" prop="gross_tonage">
            <Input v-model="formData.gross_tonage" :readonly="modalData.dialogType === 'detail'"></Input>
          </FormItem>
        </Col>
        <Col span="6">
          <FormItem label="载重吨" prop="expected_deadweight">
            <Input v-model="formData.deadweight_tonnage" :readonly="modalData.dialogType === 'detail'"></Input>
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span="6">
          <FormItem label="船籍">
            <Select v-model="formData.registry_port_id" filterable :disabled="modalData.dialogType === 'detail'">
              <Option v-for="(item, index) in registryPortList" :key="index" :value="item.entry_key">{{ item.entry_name }}</Option>
            </Select>
          </FormItem>
        </Col>
        <Col span="6">
          <FormItem label="建造时间" prop="build_date">
            <DatePicker type="date" v-model="formData.build_date" @on-change="data=>formData.build_date=data" :readonly="modalData.dialogType === 'detail'" style="width: 120px"></DatePicker>
          </FormItem>
        </Col>
        <Col span="6">
          <FormItem label="净吨" prop="net_tonage">
            <Input v-model="formData.net_tonage" :readonly="modalData.dialogType === 'detail'"></Input>
          </FormItem>
        </Col>
        <Col span="6">
          <FormItem label="预计载重吨" prop="deadweight_tonnage">
            <Input v-model="formData.expected_deadweight" :readonly="modalData.dialogType === 'detail'"></Input>
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span="13">
          <h3 class="bold-font title-font">运营信息</h3>
          <FormItem label="船舶所有者" :label-width="150">
            <Input v-model="formData.ship_owner" :readonly="modalData.dialogType === 'detail'"></Input>
          </FormItem>
          <FormItem label="船舶经营者" :label-width="150">
            <Input v-model="formData.operator" :readonly="modalData.dialogType === 'detail'"></Input>
          </FormItem>
          <FormItem label="船舶所有者（中文）" prop="owner" :label-width="150">
            <Input v-model="formData.owner" :readonly="modalData.dialogType === 'detail'"></Input>
          </FormItem>
          <FormItem label="船舶所有者联系方式" prop="owner_phone" :label-width="150">
            <Input v-model="formData.owner_phone" :readonly="modalData.dialogType === 'detail'"></Input>
          </FormItem>
          <FormItem label="船舶所有者地址（英文）" prop="ship_owner_address_en" :label-width="150">
            <Input v-model="formData.ship_owner_address_en" :readonly="modalData.dialogType === 'detail'"></Input>
          </FormItem>
          <FormItem label="船舶所有者地址（中文）" prop="ship_owner_address_cn" :label-width="150">
            <Input v-model="formData.ship_owner_address_cn" :readonly="modalData.dialogType === 'detail'"></Input>
          </FormItem>
          <FormItem label="船舶管理人地址（英文）" prop="ship_operator_address_en" :label-width="150">
            <Input v-model="formData.ship_operator_address_en" :readonly="modalData.dialogType === 'detail'"></Input>
          </FormItem>
          <FormItem label="船舶管理人地址（中文）" prop="ship_operator_address_cn" :label-width="150">
            <Input v-model="formData.ship_operator_address_cn" :readonly="modalData.dialogType === 'detail'"></Input>
          </FormItem>
        </Col>
        <Col span="8" offset="2">
          <h3 class="bold-font title-font">船舶照片</h3>
          <FormItem style="display:none">
            <Input type="text" v-model='formData.ship_image'></Input>
          </FormItem>
          <div class="userPic">
            <div class="defaultBg" v-if="imgBaseUrl === ''"></div>
            <img :src="imgBaseUrl" alt="" v-else>
          </div>
          <Upload action=''
            :show-upload-list='false'
            accept=".jpg, .jpeg, .png"
            :format="['jpg','jpeg','png']"
            :max-size="2048"
            :before-upload="handleImgUpload"
            style="margin:20px 38% 0;">
              <Button v-if="modalData.dialogType !== 'detail'">更换</Button>
          </Upload>
        </Col>
      </Row>
      <h3 class="bold-font title-font">备注信息</h3>
      <Row>
        <Col>
          <FormItem :label-width="0">
            <Input v-model="formData.comments" type="textarea" :autosize="true" placeholder="这里是备注信息" :readonly="modalData.dialogType === 'detail'"></Input>
          </FormItem>
        </Col>
      </Row>
    </Form>
    <div class="demo-drawer-footer">
      <Button @click="closeDrawer" style="margin-right:10px;">取消</Button>
      <Button type="primary" v-if="modalData.dialogType==='create'" @click="createData">保存</Button>
      <Button type="primary" v-if="modalData.dialogType==='update'" @click="updateData">保存</Button>
    </div>
  </Drawer>
</template>
<script>
import { dictEntryList, avatarImage } from '@/api/basicData'
import { validateEnglishNumber } from '@/libs/iViewValidate'
import { addBasicShip, updateBasicShip } from '@/api/shipManagement'
export default {
  props: {
    modalData: Object
  },
  data () {
    return {
      imgBaseUrl: '',
      uploadData: '',
      shipTypeList: [],
      registryPortList: [],
      formData: {},
      ruleForm: {
        ship_name: [{ required: true, message: '船名不能为空！', trigger: 'blur' }],
        mmsi: [
          { required: true, message: 'mmsi不能为空！', trigger: 'blur' },
          { required: true, validator: validateEnglishNumber, trigger: 'blur' }
        ],
        length: [{ required: false, validator: (r, v, b) => { (v && !(/^[0-9]+([.]{1}[0-9]+){0,1}$/.test(v))) ? b('请输入整数或小数') : b() }, trigger: 'blur' }],
        max_draught: [{ required: false, validator: (r, v, b) => { (v && !(/^[0-9]+([.]{1}[0-9]+){0,1}$/.test(v))) ? b('请输入整数或小数') : b() }, trigger: 'blur' }],
        call_sign: [{ required: false, validator: validateEnglishNumber, trigger: 'blur' }],
        breadth: [{ required: false, validator: (r, v, b) => { (v && !(/^[0-9]+([.]{1}[0-9]+){0,1}$/.test(v))) ? b('请输入整数或小数') : b() }, trigger: 'blur' }],
        load_draught: [{ required: false, validator: (r, v, b) => { (v && !(/^[0-9]+([.]{1}[0-9]+){0,1}$/.test(v))) ? b('请输入整数或小数') : b() }, trigger: 'blur' }],
        imo_number: [{ required: false, validator: validateEnglishNumber, trigger: 'blur' }],
        capacity: [{ required: false, validator: (r, v, b) => { (v && !(/^[0-9]+([.]{1}[0-9]+){0,1}$/.test(v))) ? b('请输入整数或小数') : b() }, trigger: 'blur' }],
        light_draught: [{ required: false, validator: (r, v, b) => { (v && !(/^[0-9]+([.]{1}[0-9]+){0,1}$/.test(v))) ? b('请输入整数或小数') : b() }, trigger: 'blur' }],
        gross_tonage: [{ required: false, validator: (r, v, b) => { (v && !(/^[0-9]+([.]{1}[0-9]+){0,1}$/.test(v))) ? b('请输入整数或小数') : b() }, trigger: 'blur' }],
        expected_deadweight: [{ required: false, validator: (r, v, b) => { (v && !(/^[0-9]+([.]{1}[0-9]+){0,1}$/.test(v))) ? b('请输入整数或小数') : b() }, trigger: 'blur' }],
        net_tonage: [{ required: false, validator: (r, v, b) => { (v && !(/^[0-9]+([.]{1}[0-9]+){0,1}$/.test(v))) ? b('请输入整数或小数') : b() }, trigger: 'blur' }],
        deadweight_tonnage: [{ required: false, validator: (r, v, b) => { (v && !(/^[0-9]+([.]{1}[0-9]+){0,1}$/.test(v))) ? b('请输入整数或小数') : b() }, trigger: 'blur' }],
        owner: [{ required: false, validator: (r, v, b) => { (v && !(/^[\u4E00-\u9FA5]+$/.test(v))) ? b('请输入中文！') : b() }, trigger: 'blur' }]
      }
    }
  },
  methods: {
    modalShow (val) {
      if (val) {
        // 获取船舶类型数据
        dictEntryList({ dic_code: 'shipType' }).then(e => {
          if (e.data.Code === 10000) {
            this.shipTypeList = e.data.Result.map(item => {
              return {
                entry_name: item.entry_name,
                entry_key: item.entry_key
              }
            })
          }
        })
        // 获取船籍
        dictEntryList({ dic_code: 'classificationSociety' }).then(e => {
          if (e.data.Code === 10000) {
            this.registryPortList = e.data.Result.map(item => {
              return {
                entry_name: item.entry_name,
                entry_key: item.entry_key
              }
            })
            this.formData.registry_port_id = this.registryPortList[0].entry_key
          }
        })
        if (this.modalData.dialogType !== 'create') {
          this.formData = { ...{}, ...this.modalData.data }
          this.imgBaseUrl = this.formData.ship_image
        }
      } else {
        this.$nextTick(() => {
          this.formData = {}
          this.$refs['formData'].resetFields()
        })
        this.uploadData = ''
        this.imgBaseUrl = ''
      }
    },
    // 上传图片
    handleImgUpload (file) {
      const isLt2M = file.size / 1024 / 1024 < 2
      const fileExt = file.name.split('.').pop().toLocaleLowerCase()
      if (!isLt2M) {
        this.$Message.warning('附件过大，附件最大2M')
      } else {
        if (fileExt === 'jpg' || fileExt === 'jpeg' || fileExt === 'png') {
          let that = this
          let reader = new FileReader()
          reader.readAsDataURL(file)
          reader.onload = function (e) {
            that.imgBaseUrl = e.target.result
          }
          this.uploadData = new FormData()
          this.uploadData.append('file', file)
          return false
        } else {
          this.$Message.warning(`${file.name}格式错误`)
          return false
        }
      }
    },
    // 新增
    createData () {
      let that = this
      that.$refs['formData'].validate((valid) => {
        if (valid) {
          that.$Modal.confirm({
            title: '提示',
            content: '<p>是否保存船舶数据？</p>',
            loading: true,
            onOk: () => {
              const userModifyFn = () => {
                let data = {
                  ship_id: that.formData.ship_id,
                  ship_name: that.formData.ship_name,
                  mmsi: that.formData.mmsi,
                  length: that.formData.length,
                  max_draught: that.formData.max_draught,
                  ship_name_en: that.formData.ship_name_en,
                  call_sign: that.formData.call_sign,
                  breadth: that.formData.breadth,
                  load_draught: that.formData.load_draught,
                  ship_type: that.formData.ship_type,
                  imo_number: that.formData.imo_number,
                  capacity: that.formData.capacity,
                  light_draught: that.formData.light_draught,
                  used_name: that.formData.used_name,
                  transaction_type: that.formData.transaction_type,
                  business_model: that.formData.business_model,
                  gross_tonage: that.formData.gross_tonage,
                  deadweight_tonnage: that.formData.deadweight_tonnage,
                  registry_port_id: that.formData.registry_port_id,
                  build_date: that.formData.build_date,
                  net_tonage: that.formData.net_tonage,
                  expected_deadweight: that.formData.expected_deadweight,
                  ship_owner: that.formData.ship_owner,
                  operator: that.formData.operator,
                  owner: that.formData.owner,
                  owner_phone: that.formData.owner_phone,
                  ship_owner_address_en: that.formData.ship_owner_address_en,
                  ship_owner_address_cn: that.formData.ship_owner_address_cn,
                  ship_operator_address_en: that.formData.ship_operator_address_en,
                  ship_operator_address_cn: that.formData.ship_operator_address_cn,
                  comments: that.formData.comments,
                  ship_image: that.formData.ship_image
                }
                addBasicShip(data).then(res => {
                  if (res.data.Code === 10000) {
                    that.loading = false
                    that.$Modal.remove()
                    that.$Message.success(res.data.Message)
                    that.$emit('callback')
                    that.modalData.modal = false
                  } else {
                    that.loading = false
                    that.$Modal.remove()
                    that.$Message.error(res.data.Message)
                  }
                })
              }
              if (that.uploadData === '') {
                userModifyFn()
              } else {
                avatarImage({ base64File: that.imgBaseUrl }).then(e => {
                  that.formData.ship_image = e.data.fileUrl
                  tempData = Object.assign({}, that.formData)
                  if (e.data.Code === -10000) {
                    that.$Message.warning(response.data.Message)
                  } else {
                    userModifyFn()
                  }
                })
              }
            }
          })
        }
      })
    },
    // 编辑
    updateData () {
      let that = this
      that.$refs['formData'].validate((valid) => {
        if (valid) {
          that.$Modal.confirm({
            title: '提示',
            content: '<p>是否保存船舶数据？</p>',
            loading: true,
            onOk: () => {
              if (typeof that.formData.build_date !== 'string') {
                let _nowDate = new Date(that.formData.build_date)
                let _year = _nowDate.getFullYear()
                let _month = _nowDate.getMonth() + 1
                let _day = _nowDate.getDate()
                that.formData.build_date = _year + '-' + _month + '-' + _day
              }
              const userModifyFn = () => {
                let data = {
                  ship_id: that.formData.ship_id,
                  ship_name: that.formData.ship_name,
                  mmsi: that.formData.mmsi,
                  length: that.formData.length,
                  max_draught: that.formData.max_draught,
                  ship_name_en: that.formData.ship_name_en,
                  call_sign: that.formData.call_sign,
                  breadth: that.formData.breadth,
                  load_draught: that.formData.load_draught,
                  ship_type: that.formData.ship_type,
                  imo_number: that.formData.imo_number,
                  capacity: that.formData.capacity,
                  light_draught: that.formData.light_draught,
                  used_name: that.formData.used_name,
                  transaction_type: that.formData.transaction_type,
                  business_model: that.formData.business_model,
                  gross_tonage: that.formData.gross_tonage,
                  deadweight_tonnage: that.formData.deadweight_tonnage,
                  registry_port_id: that.formData.registry_port_id,
                  build_date: that.formData.build_date,
                  net_tonage: that.formData.net_tonage,
                  expected_deadweight: that.formData.expected_deadweight,
                  ship_owner: that.formData.ship_owner,
                  operator: that.formData.operator,
                  owner: that.formData.owner,
                  owner_phone: that.formData.owner_phone,
                  ship_owner_address_en: that.formData.ship_owner_address_en,
                  ship_owner_address_cn: that.formData.ship_owner_address_cn,
                  ship_operator_address_en: that.formData.ship_operator_address_en,
                  ship_operator_address_cn: that.formData.ship_operator_address_cn,
                  comments: that.formData.comments,
                  ship_image: that.formData.ship_image
                }
                updateBasicShip(data).then(res => {
                  if (res.data.Code === 10000) {
                    that.loading = false
                    that.$Modal.remove()
                    that.$Message.success(res.data.Message)
                    that.$emit('callback')
                    that.modalData.modal = false
                  } else {
                    that.loading = false
                    that.$Modal.remove()
                    that.$Message.error(res.data.Message)
                  }
                })
              }
              if (that.uploadData === '') {
                userModifyFn()
              } else {
                avatarImage({ base64File: that.imgBaseUrl }).then(e => {
                  that.formData.ship_image = e.data.fileUrl
                  tempData = Object.assign({}, that.formData)
                  if (e.data.Code === -10000) {
                    that.$Message.warning(response.data.Message)
                  } else {
                    userModifyFn()
                  }
                })
              }
            }
          })
        }
      })
    },
    // 关闭弹窗
    closeDrawer () {
      this.modalData.modal = false
    }
  }
}
</script>
<style lang="less" scoped>
  .defaultBg {
    width: 100%;
    height: 150px;
    background: #E6E9ED;
  }
  .userPic {
    width: 100%;
    height: 150px;
    display: flex;
    img {
      max-width: 100%;
      margin: 0 auto;
    }
  }
</style>
