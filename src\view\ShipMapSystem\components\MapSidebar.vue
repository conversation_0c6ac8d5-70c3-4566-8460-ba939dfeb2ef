<template>
  <div class="map-sidebar">
    <div 
      v-for="item in sidebarItems" 
      :key="item.key"
      class="sidebar-item" 
      :class="{ active: activeLayer === item.key }" 
      @click="toggleLayer(item.key)"
    >
      <Icon :type="item.icon" size="24" />
      <span>{{ item.label }}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MapSidebar',
  props: {
    activeLayer: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      sidebarItems: [
        { key: 'ships', label: '船舶', icon: 'md-boat' },
        { key: 'typhoon', label: '台风', icon: 'md-thunderstorm' },
        { key: 'tide', label: '潮汐', icon: 'md-water' },
        { key: 'routes', label: '航线', icon: 'md-git-network' },
        { key: 'ports', label: '港口', icon: 'md-pin' },
        { key: 'warnings', label: '预警', icon: 'md-warning' },
        { key: 'schedule', label: '排期', icon: 'md-calendar' }
      ]
    };
  },
  methods: {
    toggleLayer(key) {
      this.$emit('toggle-layer', key);
    }
  }
};
</script>

<style scoped>
.map-sidebar {
  position: absolute;
  top: 20px;
  left: 20px;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  z-index: 1000;
}

.sidebar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 70px;
  height: 70px;
  cursor: pointer;
  transition: all 0.3s;
  color: #515a6e;
}

.sidebar-item:hover {
  background-color: #f8f8f9;
  color: #2d8cf0;
}

.sidebar-item.active {
  background-color: #e8f4ff;
  color: #2d8cf0;
  border-left: 3px solid #2d8cf0;
}

.sidebar-item span {
  margin-top: 5px;
  font-size: 12px;
}
</style> 