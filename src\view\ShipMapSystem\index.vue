<template>
  <fullscreen ref="fullscreen" @change="fullscreenChange">

    <div class="fleet-monitor">
      <!-- 顶部标题栏 -->
      <header class="header">
        <div class="logo">
          <i class="fas fa-ship logo-icon"></i>
          <span class="logo-text">Fleet Monitor</span>
        </div>
        <div class="datetime">{{ currentDateTime }}</div>
        <div class="notification-area">
          <div class="notification-container">
            <div class="alert" v-if="notifications.length > 0" :key="'notificate' + currentNotificationIndex">
              <i class="fas" :class="notifications[currentNotificationIndex].icon"
                :style="{ color: getNoticeColor(notifications[currentNotificationIndex].type) }"></i>
              <div class="alert-text-container">
                <div class="alert-text"
                  :class="{ 'scrolling-text': isTextOverflow(notifications[currentNotificationIndex].text) }">
                  <span>{{ notifications[currentNotificationIndex].text }}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="bell" @click="openAlarmModal">
            <i class="fas fa-bell" style="position: absolute;right: 0"></i>
            <span class="badge" v-if="notifications.length">{{ notifications.length }}</span>
          </div>
        </div>
        <Tooltip :content="fullscreen ? '退出全屏' : '全屏'" placement="bottom" style="position: absolute;right: 20px;">
          <Icon style="cursor: pointer; line-height: 30px; " @click.native="toggleFullScreen"
            :type="fullscreen ? 'md-contract' : 'md-expand'" :size="23"></Icon>
        </Tooltip>
      </header>

      <!-- 统计卡片区域 -->
      <div class="stat-cards">

        <div class="stat-card">
          <div class="stat-icon blue">
            <i class="fas fa-chart-line"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">
              <count-to class="num-chart" :end="statistic.chartVolumeNum" unitClass="num-unit" :usegroup="true" />
              <span class="unit">次</span>
            </div>
            <div class="stat-label">月航次数</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon purple">
            <i class="fas fa-ship"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value"> <count-to class="num-chart" :end="statistic.chartGoodsNum" unitClass="num-unit"
                :usegroup="true" :decimals="2" /> <span class="unit">万吨</span></div>
            <div class="stat-label">吨位量</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon green">
            <i class="fas fa-sync-alt"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value"> <count-to class="num-chart" :end="statistic.chartTurnoverNum" unitClass="num-unit"
                :usegroup="true" :decimals="2" /> <span class="unit">万公里</span></div>
            <div class="stat-label">周转量</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon orange">
            <i class="fas fa-clock"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value"> <count-to class="num-chart" :end="totalAnchorTime" unitClass="num-unit"
                :usegroup="true" />
              <span class="unit">小时</span>
            </div>
            <div class="stat-label">锚泊时长</div>
          </div>
        </div>
        <!-- <div class="stat-card">
          <div class="stat-icon yellow">
            <i class="fas fa-credit-card-alt"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">
              <count-to class="num-chart" :end="statistic.allShippingFee" unitClass="num-unit" :usegroup="true" />
              <span class="unit">元</span>
            </div>
            <div class="stat-label">总预收入</div>
          </div>
        </div> -->
      </div>

      <!-- 主内容区域 -->
      <div class="main-content" :style="{ height: !fullscreen ? '800px' : 'calc(100vh - 135px)' }">
        <!-- 左侧表格区域 -->
        <div class="left-panel">
          <div class="fleet-table-card">
            <div class="card-title">
              <div class="title-text">
                <i class="fas fa-list-ul"></i> 船队列表
              </div>
              <div class="autoplay-toggle">
                <input type="checkbox" id="autoplay-toggle" v-model="autoPlay" @change="toggleAutoPlay">
                <label for="autoplay-toggle">
                  <div class="toggle-track">
                    <div class="toggle-indicator"></div>
                  </div>
                  <span class="toggle-label">{{ autoPlay ? '自动播放' : '手动控制' }}</span>
                </label>
              </div>
            </div>
            <div class="fleet-table" ref="tbodyRef">
              <table>
                <thead>
                  <tr>
                    <th>船名</th>
                    <th>状态</th>
                    <th>目的港/所在港</th>
                    <th>当前航次</th>
                    <th>货物/货量</th>
                    <th>计划航次</th>
                    <th></th>
                  </tr>
                </thead>
                <tbody>
                  <template v-for="(ship, index) in dataList">
                    <tr :key="index"
                      :class="[getRowClass(ship.status, ship.anchorHours), { 'highlighted-ship': currentShipIndex === index }]"
                      @mouseenter="pauseAutoHighlight" @mouseleave="resumeAutoHighlight"
                      @click.stop="currentShipIndex = index">
                      <td> <a @click.stop="openShipDetail(ship, index)" style="cursor: pointer;font-weight: 600;">{{
                        ship.ship_name }}</a></td>
                      <td>
                        <div :class="['status-tag', getStatusClass(ship.status)]">
                          <i :class="getStatusIcon(ship.status)"></i> {{ ship.status }}
                          <span v-if="ship.status === '锚泊'" class="anchor-hours">{{ ship.anchorHours }}小时</span>
                        </div>
                      </td>
                      <td>
                        <div>
                          {{ ship.dest }}
                        </div>
                      </td>
                      <td :class="{ 'text-danger': ship.currentRoute === '待下发' }">
                        <span>{{ ship.currentRoute }}</span>
                        <div v-if="showWran(ship)" class="warning-container">
                          <i class="fas fa-exclamation-triangle warning-icon"
                            style="margin-left: 8px; color: #ff6b35; font-size: 14px; cursor: pointer;"
                            @mouseenter="handleWarningMouseEnter($event, ship, index)"
                            @mouseleave="handleWarningMouseLeave"></i>
                        </div>
                      </td>
                      <td>
                        <div>
                          {{ ship.cargo }} / {{ ship.amounts }}
                        </div>
                      </td>
                      <td>
                        <div style="display: flex;gap: 5px;align-items: center;">
                          <div v-if="ship.planList && ship.planList.length" class="plan-button"
                            :class="{ 'text-success': getFirstPlan(ship.planList) === '0' }">{{
                              getFirstPlan(ship.planList) === '0' ? '已下发' : '已预排' }}</div>
                          <div>
                            {{ getPlanText(ship.planList) }}
                          </div>
                        </div>
                      </td>
                      <td>
                        <div class="ai-text-btn" @click.stop="openAiAnalysis(ship, index)"
                          :class="{ 'ai-loading': aiAnalysisLoading[ship.ship_name], 'ai-disabled': aiAnalysisLoading[ship.ship_name] }">
                          <span class="ai-artistic-text" v-if="!aiAnalysisLoading[ship.ship_name]">AI</span>
                          <div class="ai-loading-dots" v-else>
                            <span></span>
                            <span></span>
                            <span></span>
                          </div>
                        </div>
                      </td>
                    </tr>
                    <!-- 展开行 - 仅在高亮时显示 -->
                    <tr :key="index + 'expanded'" v-if="currentShipIndex === index" class="expanded-row">
                      <td colspan="7" class="expanded-content">
                        <div class="ship-detail-expanded">
                          <div class="oil-info">
                            <div class="oil-item">
                              <i class="fas fa-tint heavy-oil-icon" style="color:#FF6B35"></i>
                              <span class="oil-label">重油:</span>
                              <span class="oil-value">{{ ship.heavy || '--' }}</span>
                            </div>
                            <div class="oil-item">
                              <i class="fas fa-tint light-oil-icon" style=" color: #00D4FF"></i>
                              <span class="oil-label">轻油:</span>
                              <span class="oil-value">{{ ship.light || '--' }}</span>
                            </div>
                            <div class="oil-item">
                              <span class="oil-label">当月累计货量:</span>
                              <span class="oil-value">{{ getRealAmount(ship.ship_name) }}万吨</span>
                            </div>
                            <div class="oil-item">
                              <span class="oil-label">当月锚泊总时长:</span>
                              <span class="oil-value">{{ getShipAnchorTime(ship.ship_name) }}小时</span>
                            </div>
                            <div class="oil-item">
                              <span class="oil-label">预抵时间:</span>
                              <span class="oil-value">{{ ship.eta_std }}</span>
                            </div>
                            <div class="oil-item">
                              <span class="oil-label">航程:</span>
                              <span class="oil-value">{{ ship.nmile }}海里</span>
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                  </template>
                </tbody>
              </table>
              <Spin size="large" class="table-loading" fix v-if="loading"></Spin>
            </div>
          </div>
        </div>

        <!-- 右侧地图和港口信息区域 -->
        <div class="right-panel">
          <!-- 地图区域 -->
          <div class="map-card">
            <div class="card-title">
              <div class="title-left">
                <i class="fas fa-map-marked-alt"></i> 船舶分布
              </div>

            </div>
            <div class="map-container">
              <div class="map-content">
                <div id="map"></div>
              </div>
            </div>
          </div>

          <!-- 港口信息区域 -->
          <div class="port-info-card">
            <div class="card-title">
              <div class="title-left">
                <i class="fas fa-anchor"></i> 港口信息
              </div>
              <div class="alarm-icon-btn" @click="openAlarmModal" title="查看告警详情">
                <i class="fas fa-bell warning-icon"></i>
              </div>
            </div>

            <!-- 天气信息 -->
            <div class="weather-section" style="position: relative;">
              <div class="section-title">
                <div>
                  <i class="fas fa-cloud-sun"></i> {{
                    dataList[currentShipIndex] ? dataList[currentShipIndex].dest : '' }} 天气预报
                </div>
                <div class="weather-more-btn" @click="openWindyWebsite" title="查看详细天气">
                  <i class="fas fa-external-link-alt"></i> 更多
                </div>
              </div>
              <template v-if="weatherInfo[currentShipIndex]">
                <div class="weather-current" @click="openWeatherModal(0)" style="cursor: pointer;">
                  <i class="fas weather-icon"
                    :class="[getWeatherIcon(weatherInfo[currentShipIndex][0].data[0].weather[0].description).icon, getWeatherIcon(weatherInfo[currentShipIndex][0].data[0].weather[0].description).color]"></i>
                  <div class="weather-info">
                    <div class="city-name">{{ dataList[currentShipIndex].dest }}</div>
                    <div class="temperature">{{ weatherInfo[currentShipIndex][0].data[0].weather[0].description }}, {{
                      weatherInfo[currentShipIndex][0].data[0].main.temp }}°C</div>
                  </div>
                  <div class="weather-update">
                    <div>
                      {{ weatherInfo[currentShipIndex][0].data[0].wind.deg }} {{
                        weatherInfo[currentShipIndex][0].data[0].wind.speed.toFixed(0) }}级
                    </div>
                    <!-- <div>能见度 {{ weatherInfo[currentShipIndex][0].data[0].visibility }}km</div> -->
                  </div>
                </div>

                <div class="weather-forecast">
                  <div class="forecast-item" v-for="(item, index) in weatherInfo[currentShipIndex]" :key="index"
                    style="cursor: pointer;" v-show="index !== 0" @click="openWeatherModal(index)">
                    <div class="day">{{ dayMap[item.day] }}</div>
                    <i class="fas weather-icon-small"
                      :class="[getWeatherIcon(item.data[0].weather[0].description).icon, getWeatherIcon(item.data[0].weather[0].description).color]"></i>
                    <div class="temp">{{ item.maxTemperature }}°/{{ item.minTemperature }}°</div>
                  </div>
                </div>
              </template>
              <Spin size="large" fix v-else></Spin>
            </div>

            <div class="divider"></div>

            <!-- 船舶统计 -->
            <div class="ship-stats-section">
              <div class="section-title">
                <div>

                  <i class="fas fa-info-circle"></i> {{
                    dataList[currentShipIndex] ? dataList[currentShipIndex].dest : '' }} 港口船舶信息
                </div>
              </div>
              <div class="ship-stats" style="position: relative;">
                <div class="stat-item" @click="openPortModal('estimate')">
                  <i class="fas fa-ship stat-item-icon ship-blue"></i>
                  <div class="stat-number">{{ portTrafficList.length ? portTrafficList[currentShipIndex].estimate.length
                    : 0 }}
                  </div>
                  <div class="stat-item-label">预抵</div>
                </div>
                <div class="stat-item" @click="openPortModal('wait')">
                  <i class="fas fa-arrow-up stat-item-icon departure-blue"></i>
                  <div class="stat-number">{{ portTrafficList.length ? portTrafficList[currentShipIndex].wait.length : 0
                    }}
                  </div>
                  <div class="stat-item-label">等泊</div>
                </div>
                <div class="stat-item" @click="openPortModal('work')">
                  <i class="fas fa-anchor stat-item-icon dock-green"></i>
                  <div class="stat-number">{{ portTrafficList.length ? portTrafficList[currentShipIndex].work.length : 0
                    }}
                  </div>
                  <div class="stat-item-label">作业</div>
                </div>
                <Spin size="large" fix v-if="portLoading"></Spin>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 船舶详情弹窗 -->
      <div class="ship-detail-modal" v-if="showShipDetail" @click.self="closeShipDetail">
        <div class="ship-detail-container" :class="{ 'show': showShipDetail }">
          <div class="modal-header">
            <div class="ship-title">
              <i class="fas fa-ship"></i> {{ selectedShip.ship_name }}
            </div>
            <button class="close-button" @click="closeShipDetail">
              <i class="fas fa-times"></i>
            </button>
          </div>

          <div class="modal-content">
            <!-- 船舶基本信息 -->
            <div class="info-section">
              <div class="section-header">
                <i class="fas fa-info-circle"></i> 船舶基本信息
              </div>
              <div class="info-grid">

                <div class="info-item">
                  <div class="info-label">状态</div>
                  <div class="info-value">
                    <span :class="['status-tag-small', getStatusClass(selectedShip.status)]">
                      <i :class="getStatusIcon(selectedShip.status)"></i> {{ selectedShip.status }}
                      <span v-if="selectedShip.status === '锚泊'" class="anchor-hours">{{ selectedShip.anchorHours
                        }}小时</span>
                    </span>
                  </div>
                </div>
                <div class="info-item">
                  <div class="info-label">类型</div>
                  <div class="info-value">{{ shipTypeTrans(selectedShip.shiptype) }}</div>
                </div>
                <div class="info-item">
                  <div class="info-label">长*宽</div>
                  <div class="info-value">{{ selectedShip.length / 10 }}m*{{ selectedShip.width / 10 }}m</div>
                </div>
                <div class="info-item">
                  <div class="info-label">吃水/航速</div>
                  <div class="info-value">{{ selectedShip.draught / 1000 }}米/{{ (selectedShip.sog / 100).toFixed(2) }}
                    kn
                  </div>
                </div>
                <div class="info-item">
                  <div class="info-label">航首向</div>
                  <div class="info-value">{{ (selectedShip.hdg / 100).toFixed(0) }}°</div>
                </div>
                <div class="info-item">
                  <div class="info-label">航迹向</div>
                  <div class="info-value">{{ (selectedShip.cog / 100).toFixed(0) }}°</div>
                </div>
              </div>
            </div>
            <div class="info-section">
              <div class="section-header">
                <i class="fas fa-bar-chart"></i> 当月营运数据
              </div>
              <div class="info-grid">

                <div class="info-item">
                  <div class="info-label">当月预计货量</div>
                  <div class="info-value">{{ getShipAmounts(selectedShip.ship_name) }}万吨</div>
                </div>
                <div class="info-item">
                  <div class="info-label">当月实际货量</div>
                  <div class="info-value">{{ getRealAmount(selectedShip.ship_name) }}万吨</div>
                </div>
                <div class="info-item">
                  <div class="info-label">货量完成比</div>
                  <div class="info-value">{{ (getRealAmount(selectedShip.ship_name) /
                    getShipAmounts(selectedShip.ship_name) *
                    100).toFixed(2) }}%
                  </div>
                </div>
                <div class="info-item">
                  <div class="info-label">当月锚泊时长</div>
                  <div class="info-value">{{ getShipAnchorTime(selectedShip.ship_name) }}小时</div>
                </div>
                <!-- <div class="info-item">
                  <div class="info-label">当月预收入</div>
                  <div class="info-value">{{ getShipFee(selectedShip.ship_name) }}</div>
                </div> -->
                <div class="info-item">
                  <div class="info-label">当月平均损耗率</div>
                  <div class="info-value">{{ getShipLoss(selectedShip.ship_name) }}‰</div>
                </div>
                <div class="info-item">
                  <div class="info-label">当月损耗</div>
                  <div class="info-value">{{ ((getRealAmount(selectedShip.ship_name) *
                    getShipLoss(selectedShip.ship_name)) * 10).toFixed(2) }}吨</div>
                </div>
                <div class="info-item">
                  <div class="info-label">当月重载率</div>
                  <div class="info-value">{{ getShipMile(selectedShip.ship_name) }}%</div>
                </div>

              </div>
            </div>

            <!-- 当前航次 -->
            <div class="info-section">
              <div class="section-header">
                <i class="fas fa-map-marked-alt"></i> 当前航次
              </div>
              <div class="route-current">
                <div class="route-item">
                  <i class="fas fa-arrow-circle-right"></i>
                  <span> {{ selectedShip.currentRoute }}</span>
                  <span style="margin-left: 10px;">{{ selectedShip.cargo }} {{ selectedShip.amounts ? '- ' +
                    selectedShip.amounts + 'T' : '' }}</span>
                </div>
              </div>
            </div>

            <!-- 计划航次 -->
            <div v-if="selectedShip.planList.length > 0" class="info-section">
              <div class="section-header">
                <i class="fas fa-calendar-alt"></i> 计划航次
              </div>
              <div class="route-planned">
                <div class="route-item planned" v-for="(plan, index) in selectedShip.planList" :key="index">
                  <i class="fas fa-arrow-circle-right"></i>
                  <span>{{ plan.load_port_name }} → {{ plan.unload_port_name }}</span>
                  <span style="margin-left: 10px;">{{ plan.goods_name }} - {{ plan.amounts }}T</span>
                  <div class="cargo-info">{{ plan.voyage_no }}</div>
                  <div class="plan-button" :class="{ 'text-success': plan.from_flag === '0' }">{{ plan.from_flag === '0'
                    ?
                    '已下发'
                    : '已预排' }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 天气信息详情弹窗 -->
    <div class="ship-detail-modal" v-if="showWeatherDetail" @click.self="closeWeatherDetail">
      <div class="ship-detail-container" :class="{ 'show': showWeatherDetail }">
        <div class="modal-header">
          <div class="ship-title">
            <i class="fas fa-cloud-sun"></i> {{ dataList[currentShipIndex] ? dataList[currentShipIndex].dest
              : ''
            }} 天气详情
          </div>
          <button class="close-button" @click="closeWeatherDetail">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <div class="modal-content">
          <template v-if="weatherInfo[currentShipIndex]">
            <!-- 当前天气详情 -->
            <div class="info-section">
              <div class="section-header">
                <i class="fas fa-sun"></i> 当前天气
              </div>
              <div class="current-weather-detail">
                <div class="weather-main-compact">
                  <i class="fas"
                    :class="[getWeatherIcon(weatherInfo[currentShipIndex][0].data[0].weather[0].description).icon, getWeatherIcon(weatherInfo[currentShipIndex][0].data[0].weather[0].description).color, 'weather-icon-compact']"></i>
                  <div class="weather-main-info-compact">
                    <span class="weather-desc">{{ weatherInfo[currentShipIndex][0].data[0].weather[0].description ||
                      '--' }}</span>
                    <span class="weather-temp">{{ weatherInfo[currentShipIndex][0].data[0].main.temp || '--' }}°C</span>
                    <span class="weather-feel">体感{{ weatherInfo[currentShipIndex][0].data[0].main.feels_like || '--'
                      }}°C</span>
                  </div>
                </div>
                <div class="weather-details-compact">
                  <span>温度 {{ weatherInfo[currentShipIndex][0].minTemperature || '--' }}~{{
                    weatherInfo[currentShipIndex][0].maxTemperature || '--' }}°C</span>
                  <span>湿度 {{ weatherInfo[currentShipIndex][0].data[0].main.humidity || '--' }}%</span>
                  <span>风速 {{ weatherInfo[currentShipIndex][0].data[0].wind.speed ?
                    weatherInfo[currentShipIndex][0].data[0].wind.speed.toFixed(1) : '--' }}m/s</span>
                  <span>风向 {{ weatherInfo[currentShipIndex][0].data[0].wind.deg || '--' }}</span>
                  <span>气压 {{ weatherInfo[currentShipIndex][0].data[0].main.pressure || '--' }}hPa</span>
                </div>
              </div>
            </div>
            <!-- 未来天气预报 -->
            <div class="info-section">
              <div class="section-header">
                <i class="fas fa-calendar-alt"></i> 未来天气预报
              </div>
              <div class="forecast-cards">
                <div class="forecast-card" v-for="(day, index) in weatherInfo[currentShipIndex]" :key="index"
                  :class="{ 'active': selectedForecastDayIndex === index }" @click="selectForecastDay(index)">
                  <div class="forecast-card-header">
                    <div class="forecast-day-name">{{ dayMap[day.day] }}</div>
                    <div class="forecast-date">{{ day.date }}</div>
                  </div>
                  <div class="forecast-card-body">
                    <div class="forecast-card-icon">
                      <i class="fas"
                        :class="[getWeatherIcon(day.data[0].weather[0].description).icon, getWeatherIcon(day.data[0].weather[0].description).color]"></i>
                    </div>
                    <div class="forecast-card-info">
                      <div class="forecast-card-weather">{{ day.data[0].weather[0].description || '--' }}</div>
                      <div class="forecast-card-temp">{{ day.minTemperature || '--' }}° / {{ day.maxTemperature || '--'
                        }}°</div>
                    </div>
                  </div>
                  <div class="forecast-card-footer">
                    <div class="forecast-card-detail">
                      <i class="fas fa-tint"></i>
                      <span>{{ day.data[0].main.humidity || '--' }}%</span>
                    </div>
                    <div class="forecast-card-detail">
                      <i class="fas fa-wind"></i>
                      <span>{{ day.data[0].wind.speed ? day.data[0].wind.speed.toFixed(1) : '--' }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 24小时详细预报 -->
            <div class="info-section">
              <div class="section-header">
                <i class="fas fa-clock"></i> 24小时详细预报
              </div>
              <div style="height: 120px;">
                <div ref="weatherChart" class="weather-echart" style="width: 100%; height: 120px;"></div>
              </div>
            </div>

          </template>
          <Spin size="large" fix v-else></Spin>
        </div>
      </div>
    </div>

    <!-- 港口船舶信息弹窗 -->
    <div class="ship-detail-modal" v-if="showPortDetail" @click.self="closePortDetail">
      <div class="ship-detail-container" :class="{ 'show': showPortDetail }">
        <div class="modal-header">
          <div class="ship-title">
            <i class="fas fa-ship"></i> {{ dataList[currentShipIndex] ? dataList[currentShipIndex].dest : ''
            }}
            港口船舶信息
          </div>
          <button class="close-button" @click="closePortDetail">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-content">
          <div class="port-tabs">
            <div v-for="tab in [
              { key: 'estimate', label: '预抵' },
              { key: 'wait', label: '等泊' },
              { key: 'work', label: '作业' }
            ]" :key="tab.key" :class="['port-tab', { active: portTabActive === tab.key }]"
              @click="setPortTab(tab.key)">
              {{ tab.label }}
            </div>
          </div>
          <div class="port-table-container">
            <table class="port-table">
              <thead>
                <tr>
                  <th>船舶</th>
                  <th>{{ portTabActive === 'estimate' ? 'ETA' : portTabActive === 'wait' ? 'ATA' : 'ATB' }}</th>
                  <th>{{ portTabActive === 'work' ? '作业码头' : '预测码头' }}</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(ship, index) in portTrafficList[currentShipIndex][portTabActive]" :key="'port' + index">
                  <td>{{ ship.mapbase_vessel_info_vessel_name || '--' }}</td>
                  <td>{{ (portTabActive === 'estimate' ? ship.mapbase_vessel_realtime_estimate_eta : portTabActive ===
                    'wait'
                    ? ship.mapbase_vessel_track_ata : ship.mapbase_vessel_track_atb).substring(0, 10) }}</td>
                  <td>{{ ship.mapbase_terminals_name || '--' }}</td>
                </tr>
                <tr v-if="!(portTrafficList[currentShipIndex][portTabActive].length)">
                  <td colspan="5" style="text-align:center;color:#8eb0d1;">暂无数据</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- AI分析弹窗 -->
    <div class="ship-detail-modal" v-if="showAiAnalysisModal" @click.self="closeAiAnalysis">
      <div class="ship-detail-container ai-analysis-modal" :class="{ 'show': showAiAnalysisModal }">
        <div class="modal-header">
          <div class="ship-title">
            <i class="fas fa-robot"></i> {{ currentAnalysisShip ? currentAnalysisShip.ship_name : '' }} - AI智能分析
          </div>
          <div class="header-actions">
            <!-- 重新分析按钮 -->
            <button
              v-if="aiAnalysisText[currentAnalysisShip.ship_name] && !aiAnalysisLoading[currentAnalysisShip.ship_name]"
              class="reanalyze-button-header" @click="reanalyzeShip(currentAnalysisShip)" title="重新分析">
              <i class="fas fa-redo-alt"></i>
              <span>重新分析</span>
            </button>
            <button class="close-button" @click="closeAiAnalysis">
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>

        <div class="modal-content">
          <template v-if="currentAnalysisShip">
            <!-- AI分析结果 -->
            <div class="ai-analysis-content">
              <div v-if="!aiAnalysisText[currentAnalysisShip.ship_name]" class="ai-status-indicator">
                <i class="fas fa-robot"></i>
                <span class="status-text">AI正在分析中...</span>
                <div class="typing-indicator">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>
              <div class="ai-text-display" ref="aiTextDisplay" v-if="aiAnalysisText[currentAnalysisShip.ship_name]">
                <div class="analysis-text" v-html="formatAnalysisText(aiAnalysisText[currentAnalysisShip.ship_name])">
                </div>
              </div>
            </div>
            <!-- <div class="info-section">
            </div> -->
          </template>
        </div>
      </div>
    </div>

    <!-- 告警详情弹窗 -->
    <div class="ship-detail-modal" v-if="showAlarmModal" @click.self="closeAlarmModal">
      <div class="alarm-modal-container" :class="{ 'show': showAlarmModal }">
        <div class="modal-header">
          <div class="ship-title">
            <i class="fas fa-bell"></i> 告警详情
          </div>
          <button class="close-button" @click="closeAlarmModal">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <div class="alarm-modal-content">
          <!-- 左侧海事局列表 -->
          <div class="bureau-list-panel">
            <div class="panel-header">
              <h3>海事局列表</h3>
              <span class="total-count">共 {{ alarmBureauList.length }} 个</span>
            </div>
            <div class="bureau-list">
              <div v-for="bureau in alarmBureauList" :key="bureau.name" class="bureau-item"
                :class="{ 'active': selectedBureau && selectedBureau.name === bureau.name }"
                @click="selectBureau(bureau)">
                <div class="bureau-name">{{ bureau.name }}</div>
                <div class="bureau-count">{{ bureau.count }} 条</div>
              </div>
              <div v-if="!alarmBureauList.length" class="empty-state">
                <i class="fas fa-info-circle"></i>
                <span>暂无告警数据</span>
              </div>
            </div>
          </div>

          <!-- 右侧告警列表 -->
          <div class="alarm-list-panel">
            <div class="panel-header">
              <h3>{{ selectedBureau ? selectedBureau.name : '告警列表' }}</h3>
              <span class="total-count" v-if="selectedBureau">{{ selectedBureau.count }} 条告警</span>
            </div>
            <div class="alarm-list">
              <div v-for="(alarm, index) in alarmDetailList" :key="index" class="alarm-item"
                :class="{ 'active': selectedAlarm && selectedAlarm === alarm }" @click="selectAlarm(alarm)">
                <div class="alarm-header">
                  <i class="fas" :class="alarm.icon"
                    :style="{ color: alarm.type === 'warning' ? '#f59e0b' : '#3b82f6' }"></i>
                  <span class="alarm-type" v-if="alarm.category === 'notice'">通告</span>
                  <span class="alarm-category" v-if="alarm.category === 'alarm'">警告</span>
                </div>
                <div class="alarm-title">{{ alarm.title }}</div>
                <div class="alarm-meta">
                  <span class="alarm-bureau">{{ alarm.bureau }}</span>
                  <span class="alarm-type-detail">{{ alarm.date }}</span>
                </div>
              </div>
              <div v-if="!alarmDetailList.length" class="empty-state">
                <i class="fas fa-exclamation-triangle"></i>
                <span>请选择海事局查看告警</span>
              </div>
            </div>
          </div>

          <!-- 告警详情面板 -->
          <div class="alarm-detail-panel" v-if="selectedAlarm">
            <div class="panel-header">
              <h3>告警详情</h3>
            </div>
            <div class="alarm-detail-content">
              <div class="detail-item">
                <!-- <label>：</label> -->
                <span>{{ selectedAlarm.detail_content }}</span>
              </div>
              <div class="detail-item">
                <label>海事局：</label>
                <span>{{ selectedAlarm.bureau }}</span>
              </div>
              <div class="detail-item">
                <label>分类：</label>
                <span>{{ selectedAlarm.category === 'alarm' ? '警告' : '通告' }}</span>
              </div>
              <div class="detail-item">
                <label>时间：</label>
                <span>{{ selectedAlarm.date }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 全局警告提示框 -->
    <div v-if="showWarningTooltip && tooltipData && tooltipData.currentPlan" class="custom-tooltip"
      :style="{ left: tooltipPosition.x + 'px', top: tooltipPosition.y + 'px' }">
      <div class="tooltip-content">
        航次 {{ tooltipData.currentPlan.voyage_no }} 已偏离计划，未按原定时间完成，请尽快跟进船舶实时动态，以便及时协调后续安排。
      </div>
      <div class="tooltip-arrow"></div>
    </div>
  </fullscreen>
</template>

<script>
import axios from 'axios'
import { queryShipsAmounts, queryShipsGoodsLoss } from '@/api/statistics/operationAnalysis'
import { queryCurVoyageMmsi, queryVoyagePlanConsolePage } from '@/api/ais'
import API from '@/api/control'
import { getShipsTrajectory, queryMapBaseList, queryShipFormulaInfo } from '@/api/shipManagement'
import { queryVoyageMonthPlanListAndVoyagesByShipGroup, queryVoyageMonthPlanListAndStat } from '@/api/shipSchedule'
import { queryPortPage } from '@/api/portManagement'
import { queryShipService } from '@/api/statistics'
import { queryAnchorReport } from '@/api/statistics/anchorStatistics'
import { queryShipsGoodsAmountOverall } from '@/api/statistics/transPortView'
import { queryVoyageMileOverall } from '@/api/statistics/voyageAnalysis'
import { getToken } from '@/libs/util'
import config from '@/config'
import { forEach } from 'lodash'
import dayjs from 'dayjs'
import CountTo from '_c/count-to'
import { ChartLine } from '_c/charts'
import echarts from 'echarts'
import { pinyin } from 'pinyin-pro'; // 引入拼音转换库（若用CDN则无需import）
import MarkdownIt from 'markdown-it' // 引入Markdown解析库

export default {
  name: 'FleetMonitor',
  components: {
    CountTo,
    ChartLine
  },
  data() {
    return {
      worker: null,
      currentDateTime: '',
      currentShipIndex: 0,
      currentNotificationIndex: 0,
      notificationInterval: null,
      horizontalScrollActive: false,
      totalAnchorTime: 0, // 自营船舶锚泊时长
      anchorTimeList: [], //自营船舶锚泊数据列表
      textWidths: {},
      highlightInterval: null,
      isHighlightPaused: false,
      autoPlay: true,
      showShipDetail: false,
      selectedShip: {},
      selectedShipIndex: 0,
      showWarningTooltip: null, // 控制警告提示框显示
      tooltipData: null, // 提示框数据
      tooltipPosition: { x: 0, y: 0 }, // 提示框位置
      notifications: [

      ],
      dataList: [],
      selfShipList: [],
      // dataList: [], // 船舶显示列表
      shipDetailList: [], // 船舶详细信息
      anchorMmsiList: [],
      anchorIdx: [],
      loading: false,
      isWanBang: false,
      shipIgnoreList: ['413375790', '413375810', '413376840', '412379380', '412379370', '413376570', '413693020'],
      oilData: [], // 船舶油料数据
      weatherInfo: [
      ],
      iconWeatherMap: {
        'fa-wind': ['有风', '平静', '微风', '和风', '清风', '强风/劲风', '疾风', '大风', '烈风', '风暴', '狂爆风', '飓风', '热带风暴', '龙卷风'],
        'fa-cloud-sun': ['少云', '晴间多云', '多云', '阴', '阴，多云'],
        'fa-snowflake': ['雪', '阵雪', '小雪', '中雪', '大雪', '暴雪', '小雪-中雪', '中雪-大雪', '大雪-暴雪', '冷', '雨雪天气', '雨夹雪', '阵雨夹雪'],
        'fa-smog': ['浮尘', '扬沙', '沙尘暴', '强沙尘暴', '雾', '浓雾', '强浓雾', '轻雾', '大雾', '特强浓雾'],
        'fa-sun': ['晴', '热'],
        'fa-cloud-rain': ['阵雨', '雷阵雨', '雷阵雨并伴有冰雹', '小雨', '中雨', '大雨', '暴雨', '大暴雨', '特大暴雨', '强阵雨', '强雷阵雨', '极端降雨', '毛毛雨/细雨', '雨', '小雨-中雨', '中雨-大雨', '大雨-暴雨', '暴雨-大暴雨', '大暴雨-特大暴雨', '冻雨'],
        'fa-water': ['霾', '中度霾', '重度霾', '严重霾', '未知']
      },
      dayMap: {
        0: '周日',
        1: '周一',
        2: '周二',
        3: '周三',
        4: '周四',
        5: '周五',
        6: '周六'
      },
      searchPortList: [],
      basePortList: [],
      portTrafficList: [],
      portTrafficNum: {
        estimate: 0,
        wait: 0,
        work: 0
      },
      portLoading: false,
      map: null,
      options: {
        ak: 'a5bb8f37140d428391e1546d7b704413', // '57df9eaa033b44809d4bdaf919af457e',
        // 初始中心点坐标
        centerPoint: [30.1431749158, 121.9380381277],
        // 初始缩放级别
        zoom: 7,
        // zoomSnap: 0.1, // 缩放递度
        // 最小缩放级别
        minZoom: 4,
        // 最大缩放级别
        maxZoom: 18,
        mapTypes: ['MT_SEA'],
        // 栅格
        gratings: { isShow: false },
        // 公司版权信息( 支持html )，默认Elane Inc.
        attribution: {
          isShow: false,
          emptyString:
            '&copy;2019 &nbsp;<a >兴通海运股份有限公司 保留所有版权 闽ICP备15001600号-3</a>'
        },
        measureCtrl: {
          // 是否开启测距控件，默认：true
          isShow: false,
          // 是否显示测距按钮，默认：true
          showMeasurementsMeasureControl: true,
          // 是否显示删除按钮，默认：true
          showMeasurementsClearControl: true,
          // 是否显示切换单位按钮，默认：true
          showUnitControl: true
        },
        //鼠标移动悬浮经纬度控件
        mousePostionCtrl: { isShow: false, position: 'bottomright' },
        //缩放工具控件的显示隐藏
        zoomControlElane: { isShow: true, position: 'topright' },
        // 缩放级别显示控件
        zoomviewControl: { isShow: false, position: 'topleft' },
        //地图切换控件的位置
        basemapsControl: { isShow: false, position: 'topright' },
        // 比例尺，控件
        scaleCtrl: { isShow: true, position: "bottomleft" },
      },
      statistic: {
        chartGoodsNum: 0,
        chartTurnoverNum: 0,
        chartVolumeNum: 0,
        chartWaitingTime: 0,
        allShippingFee: 0,
        allGoodsLoss: [],
        allVoyageMile: [],
        allGoodsAmount: []
      },
      fullscreen: false,
      feeList: [],

      showWeatherDetail: false,
      selectedForecastDayIndex: 0, // 新增，默认显示今天
      weatherChartInstance: null,

      showPortDetail: false,
      portTabActive: 'estimate', // 新增，默认显示"预抵"

      // AI分析相关
      showAiAnalysisModal: false, // 控制AI分析弹窗显示
      currentAnalysisShip: null, // 当前分析的船舶
      aiAnalysisText: {}, // 存储AI分析文本内容
      aiAnalysisLoading: {}, // AI分析加载状态
      mdIt: null,

      // 告警弹窗相关
      showAlarmModal: false, // 控制告警弹窗显示
      alarmBureauList: [], // 海事局列表
      selectedBureau: null, // 选中的海事局
      alarmDetailList: [], // 当前海事局的告警列表
      selectedAlarm: null, // 选中的告警详情
      rawAlarmData: null // 原始告警数据
    }
  },
  async created() {
    const token = getToken()
    const baseUrl = process.env.NODE_ENV === 'development' ? config.baseUrl.dev : config.baseUrl.pro
    this.worker = new Worker('/worker.js')
    await queryMapBaseList({ base_type: 1 }).then(res => {
      if (res.data.Code === 10000) {
        this.basePortList = res.data.Result.map(item => {
          return {
            ...item,
            pinyin: this.normalizePinyin(this.getPortPinyin(item.name)).trim().replace(/\s+/g, '')
          }
        })

      }
    })
    queryPortPage({pageIndex:1,pageSize:1000}).then(response => {
      if (response.data.Code === 10000) {
        this.searchPortList = response.data.Result

      }
    })
    this.loading = true
    await this.getOilData()
    await this.getList()
    await this.getCurVoyage()
    await this.getAnchorTime()
    await this.queryShipsGoodsAmountOverall()
    await this.queryShipsGoodsLoss()
    await this.queryVoyageMileOverall()
    await this.queryVoyageDistance()

    // await this.getWarnTime()
  },
  async mounted() {
    this.getNotification()
    this.startClock();
    this.startAutoHighlight();

    this.initMap()
    this.renameShip()
    this.getStatistic()
    await this.getMonPlanStat()

    this.$nextTick(() => {
      if (this.showWeatherDetail) {
        this.renderWeatherChart();
      }
    });
    queryShipFormulaInfo({
      url: 'https://mobile.shipformula.com/v1/api/node/map/MapBaseControl/getVesselDetail',
      paramMap: JSON.stringify({
        mapbase_vessel_info_vessel_id: '413699940,413315220'
      })
    })
  },
  beforeDestroy() {
    clearInterval(this.clockInterval);
    clearInterval(this.highlightInterval);
    clearTimeout(this.notificationInterval);
    if (this.weatherChartInstance) {
      this.weatherChartInstance.dispose();
      this.weatherChartInstance = null;
    }
  },
  watch: {
    async currentShipIndex(val, oldVal) {
      console.log(this.dataList[val]);

      this.getWeather(val)
      this.getPortDetail(val)
      this.locShip(val)
      if (val === 0) {
        this.scrollTable('top')
      } else if (val >= 12) {
        this.scrollTable('bottom')
      }
      if (val === 0 && oldVal === this.dataList.length - 1) {
        // this.loading = true
        await this.getOilData()
        await this.getList()
        await this.getCurVoyage()
        await this.getAnchorTime()
        await this.queryShipsGoodsAmountOverall()
        await this.queryShipsGoodsLoss()
        await this.queryVoyageMileOverall()
        await this.queryVoyageDistance()
      }
      this.startAutoHighlight()
    },
    selectedForecastDayIndex() {
      this.$nextTick(() => {
        this.renderWeatherChart();
      });
    },
    showWeatherDetail(val) {
      if (val) {
        this.$nextTick(() => {
          this.renderWeatherChart();
        });
      } else {
        if (this.weatherChartInstance) {
          this.weatherChartInstance.dispose();
          this.weatherChartInstance = null;
        }
      }
    },
  },
  computed: {
    filteredWeatherForecast() {
      if (!this.weatherInfo[this.currentShipIndex]) {
        return [];
      }
      return this.weatherInfo[this.currentShipIndex].filter((item, index) => index !== 0);
    }
  },
  methods: {
    // AI分析相关方法
    async openAiAnalysis(ship, index) {
      // 设置当前分析的船舶
      this.currentAnalysisShip = ship;

      // 显示弹窗
      this.showAiAnalysisModal = true;

      // 暂停自动高亮
      if (this.autoPlay) {
        this.pauseAutoHighlight();
      }
      document.body.classList.add('modal-open');


      this.startAiAnalysis(ship);
    },

    // 关闭AI分析弹窗
    closeAiAnalysis() {
      // 停止正在进行的分析

      this.showAiAnalysisModal = false;
      this.currentAnalysisShip = null;
      document.body.classList.remove('modal-open');

      // 恢复自动高亮
      if (this.autoPlay) {
        this.resumeAutoHighlight();
      }
    },

    // 重新分析船舶
    async reanalyzeShip(ship) {
      const shipName = ship.ship_name;

      // 清除之前的分析结果
      this.$set(this.aiAnalysisText, shipName, '');

      // 开始重新分析
      await this.startAiAnalysis(ship);
    },

    // 开始AI分析
    async startAiAnalysis(ship) {
      const shipName = ship.ship_name;

      if (this.aiAnalysisText[shipName]) {
        return
      }
      this.$set(this.aiAnalysisLoading, shipName, true);

      try {
        if (!ship.completedNmile) {

          let completedList = ship.completedList
          let startPort = this.getLocationByPortName(completedList[completedList.length - 1].load_port_name)
          let endPort = this.getLocationByPortName(completedList[completedList.length - 1].unload_port_name)
          let data = [{
            ship_name: ship.ship_name+'历史',
            start: {
              lon: startPort.longitude,
              lat: startPort.latitude
            },
            end: {
              lon: endPort.longitude,
              lat: endPort.latitude
            }
          }]
          if (ship.planList.length) {
            let plan = ship.planList[0]
            let startPort = this.getLocationByPortName(plan.load_port_name)
            let endPort = this.getLocationByPortName(plan.unload_port_name)
            data.push({
              ship_name: ship.ship_name+'计划',
              start: {
                lon: startPort.longitude,
                lat: startPort.latitude
              },
              end: {
                lon: endPort.longitude,
                lat: endPort.latitude
              }
            })
          }

          let requestData = {
            inputs: {
              shipList: JSON.stringify(data)
            },
            response_mode: 'blocking',
            user: "abc-123"
          }
          const response = await axios.post('http://************/v1/workflows/run', requestData, {
            headers: {
              'Authorization': 'Bearer app-nqRpQuEij2tXpfelcPFiOqf7',
              'Content-Type': 'application/json'
            }
          })
          ship.completedNmile = response.data.data.outputs.data[0].nmile
          if (response.data.data.outputs.data.length > 1) {
            ship.planNmile = response.data.data.outputs.data[1].nmile
          }
        }

        // 开始流式分析
        await this.fetchAIMessage(ship);
      } catch (error) {
        if (error.name !== 'AbortError') {
          console.error('AI分析失败:', error);
          this.$set(this.aiAnalysisText, shipName, '分析过程中出现错误，请重试。');
        }
      } finally {
        this.$set(this.aiAnalysisLoading, shipName, false);
      }
    },

    async fetchAIMessage(ship) {
      let rowData = {
        '上航次结束时剩余重油': ship.heavy,
        '上航次结束时剩余轻油': ship.light,
        '上航次重油消耗': ship.heavyConsum,
        '上航次轻油消耗': ship.lightConsum,
        '船名': ship.ship_name,
        '计划航次': ship.planList[0],
        '当前航次': ship.currentList,
        '已完成航次': ship.completedList[ship.completedList.length - 1],
        '上航次里程': ship.completedNmile,
        '当前航次里程': ship.nmile,
        '计划航次里程': ship.planNmile

      }
      let requestData = {
        inputs: {
          rowData: JSON.stringify(rowData)
        },
        response_mode: 'streaming',
        user: "abc-123"
      }
      const response = await fetch('http://************/v1/workflows/run', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer app-fiEeSJdjR6bzZpiHCgTbUqBG`
        },
        body: JSON.stringify(requestData)
      })

      // 处理流式响应
      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let partialLine = ''
      let fullResponse = '' // 用于累积完整响应
      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        // 解码二进制数据
        const text = decoder.decode(value)
        const lines = (partialLine + text).split('\n')
        partialLine = lines.pop() || ''

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const jsonStr = line.substring(6) // 移除 "data: " 前缀
              const data = JSON.parse(jsonStr)
              // 更新系统消息内容
              if (data.event === 'text_chunk' && data.data.text) {
                // 累积到完整响应中
                fullResponse += data.data.text;
                this.$set(this.aiAnalysisText, ship.ship_name, fullResponse);

                this.$nextTick(() => {
                  this.scrollToBottom(true)
                })
              }
            } catch (e) {
              console.error('解析流式响应失败:', e)
            }
          }
        }
      }
    },

    scrollToBottom(force = false) {
      const container = this.$refs.aiTextDisplay
      if (container) {
        // 如果强制滚动或用户在底部，则滚动到底部
        if (force || this.isUserAtBottom) {
          container.scrollTop = container.scrollHeight
          this.isUserAtBottom = true
        }
      }
    },


    // 格式化分析文本（支持Markdown）
    formatAnalysisText(text) {
      if (!text) return '';

      try {
        // 创建markdown-it实例并配置选项
        const md = new MarkdownIt({
          html: false, // 禁用HTML标签（防止XSS攻击）
          breaks: true, // 支持换行符转换为<br>
          linkify: true, // 自动识别链接
          typographer: true // 启用智能标点符号
        });

        // 将Markdown转换为HTML
        return md.render(text);
      } catch (error) {
        console.error('Markdown解析错误:', error);
        // 如果解析失败，返回原始文本并进行基本的HTML转义
        return text.replace(/&/g, '&amp;')
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;')
          .replace(/"/g, '&quot;')
          .replace(/'/g, '&#39;')
          .replace(/\n/g, '<br>');
      }
    },



    showWran(ship) {
      if (ship.currentPlan && JSON.stringify(ship.currentPlan) !== '{}') {
        return dayjs().isAfter(dayjs(ship.currentPlan.estimated_over_day).endOf('day'))
      }
      return false
    },
    handleWarningMouseEnter(event, ship, index) {
      const uniqueId = `${ship.id}_${index}`;
      this.showWarningTooltip = uniqueId;
      this.tooltipData = ship;

      // 计算提示框位置
      const rect = event.target.getBoundingClientRect();
      const tooltipWidth = 300; // 预估提示框宽度
      const tooltipHeight = 80; // 预估提示框高度

      let x = rect.left + rect.width / 2 - tooltipWidth / 2;
      let y = rect.top - tooltipHeight - 10;

      // 边界检测
      if (x < 10) x = 10;
      if (x + tooltipWidth > window.innerWidth - 10) {
        x = window.innerWidth - tooltipWidth - 10;
      }
      if (y < 10) {
        y = rect.bottom + 10; // 如果上方空间不够，显示在下方
      }

      this.tooltipPosition = { x, y };
    },
    handleWarningMouseLeave() {
      this.showWarningTooltip = null;
      this.tooltipData = null;
    },
    formatDate(date) {
      if (!date) return ''
      return dayjs(date).format('YYYY-MM-DD')
    },
    async getOilData() {
      let res = await axios.get((process.env.NODE_ENV === 'development' ? config.baseUrl.multi_dev : config.baseUrl.multi_pro) + '/multi/source/api/crew/oil-voyage-consumption/latest-by-vessel', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('MultiDataToken')}`,
          'Content-Type': 'application/json'
        }
      })

      this.oilData = res.data.data.map(item => {
        let light = item.detailList.find(detail => detail.oilNameCn === '轻油' && detail.oilMark === '0#')
        let heavy = item.detailList.find(detail => detail.oilNameCn === '重油')

        return {
          vesselName: item.vesselName,
          light: light ? `${light.thisVoyageInventory}${light.oilUnitName}` : '0',
          heavy: heavy ? `${heavy.thisVoyageInventory}${heavy.oilUnitName}` : '0',
          lightConsum: light ? (light.thisVoyageConsum + light.oilUnitName) : '0',
          heavyConsum: heavy ? (heavy.thisVoyageConsum + heavy.oilUnitName) : '0',
        }
      })
    },
    // 缓存管理辅助方法
    getCachedData(key) {
      try {
        const cached = localStorage.getItem(key);
        if (!cached) return null;

        const data = JSON.parse(cached);
        const now = new Date().getTime();
        const cacheTime = new Date(data.timestamp).getTime();
        const eightHours = 8 * 60 * 60 * 1000; // 8小时的毫秒数

        // 检查是否超过8小时
        if (now - cacheTime > eightHours) {
          localStorage.removeItem(key);
          return null;
        }

        return data.value;
      } catch (error) {
        console.error('读取缓存数据失败:', error);
        localStorage.removeItem(key);
        return null;
      }
    },

    setCachedData(key, value) {
      try {
        const data = {
          value: value,
          timestamp: new Date().toISOString()
        };
        localStorage.setItem(key, JSON.stringify(data));
      } catch (error) {
        console.error('保存缓存数据失败:', error);
      }
    },

    async getNotification() {
      let weatherAlarm = await axios.post('https://www.oceanguide.org.cn/hyyj2/index/indexAlarm', {
      })
      let dangerList = ['Ⅲ级', 'Ⅱ级', 'I级']
      if (weatherAlarm.data.obj) {
        this.notifications = weatherAlarm.data.obj.alarms.map(item => {
          return {
            text: `${item.title}：${item.description}`,
            icon: dangerList.includes(item.level) ? 'fa-exclamation-circle' : 'fa-exclamation-triangle',
            type: dangerList.includes(item.level) ? 'danger' : 'warning'
          }
        })
      }

      // 检查msaAlarm缓存
      const cacheKey = 'msaAlarm_cache';
      let msaAlarmData = this.getCachedData(cacheKey);

      if (!msaAlarmData) {
        // 缓存不存在或已过期，重新获取数据
        try {
          let msaAlarm = await axios.post('http://************/v1/workflows/run', {
            "inputs": {},
            "response_mode": "blocking",
            "user": "abc-123"
          }, {
            headers: {
              'Authorization': 'Bearer app-9BAEfzheTTzzu2IyZliltvcI',
              'Content-Type': 'application/json'
            }
          })
          msaAlarmData = msaAlarm.data.data.outputs;
          // 保存到缓存
          this.setCachedData(cacheKey, msaAlarmData);
        } catch (error) {
          console.error('获取MSA告警数据失败:', error);
          msaAlarmData = null;
        }
      }

      // 保存原始告警数据用于弹窗显示
      this.rawAlarmData = msaAlarmData;

      msaAlarmData.alarm.forEach(item => {
        if (item) {
          let data = JSON.parse(item).data
          data.forEach(item2 => {
            this.notifications.push({
              text: `${item2.bureau}${item2.type}: ${item2.title}`,
              icon: 'fa-exclamation-triangle',
              type: 'warning'
            })
          })
        }
      })
      msaAlarmData.notice.forEach(item => {
        if (item) {

          let data = JSON.parse(item).data
          data.forEach(item2 => {
            this.notifications.push({
              text: `${item2.bureau}${item2.type}: ${item2.title}`,
              icon: 'fa-exclamation-triangle',
              type: 'warning'
            })
          })
        }
      })

      // 处理告警数据用于弹窗显示
      this.processAlarmData();

      this.$nextTick(() => {
        this.calculateTextWidths();
        // 确保文本宽度计算完成后再开始轮播
        this.startNotificationRotation();
      });
    },

    // 处理告警数据用于弹窗显示
    processAlarmData() {
      if (!this.rawAlarmData) return;

      const bureauMap = new Map();

      // 处理告警数据
      if (this.rawAlarmData.alarm && Array.isArray(this.rawAlarmData.alarm)) {
        this.rawAlarmData.alarm.forEach(item => {
          if (item) {
            try {
              let data = JSON.parse(item);
              if (data && data.data && Array.isArray(data.data)) {
                data.data.forEach(alarmItem => {
                  if (alarmItem && alarmItem.bureau) {
                    if (!bureauMap.has(alarmItem.bureau)) {
                      bureauMap.set(alarmItem.bureau, []);
                    }
                    bureauMap.get(alarmItem.bureau).push({
                      ...alarmItem,
                      category: 'alarm',
                      icon: 'fa-exclamation-triangle',
                      type: 'warning'
                    });
                  }
                });
              }
            } catch (error) {
              console.error('解析告警数据失败:', error);
            }
          }
        });
      }

      // 处理通知数据
      if (this.rawAlarmData.notice && Array.isArray(this.rawAlarmData.notice)) {
        this.rawAlarmData.notice.forEach(item => {
          if (item) {
            try {
              let data = JSON.parse(item);
              if (data && data.data && Array.isArray(data.data)) {
                data.data.forEach(noticeItem => {
                  if (noticeItem && noticeItem.bureau) {
                    if (!bureauMap.has(noticeItem.bureau)) {
                      bureauMap.set(noticeItem.bureau, []);
                    }
                    bureauMap.get(noticeItem.bureau).push({
                      ...noticeItem,
                      category: 'notice',
                      icon: 'fa-info-circle',
                      type: 'info'
                    });
                  }
                });
              }
            } catch (error) {
              console.error('解析通知数据失败:', error);
            }
          }
        });
      }

      // 转换为数组格式
      this.alarmBureauList = Array.from(bureauMap.keys()).map(bureau => ({
        name: bureau,
        alarms: bureauMap.get(bureau),
        count: bureauMap.get(bureau).length
      }));

      // 默认选中第一个海事局
      if (this.alarmBureauList.length > 0) {
        this.selectedBureau = this.alarmBureauList[0];
        this.alarmDetailList = this.selectedBureau.alarms;
      }
    },

    // 打开告警弹窗
    openAlarmModal() {
      this.showAlarmModal = true;
      // 暂停自动高亮
      if (this.autoPlay) {
        this.pauseAutoHighlight();
      }
      document.body.classList.add('modal-open');
    },

    // 关闭告警弹窗
    closeAlarmModal() {
      this.showAlarmModal = false;
      this.selectedAlarm = null;
      // 恢复自动高亮
      if (this.autoPlay) {
        this.resumeAutoHighlight();
      }
      document.body.classList.remove('modal-open');
    },

    // 选择海事局
    selectBureau(bureau) {
      this.selectedBureau = bureau;
      this.alarmDetailList = bureau.alarms;
      this.selectedAlarm = null; // 清空选中的告警详情
    },

    // 选择告警查看详情
    selectAlarm(alarm) {
      this.selectedAlarm = alarm;
    },

    selectForecastDay(index) {
      this.selectedForecastDayIndex = index
    },
    openPortModal(type) {
      this.showPortDetail = true
      this.portTabActive = type
      // 暂停自动高亮
      if (this.autoPlay) {
        this.pauseAutoHighlight();
      }
      document.body.classList.add('modal-open');
    },
    openWeatherModal(idx) {
      this.showWeatherDetail = true
      // 默认切换到当前天
      this.selectedForecastDayIndex = idx
      // 暂停自动高亮
      if (this.autoPlay) {
        this.pauseAutoHighlight();
      }
      document.body.classList.add('modal-open');
    },

    getShipFee(name) {
      return this.feeList.find(item => item.ship_name === name).ship_shipping_fee + '元'
    },
    async getMonPlanStat() {
      const res = await queryVoyageMonthPlanListAndStat({ belong_month: dayjs().format('YYYY-MM') })

      if (res.data.Code === 10000) {
        this.feeList = res.data.Result
        this.statistic.allShippingFee = res.data.all_shipping_fee
      }
    },
    // 获取本月实际货运量对比数据，来提取每条船的货运量
    async queryShipsGoodsAmountOverall() {
      const res = await queryShipsGoodsAmountOverall({
        start_month: dayjs().format('YYYY-MM'),
        end_month: dayjs().format('YYYY-MM'),
        start_contrast_month: dayjs().format('YYYY-MM'),
        end_contrast_month: dayjs().format('YYYY-MM')
      })

      if (res.data.Code === 10000) {
        this.statistic.allGoodsAmount = res.data.contrastShipNumAndAmountArray
      }
    },
    // 实际货量
    getRealAmount(name) {
      if (this.statistic.allGoodsAmount.length) {
        return this.statistic.allGoodsAmount.find(item => item.ship_name === name).amount_sum
      } else {
        return 0
      }
    },
    // 预计货量
    getShipAmounts(name) {
      return this.feeList.find(item => item.ship_name === name).ship_amounts / 10000
    },
    // 获取本月锚泊时长
    async getAnchorTime() {
      const _param = {
        pageSize: 1000,
        pageIndex: 1,
        start_month: dayjs().format('YYYY-MM'),
        end_month: dayjs().format('YYYY-MM')
      }
      const selfShipList = JSON.parse(localStorage.getItem('shipNameList')).filter(item => item.business_model === '1').filter(list => !list.ship_name.includes('善')).map(item => item.ship_name)
      queryAnchorReport(_param).then(res => {
        if (res.data.Code === 10000) {
          this.anchorTimeList = res.data.Result.filter(item => selfShipList.includes(item.ship_name))
          let totalAnchorTime = res.data.Result.filter(item => selfShipList.includes(item.ship_name)).reduce((sum, item) => sum + parseFloat(item.anchor_time), 0)
          this.totalAnchorTime = parseInt(totalAnchorTime.toFixed(0))
        }
      })
    },
    // 获取单船锚泊时长
    getShipAnchorTime(name) {
      let time = this.anchorTimeList.filter(item => item.ship_name === name).reduce((sum, item) => sum + parseFloat(item.anchor_time), 0)
      return time.toFixed(0)
    },
    // 获取船舶损耗
    async queryShipsGoodsLoss() {
      const res = await queryShipsGoodsLoss({
        ship_id: '',
        start_month: dayjs().format('YYYY-MM'),
        end_month: dayjs().format('YYYY-MM')
      })

      if (res.data.Code === 10000) {
        this.statistic.allGoodsLoss = res.data.Result
      }
    },
    // 获取单船损耗率
    getShipLoss(name) {
      return this.statistic.allGoodsLoss.find(item => item.ship_name === name).goods_loss_rate_average
    },
    // 获取船舶航行里程分析数据
    async queryVoyageMileOverall() {
      const res = await queryVoyageMileOverall({
        start_month: dayjs().format('YYYY-MM'),
        end_month: dayjs().format('YYYY-MM')
      })

      if (res.data.Code === 10000) {
        this.statistic.allVoyageMile = res.data.heaveMileRateAverage
      }
    },
    // 获取单船重载率
    getShipMile(name) {
      let data = this.statistic.allVoyageMile.find(item => item.ship_name === name)
      if (!data) {
        return 0
      }
      return data.heave_mile_rate_average
    },
    // 获取当前高亮船舶的油料数据
    getCurrentShipOilData(type) {
      if (!this.dataList[this.currentShipIndex] || !this.oilData || this.oilData.length === 0) {
        return '--';
      }

      const currentShip = this.dataList[this.currentShipIndex];
      const oilInfo = this.oilData.find(item => item.vesselName === currentShip.ship_name);

      if (!oilInfo) {
        return '--';
      }

      return type === 'heavy' ? oilInfo.heavy : oilInfo.light;
    },
    getLocationByPortName(portName) {
      if (portName) {
        if (portName === '福清江阴') {
          portName = '江阴港区'
        } else if (portName === '烟台龙口') {
          portName = '龙口港区'
        }
        return this.basePortList.find(item => item.name.includes(portName))
      }
      return null
    },
    // 获取当前航次的航程距离
    async queryVoyageDistance() {
      let destanceList = this.dataList.filter(item => item.currentList.length > 0).map(item => {
        let startPort = this.getLocationByPortName(item.currentList[0].load_port_name)
        let endPort = this.getLocationByPortName(item.currentList[0].unload_port_name)
        return {
          ship_name: item.ship_name,
          start: {
            lon: startPort.longitude,
            lat: startPort.latitude
          },
          end: {
            lon: endPort.longitude,
            lat: endPort.latitude
          }
        }
      })
      let requestData = {
        inputs: {
          shipList: JSON.stringify(destanceList)
        },
        response_mode: 'blocking',
        user: "abc-123"
      }
      const response = await axios.post('http://************/v1/workflows/run', requestData, {
        headers: {
          'Authorization': 'Bearer app-nqRpQuEij2tXpfelcPFiOqf7',
          'Content-Type': 'application/json'
        }
      })
      const data = response.data.data.outputs.data
      this.dataList.forEach(item => {
        let target = data.find(_item => _item.ship_name === item.ship_name)
        item.nmile = target ? target.nmile : '-'
      })

    },
    scrollTable(val) {
      let container = this.$refs.tbodyRef
      if (container) {
        if (val === 'top') {
          container.scrollTop = 0
        } else {
          container.scrollTop = container.scrollHeight
        }
      }
    },
    getStatistic() {
      queryShipService({ start_month: dayjs().format('YYYY-MM'), end_month: dayjs().format('YYYY-MM') }).then(res => {
        if (res.data.Code === 10000) {
          this.statistic.chartGoodsNum = parseFloat(res.data.amount_sum) // 货运量
          this.statistic.chartTurnoverNum = parseFloat(res.data.turnover_sum) // 周转量
          this.statistic.chartVolumeNum = parseInt(res.data.voyage_sum) // 航次数
          // this.chartVoyageNum = parseFloat(res.data.mile_sum) // 航程
          this.statistic.chartWaitingTime = parseFloat(res.data.wait_berth_sum) // 待泊时长
          // this.chartTotalLoss = parseFloat(res.data.goods_loss_sum) // 总损耗
          // this.chartAverageLossRate = parseFloat(res.data.goods_loss_average) // 平均损耗

        }
      })
    },
    // 绘制船舶
    renameShip() {
      if (!localStorage.shipNameList) return
      let shipList = JSON.parse(localStorage.shipNameList).filter(item => item.business_model === '1' && !this.shipIgnoreList.includes(item.mmsi))
      let fleetships = []
      shipList.forEach(item => {
        var ship = item;
        var c_ship = new CanvasShip();
        c_ship.mmsi = ship.mmsi;
        c_ship.name = ship.ship_name;
        c_ship.custom_name = ship.ship_name;
        c_ship.istop = true;
        // 获取船位
        var _s_d = this.map.shipsService.getShipByMmsi(c_ship.mmsi, true);
        if (_s_d != null) {
          c_ship.shipid = _s_d.shipid;

          c_ship.hdg = _s_d.hdg
          c_ship.cog = _s_d.cog
          c_ship.draught = _s_d.draught
          c_ship.lat = _s_d.lat
          c_ship.lng = _s_d.lng
          c_ship.width = _s_d.width
          c_ship.length = _s_d.length
          c_ship.color = 'yellow'
        }
        //
        fleetships.push(c_ship);
      })
      this.map.shipsService.addFleetShips(fleetships);
    },
    // 定位船舶
    locShip(val) {
      if (this.dataList[val]) {
        this.map.shipsService.locationShip(this.dataList[val].mmsi, true)
        this.map.setZoom(7)
      }

      // this.refreshSingleShip(this.dataList[val].mmsi)

    },
    // 初始化地图
    initMap() {
      // 创建地图示例
      this.map = new ShipxyAPI.Map("map", this.options);
      // 默认 MT_SATELLITE 卫星图 MT_GOOGLE 谷歌地图 MT_SEA 海图
      this.map.basemapsControl.changeMap('MT_SEA')
      // 开启区域船服务
      const canvasShips = ShipxyAPI.ShipService(this.map, {
        enableAreaShip: false, // 区域船
        enableFleetShip: false, // 船队船
        // enableDShip: true, // D+船
        lableFont: ['600 12px Arial', '600 12px 宋体'], // 船舶名称，文字字体，默认值：["600 12px Arial", "500 12px Arial"]
        lableTxtColor: ['#000', '#eee'], // 船舶名称，文字颜色，默认值：["#000","#fff"]
        lableLineColor: ['rgba(1, 30, 62, 1)', 'rgba(1, 30, 62, 1)'], //  边框颜色，默认值：["#000","#000"]
        lableLinefillColor: ['rgba(255, 255, 255, 0.7)', 'rgba(1, 30, 62, 0.3)'], // 框内填充颜色，默认值：[null,null]
        obliqueLineColor: ['#000', '#000'], // 船舶名称，斜线颜色，默认值：[null,null]
        dShipColor: '#FF6437' // D+船颜色，默认：#ff6347
      })
      canvasShips.addSelectedListener(function (ship) {
        // 选中船监听
      });
    },
    specialPortName(portName) {
      if (portName === 'FU QING             ') {
        portName = 'JIANGYIN'
      }
      if (portName === 'CJK                 ') {
        portName = 'CHANGJIANGKOU';
      }
      if (portName === 'HUI ZH0U') {
        portName = 'HUIZHOU';
      }
      if (portName === 'HUI ZH0U            ') {
        portName = 'HUIZHOU';
      }
      if (portName === 'LONG_KOU            ') {
        portName = 'LONGKOU';
      }
      return portName
    },
    // 获取港口详情
    async getPortDetail(index) {
      if (index === undefined) return
      // if (this.portTrafficList[index]) return
      let ship = this.dataList[index]
      let portTrafficNum = {
        estimate: [],
        wait: [],
        work: []
      }
      this.portTrafficList[index] = portTrafficNum
      if (!ship || !ship.port_name) return
      // let portName = ship.unloadPortNames
      // if (portName === '福清江阴') {
      //   portName = '江阴港区'
      // }
      // let portDetail = this.basePortList.find(item => item.name === portName) 
      ship.port_name = this.specialPortName(ship.port_name)
      let portDetail = this.basePortList.find(portName => this.isMatch(ship.port_name, portName.pinyin, false))

      if (portDetail) {
        this.portLoading = true
        let _param = {
          mapbase_ports_id: portDetail.id
        }
        let [estimate, wait, work] = await Promise.all([
          queryShipFormulaInfo({
            url: 'https://mobile.shipformula.com/v1/api/node/map/MapBaseControl/getPortTrafficEstimate',
            paramMap: JSON.stringify(_param)
          }),
          queryShipFormulaInfo({
            url: 'https://mobile.shipformula.com/v1/api/node/map/MapBaseControl/getPortTrafficWait',
            paramMap: JSON.stringify(_param)
          }),
          queryShipFormulaInfo({
            url: 'https://mobile.shipformula.com/v1/api/node/map/MapBaseControl/getPortTrafficWork',
            paramMap: JSON.stringify(_param)
          })
        ])
        if (estimate.data.Code === 10000 && estimate.data.Result.info) {
          portTrafficNum.estimate = estimate.data.Result.info.rows || []
        }
        if (wait.data.Code === 10000 && wait.data.Result.info) {
          portTrafficNum.wait = wait.data.Result.info.rows || []
        }
        if (work.data.Code === 10000 && work.data.Result.info) {
          portTrafficNum.work = work.data.Result.info.rows || []
        }
        this.portTrafficList[index] = portTrafficNum
        this.portLoading = false
      }
    },
    // 获取天气
    async getWeather(index) {
      if (index === undefined) return
      // 如果当天已获取过天气数据  做数据缓存
      if (this.weatherInfo[index] && this.weatherInfo[index][0].date === dayjs().format('YYYY-MM-DD')) return
      let ship = this.dataList[index]

      if (!ship || !ship.dest) return
      let portName = ship.dest
      ship.port_name = this.specialPortName(ship.port_name)
      let portDetail = this.basePortList.find(item => this.isMatch(ship.port_name, item.pinyin, false))
      let res = null
      if (portDetail) {
        res = await axios.get(`http://api.openweathermap.org/data/2.5/forecast?lat=${portDetail.latitude}&lon=${portDetail.longitude}&appid=c26c6e6d37b96522d668ae55da072c71&lang=zh_cn&units=metric`)
      } else {
        res = await axios.get(`http://api.openweathermap.org/data/2.5/forecast?q=${portName}&appid=c26c6e6d37b96522d668ae55da072c71&lang=zh_cn`)
      }
      // OpenWeather 
      // axios.get(`http://api.openweathermap.org/data/2.5/forecast?q=${res.data.geocodes[0].city}&appid=c26c6e6d37b96522d668ae55da072c71&lang=zh_cn`).then(res => {
      if (res.data.cod === '200') {
        const weatherData = res.data.list
        // 按天分组数据
        const groupedData = {};
        weatherData.forEach((item) => {
          // const date = new Date(item.dt * 1000).toISOString().split('T')[0];
          const date = dayjs(item.dt * 1000).format('YYYY-MM-DD');
          if (!groupedData[date]) {
            groupedData[date] = [];
          }
          // 温度转换为摄氏度
          const tempCelsius = item.main.temp;
          item.main.temp = tempCelsius.toFixed(0);
          item.main.feels_like = item.main.feels_like.toFixed(0);
          item.main.temp_min = item.main.temp_min.toFixed(0);
          item.main.temp_max = item.main.temp_max.toFixed(0);

          // 风向转换为东西南北风
          const deg = item.wind.deg;
          let windDirection;
          if ((deg >= 337.5) || (deg < 22.5)) {
            windDirection = '北风';
          } else if (deg < 67.5) {
            windDirection = '东北风';
          } else if (deg < 112.5) {
            windDirection = '东风';
          } else if (deg < 157.5) {
            windDirection = '东南风';
          } else if (deg < 202.5) {
            windDirection = '南风';
          } else if (deg < 247.5) {
            windDirection = '西南风';
          } else if (deg < 292.5) {
            windDirection = '西风';
          } else {
            windDirection = '西北风';
          }
          item.wind.deg = windDirection;

          groupedData[date].push(item);
        });

        // 处理分组后的数据，添加一天内的最高和最低温度
        const result = Object.entries(groupedData).map(([date, data]) => {
          let minTemp = Infinity;
          let maxTemp = -Infinity;
          data.forEach((item) => {
            const temp = parseFloat(item.main.temp);
            minTemp = Math.min(minTemp, temp);
            maxTemp = Math.max(maxTemp, temp);
          });
          return {
            date,
            data,
            day: dayjs(date).day(),
            minTemperature: minTemp.toFixed(0),
            maxTemperature: maxTemp.toFixed(0)
          };
        });

        this.weatherInfo[index] = result
      }
      // axios.get(`https://restapi.amap.com/v3/weather/weatherInfo?city=${adcode}&key=2fe81a10ede49cfcafbbb0ec2104f06c&extensions=all`).then(weatherRes => {
      //   if (weatherRes.data.infocode === '10000') {
      //     this.weatherInfo = weatherRes.data.forecasts[0].casts.map((item, idx) => {
      //       return {
      //         ...item,
      //         power: isDaytime ? item.daypower : item.nightpower,
      //         temp: isDaytime ? item.daytemp : item.nighttemp,
      //         weather: isDaytime ? item.dayweather : item.nightweather,
      //         wind: isDaytime ? item.daywind : item.nightwind,
      //         day: dayjs(item.date).day()
      //       }
      //     })
      //   }
      // })
    },
    // 获取船舶列表
    async getList(val = 0) {
      let that = this
      if (!localStorage.shipNameList) return
      this.selfShipList = JSON.parse(localStorage.shipNameList)
      this.selfShipList = this.selfShipList.filter(item => item.business_model === '1' && !this.shipIgnoreList.includes(item.mmsi))
      // this.dataList = []
      this.anchorMmsiList = []
      this.anchorIdx = []
      let totalShipList = []
      let mmsiList = []
      this.selfShipList.forEach((item, idx) => { // 剔除万邦船舶
        if (val === 1) { // 针对爱兰账号处理万邦船舶展示
          totalShipList.push(item)
          mmsiList.push(item.mmsi)
        } else {
          if (this.isWanBang) {
            totalShipList.push(item)
            mmsiList.push(item.mmsi)
          } else {
            if (!this.shipIgnoreList.includes(item.mmsi)) {
              totalShipList.push(item)
              mmsiList.push(item.mmsi)
            }
          }
        }
      })
      let queryUrl = 'https://api.shipxy.com/apicall/GetManyShip?v=2&k=a5bb8f37140d428391e1546d7b704413&enc=1&id=' + mmsiList.join(',')
      await axios.get(queryUrl).then(res => {
        that.shipDetailList = res.data.data

        if (!that.dataList.length) {
          totalShipList.forEach((item, idx) => {
            that.dataList.push({
              business_model: item.business_model,
              business_name: item.business_name,
              mmsi: item.mmsi,
              ship_name: item.ship_name,
              status_code: that.shipDetailList[idx].navistat,
              port_name: that.shipDetailList[idx].dest,
              dest: this.matchPortByPinyin(that.shipDetailList[idx].dest),
              ata: that.shipDetailList[idx].eta_std,
              status: CanvasShipUtils.getDisValue(that.shipDetailList[idx].navistat, 'naviStatus', 'zh_CN'),
              delayTime: 0,
              delayHour: '--',
              delayTimeStr: '--',
              length: that.shipDetailList[idx].length,
              width: that.shipDetailList[idx].width,
              draught: that.shipDetailList[idx].draught,
              eta_std: that.shipDetailList[idx].eta_std,
              sog: that.shipDetailList[idx].sog,
              lasttime: dayjs(that.shipDetailList[idx].lasttime).format('YYYY-MM-DD HH:mm:ss'),
              lat: that.shipDetailList[idx].lat,
              lng: that.shipDetailList[idx].lon,
              cog: that.shipDetailList[idx].cog,
              hdg: that.shipDetailList[idx].hdg,
              rot: that.shipDetailList[idx].rot,
              shiptype: that.shipDetailList[idx].shiptype,
              light: (that.oilData.find(item1 => item1.vesselName === item.ship_name) || {}).light || '--',
              heavy: (that.oilData.find(item1 => item1.vesselName === item.ship_name) || {}).heavy || '--',
              lightConsum: (that.oilData.find(item1 => item1.vesselName === item.ship_name) || {}).lightConsum || '--',
              heavyConsum: (that.oilData.find(item1 => item1.vesselName === item.ship_name) || {}).heavyConsum || '--'

            })

            if (that.dataList[idx] && that.dataList[idx].status_code === 1) { // 锚泊状态
              that.anchorIdx.push(idx)
              that.getLastVoyageListShipXy(item.mmsi, idx, totalShipList.length)
            }

          })
        } else {

          that.dataList.forEach((item) => {
            let _shipDetail = that.shipDetailList.find(_item => _item.mmsi == item.mmsi)
            let _totalShip = totalShipList.find(_item => _item.mmsi === item.mmsi)
            if (_shipDetail && _totalShip) {

              item.business_model = _totalShip.business_model
              item.business_name = _totalShip.business_name
              item.ship_name = _totalShip.ship_name
              item.status_code = _shipDetail.navistat
              item.port_name = _shipDetail.dest
              item.ata = _shipDetail.eta_std
              item.status = CanvasShipUtils.getDisValue(_shipDetail.navistat, 'naviStatus', 'zh_CN')
              item.length = _shipDetail.length
              item.width = _shipDetail.width
              item.draught = _shipDetail.draught
              item.sog = _shipDetail.sog
              item.lasttime = dayjs(_shipDetail.lasttime).format('YYYY-MM-DD HH:mm:ss')
              item.lat = _shipDetail.lat
              item.lng = _shipDetail.lon
              item.cog = _shipDetail.cog
              item.hdg = _shipDetail.hdg
              item.rot = _shipDetail.rot
              item.shiptype = _shipDetail.shiptype

            }
          })
        }



      })
    },
    // 获取预警时间
    async getWarnTime() {
      let that = this
      this.dataList.forEach((item, idx) => {

        if (item.status_code === 1) { // 锚泊状态
          that.anchorIdx.push(idx)
          that.getLastVoyageListShipXy(item.mmsi, idx, totalShipList.length)
        }
      })
    },
    // 获取历史航次列表 shipXy
    async getLastVoyageListShipXy(mmsi, idx, len) {
      const that = this
      let dateTime = new Date()
      let currentYear = dateTime.getFullYear()
      let currentMonth = dateTime.getMonth()
      let currentDate = dateTime.getDate()
      let startDay = new Date(currentYear, currentMonth - 1, currentDate).getTime().toString().substring(0, 10)
      let endDay = dateTime.getTime().toString().substring(0, 10)
      // 获取时间段内航次 船舶靠港记录
      let url1 = 'http://api.shipxy.com/apicall/GetPortOfCallByShip?k=a5bb8f37140d428391e1546d7b704413' + '&v=2&mmsi=' + mmsi + '&timetype=2&begin=' + startDay + '&end=' + endDay
      // 获取港口及到港时间
      await jQuery.getJSON(url1 + '&jsf=?', function (result) {
        if (result.records.length > 0) {
          let startDate = result.records[result.records.length - 1].atd || result.records[result.records.length - 1].ata // 先拿离港，如果没有离港就拿到港时间
          that.getCurWarnList(mmsi, idx, len, startDate)
        }
      })
    },
    // 获取预警时间 hifleet
    async getCurWarnList(mmsi, idx, len, startDate) {
      const that = this
      const date = new Date()
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')

      let currentTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
      let _param = {
        mmsis: mmsi,
        zoom: 1,
        startdates: startDate,
        endates: currentTime
      }
      await getShipsTrajectory(_param).then(res => {
        if (res.data.status !== '1') {
          // this.pushMsg()
        }

        if (res.data.data.length > 0) {
          let delayHour = that.getDelayHours(mmsi, res.data.data[0].offers)
          Object.assign(that.dataList[idx], {
            anchorHours: parseFloat(delayHour) === 0 ? 0.1 : delayHour,
          })
        } else {
          Object.assign(that.dataList[idx], {
            port_name: '--',
            ata: '--'
          })
        }
        this.trackId += 1
        if (this.trackId === this.anchorIdx.length) {
          setTimeout(() => {
            that.dataList.sort((a, b) => {
              if (parseFloat(a.delayTime) > parseFloat(b.delayTime)) {
                return -1
              } else if (parseFloat(a.delayTime) < parseFloat(b.delayTime)) {
                return 1
              } else {
                if (parseInt(a.status_code) > parseInt(b.status_code)) {
                  return -1
                } else if (parseInt(a.status_code) < parseInt(b.status_code)) {
                  return 1
                } else {
                  return 0
                }
              }
            })
            that.$nextTick(() => {
              that.modelSort()
            })
            that.loading = false
          }, 2000)
        }


      }).catch(err => {
        // this.pushMsg()
      })
    },
    // 获取天气图标
    getWeatherIcon(weather) {
      for (let key in this.iconWeatherMap) {
        if (this.iconWeatherMap[key].includes(weather)) {
          return {
            icon: key,
            color: this.getWeatherColor(key)
          }
        }
      }
      return {
        icon: 'fa-sun',
        color: 'weather-sunny'
      }
    },
    // 获取天气颜色
    getWeatherColor(iconType) {
      const colorMap = {
        'fa-wind': 'weather-wind',
        'fa-cloud-sun': 'weather-cloudy',
        'fa-snowflake': 'weather-snow',
        'fa-smog': 'weather-fog',
        'fa-sun': 'weather-sunny',
        'fa-cloud-rain': 'weather-rain',
        'fa-water': 'weather-haze'
      }
      return colorMap[iconType] || 'weather-sunny'
    },
    // 获取预警时间
    getDelayHours(mmsi, list) {
      let totalMinutes = 0
      let backHour = 0
      if (list.length > 0) {
        list.forEach(item => {
          if (item.accumulatetime && item.accumulatetime !== '-') {
            const timeStr = item.accumulatetime.replace('h', ' ').replace('m', '')
            const [hours, minutes] = timeStr.split(' ')
            totalMinutes += parseInt(hours) * 60 + parseInt(minutes)
          }
        })
        backHour = (parseFloat(totalMinutes / 60)).toFixed(1)
      } else {
        backHour = '-'
      }
      return backHour
    },
    // 获取当前航次
    async getCurVoyage() {
      await queryVoyageMonthPlanListAndVoyagesByShipGroup().then(res => {
        if (res.data.Code === 10000) {
          let shipList = res.data.Result
          shipList.forEach(item => {
            item.currentList = item.planList.filter(item => item.from_flag === '0' && item.status === '2')
            item.completedList = item.planList.filter(item => item.status === '3')
            item.planList = item.planList.filter(item => item.status === '1')
          })
          this.dataList.forEach(item => {
            let targetItem = shipList.find(row => {
              return row.mmsi === item.mmsi
            })
            if (targetItem && targetItem.currentList.length > 0) {
              item.unloadPortNames = targetItem.currentList[0].unload_port_name
              item.currentRoute = `${targetItem.currentList[0].load_port_name}→${targetItem.currentList[0].unload_port_name}`
              item.cargo = targetItem.currentList[0].goods_name
              item.amounts = targetItem.currentList[0].amounts
            } else {
              item.currentRoute = '待下发'
              item.unloadPortNames = ''
              item.cargo = ''
              item.amounts = ''
            }
            item.planList = targetItem.planList
            item.currentList = targetItem.currentList
            item.completedList = targetItem.completedList
            item.currentPlan = targetItem.currentPlan
          })

          // 定义排序顺序
          const order = ["锚泊", "靠泊", "在航(主机推动)"];

          this.dataList.sort((a, b) => {
            const indexA = order.indexOf(a.status);
            const indexB = order.indexOf(b.status);
            if (indexA === indexB) {
              // 如果状态相同，并且是锚泊状态，按照时长降序排列
              if (a.status === "锚泊" && b.status === "锚泊") {
                return b.anchorHours - a.anchorHours;
              }
              return 0;
            }
            return indexA - indexB;
          });
          this.loading = false
          this.getWeather(0)
          this.getPortDetail(0)
          this.locShip(0)
        }
      })
    },
    getFirstPlan(planList) {
      if (!planList) {
        return ''
      }
      if (planList.some(item => item.from_flag === '0' && item.status === '1')) {
        return '0'
      } else if (planList.some(item => item.from_flag === '1' && item.status === '1')) {
        return `1`
      } else {
        return ''
      }
    },
    getPlanText(planList) {
      if (!planList) {
        return '--'
      }
      if (planList.some(item => item.from_flag === '0' && item.status === '1')) {
        let plan = planList.filter(item => item.from_flag === '0' && item.status === '1')[0]
        return `${plan.load_port_name}→${plan.unload_port_name} (${plan.goods_name}:${plan.amounts})`
      } else if (planList.some(item => item.from_flag === '1' && item.status === '1')) {
        let plan = planList.filter(item => item.from_flag === '1' && item.status === '1')[0]
        return `${plan.load_port_name}→${plan.unload_port_name} (${plan.goods_name}:${plan.amounts})`
      } else {
        return '--'
      }
    },
    // 获取计划航次
    async getPlanVoyage() {
      API.queryVoyagePlanConsolePage({
        pageSize: 1000,
        pageIndex: 1
      }).then(res => {
        if (res.data.Code === 10000) {
          this.dataList.forEach(item => {
            let targetItem = res.data.Result.find(row => {
              return row.mmsi === item.mmsi
            })
            if (targetItem) {
              item.planCargo = targetItem.cargoResult.map(item => {
                return `(${item.goods_name}:${item.amounts})`
              }).join(',')
              item.plannedRoute = `${targetItem.portResult[0].port_name}→${targetItem.portResult[1].port_name}`

            }
          })
        }
      })
    },
    // 计算文本宽度
    calculateTextWidths() {
      const container = document.querySelector('.alert-text-container');
      if (!container) return;

      const containerWidth = container.offsetWidth;
      this.notifications.forEach((notice, index) => {
        const tempSpan = document.createElement('span');
        tempSpan.style.visibility = 'hidden';
        tempSpan.style.position = 'absolute';
        tempSpan.style.whiteSpace = 'nowrap';
        tempSpan.style.font = window.getComputedStyle(container).font;
        tempSpan.textContent = notice.text;
        document.body.appendChild(tempSpan);
        this.textWidths[index] = tempSpan.offsetWidth;
        document.body.removeChild(tempSpan);
      });
    },
    isTextOverflow(text) {
      const index = this.notifications.findIndex(n => n.text === text);
      if (index === -1) return false;

      const container = document.querySelector('.alert-text-container');
      if (!container) return false;

      const textWidth = this.textWidths[index] || 0;
      const containerWidth = container.offsetWidth - 20; // 安全边距

      return textWidth > containerWidth;
    },

    // 开始通知轮播
    startNotificationRotation() {
      this.switchToNextNotification();
    },

    // 切换到下一条通知
    switchToNextNotification() {
      if (this.notifications.length === 0) return;

      const currentNotice = this.notifications[this.currentNotificationIndex];

      if (this.isTextOverflow(currentNotice.text)) {
        // 文本需要滚动，根据文本长度动态计算滚动时长
        const scrollDuration = this.calculateScrollDuration(currentNotice.text);

        // 设置动态CSS变量
        this.setScrollAnimationDuration(scrollDuration);

        this.notificationInterval = setTimeout(() => {
          this.goToNextNotification();
        }, scrollDuration);
      } else {
        // 文本不需要滚动，显示3秒后切换
        this.notificationInterval = setTimeout(() => {
          this.goToNextNotification();
        }, 3000);
      }
    },

    // 切换到下一条并重置动画
    goToNextNotification() {
      this.currentNotificationIndex = (this.currentNotificationIndex + 1) % this.notifications.length;

      // 等待DOM更新后继续轮播
      this.$nextTick(() => {
        // 继续下一轮轮播
        setTimeout(() => {
          this.switchToNextNotification();
        }, 200); // 给足够时间让新的通知显示和动画开始
      });
    },

    // 根据文本长度计算滚动时长
    calculateScrollDuration(text) {
      const textLength = text.length;
      const container = document.querySelector('.alert-text-container');

      if (!container) {
        // 如果无法获取容器，使用基于字符数的估算
        const baseTime = 2000; // 2秒延迟
        const scrollTime = Math.max(8000, textLength * 200); // 每个字符200ms，最少8秒
        return baseTime + scrollTime;
      }

      // 基于实际宽度计算更精确的时长
      const containerWidth = container.offsetWidth;
      const index = this.notifications.findIndex(n => n.text === text);
      const textWidth = this.textWidths[index] || 0;

      if (textWidth <= containerWidth) {
        return 3000; // 不需要滚动
      }

      // 计算滚动距离和合适的滚动速度
      const scrollDistance = textWidth + 50; // 文本宽度 + padding
      const readingSpeed = 150; // 每秒150像素的阅读速度（较慢，便于阅读）
      const scrollTime = Math.ceil(scrollDistance / readingSpeed) * 1000;

      // 添加延迟时间和缓冲时间
      const delayTime = 2000; // 2秒开始延迟
      const bufferTime = 1000; // 1秒缓冲时间
      const totalTime = delayTime + scrollTime + bufferTime;

      // 设置合理的时长范围：最少10秒，最多45秒
      return Math.max(10000, Math.min(45000, totalTime));
    },

    // 设置滚动动画的动态时长
    setScrollAnimationDuration(totalDuration) {
      const delayTime = 2000; // 2秒延迟
      const animationTime = totalDuration - delayTime - 1000; // 减去延迟和缓冲时间

      // 确保动画时间不小于5秒
      const finalAnimationTime = Math.max(5000, animationTime);

      // 设置CSS变量
      const alertTextElement = document.querySelector('.alert-text');
      if (alertTextElement) {
        alertTextElement.style.setProperty('--scroll-duration', `${finalAnimationTime}ms`);
        alertTextElement.style.setProperty('--scroll-delay', `${delayTime}ms`);
      }
    },


    getNoticeColor(type) {
      switch (type) {
        case 'warning': return '#ffb73f';
        case 'danger': return '#ff5a5a';
        case 'info': return '#38b0ff';
        default: return '#ff5a5a';
      }
    },
    toggleAutoPlay() {
      if (this.autoPlay) {
        this.startAutoHighlight();
      } else {
        clearInterval(this.highlightInterval);
        this.highlightInterval = null;
      }
    },
    startAutoHighlight() {
      if (this.highlightInterval) {
        clearInterval(this.highlightInterval);
      }
      if (this.autoPlay) {
        this.highlightInterval = setInterval(() => {
          if (!this.isHighlightPaused) {
            this.currentShipIndex = (this.currentShipIndex + 1) % this.dataList.length;
          }
        }, 10000); // 每10秒切换一次高亮船舶
      }
    },
    pauseAutoHighlight() {
      this.isHighlightPaused = true;
    },
    resumeAutoHighlight() {
      this.isHighlightPaused = false;
    },
    getRowClass(status, anchorHours) {
      if (status === '锚泊') {
        if (anchorHours >= 72) return 'row-danger';
        if (anchorHours >= 48) return 'row-warning';
        if (anchorHours >= 24) return 'row-caution';
      }
      return '';
    },
    getStatusClass(status) {
      if (status === '在航') return 'sailing';
      if (status === '在航(主机推动)') return 'sailing';
      if (status === '靠泊') return 'docked';
      if (status === '锚泊') return 'anchored';
      return '';
    },
    getStatusIcon(status) {
      if (status === '在航') return 'fas fa-ship';
      if (status === '在航(主机推动)') return 'fas fa-ship';
      if (status === '靠泊') return 'fas fa-anchor';
      if (status === '锚泊') return 'fas fa-life-ring';
      return 'fas fa-circle';
    },
    startClock() {
      this.updateDateTime();
      this.clockInterval = setInterval(this.updateDateTime, 1000);
    },
    updateDateTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');
      this.currentDateTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    openShipDetail(ship, index) {
      this.selectedShip = { ...ship }; // 克隆对象，避免直接引用
      this.selectedShipIndex = index;
      this.showShipDetail = true;

      // 暂停自动高亮
      if (this.autoPlay) {
        this.pauseAutoHighlight();
      }

      // 添加禁止滚动类到body
      document.body.classList.add('modal-open');
    },
    closeShipDetail() {
      this.showShipDetail = false;

      // 恢复自动高亮
      if (this.autoPlay) {
        this.resumeAutoHighlight();
      }

      // 移除禁止滚动类
      document.body.classList.remove('modal-open');
    },
    closeWeatherDetail() {
      this.showWeatherDetail = false;

      // 恢复自动高亮
      if (this.autoPlay) {
        this.resumeAutoHighlight();
      }

      // 移除禁止滚动类
      document.body.classList.remove('modal-open');
    },
    openWindyWebsite() {
      // 在新标签页中打开Windy天气网站
      window.open('https://www.windy.com/', '_blank');
    },
    closePortDetail() {
      this.showPortDetail = false;

      // 恢复自动高亮
      if (this.autoPlay) {
        this.resumeAutoHighlight();
      }

      // 移除禁止滚动类
      document.body.classList.remove('modal-open');
    },
    // 经纬度转换
    toLnglat(data) {
      let newData = data + ''
      if (data < 1) {
        newData = data * 1000000 + ''
      }
      if (newData.indexOf('.') < 0) {
        let _curIntData = data / 1000000
        newData = _curIntData + ''
      }
      let newArr = newData.split('.')
      let newStr = newArr[0] + '-' + (parseInt(newArr[1]) / 16666.36).toFixed(3)
      return newStr
    },
    // 船舶类型转换
    shipTypeTrans(typein) {
      let curShipType = ''
      let type = typein
      switch (true) {
        case (type >= 20 && type <= 29):
          curShipType = '地效应船'
          break
        case (type === 30):
          curShipType = '捕捞'
          break
        case (type === 31):
          curShipType = '拖引'
          break
        case (type === 32):
          curShipType = '拖引并且船长>200m 或船宽>25m'
          break
        case (type === 33):
          curShipType = '疏浚或水下作业'
          break
        case (type === 34):
          curShipType = '潜水作业'
          break
        case (type === 35):
          curShipType = '参与军事行动'
          break
        case (type === 36):
          curShipType = '帆船航行'
          break
        case (type === 37):
          curShipType = '娱乐船'
          break
        case (type >= 40 && type <= 49):
          curShipType = '高速船'
          break
        case (type === 50):
          curShipType = '引航船'
          break
        case (type === 51):
          curShipType = '搜救船'
          break
        case (type === 52):
          curShipType = '拖轮'
          break
        case (type === 53):
          curShipType = '港口供应船'
          break
        case (type === 54):
          curShipType = '载有防污染装置和设备的船舶'
          break
        case (type === 55):
          curShipType = '执法艇'
          break
        case (type === 56):
          curShipType = '备用-用于当地船舶的任务分配'
          break
        case (type === 57):
          curShipType = '备用-用于当地船舶的任务分配'
          break
        case (type === 58):
          curShipType = '医疗船（如 1949 年日内瓦公约及附加条款所规定）'
          break
        case (type === 59):
          curShipType = '符合 18 号决议（Mob-83）的船舶'
          break
        case (type >= 60 && type <= 69):
          curShipType = '客船'
          break
        case (type >= 60 && type <= 79):
          curShipType = '货船'
          break
        case (type >= 80 && type <= 89):
          curShipType = '油轮'
          break
        case (type >= 90 && type <= 99):
          curShipType = '其他类型的船舶'
          break
        case (type === 100):
          curShipType = '集装箱'
          break
        default:
          curShipType = '未知类型'
      }
      return curShipType
    },
    // 全屏显示
    toggleFullScreen() {
      this.$refs['fullscreen'].toggle()
    },
    fullscreenChange(fullscreen) {
      this.fullscreen = fullscreen
      // fullscreen ? this.map.basemapsControl.hide() : this.map.basemapsControl.show()
    },
    getForecastLineData(dayData) {
      if (!dayData || !dayData.data) return { xAxis: [], data: [], legend: ['温度'] };
      // 取出每3小时的时间点和温度
      const xAxis = dayData.data.map(d => dayjs(d.dt * 1000).format('HH:mm'));
      const data = dayData.data.map(d => parseFloat(d.main.temp));
      return {
        xAxis,
        data,
        legend: ['温度']
      };
    },
    renderWeatherChart() {
      if (!this.weatherInfo[this.currentShipIndex] || !this.weatherInfo[this.currentShipIndex][this.selectedForecastDayIndex]) return;
      const dayData = this.weatherInfo[this.currentShipIndex][this.selectedForecastDayIndex];

      const xAxis = dayData.data.map(d => dayjs(d.dt * 1000).format('HH:mm'));
      const data = dayData.data.map(d => parseFloat(d.main.temp));
      if (!this.$refs.weatherChart) return;
      if (this.weatherChartInstance) {
        this.weatherChartInstance.dispose();
      }
      this.weatherChartInstance = echarts.init(this.$refs.weatherChart);
      const option = {
        backgroundColor: 'transparent',
        grid: { left: 30, right: 20, top: 10, bottom: 0, containLabel: true },
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(30,40,60,0.95)',
          borderColor: '#38b0ff',
          textStyle: { color: '#e6f7ff' },
          formatter: (params) => {
            // params[0] 是当前点
            const idx = params[0].dataIndex;
            const d = dayData.data[idx];
            return `
              <div>
                <div><b>${dayjs(d.dt * 1000).format('HH:mm')}</b></div>
                <div>天气：${d.weather[0].description}</div>
                <div>温度：${d.main.temp}°C</div>
                <div>体感：${d.main.feels_like}°C</div>
                <div>湿度：${d.main.humidity}%</div>
                <div>风速：${d.wind.speed} m/s</div>
                <div>风向：${d.wind.deg}</div>
                <div>气压：${d.main.pressure} hPa</div>
              </div>
            `;
          }
        },
        xAxis: {
          type: 'category',
          data: xAxis,
          axisLine: { lineStyle: { color: '#8eb0d1' } },
          axisLabel: { color: '#8eb0d1', fontSize: 12 },
          splitLine: { show: false }
        },
        yAxis: {
          type: 'value',
          name: '°C',
          axisLine: { lineStyle: { color: '#8eb0d1' } },
          axisLabel: { color: '#8eb0d1', fontSize: 12 },
          splitLine: { lineStyle: { color: '#1a3d6a', type: 'dashed' } }
        },
        series: [{
          name: '温度',
          data: data,
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: { color: '#38b0ff', width: 2 },
          itemStyle: { color: '#38b0ff' },
          areaStyle: { color: 'rgba(56,176,255,0.08)' }
        }]
      };
      this.weatherChartInstance.setOption(option);
    },
    setPortTab(tab) {
      this.portTabActive = tab;
    },
    /**
     * 标准化拼音格式（统一大写、处理空格）
     * @param {string} str - 拼音字符串
     * @returns {string} 标准化后的拼音
     */
    normalizePinyin(str) {
      return str.trim().toUpperCase().replace(/\s+/g, ' '); // 多个空格合并为一个
    },

    /**
     * 将港口名称转换为拼音（处理空格分隔和多音字）
     * @param {string} portName - 港口名称（如"惠州港"）
     * @returns {string} 港口名称对应的拼音（如"HUI ZHOU GANG"）
     */
    getPortPinyin(portName) {
      // 将汉字转为拼音数组（无声调、空格分隔）
      // 配置：mode: 'normal' 全拼，tone: false 无声调，type: 'array' 返回数组

      const pinyinArray = pinyin(portName, { mode: 'normal', toneType: 'none', type: 'array' });

      // 拼接为空格分隔的字符串（如["hui", "zhou", "gang"] → "hui zhou gang"）
      return pinyinArray.join(' ');
    },

    /**
     * 检查目标拼音与港口名称是否匹配
     * @param {string} targetPinyin - 输入的目标拼音（如"HUI ZHOU"）
     * @param {string} portName - 港口名称（如"惠州港"）
     * @param {boolean} exact - 是否精确匹配（默认false：支持模糊/部分匹配）
     * @returns {boolean} 是否匹配
     */
    isMatch(targetPinyin, portName, exact = false) {
      const normalizedTarget = this.normalizePinyin(targetPinyin).trim().replace(/\s+/g, '');
      // const portPinyin = this.normalizePinyin(this.getPortPinyin(portName)).trim().replace(/\s+/g, '');

      if (exact) {
        // 精确匹配：必须完全一致
        return portName === normalizedTarget;
      } else {
        // 模糊匹配：目标拼音是港口拼音的前缀，或包含在其中
        return portName.startsWith(normalizedTarget) || portName.includes(normalizedTarget);
      }
    },

    /**
     * 从港口列表中匹配目标拼音对应的港口
     * @param {string} targetPinyin - 输入的目标拼音（如"HUI ZHOU"）
     * @param {boolean} exact - 是否精确匹配
     * @returns {string} 匹配的港口名称
     */
    matchPortByPinyin(targetPinyin, exact = false) {
      targetPinyin = this.specialPortName(targetPinyin)

      const res = this.basePortList.find(portName => this.isMatch(targetPinyin, portName.pinyin, exact))
      return res ? res.name : '-';
    }

  }
};
</script>

<style scoped>
/* 导入Font Awesome */
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css');

/* 基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.fleet-monitor {
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Microsoft YaHei", sans-serif;
  background-color: #011628;
  color: #e6f7ff;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 头部样式 */
.header {
  height: 50px;
  background-color: #001529;
  border-bottom: 1px solid #1a3d6a;
  display: flex;
  align-items: center;
  padding: 0 20px;
  position: relative;
}

.logo {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #072040, #0a345e);
  padding: 0 20px;
  height: 35px;
  border-radius: 4px;
  border: 1px solid #1a3d6a;
}

.logo-icon {
  font-size: 24px;
  color: #1ec0bb;
  margin-right: 10px;
}

.logo-text {
  color: #1ec0bb;
  font-size: 22px;
  font-weight: 500;
}

.datetime {
  font-weight: 600;
  padding: 8px 20px;
}

.notification-area {
  display: flex;
  align-items: center;
  height: 35px;
  background: linear-gradient(135deg, #072040, #0a345e);
  padding: 0 15px;
  border-radius: 4px;
  border: 1px solid #1a3d6a;
  position: absolute;
  right: 60px;
  overflow: hidden;
}

.notification-container {
  width: 580px;
  height: 100%;
  overflow: hidden;
}

.alert {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  padding-right: 10px;
}

.alert i {
  font-size: 16px;
  margin-right: 8px;
  flex-shrink: 0;
}

.alert-text-container {
  overflow: hidden;
  width: 100%;
}

.alert-text {
  white-space: nowrap;
  display: inline-block;
  padding: 4px 0;
}

.scrolling-text {
  display: inline-block;
  padding-right: 50px;
  animation: scroll-text var(--scroll-duration, 15s) linear 1;
  animation-delay: var(--scroll-delay, 1s);
  animation-fill-mode: forwards;
}

@keyframes scroll-text {

  0%,
  10% {
    transform: translateX(0);
  }

  90%,
  100% {
    transform: translateX(calc(-100% - 50px));
  }
}

.bell {
  position: relative;
  margin-left: 20px;
  height: 18px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.bell:hover {
  transform: scale(1.1);
}

.bell i {
  font-size: 16px;
  color: #ffb73f;
  transition: color 0.3s ease;
}

.bell:hover i {
  color: #ffa246;
}

.badge {
  position: absolute;
  top: -7px;
  right: -7px;
  width: 14px;
  height: 14px;
  background-color: #ff4d4f;
  color: white;
  border-radius: 50%;
  font-size: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 统计卡片区域样式 */
.stat-cards {
  display: flex;
  padding: 10px;
  gap: 10px;
}

.stat-card {
  flex: 1;
  background: linear-gradient(160deg, #0c294b, #072040);
  border-radius: 6px;
  padding: 8px 15px;
  display: flex;
  align-items: center;
  border: 1px solid #0e3461;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15px;
  font-size: 20px;
}

.blue {
  background-color: rgba(24, 144, 255, 0.15);
  color: #38b0ff;
}

.purple {
  background-color: rgba(114, 46, 209, 0.15);
  color: #a37feb;
}

.green {
  background-color: rgba(19, 194, 194, 0.15);
  color: #22e1e1;
}

.orange {
  background-color: rgba(250, 140, 22, 0.15);
  color: #ffa246;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 20px;
  font-weight: 500;
  color: #fff;
  display: flex;
  align-items: flex-end;
}

.unit {
  font-size: 14px;
  color: #8eb0d1;
  margin-left: 4px;
}

.stat-label {
  font-size: 14px;
  color: #8eb0d1;
}

/* 主内容区域样式 */
.main-content {
  display: flex;
  padding: 0 10px 10px;
  gap: 10px;
}

.left-panel {
  flex: 7;
}

.right-panel {
  flex: 3;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.fleet-table-card,
.map-card,
.port-info-card {
  background: linear-gradient(140deg, #0c294b, #072040);
  border-radius: 6px;
  height: 100%;
  padding: 10px 15px;
  border: 1px solid #0e3461;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #1ec0bb;
  border-bottom: 1px solid #1a3d6a;
  padding-bottom: 10px;
  display: flex;
  align-items: center;
  min-height: 24px;
  justify-content: space-between;
}

.card-title i {
  margin-right: 6px;
  color: #1ec0bb;
}

/* 自动播放开关 */
.autoplay-toggle {
  display: flex;
  align-items: center;
  margin-left: auto;
}

.autoplay-toggle input {
  opacity: 0;
  position: absolute;
}

.autoplay-toggle label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.toggle-track {
  width: 40px;
  height: 20px;
  background-color: rgba(0, 20, 40, 0.7);
  border: 1px solid #1a3d6a;
  border-radius: 20px;
  position: relative;
  transition: all 0.3s ease;
  margin-right: 8px;
}

.toggle-indicator {
  width: 16px;
  height: 16px;
  background-color: #555;
  border-radius: 50%;
  position: absolute;
  top: 1px;
  left: 2px;
  transition: all 0.3s ease;
}

input:checked+label .toggle-track {
  background-color: rgba(56, 176, 255, 0.3);
  border-color: #38b0ff;
}

input:checked+label .toggle-indicator {
  transform: translateX(20px);
  background-color: #38b0ff;
}

.toggle-label {
  font-size: 13px;
  color: #8eb0d1;
}

input:checked+label .toggle-label {
  color: #e6f7ff;
}

/* 表格样式 */
.fleet-table {
  width: 100%;
  height: calc(100% - 50px);
  overflow-x: hidden;
  position: relative;
}

table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  table-layout: fixed;
  /* 固定表格布局 */
}

table tbody {
  display: block;
  width: calc(100% + 8px);
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}

table thead tr,
table tbody tr {
  box-sizing: border-box;
  table-layout: fixed;
  display: table;
  width: 100%;
}

thead {
  background-color: #081f3a;
  position: sticky;
  top: 0;
  z-index: 10;
}

th {
  padding: 12px 8px;
  text-align: left;
  color: #8eb0d1;
  font-weight: 500;
  font-size: 14px;
  border-bottom: 1px solid #1a3d6a;
}

td {
  padding: 0px 8px;
  border-bottom: 1px solid #1a3d6a;
  font-size: 14px;
  color: #e6f7ff;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.status-tag {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  text-align: center;
}

.status-tag i {
  margin-right: 4px;
  font-size: 10px;
}

.anchor-hours {
  margin-left: 4px;
  font-size: 10px;
  opacity: 0.9;
}

.sailing {
  background-color: #22c55e;
  color: white;
}

.docked {
  background-color: #0b84e5;
  color: white;
}

.anchored {
  background-color: #f59e0b;
  color: white;
}

/* 整行预警样式 */
.row-caution {
  background-color: rgba(234, 179, 8, 0.15) !important;
}

.row-warning {
  background-color: rgba(249, 115, 22, 0.2) !important;
}

.row-danger {
  background-color: rgba(239, 68, 68, 0.25) !important;
}

tbody tr:hover {
  background-color: rgba(56, 176, 255, 0.1) !important;
}

/* 确保表格交替行的颜色与预警不冲突 */
tr:nth-child(even) {
  background-color: rgba(9, 30, 53, 0.3);
}

tr:hover {
  background-color: rgba(26, 61, 106, 0.3);
}

/* 预警行的悬停效果 */
.row-caution:hover {
  background-color: rgba(234, 179, 8, 0.25) !important;
}

.row-warning:hover {
  background-color: rgba(249, 115, 22, 0.3) !important;
}

.row-danger:hover {
  background-color: rgba(239, 68, 68, 0.35) !important;
}

/* 船舶高亮浮动效果 */
.highlighted-ship {
  background: linear-gradient(90deg, rgba(16, 52, 97, 0.4), rgba(56, 176, 255, 0.15), rgba(16, 52, 97, 0.4)) !important;
  border-radius: 4px;
  height: 50px;
}

.highlighted-ship td {
  color: #ffffff !important;
  border-bottom: 1px solid #1a3d6a;
  font-weight: 600;
  padding: 0px 8px;
}

/* 预警样式与高亮船舶兼容 */
.row-caution.highlighted-ship {
  background: linear-gradient(90deg, rgba(234, 179, 8, 0.2), rgba(234, 179, 8, 0.3), rgba(234, 179, 8, 0.2)) !important;
}

.row-warning.highlighted-ship {
  background: linear-gradient(90deg, rgba(249, 115, 22, 0.2), rgba(249, 115, 22, 0.3), rgba(249, 115, 22, 0.2)) !important;
}

.row-danger.highlighted-ship {
  background: linear-gradient(90deg, rgba(239, 68, 68, 0.2), rgba(239, 68, 68, 0.35), rgba(239, 68, 68, 0.2)) !important;
}


tbody tr {
  height: 42px;
}

/* 设置表格列宽 */
table th:nth-child(1),
table td:nth-child(1) {
  width: 8%;
}

table th:nth-child(2),
table td:nth-child(2) {
  width: 14%;
}

table th:nth-child(3),
table td:nth-child(3) {
  width: 13%;
}

table th:nth-child(4),
table td:nth-child(4) {
  width: 15%;
}

table th:nth-child(5),
table td:nth-child(5) {
  width: 17%;
}

table th:nth-child(6),
table td:nth-child(6) {
  width: 35%;
}

table th:nth-child(7),
table td:nth-child(7) {
  width: 8%;
}

/* 地图区域样式 */
.map-card {
  flex: 6;
}

.map-container {
  height: calc(100% - 40px);
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #1a3d6a;
}

.map-content {
  width: 100%;
  height: 100%;
  position: relative;
}

.ship-marker {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.ship-marker i {
  color: #38b0ff;
  font-size: 18px;
}

.marker-highlight {
  color: #ffa246 !important;
}

.marker-label {
  background-color: rgba(0, 10, 20, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 2px;
  font-size: 12px;
  margin-top: 2px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 港口信息区域样式 */
.port-info-card {
  flex: 4;
}

.section-title {
  font-weight: 500;
  margin-bottom: 8px;
  color: #b7c5d6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-title i {
  margin-right: 6px;
  color: #1ec0bb;
}

.weather-more-btn {
  color: #38b0ff;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
}

.weather-section {
  padding: 0 10px;
  height: 130px;
}

.weather-current {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.weather-icon {
  font-size: 32px;
  margin-right: 15px;
}

.weather-info {
  flex: 1;
}

.city-name {
  font-weight: 500;
  margin-bottom: 5px;
  color: #e6f7ff;
}

.temperature {
  color: #8eb0d1;
  font-size: 12px;
}

.weather-update {
  color: #8eb0d1;
  font-size: 12px;
}

.weather-forecast {
  display: flex;
  justify-content: space-between;
}

.forecast-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.day {
  font-size: 12px;
  color: #8eb0d1;
  margin-bottom: 4px;
}

.weather-icon-small {
  font-size: 12px;
  margin-bottom: 4px;
}

.temp {
  font-size: 12px;
  color: #8eb0d1;
}

.divider {
  height: 1px;
  background-color: #1a3d6a;
  margin: 8px 0;
}

.ship-stats-section {
  padding: 0 10px;
}

.ship-stats {
  display: flex;
  justify-content: space-between;
  gap: 10px;
  margin-top: 15px;
}


.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: rgba(8, 29, 49, 0.7);
  border-radius: 6px;
  border: 1px solid #1a3d6a;
}

.stat-item-icon {
  font-size: 24px;
  margin-top: 10px;
}

.ship-blue,
.departure-blue {
  color: #38b0ff;
}

.dock-green {
  color: #22c55e;
}

.stat-number {
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 5px;
  color: #fff;
}

.stat-item-label {
  font-size: 14px;
  color: #8eb0d1;
  margin-bottom: 5px;
}

/* 船舶详情弹窗样式 */
.ship-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 13, 26, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.ship-detail-container {
  width: 700px;
  max-height: 85vh;
  background: linear-gradient(135deg, #07213f, #0a3055);
  border-radius: 8px;
  border: 1px solid #1a3d6a;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: rgba(13, 41, 71, 0.7);
  border-bottom: 1px solid #1a3d6a;
}

.ship-title {
  display: flex;
  align-items: center;
  color: #38b0ff;
  font-size: 20px;
  font-weight: 600;
}

.ship-title i {
  margin-right: 10px;
}

.close-button {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: rgba(56, 176, 255, 0.1);
  border: 1px solid rgba(56, 176, 255, 0.3);
  color: #8eb0d1;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.close-button:hover {
  background-color: rgba(56, 176, 255, 0.2);
  color: #fff;
}

.modal-content {
  padding: 20px;
  overflow-y: auto;
  max-height: calc(85vh - 60px);
}

.info-section {
  margin-bottom: 12px;
  background-color: rgba(9, 30, 54, 0.5);
  border-radius: 6px;
  padding: 15px;
  border: 1px solid rgba(26, 61, 106, 0.6);
}

.section-header {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 15px;
  color: #e6f7ff;
  display: flex;
  align-items: center;
  border-bottom: 1px solid rgba(26, 61, 106, 0.6);
  padding-bottom: 10px;
}

.section-header i {
  margin-right: 8px;
  color: #38b0ff;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 13px;
  color: #8eb0d1;
  margin-bottom: 5px;
}

.info-value {
  font-size: 15px;
  color: #e6f7ff;
  font-weight: 500;
}

.status-tag-small {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  text-align: center;
}

.efficiency-good {
  color: #22c55e;
}

.route-list,
.route-current,
.route-planned {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.route-item {
  display: flex;
  align-items: center;
  color: #e6f7ff;
  padding: 8px 12px;
  background-color: rgba(13, 41, 71, 0.5);
  border-radius: 4px;
  border: 1px solid rgba(26, 61, 106, 0.4);
}

.route-item i {
  margin-right: 10px;
  color: #38b0ff;
}

.route-item.planned {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #e6f7ff;
}

.cargo-info {
  color: #8eb0d1;
  margin-left: auto;
  margin-right: 15px;
}

.plan-button {
  background-color: rgba(56, 176, 255, 0.2);
  color: #38b0ff;
  border: 1px solid rgba(56, 176, 255, 0.4);
  padding: 4px 10px;
  border-radius: 4px;
  font-size: 12px;
}

.text-success {
  background-color: rgba(0, 255, 98, 0.2);
  color: #00ff62;
  border: 1px solid rgba(0, 255, 98, 0.4);
}

/* 展开行样式 */
.expanded-row {
  background: rgba(16, 52, 97, 0.1) !important;
  border-top: 1px solid rgba(56, 176, 255, 0.2);
  height: 40px;
}

.expanded-content {
  padding: 15px 20px !important;
  border: none !important;
  height: 40px;
  line-height: 24px;
}

.ship-detail-expanded {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #e8f4fd;
  height: 100%;
}

/* 油料信息区域 */
.oil-info {
  display: flex;
  gap: 40px;
  align-items: center;
}

.oil-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.oil-label {
  font-size: 12px;
  color: #a8c8ec;
}

.oil-value {
  font-size: 13px;
  color: #ffffff;
  font-weight: 500;
}

/* AI分析按钮样式 */
.ai-text-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
}

.ai-text-btn:hover:not(.ai-disabled) {
  transform: scale(1.1);
}


.ai-artistic-text {
  font-size: 12px;
  font-weight: 600;
  /* font-style: italic; */
  background: linear-gradient(45deg, #5499ff 0%, #0066ff 30%, #c300ff 60%, #dd6cff 100%);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  /* letter-spacing: 0.5px; */
  animation: gradientFlow 8s ease-in-out infinite;
  /* transform: skew(-8deg); */
  font-family: 'Font Awesome 6 Free';
}

.ai-loading-dots {
  display: flex;
  gap: 3px;
  align-items: center;
}

.ai-loading-dots span {
  width: 4px;
  height: 4px;
  background: linear-gradient(135deg, #00d4ff, #0066ff);
  border-radius: 50%;
  animation: loadingBounce 1.4s infinite ease-in-out both;
}

.ai-loading-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.ai-loading-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

.ai-loading-dots span:nth-child(3) {
  animation-delay: 0s;
}

@keyframes gradientFlow {

  0%,
  100% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }
}

@keyframes loadingBounce {

  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.5;
  }

  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* AI分析弹窗样式 */
.ai-analysis-modal {
  max-width: 900px;
  width: 90vw;
  max-height: 80vh;
  overflow-y: auto;
}

/* 弹窗头部操作区域 */
.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 头部重新分析按钮 */
.reanalyze-button-header {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: linear-gradient(135deg, #38b0ff, #2196f3);
  border: none;
  border-radius: 6px;
  color: #ffffff;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(56, 176, 255, 0.3);
}

.reanalyze-button-header:hover {
  background: linear-gradient(135deg, #2196f3, #1976d2);
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(56, 176, 255, 0.4);
}

.reanalyze-button-header:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(56, 176, 255, 0.3);
}

.reanalyze-button-header i {
  font-size: 12px;
  transition: transform 0.3s ease;
}

.reanalyze-button-header:hover i {
  transform: rotate(180deg);
}

.ship-basic-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-bottom: 16px;
}

.basic-info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(56, 176, 255, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(56, 176, 255, 0.2);
}

.basic-info-item .info-label {
  font-size: 12px;
  color: #a8c8ec;
  font-weight: 500;
}

.basic-info-item .info-value {
  font-size: 13px;
  color: #ffffff;
  font-weight: 600;
}

/* AI分析内容区域 */
.ai-analysis-content {
  position: relative;
  min-height: 400px;
}

.ai-status-indicator {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: rgba(56, 176, 255, 0.1);
  border: 1px solid rgba(56, 176, 255, 0.3);
  border-radius: 8px;
  margin-bottom: 16px;
}

.ai-status-indicator i {
  font-size: 18px;
  color: #38b0ff;
  animation: robotPulse 2s ease-in-out infinite;
}

.status-text {
  font-size: 14px;
  color: #e1e8f0;
  font-weight: 500;
}

.typing-indicator {
  display: flex;
  gap: 4px;
  margin-left: auto;
}

.typing-indicator span {
  width: 6px;
  height: 6px;
  background: #38b0ff;
  border-radius: 50%;
  animation: typingDot 1.4s ease-in-out infinite both;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0s;
}

.ai-text-display {
  background: rgba(16, 52, 97, 0.3);
  border: 1px solid rgba(56, 176, 255, 0.2);
  border-radius: 12px;
  padding: 20px;
  min-height: 400px;
  max-height: 500px;
  overflow-y: auto;
  font-family: 'Microsoft YaHei', sans-serif;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  text-align: center;
  color: #a8c8ec;
}

.empty-state i {
  color: #38b0ff;
  opacity: 0.6;
  margin-bottom: 16px;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
  line-height: 1.6;
}


@keyframes robotPulse {

  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }

  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

@keyframes typingDot {

  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.5;
  }

  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 重新分析按钮区域 */
.ai-reanalyze-section {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid rgba(56, 176, 255, 0.2);
  display: flex;
  justify-content: center;
}

.reanalyze-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: linear-gradient(135deg, #38b0ff, #2196f3);
  border: none;
  border-radius: 8px;
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(56, 176, 255, 0.3);
}

.reanalyze-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #2196f3, #1976d2);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(56, 176, 255, 0.4);
}

.reanalyze-button:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(56, 176, 255, 0.3);
}

.reanalyze-button:disabled {
  background: rgba(56, 176, 255, 0.3);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.reanalyze-button.loading {
  background: rgba(56, 176, 255, 0.3);
  cursor: not-allowed;
}

.reanalyze-button i {
  font-size: 14px;
  transition: transform 0.3s ease;
}

.reanalyze-button:hover:not(:disabled) i {
  transform: rotate(180deg);
}

.button-loading-dots {
  display: flex;
  gap: 3px;
}

.button-loading-dots span {
  width: 4px;
  height: 4px;
  background: #ffffff;
  border-radius: 50%;
  animation: buttonLoadingDot 1.4s ease-in-out infinite both;
}

.button-loading-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.button-loading-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

.button-loading-dots span:nth-child(3) {
  animation-delay: 0s;
}

@keyframes buttonLoadingDot {

  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.5;
  }

  40% {
    transform: scale(1);
    opacity: 1;
  }
}



/* 展开行样式 */
.expanded-row {
  background: linear-gradient(135deg, rgba(16, 52, 97, 0.15), rgba(56, 176, 255, 0.08)) !important;
  animation: expandRow 0.3s ease-out;
}

@keyframes expandRow {
  from {
    opacity: 0;
    transform: scaleY(0);
  }

  to {
    opacity: 1;
    transform: scaleY(1);
  }
}


/* 禁止滚动body */
:global(.modal-open) {
  overflow: hidden;
}

/* 天气图标颜色 */
.weather-sunny {
  color: #ffa246;
}

.weather-cloudy {
  color: #eedc8d;
}

.weather-rain {
  color: #38b0ff;
}

.weather-snow {
  color: #a5d8ff;
}

.weather-fog {
  color: #b7c5d6;
}

.weather-wind {
  color: #22e1e1;
}

.weather-haze {
  color: #d4b483;
}

/* 修改天气图标样式 */
.weather-icon {
  font-size: 32px;
  margin-right: 15px;
}


.ivu-spin-fix {
  background-color: #09244590
}

.ivu-spin-fix.table-loading {
  background-color: #092546
}

#map {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: #a3ccff;
  z-index: 10;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.text-danger {
  color: #ffc400;
}

/* 修改天气图标样式 */
.weather-icon {
  font-size: 32px;
  margin-right: 15px;
}

/* 天气详情弹窗样式 */
.current-weather-detail {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.weather-main {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 8px 8px 8px;
  background-color: rgba(13, 41, 71, 0.3);
  border-radius: 8px;
  border: 1px solid rgba(26, 61, 106, 0.4);
}

.weather-icon-large {
  font-size: 28px;
  color: #ffa246;
  text-shadow: 0 0 10px rgba(255, 162, 70, 0.3);
}

.weather-main-info {
  flex: 1;
}

.weather-description {
  font-size: 14px;
  color: #e6f7ff;
  margin-bottom: 4px;
  font-weight: 500;
}

.temperature-large {
  font-size: 20px;
  color: #ffa246;
  font-weight: 600;
  margin-bottom: 2px;
}

.feels-like {
  font-size: 12px;
  color: #8eb0d1;
}

.weather-details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px;
  background-color: rgba(13, 41, 71, 0.3);
  border-radius: 6px;
  border: 1px solid rgba(26, 61, 106, 0.3);
}

.detail-item i {
  color: #38b0ff;
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.detail-item span {
  color: #e6f7ff;
  font-size: 14px;
}

/* 未来天气预报样式 */
.forecast-cards {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  /* 关键：不换行 */
  width: 100%;
  gap: 8px;
  padding-bottom: 0;
}

.forecast-card {
  background: linear-gradient(160deg, #0c294b, #072040);
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #0e3461;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  min-height: 120px;
  display: flex;
  flex-direction: column;
}

.forecast-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  border-color: #38b0ff;
}

.forecast-card-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(26, 61, 106, 0.3);
}

.forecast-day-name {
  font-size: 14px;
  font-weight: 600;
  color: #1ec0bb;
  margin-bottom: 2px;
}

.forecast-date {
  font-size: 11px;
  color: #8eb0d1;
}

.forecast-card-body {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  gap: 8px;
}

.forecast-card-icon {
  font-size: 28px;
  color: #ffa246;
  margin-bottom: 4px;
}

.forecast-card-info {
  text-align: center;
  flex: 1;
}

.forecast-card-weather {
  font-size: 12px;
  color: #e6f7ff;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.forecast-card-temp {
  font-size: 14px;
  color: #ffa246;
  font-weight: 600;
}

.forecast-card-footer {
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid rgba(26, 61, 106, 0.3);
}

.forecast-card-detail {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: #8eb0d1;
}

.forecast-card-detail i {
  font-size: 10px;
  color: #38b0ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .weather-details {
    grid-template-columns: 1fr;
  }

  .forecast-cards {
    grid-template-columns: repeat(2, 1fr);
  }

  .hourly-forecast {
    grid-template-columns: repeat(2, 1fr);
  }

  .forecast-content {
    flex-direction: column;
    text-align: center;
  }

  .forecast-details {
    flex-direction: row;
    justify-content: center;
    gap: 20px;
  }
}

@media (max-width: 480px) {
  .forecast-cards {
    grid-template-columns: 1fr;
  }

  .hourly-forecast {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 24小时详细预报样式 */
.hourly-forecast {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
}

.hourly-item {
  background-color: rgba(13, 41, 71, 0.3);
  border-radius: 6px;
  padding: 12px;
  border: 1px solid rgba(26, 61, 106, 0.3);
  text-align: center;
  transition: all 0.3s ease;
}

.hourly-item:hover {
  background-color: rgba(13, 41, 71, 0.5);
  transform: translateY(-2px);
}

.hourly-time {
  font-size: 12px;
  color: #8eb0d1;
  margin-bottom: 8px;
}

.hourly-icon {
  font-size: 20px;
  color: #ffa246;
  margin-bottom: 8px;
}

.hourly-temp {
  font-size: 16px;
  color: #ffa246;
  font-weight: 500;
  margin-bottom: 5px;
}

.hourly-desc {
  font-size: 11px;
  color: #e6f7ff;
  margin-bottom: 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.hourly-wind {
  font-size: 11px;
  color: #8eb0d1;
}

/* 响应式设计 */

.forecast-card.active {
  border: 2px solid #38b0ff;
  box-shadow: 0 0 12px #38b0ff44;
  background: linear-gradient(160deg, #0c294b 80%, #38b0ff22 100%);
}

.weather-echart {
  width: 100%;
  height: 220px;
  background: transparent;
  border-radius: 8px;
  box-shadow: none;
  margin: 0 auto;
}

.weather-main-compact {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 4px 10px;
  background: rgba(13, 41, 71, 0.18);
  border-radius: 6px;
  border: 1px solid rgba(26, 61, 106, 0.18);
  min-height: 38px;
}

.weather-icon-compact {
  font-size: 22px;
  margin-right: 0;
}

.weather-main-info-compact {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 14px;
  color: #e6f7ff;
  font-weight: 500;
}

.weather-desc {
  color: #38b0ff;
  font-weight: 600;
}

.weather-temp {
  color: #ffa246;
  font-size: 16px;
  font-weight: 700;
}

.weather-feel {
  color: #8eb0d1;
  font-size: 13px;
}

.weather-details-compact {
  display: flex;
  flex-wrap: wrap;
  gap: 18px;
  margin-top: 6px;
  font-size: 12px;
  color: #8eb0d1;
  padding-left: 2px;
}

.weather-details-compact span {
  white-space: nowrap;
}

.port-tabs {
  display: flex;
  margin-bottom: 16px;
  border-bottom: 1px solid #1a3d6a;
}

.port-tab {
  padding: 8px 24px;
  cursor: pointer;
  color: #8eb0d1;
  font-weight: 500;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.port-tab.active {
  color: #38b0ff;
  border-bottom: 2px solid #38b0ff;
  background: linear-gradient(90deg, #0c294b 80%, #38b0ff22 100%);
}

/* 港口表格容器样式 */
.port-table-container {
  max-height: 600px;
  /* 设置固定高度 */
  overflow-y: auto;
  /* 垂直滚动 */
  overflow-x: hidden;
  /* 隐藏水平滚动 */
  border: 1px solid #1a3d6a;
  border-radius: 4px;
  background-color: rgba(8, 29, 49, 0.3);
}

/* 港口表格样式 */
.port-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  table-layout: fixed;
}

.port-table thead {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: #081f3a;
}

.port-table th {
  padding: 12px 8px;
  text-align: left;
  color: #8eb0d1;
  font-weight: 500;
  font-size: 14px;
  border-bottom: 1px solid #1a3d6a;
  background-color: #081f3a;
}

.port-table td {
  padding: 12px 8px;
  border-bottom: 1px solid #1a3d6a;
  font-size: 14px;
  color: #e6f7ff;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.port-table tbody tr {
  transition: background-color 0.2s ease;
}

.port-table tbody tr:nth-child(even) {
  background-color: rgba(9, 30, 53, 0.3);
}

.port-table tbody tr:hover {
  background-color: rgba(26, 61, 106, 0.3);
}

/* 港口表格列宽设置 */
.port-table th:nth-child(1),
.port-table td:nth-child(1) {
  width: 35%;
}

.port-table th:nth-child(2),
.port-table td:nth-child(2) {
  width: 30%;
}

.port-table th:nth-child(3),
.port-table td:nth-child(3) {
  width: 35%;
}

/* 滚动条样式 */
.port-table-container::-webkit-scrollbar {
  width: 6px;
}

.port-table-container::-webkit-scrollbar-track {
  background: rgba(26, 61, 106, 0.2);
  border-radius: 3px;
}

.port-table-container::-webkit-scrollbar-thumb {
  background: rgba(56, 176, 255, 0.4);
  border-radius: 3px;
}

.port-table-container::-webkit-scrollbar-thumb:hover {
  background: rgba(56, 176, 255, 0.6);
}

/* 告警图标样式 */
.warning-icon {
  color: #ff4500 !important;
  transition: all 0.3s ease;
  animation: pulse 2s infinite;
  margin-right: 0 !important;
}

.warning-icon:hover {
  transform: scale(1.2);
  
}

/* 告警按钮样式 */
.alarm-icon-btn {
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
  color: #ffa246;
  display: flex;
  align-items: center;
  justify-content: center;
}

.alarm-icon-btn:hover {
  background: rgba(255, 162, 70, 0.2);
  transform: translateY(-1px);
}

@keyframes pulse {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0.6;
  }

  100% {
    opacity: 1;
  }
}

/* 自定义警告提示框样式 */
.warning-container {
  position: relative;
  display: inline-block;
}

.custom-tooltip {
  position: fixed;
  z-index: 9999;
  opacity: 1;
  visibility: visible;
  transition: opacity 0.3s ease, visibility 0.3s ease;
  pointer-events: none;
}

.tooltip-content {
  background: #2d2d2d;
  color: #ffffff;
  padding: 12px 16px;
  border-radius: 6px;
  font-size: 13px;
  line-height: 1.4;
  text-align: center;
  max-width: 300px;
  word-wrap: break-word;
  white-space: normal;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  position: relative;
}

.tooltip-arrow {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid #2d2d2d;
}

/* 告警弹窗样式 */
.alarm-modal-container {
  width: 90vw;
  max-width: 1200px;
  height: 80vh;
  background: linear-gradient(135deg, #07213f, #0a3055);
  border-radius: 8px;
  border: 1px solid #1a3d6a;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transform: scale(0.9);
  opacity: 0;
  transition: all 0.3s ease;
}

.alarm-modal-container.show {
  transform: scale(1);
  opacity: 1;
}

.alarm-modal-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 左侧海事局列表面板 */
.bureau-list-panel {
  width: 280px;
  background: rgba(13, 41, 71, 0.7);
  border-right: 1px solid #1a3d6a;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 15px 20px;
  background-color: rgba(13, 41, 71, 0.9);
  border-bottom: 1px solid #1a3d6a;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h3 {
  color: #38b0ff;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.total-count {
  color: #8eb0d1;
  font-size: 12px;
}

.bureau-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.bureau-item {
  padding: 12px 15px;
  margin-bottom: 8px;
  background: rgba(16, 52, 97, 0.3);
  border: 1px solid rgba(56, 176, 255, 0.2);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.bureau-item:hover {
  background: rgba(56, 176, 255, 0.1);
  border-color: rgba(56, 176, 255, 0.4);
}

.bureau-item.active {
  background: rgba(56, 176, 255, 0.2);
  border-color: #38b0ff;
}

.bureau-name {
  color: #e8f4fd;
  font-size: 14px;
  font-weight: 500;
}

.bureau-count {
  color: #ffa246;
  font-size: 12px;
  background: rgba(255, 162, 70, 0.2);
  padding: 2px 8px;
  border-radius: 12px;
}

/* 中间告警列表面板 */
.alarm-list-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #1a3d6a;
}

.alarm-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.alarm-item {
  padding: 15px;
  margin-bottom: 10px;
  background: rgba(16, 52, 97, 0.3);
  border: 1px solid rgba(56, 176, 255, 0.2);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.alarm-item:hover {
  background: rgba(56, 176, 255, 0.1);
  border-color: rgba(56, 176, 255, 0.4);
}

.alarm-item.active {
  background: rgba(56, 176, 255, 0.2);
  border-color: #38b0ff;
}

.alarm-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.alarm-type {
  color: #8eb0d1;
  font-size: 12px;
  background: rgba(142, 176, 209, 0.2);
  padding: 2px 6px;
  border-radius: 4px;
}

.alarm-category {
  color: #ffa246;
  font-size: 12px;
  background: rgba(255, 162, 70, 0.2);
  padding: 2px 6px;
  border-radius: 4px;
}

.alarm-title {
  color: #e8f4fd;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  line-height: 1.4;
}

.alarm-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.alarm-bureau {
  color: #38b0ff;
  font-size: 12px;
}

.alarm-type-detail {
  color: #8eb0d1;
  font-size: 12px;
}

/* 右侧告警详情面板 */
.alarm-detail-panel {
  width: 350px;
  background: rgba(13, 41, 71, 0.7);
  display: flex;
  flex-direction: column;
}

.alarm-detail-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.detail-item {
  margin-bottom: 20px;
}

.detail-item label {
  display: block;
  color: #38b0ff;
  font-size: 14px;
  font-weight: 600;
  /* margin-bottom: 8px; */
}

.detail-item span {
  color: #e8f4fd;
  font-size: 14px;
  line-height: 1.5;
}

.alarm-content {
  color: #e8f4fd;
  font-size: 14px;
  line-height: 1.6;
  background: rgba(16, 52, 97, 0.3);
  padding: 15px;
  border-radius: 6px;
  border: 1px solid rgba(56, 176, 255, 0.2);
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #8eb0d1;
  font-size: 14px;
}

.empty-state i {
  font-size: 24px;
  margin-bottom: 10px;
  opacity: 0.6;
}

/* 滚动条样式 */
.bureau-list::-webkit-scrollbar,
.alarm-list::-webkit-scrollbar,
.alarm-detail-content::-webkit-scrollbar {
  width: 6px;
}

.bureau-list::-webkit-scrollbar-track,
.alarm-list::-webkit-scrollbar-track,
.alarm-detail-content::-webkit-scrollbar-track {
  background: rgba(16, 52, 97, 0.3);
  border-radius: 3px;
}

.bureau-list::-webkit-scrollbar-thumb,
.alarm-list::-webkit-scrollbar-thumb,
.alarm-detail-content::-webkit-scrollbar-thumb {
  background: rgba(56, 176, 255, 0.4);
  border-radius: 3px;
}

.bureau-list::-webkit-scrollbar-thumb:hover,
.alarm-list::-webkit-scrollbar-thumb:hover,
.alarm-detail-content::-webkit-scrollbar-thumb:hover {
  background: rgba(56, 176, 255, 0.6);
}

.analysis-text {
  color: #e6f7ff !important;
}
</style>

<style>
/* AI分析文本样式 - 优化后的颜色搭配 */
.analysis-text h1 {
  color: #1ec0bb;
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 20px 0;
  padding-bottom: 12px;
  border-bottom: 2px solid rgba(30, 192, 187, 0.4);
  line-height: 1.3;
  letter-spacing: 0.5px;
  text-shadow: 0 0 8px rgba(30, 192, 187, 0.3);
}

.analysis-text h2 {
  color: #8eb0d1;
  font-size: 16px;
  font-weight: 600;
  margin: 28px 0 14px 0;
  display: flex;
  align-items: center;
  gap: 8px;
  line-height: 1.3;
  letter-spacing: 0.3px;
}

.analysis-text h3 {
  color: #22e1e1;
  font-size: 18px;
  font-weight: 600;
  margin: 10px 0 10px 0;
  line-height: 1.3;
  letter-spacing: 0.2px;
}

.analysis-text h4 {
  color: #8eb0d1;
  font-size: 16px;
  font-weight: 600;
  margin: 8px 0 8px 0;
  line-height: 1.3;
  letter-spacing: 0.1px;
}

.analysis-text strong {
  color: #ffa246;
  font-weight: 600;
  letter-spacing: 0.1px;
}

.analysis-text em {
  color: #b7c5d6;
  font-style: italic;
  letter-spacing: 0.1px;
}

.analysis-text ul {
  margin: 12px 0;
  padding-left: 20px;
}

.analysis-text li {
  margin: 4px 0;
  color: #e6f7ff !important;
  line-height: 1.5;
  letter-spacing: 0.1px;
  font-size: 15px !important;
}

/* Markdown 额外样式支持 */
.analysis-text ol {
  margin: 12px 0;
  padding-left: 20px;
}


.analysis-text p {
  margin: 12px 0;
  color: #e6f7ff;
  line-height: 1.5;
  letter-spacing: 0.1px;
  font-size: 15px;
}

.analysis-text blockquote {
  margin: 16px 0;
  padding: 12px 16px;
  border-left: 4px solid #1ec0bb;
  background: rgba(30, 192, 187, 0.08);
  color: #b7c5d6;
  font-style: italic;
  line-height: 1.5;
  letter-spacing: 0.1px;
  border-radius: 0 4px 4px 0;
  font-size: 15px;
}

.analysis-text code {
  background: rgba(30, 192, 187, 0.15);
  color: #ffa246;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 14px;
  letter-spacing: 0;
  border: 1px solid rgba(30, 192, 187, 0.2);
}

.analysis-text pre {
  background: rgba(0, 21, 41, 0.6);
  border: 1px solid rgba(30, 192, 187, 0.2);
  border-radius: 6px;
  padding: 16px;
  margin: 16px 0;
  overflow-x: auto;
  line-height: 1.4;
  letter-spacing: 0;
  font-size: 14px;
}

.analysis-text pre code {
  background: none;
  padding: 0;
  color: #e6f7ff;
  border: none;
  letter-spacing: 0;
  font-size: 14px;
}

.analysis-text table {
  width: 100%;
  border-collapse: collapse;
  margin: 12px 0;
  background: rgba(0, 21, 41, 0.3);
  border-radius: 6px;
}

.analysis-text th,
.analysis-text td {
  border: 1px solid rgba(30, 192, 187, 0.2);
  padding: 8px 10px;
  text-align: left;
}

.analysis-text th {
  background: rgba(30, 192, 187, 0.15);
  color: #1ec0bb;
  font-weight: 600;
  font-size: 15px;
}

.analysis-text td {
  color: #e6f7ff;
  font-size: 15px;
}

.analysis-text hr {
  border: none;
  height: 1px;
  background: linear-gradient(to right, transparent, rgba(30, 192, 187, 0.4), transparent);
  margin: 16px 0;
}

.analysis-text a {
  color: #38b0ff;
  text-decoration: none;
}

.analysis-text a:hover {
  color: #1ec0bb;
}
</style>