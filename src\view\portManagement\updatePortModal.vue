<template>
  <Drawer
    v-model="modalData.modal"
    :title="modalData.title"
    width="750"
    :mask-closable="false"
    @on-visible-change="modalShow"
  >
    <Form ref="formData" :model="formData" :rules="ruleForm" :label-width="105">
      <Row>
        <Col span="10">
          <FormItem label="港口名称" prop="port_name">
            <Input v-model="formData.port_name"></Input>
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span="10">
          <FormItem label="港口名称全拼">
            <Input v-model="formData.full_spelling"></Input>
          </FormItem>
        </Col>
        <Col span="10" offset="1">
          <FormItem label="港口名称首字母">
            <Input v-model="formData.initial"></Input>
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span="10">
          <FormItem label="港口所属省份" prop="port_province_key">
            <Select v-model="formData.port_province_key" filterable @on-change="getTidePort">
              <Option v-for="(item1, idx) in portProvinceList" :key="idx" :value="item1.value">{{ item1.label }}</Option>
            </Select>
          </FormItem>
        </Col>
        <Col span="10" offset="1">
          <FormItem label="港口所属区域" prop="port_area_key">
            <Select v-model="formData.port_area_key" filterable>
              <Option v-for="(item1, idx) in portAreaList" :key="idx" :value="item1.value">{{ item1.label }}</Option>
            </Select>
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span="10">
          <FormItem label="经度">
            <Input v-model="formData.lng"></Input>
          </FormItem>
        </Col>
        <Col span="10" offset="1">
          <FormItem label="纬度">
            <Input v-model="formData.lat"></Input>
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span="10">
          <FormItem label="时区" prop="timezone_key">
            <Select v-model="formData.timezone_key" filterable>
              <Option v-for="(item1, idx) in timezoneList" :key="idx" :value="item1.value">{{ item1.label }}</Option>
            </Select>
          </FormItem>
        </Col>
        <Col span="10" offset="1">
          <FormItem label="排序号">
            <Input v-model="formData.order_num"></Input>
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span="10">
          <FormItem label="关联潮汐港口">
            <Select v-model="formData.tide_port_id" filterable>
              <Option v-for="(item1, idx) in tidePortList" :key="idx" :value="item1.value">{{ item1.label }}</Option>
            </Select>
          </FormItem>
        </Col>
        <Col span="10" offset="1">
          <FormItem label="潮高基准面">
            <Input v-model="formData.tidal_datum"></Input>
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span="21">
          <FormItem label="附近港口">
            <Select v-model="formData.nearby_ports" multiple>
              <Option v-for="item in nearbyPortsList" :value="item.value" :key="item.value">{{ item.label }}</Option>
            </Select>
          </FormItem>
        </Col>
        <Col span="21">
          <FormItem label="摘要" prop="port_simple_intro">
            <Input type="textarea" :autosize="true" v-model="formData.port_simple_intro"></Input>
          </FormItem>
        </Col>
      </Row>
    </Form>
    <div class="demo-drawer-footer">
      <Button @click="clearData" style="margin-right:10px;">取消</Button>
      <Button type="primary" v-if="modalData.dialogType==='create'" @click="createData">保存</Button>
      <Button type="primary" v-if="modalData.dialogType==='update'" @click="updateData">保存</Button>
    </div>
  </Drawer>
</template>
<script>
import API from '@/api/portManagement'

export default {
  props: {
    modalData: Object
  },
  data () {
    return {
      portProvinceList: [],
      portAreaList: [],
      timezoneList: [],
      tidePortList: [],
      nearbyPortsList: [],
      // disabled: false,
      formData: {
        port_name: '',
        full_spelling: '',
        initial: '',
        port_province_key: '',
        port_area_key: '',
        lat: '',
        lng: '',
        timezone_key: '',
        order_num: '',
        nearby_ports: '',
        tide_port_id: '',
        tidal_datum: '',
        port_simple_intro: ''
      },
      ruleForm: {
        port_name: [
          { required: true, message: '港口名称不能为空', trigger: 'blur' }
        ],
        port_province_key: [
          { required: true, message: '港口所属省份不能为空', trigger: 'change' }
        ],
        port_area_key: [
          { required: true, message: '港口所属区域不能为空', trigger: 'change' }
        ],
        timezone_key: [
          { required: true, message: '时区不能为空', trigger: 'change' }
        ],
        port_simple_intro: [
          { required: true, message: '摘要不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    // 关联潮汐港口
    getTidePort () {
      API.queryTidePorts().then(res => {
        if (res.data.Code === 10000) {
          this.tidePortList = res.data.Result.map(item => {
            return {
              value: item.tide_port_id,
              label: item.tide_port_name
            }
          })
          // this.disabled = false
        }
      })
    },
    // 新增
    createData () {
      this.$refs['formData'].validate((valid) => {
        if (valid) {
          this.$Modal.confirm({
            title: '提示',
            content: '<p>是否确认新增港口？</p>',
            loading: true,
            onOk: () => {
              if (typeof this.formData.nearby_ports === 'object') this.formData.nearby_ports = this.formData.nearby_ports.join(',')
              let data = Object.assign({}, this.formData)
              API.addPort(data).then(res => {
                if (res.data.Code === 10000) {
                  this.$Message.success(res.data.Message)
                  this.$Modal.remove()
                  this.$emit('callback')
                  this.modalData.modal = false
                } else {
                  this.modalData.modal = true
                  this.$Message.error(res.data.Message)
                  this.$Modal.remove()
                }
              }).catch()
            }
          })
        }
      })
    },
    // 修改
    updateData () {
      this.$refs['formData'].validate((valid) => {
        if (valid) {
          this.$Modal.confirm({
            title: '提示',
            content: '<p>是否修改港口信息？</p>',
            loading: true,
            onOk: () => {
              if (typeof this.formData.nearby_ports === 'object') this.formData.nearby_ports = this.formData.nearby_ports.join(',')
              let data = {
                port_id: this.formData.port_id,
                port_name: this.formData.port_name,
                full_spelling: this.formData.full_spelling,
                initial: this.formData.initial,
                port_province_key: this.formData.port_province_key,
                port_area_key: this.formData.port_area_key,
                lat: this.formData.lat,
                lng: this.formData.lng,
                timezone_key: this.formData.timezone_key,
                order_num: this.formData.order_num,
                tide_port_id: this.formData.tide_port_id,
                tidal_datum: this.formData.tidal_datum,
                nearby_ports: this.formData.nearby_ports,
                port_simple_intro: this.formData.port_simple_intro
              }
              API.updatePort(data).then(res => {
                if (res.data.Code === 10000) {
                  this.$Message.success(res.data.Message)
                  this.$Modal.remove()
                  this.$emit('callback')
                  this.modalData.modal = false
                } else {
                  this.modalData.modal = true
                  this.$Message.error(res.data.Message)
                  this.$Modal.remove()
                }
              }).catch()
            }
          })
        }
      })
    },
    // 取消
    clearData () {
      this.modalData.modal = false
    },
    // 模态框展示
    modalShow (val) {
      if (val) {
        // 获取省份/区域/时区/附近港口
        API.queryDicListByCode({ dic_code: 'portProvince' }).then(res => {
          if (res.data.Code === 10000) {
            this.portProvinceList = res.data.Result.map(item => {
              return {
                value: item.port_province_key,
                label: item.port_province_name
              }
            })
          }
        })
        API.queryDicListByCode({ dic_code: 'portArea' }).then(res => {
          if (res.data.Code === 10000) {
            this.portAreaList = res.data.Result.map(item => {
              return {
                value: item.port_area_key,
                label: item.port_area_name
              }
            })
          }
        })
        API.queryDicListByCode({ dic_code: 'timezone' }).then(res => {
          if (res.data.Code === 10000) {
            this.timezoneList = res.data.Result.map(item => {
              return {
                value: item.timezone_key,
                label: item.timezone_name
              }
            })
          }
        })
        API.queryNearbyPorts().then(res => {
          if (res.data.Code === 10000) {
            this.nearbyPortsList = res.data.Result.map(item => {
              return {
                value: item.port_id,
                label: item.name
              }
            })
          }
        })
        if (this.modalData.dialogType === 'update') {
          this.formData = { ...{}, ...this.modalData.data }
          if (typeof this.formData.nearby_ports === 'string') this.formData.nearby_ports = this.formData.nearby_ports.split(',')
        }
        // if (this.formData.port_province_key === '') {
        //   this.disabled = true
        // } else {
        //   this.getTidePort()
        // }
        this.getTidePort()
      } else {
        this.$nextTick(() => {
          this.formData = {}
          this.$refs['formData'].resetFields()
        })
      }
    }
  }
}
</script>
