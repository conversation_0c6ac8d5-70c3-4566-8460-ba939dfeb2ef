<template>
  <div>
    <Drawer
      v-model="modalData.modal"
      :title="modalData.title"
      :width="1200"
      @on-visible-change="visibleChange">
      <div>
        时间：<month-select class="month-select" @on-change="sdateSelect" :placeMent="startPlace" v-model="curDate"></month-select>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        对比时间：<month-select class="month-select" @on-change="cdateSelect" v-model="checkDate"></month-select>
        <Button style="margin-left: 20px;" type="primary" size="small" @click="searchData">查询</Button>
        <Button style="margin-left: 10px;" size="small" @click="searchReset">重置</Button>
      </div>
      <div v-if="modalData.modal" style="margin-top: 20px;">
        <!-- <ChartLine class="box-chart" :value="lineData" :formatter="true" :color="lineColor" text="船舶航次" /> -->
        <ChartBar class="box-chart" :value="lineData" unit="次" :color="lineColor" text="船舶航次" rotate="45"></ChartBar>
      </div>
      <Table border max-height="450" :loading="listLoading" ref="selection" sum-text="总计" show-summary :columns="columns" :data="dataList"></Table>
      <!-- <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="queryParam.pageSize" :current.sync="queryParam.pageIndex"
      :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/> -->
    </Drawer>
  </div>
</template>
<script>
import API from '@/api/statistics/transPortView'
import MonthSelect from '@/components/monthSelect'
import { ChartLine, ChartBar } from '_c/charts'

export default {
  props: {
    modalData: Object,
    dateObj: Object,
    defaultStartDate: String,
    defaultEndDate: String
  },
  components: {
    MonthSelect,
    ChartLine,
    ChartBar
  },
  data () {
    return {
      startPlace: 'bottom-start',
      isLegend: false, // 不显示图例
      listLoading: false, // 表格加载
      lineColor: ['#5B8FF9', '#73DEB3'],
      shipList: [], // 公司船舶列表
      queryParam: {
        start_month: '',
        end_month: '',
        start_contrast_month: '',
        end_contrast_month: ''
      },
      lineData: {
        xAxis: [],
        legend: ['时间', '对比时间'],
        data: [[], []],
        symbol: ['circle', 'triangle']
      },
      columns: [
        {
          title: '船舶',
          key: 'ship_name',
          align: 'center'
        }
      ],
      dataList: [] // 表格数据
    }
  },
  computed: {
    curDate () {
      if (this.queryParam.start_month !== '' && this.queryParam.end_month !== '') {
        return this.queryParam.start_month + '~' + this.queryParam.end_month
      }
      if (this.queryParam.start_month !== '' && this.queryParam.end_month === '') {
        return this.queryParam.start_month
      }
      return ''
    },
    checkDate () {
      if (this.queryParam.start_contrast_month !== '' && this.queryParam.end_contrast_month !== '') {
        return this.queryParam.start_contrast_month + '~' + this.queryParam.end_contrast_month
      }
      if (this.queryParam.start_contrast_month !== '' && this.queryParam.end_contrast_month === '') {
        return this.queryParam.start_contrast_month
      }
      return ''
    }
  },
  methods: {
    tabChange (id) {
      this.queryParam.pageIndex = 1
      this.queryParam.direction_type = id
      this.getList()
    },
    // 日期改变触发
    dateSelect (dateObj) {
      this.queryParam.start_month = dateObj[0] ? dateObj[0] : ''
      this.queryParam.end_month = dateObj[1] ? dateObj[1] : ''
    },
    // 船舶数据改变触发
    shipSelect (value) {
      this.queryParam.ship_id = value
    },
    // 开始时间变化触发
    sdateSelect (dateObj) {
      this.queryParam.start_month = dateObj[0] ? dateObj[0] : ''
      this.queryParam.end_month = dateObj[1] ? dateObj[1] : ''
    },
    // 对比时间变化触发
    cdateSelect (dateObj) {
      this.queryParam.start_contrast_month = dateObj[0] ? dateObj[0] : ''
      this.queryParam.end_contrast_month = dateObj[1] ? dateObj[1] : ''
    },
    // 开始查询
    searchData () {
      this.queryParam.pageIndex = 1
      this.getList()
    },
    // 重置按钮点击
    searchReset () {
      this.queryParam = {
        start_month: this.defaultStartDate,
        end_month: this.defaultEndDate,
        start_contrast_month: this.defaultStartDate,
        end_contrast_month: this.defaultEndDate
      }
      this.getList()
    },
    getList () {
      this.listLoading = true
      this.columns = [
        {
          title: '船舶',
          key: 'ship_name',
          align: 'center'
        }
      ]
      this.dataList = []
      this.lineData.xAxis = []
      this.lineData.data = [[], []]
      this.lineData.legend = [this.curDate, this.checkDate]
      // 折线数据
      let curDataArr = []
      let compareDataArr = []
      API.queryShipsGoodsAmountOverall(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          if (res.data.shipNumAndAmountArray.length > 0) { // 剔除货运量 航次数据为0船舶(暂时以货运量为标准数据计算)
            res.data.shipNumAndAmountArray.forEach((item, idx) => {
              if (res.data.shipNumAndAmountArray[idx].voyage_total !== '0' || res.data.contrastShipNumAndAmountArray[idx].voyage_total !== '0') {
                curDataArr.push(res.data.shipNumAndAmountArray[idx])
                compareDataArr.push(res.data.contrastShipNumAndAmountArray[idx])
              }
            })
          }
          // 船舶货运量 & 船舶航次对比数据
          this.resetShipAmountVoyage(curDataArr, 0)
          this.resetShipAmountVoyage(compareDataArr, 1)
        }
      })
      // 表格数据
      API.queryShipsGoodsAmountOverallReport(this.queryParam).then(res => {
        this.listLoading = false
        if (res.data.Code === 10000) {
          res.data.monthReport.forEach((item, index) => {
            this.dataList.push({
              ship_name: item.ship_name
            })
            item.month_details.forEach((list, idx) => {
              Object.assign(this.dataList[index], {
                ['value' + idx]: list.voyage_total
              })
            })
          })
          if (res.data.monthReport.length > 0) {
            res.data.monthReport[0].month_details.forEach((item, idx) => {
              this.columns.push({
                title: item.report_month,
                key: 'value' + idx,
                align: 'center'
              })
            })
          }
        }
      })
    },
    // 船舶货运量 & 船舶航次对比数据
    resetShipAmountVoyage (data, backIdx) {
      data.forEach(item => {
        if (!item.ship_name.includes('万华')) { // 剔除万华8
          if (this.lineData.xAxis.includes(item.ship_name)) {
            let _curIdx = this.lineData.xAxis.findIndex(list => { return item.ship_name === list.ship_name })
            if (_curIdx > 0 && this.lineData.xAxis.length > 1) { // 判断是否有数据  无数据补0
              this.lineData.xAxis.forEach((item, idx) => {
                if (idx < _curIdx) {
                  this.lineData.data[backIdx][idx] = 0
                } else {
                  this.lineData.data[_curIdx] = item.voyage_total
                }
              })
            } else {
              this.lineData.data[backIdx].push(item.voyage_total)
            }
          } else {
            this.lineData.xAxis.push(item.ship_name)
            this.lineData.data[backIdx].push(item.voyage_total)
          }
        }
      })
    },
    visibleChange (val) {
      if (val) {
        this.queryParam = this.dateObj
        this.getList()
      } else {
        this.queryParam = {
          start_month: '',
          end_month: '',
          start_contrast_month: '',
          end_contrast_month: ''
        }
      }
    }
  }
}
</script>
<style scoped>
  .box-chart {
    height: 250px;
    margin-top: 20px;
  }
  .select-ship-content {
    width: 110px;
    margin-left:12px !important;
  }
</style>
