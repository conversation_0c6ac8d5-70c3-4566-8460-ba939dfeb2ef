<template>
  <div>
    <div class="control-top-area">
      <div class="control-top-area-base">
        <div class="control-user">{{ full_name }}</div>
        <div class="control-name">管理船舶</div>
        <div class="control-boat">{{ shipNum }}</div>
        <Button v-if="getCompanyAdmin" class="control-btn" type="primary" @click="addPlanVoyage">
          新增航次
          <Icon type="md-add-circle" size="20"/>
        </Button>
      </div>
      <Row class="control-top-area-list">
        <Col class="control-top-area-voyage" span="14">
          <div>
            <span class="title-font">管理船舶</span>
            <Button type="text" class="show-more" v-if="getAccess" @click="showAllVoyage">查看全部</Button>
          </div>
          <curTemp></curTemp>
        </Col>
        <Col class="control-top-area-berth" span="10">
          <div>
            <span class="title-font">靠泊计划</span>
            <Button type="text" class="show-more" v-if="getAccess" @click="addBerth">新增</Button>
            <Button class="time-set" v-if="getSuperAdmin" @click="setStatTime" type="primary" size="small" icon="md-settings"></Button>
            <month-select class="set_date" @on-change="setDateSelect" v-model="setTimeDate" v-show="showSetDate"></month-select>
          </div>
          <berthTemp ref="berthPlanRef"></berthTemp>
        </Col>
      </Row>
    </div>
    <div class="cur-area">
      <Tabs type="card" v-model="curTab" :animated="false">
          <TabPane label="当前航次" name="curVoyage">
            <ctrlCurVoyage ref="curVoyageRef"></ctrlCurVoyage>
          </TabPane>
          <TabPane label="计划航次" name="planVoyage">
            <ctrlPlanVoyage ref="planVoyageRef"></ctrlPlanVoyage>
          </TabPane>
          <Button @click="handleTabsFresh" type="primary" icon="md-sync" size="small" slot="extra">刷新</Button>
      </Tabs>
    </div>
    <!-- 新增航次 -->
    <voyageAdd ref="voyageAddDrawer" @addBack="addVoyageBack"></voyageAdd>
    <!-- 新增靠泊计划 -->
    <berthingPlanEdit ref="berthingPlanEditModal" @addSuccess="addBerthBack"></berthingPlanEdit>
  </div>
</template>

<script>
import boatBack from '@/assets/images/controlBoatBack.png'
import './home.less'
import voyageAdd from '../../voyage/drawer/voyageAddDraw'
import curTemp from './components/curTemp'
import berthTemp from './components/berthTemp'
import ctrlCurVoyage from './components/ctrlCurVoyage'
import ctrlPlanVoyage from './components/ctrlPlanVoyage'
import berthingPlanEdit from '@/view/berthingPlan/berthingPlanEdit'
import MonthSelect from '@/components/monthSelect'
import { addStatTime } from '@/api/basicData.js'

export default {
  name: 'home',
  components: {
    voyageAdd,
    curTemp,
    berthTemp,
    ctrlCurVoyage,
    ctrlPlanVoyage,
    berthingPlanEdit,
    MonthSelect
  },
  data () {
    return {
      showSetDate: false,
      setTimeParam: {
        start_month: '',
        end_month: ''
      },
      curTab: 'curVoyage',
      boatBack,
      shipNum: 0,
      full_name: ''
    }
  },
  created () {
    this.$nextTick(() => {
      this.shipNum = localStorage.bussiShipList ? JSON.parse(localStorage.bussiShipList).length : 0
      this.full_name = this.$store.state.user.full_name
    })
  },
  computed: {
    getCompanyAdmin () {
      return this.$store.state.user.access.includes('companyAdmin')
    },
    getAccess () {
      let accessObj = this.$store.state.user.access
      if (accessObj === 'business' || accessObj.includes('business')) {
        return true
      }
      return false
    },
    getSuperAdmin () {
      let accessObj = this.$store.state.user.access
      if (accessObj === 'super_admin') {
        return true
      }
      return false
    },
    setTimeDate () {
      if (this.setTimeParam.start_month !== '' && this.setTimeParam.end_month !== '') {
        return this.setTimeParam.start_month + '~' + this.setTimeParam.end_month
      }
      if (this.setTimeParam.start_month !== '' && this.setTimeParam.end_month === '') {
        return this.setTimeParam.start_month
      }
      return ''
    }
  },
  methods: {
    // 设定时间区间点击时间
    setStatTime () {
      this.showSetDate = !this.showSetDate
    },
    // 设定区间日期选择
    setDateSelect (dateObj) {
      this.setTimeParam.start_month = dateObj[0] ? dateObj[0] : ''
      this.setTimeParam.end_month = dateObj[1] ? dateObj[1] : ''
      let data = {
        start_month: this.setTimeParam.start_month,
        end_month: this.setTimeParam.end_month
      }
      addStatTime(data).then(res => {
        if (res.data.Code === 10000) {
          this.$Message.success(res.data.Message)
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 新增计划航次
    addPlanVoyage () {
      this.$refs.voyageAddDrawer.formModal = true
      this.$refs.voyageAddDrawer.title = '新增航次'
      this.$refs.voyageAddDrawer.showType = 'create'
    },
    // 新增回调
    addVoyageBack () {
      this.curTab = 'planVoyage'
      this.$refs.planVoyageRef.getList()
      this.$forceUpdate()
    },
    // 新增靠泊计划回调
    addBerthBack () {
      this.$refs.berthPlanRef.getList()
      this.$forceUpdate()
    },
    // 显示全部航次信息
    showAllVoyage () {
      this.$router.push({
        name: 'voyageManagement'
      })
    },
    // 显示全部靠泊计划
    addBerth () {
      this.$refs.berthingPlanEditModal.berthingFormModal = true
      this.$refs.berthingPlanEditModal.dialogType = 'create'
      this.$refs.berthingPlanEditModal.berthingPlanFormTitle = '新增'
    },
    // 刷新当前数据
    handleTabsFresh () {
      if (this.curTab === 'planVoyage') {
        this.$refs.planVoyageRef.getList()
      }
      if (this.curTab === 'curVoyage') {
        this.$refs.curVoyageRef.getList()
      }
    }
  }
}
</script>

<style lang="less">
.count-style{
  font-size: 50px;
}
</style>
