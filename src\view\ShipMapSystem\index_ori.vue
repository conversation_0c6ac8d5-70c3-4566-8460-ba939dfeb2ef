<template>
  <div class="ship-schedule-page">
    <!-- 全局数据刷新指示器 -->
    <div class="global-refresh-indicator" v-if="isExiting">
      <div class="refresh-circle"></div>
      <div class="refresh-text">数据刷新中...</div>
    </div>

    <!-- 页面头部 -->
    <header class="page-header">
      <div class="left">
        <div class="date">{{ today }}</div>
        <div class="time">{{ currentTime }}</div>
      </div>
      <div class="stats">
        <div class="stat-item">
          <Icon type="md-cube" size="24" />
          <div class="stat-content">
            <div class="stat-title">本月规运量</div>
            <div class="stat-value">{{ monthlyPlan }} 吨</div>
          </div>
        </div>
        <div class="stat-item">
          <Icon type="logo-usd" size="24" />
          <div class="stat-content">
            <div class="stat-title">本月毛业额</div>
            <div class="stat-value">{{ monthlyRevenue }} 万元</div>
          </div>
        </div>
        <div class="stat-item">
          <Icon type="md-trending-up" size="24" />
          <div class="stat-content">
            <div class="stat-title">本月利润</div>
            <div class="stat-value">{{ monthlyProfit }} 万元</div>
          </div>
        </div>
      </div>
      <!-- 通知消息 -->
      <div class="notification">
        <Icon type="md-notifications" size="24" color="#f30" />
        <div class="notification-content" ref="notificationContent">
          <div class="notification-scroller" :style="{ transform: `translateY(-${notificationScrollIndex * 30}px)` }">
            <div class="notification-item" v-for="(notice, index) in notifications" :key="index" :class="{ 'active': notificationScrollIndex === index }">
              <div class="notification-text" ref="notificationText">{{ notice }}</div>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- 页面内容 -->
    <div class="page-content">
      <!-- 左侧船舶列表 -->
      <div class="ship-list-container">
        <div class="ship-list-header">
          <div class="title">
            <span>船舶列表</span>
            <!-- 自动滚动开关 -->
            <span class="auto-scroll-switch">
              <Checkbox v-model="autoScroll" @on-change="handleAutoScrollChange">自动滚动</Checkbox>
            </span>
          </div>
          <div class="filters">
            <Tag color="green" v-if="filters.includes('航行中')">航行中</Tag>
            <Tag color="orange" v-if="filters.includes('靠泊中')">靠泊中</Tag>
            <Tag color="blue" v-if="filters.includes('锚泊中')">锚泊中</Tag>
          </div>
        </div>
        <div class="ship-list-scroll" ref="shipListScroll">
          <!-- 加载指示器 -->
          <div class="loading-overlay" v-if="isLoading && displayShips.length === 0">
            <div class="loading-spinner"></div>
            <div class="loading-text">加载船舶数据...</div>
          </div>
          <!-- 船舶列表 -->
          <transition-group 
            name="ship-item" 
            tag="div" 
            class="ship-list-content"
          >
            <div :class="['ship-item', { 
                  'active': currentShipIndex === index,
                  'exit-animation': isExiting,
                  'scan-effect': currentShipIndex === index
                }]" 
                v-for="(ship, index) in displayShips" 
                :key="ship.id + '-' + index"
                @click="selectShip(index)"
                :style="{ 
                  animationDelay: `${index * 100}ms`,
                  transitionDelay: isExiting ? `${(displayShips.length - index - 1) * 50}ms` : `${index * 100}ms`
                }">
              <div :class="['status-bar', getStatusClass(ship.status)]"></div>
              <div class="ship-info">
                <div class="ship-name">
                  {{ ship.ship_name }}({{ ship.ship_id }})
                </div>
                <div class="cargo-info">
                  <div class="cargo-label">货品：</div>
                  <div class="cargo-value">{{ ship.goods_names }}</div>
                </div>
                <div class="route-info">
                  <div class="route-label">{{ ship.load_port_names }} - {{ ship.unload_port_names }}</div>
                  <div class="cargo-amount">货量：{{ ship.goods_amounts }}吨</div>
                </div>
                <div class="company-info" v-if="ship.cargoResult.length > 0">
                  <div class="company-label">货主：</div>
                  <span v-for="(cargo, idx) in ship.cargoResult" :key="'cargo' + idx">
                    {{ cargo.cargo_company_name }}
                    <span v-if="idx > 0">,</span>
                  </span>
                  <!-- <div class="company-value">{{ ship.cargoResult[0].cargo_company_name }}</div> -->
                </div>
                <div class="voyage-info">
                  <div class="voyage-label">计划航次：</div>
                  <!-- <div class="voyage-detail">{{ ship.voyage }} {{ ship.route }}</div>
                  <div class="cargo-amount">货量：{{ ship.plannedAmount }}吨</div>
                  <div class="receiving-period">接载日期：{{ ship.receivingPeriod }}</div> -->
                </div>
              </div>
              <div :class="['status-tag', getStatusClass(ship.status)]">
                {{ ship.status }}
              </div>
              <!-- 48小时标签 -->
              <div class="tag-48h" v-if="index === 0">48h</div>
              <!-- 扫描线效果 -->
              <div class="scan-line" v-if="currentShipIndex === index"></div>
              <!-- 科技感选中指示器 -->
              <div class="tech-indicator" v-if="currentShipIndex === index">
                <div class="tech-indicator-line"></div>
                <div class="tech-indicator-dot"></div>
                <!-- 添加数据加载进度指示器 -->
                <div class="data-progress">
                  <div class="data-text">数据同步中...</div>
                  <div class="progress-bar">
                    <div class="progress-fill"></div>
                  </div>
                </div>
              </div>
            </div>
          </transition-group>
        </div>
      </div>

      <!-- 右侧地图和信息 -->
      <div class="map-info-container">
        <div class="map-container">
          <div class="map-placeholder">
            <!-- 实际项目中这里应该是地图组件 -->
            <img src="../assets/map-placeholder.png" alt="地图" v-if="false" />
            <div class="map-content">
              <div class="ship-location">
                当前位置：兴通759
              </div>
            </div>
          </div>
        </div>
        <div class="port-info">
          <div class="port-name">
            <Icon type="md-boat" />
            港口信息(嘉兴港)
          </div>
          
          <!-- 天气信息 -->
          <div class="weather-info">
            <div class="weather-day">
              <div class="date">今日</div>
              <div class="temp">23°C</div>
              <div class="weather">晴</div>
              <div class="wind">西南风 3级</div>
              <div class="pressure">气压1011百帕</div>
            </div>
            <div class="weather-day">
              <div class="date">04.05</div>
              <div class="temp">25°C</div>
              <div class="weather">阴转晴</div>
              <div class="wind">西南风 4级</div>
              <div class="pressure">气压988百帕</div>
            </div>
            <div class="weather-day">
              <div class="date">04.06</div>
              <div class="temp">24°C</div>
              <div class="weather">小雨</div>
              <div class="wind">西南风 4级</div>
              <div class="pressure">气压988百帕</div>
            </div>
          </div>
          
          <!-- 潮汐图表 -->
          <div class="tide-chart">
            <div class="chart-title">潮汐(cm)</div>
            <div class="tide-chart-container" ref="tideChart"></div>
          </div>
          
          <!-- 作业状态 -->
          <div class="port-operations-section">
            <div class="operations-row">
              <div class="operations-item">
                <div class="operations-label">作业：</div>
                <div class="operations-value">4艘</div>
              </div>
              <div class="operations-item">
                <div class="operations-label">等泊：</div>
                <div class="operations-value">15艘</div>
              </div>
              <div class="operations-item">
                <div class="operations-label">预到港：</div>
                <div class="operations-value">20艘</div>
              </div>
            </div>
          </div>
          
          <!-- 港口预警信息 -->
          <div class="port-warning">
            <Icon type="md-warning" color="#f30" />
            <div class="port-warning-text">
              受东北低压和高压迁都南压南气流影响，4日至5日，渤海、黄海大部海域有南风6-7级，阵风8级转西北风6-7级。
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部状态信息 -->
    <div class="bottom-status">
      <div class="operation-status">
        <div class="status-title">作业状态</div>
        <div class="status-items">
          <div class="status-item">
            <div class="status-label">作业中：</div>
            <div class="status-value">4艘</div>
          </div>
          <div class="status-item">
            <div class="status-label">待泊：</div>
            <div class="status-value">15艘</div>
          </div>
        </div>
      </div>
      <div class="port-forecast">
        <div class="forecast-title">港口预报</div>
        <div class="forecast-content">
          <Icon type="md-alert" color="#f90" />
          <span>未来24小时在和嘉兴港气象海域城市将出现大风降温，阵风可达7-8级</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts';
import API from '@/api/control'

export default {
  name: 'ShipSchedulePage',
  data() {
    return {
      worker: null,
      today: '2025年4月3日星期四',
      currentTime: '10:07:25',
      monthlyPlan: '76,231',
      monthlyRevenue: '4,277',
      monthlyProfit: '1,032',
      notifications: [
        '警告:港口已超承载不明客号，请过往船舶避让',
        '通知:4号码头施工,禁止停靠',
        '警告:台风"玲玲"即将登陆,请船舶做好防护措施'
      ],
      notificationScrollIndex: 0,
      notificationTextScrollPosition: 0,
      filters: ['航行中', '靠泊中', '锚泊中'],
      ships: [],
      displayShips: [],
      currentShipIndex: 0,
      autoScroll: true,
      autoScrollInterval: null,
      notificationInterval: null,
      textScrollInterval: null,
      portInfo: {
        today: {
          temp: '23°C',
          weather: '晴',
          wind: '西南风 3级',
          pressure: '1011'
        },
        tomorrow: {
          temp: '25°C',
          weather: '阴转晴',
          wind: '西南风 4级',
          pressure: '988'
        },
        afterTomorrow: {
          temp: '24°C',
          weather: '小雨',
          wind: '西南风 4级',
          pressure: '988'
        }
      },
      tideChart: null,
      resizeObserver: null,
      isExiting: false,
      isLoading: false,
    };
  },
  created() {
    this.fetchShipData();
    // 更新当前时间
    setInterval(this.updateTime, 1000);
  },
  mounted() {
    this.worker = new Worker('dataWorker.js')
    this.worker.onmessage = (e) => {
      console.log(e)
    };
    // 初始化潮汐图表
    this.initTideChart();
    
    // 设置通知消息滚动
    this.notificationInterval = setInterval(() => {
      this.notificationScrollIndex = (this.notificationScrollIndex + 1) % this.notifications.length;
    }, 5000);
    
    // 检查通知文本是否需要横向滚动
    this.$nextTick(() => {
      this.checkNotificationTextOverflow();
    });
    
    // 设置船舶列表自动滚动
    if (this.autoScroll) {
      this.startAutoScroll();
    }
    
    // 添加页面可见性变化监听器
    document.addEventListener('visibilitychange', this.handleVisibilityChange);
    
    // 使用ResizeObserver监听容器大小变化
    if (window.ResizeObserver && this.$refs.tideChart) {
      this.resizeObserver = new ResizeObserver(entries => {
        if (this.tideChart) {
          this.tideChart.resize();
        }
      });
      this.resizeObserver.observe(this.$refs.tideChart);
    }
  },
  updated() {
    // 确保在DOM更新后重新调整图表大小
    this.$nextTick(() => {
      if (this.tideChart) {
        this.tideChart.resize();
      }
    });
  },
  beforeDestroy() {
    // 清除所有定时器
    clearInterval(this.autoScrollInterval);
    clearInterval(this.notificationInterval);
    clearInterval(this.textScrollInterval);
    
    // 销毁图表实例
    if (this.tideChart) {
      this.tideChart.dispose();
    }
    
    // 移除页面可见性变化监听器
    document.removeEventListener('visibilitychange', this.handleVisibilityChange);
    
    // 取消ResizeObserver监听
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
  },
  methods: {
    updateTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const dayOfWeek = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'][now.getDay()];
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');
      this.today = `${year}年${month}月${day}日 ${dayOfWeek}`;
      this.currentTime = `${hours}:${minutes}:${seconds}`;
    },
    handleVisibilityChange() {
      if (document.visibilityState === 'visible') {
        // 页面变为可见时，重新渲染图表
        this.$nextTick(() => {
          if (this.tideChart) {
            this.tideChart.resize();
          }
        });
      }
    },
    async fetchShipData() {
      // 模拟从API获取数据
      const mockShips = [
        {
          id: 'V2508',
          name: '兴通759',
          status: '抛锚中',
          cargo: {
            name: '对二甲苯',
            code: 'PX',
            amount: '19000'
          },
          voyage: 'V2509',
          plannedAmount: '18888',
          receivingPeriod: '04.05 - 04.08',
          route: '舟山 - 嘉兴',
          company: '中海油华东销售有限公司华东化工销售分公司'
        },
        {
          id: 'V2508',
          name: '兴通759',
          status: '航行中',
          cargo: {
            name: '对二甲苯',
            code: 'PX',
            amount: '19000'
          },
          voyage: 'V2509',
          plannedAmount: '18888',
          receivingPeriod: '04.05 - 04.08',
          route: '舟山 - 嘉兴',
          company: '中海油华东销售有限公司华东化工销售分公司'
        },
        {
          id: 'V2508',
          name: '兴通759',
          status: '靠泊中',
          cargo: {
            name: '对二甲苯',
            code: 'PX',
            amount: '19000'
          },
          voyage: 'V2509',
          plannedAmount: '18888',
          receivingPeriod: '04.05 - 04.08',
          route: '舟山 - 嘉兴',
          company: '中海油华东销售有限公司华东化工销售分公司'
        },
        {
          id: 'V2508',
          name: '兴通759',
          status: '航行中',
          cargo: {
            name: '对二甲苯',
            code: 'PX',
            amount: '19000'
          },
          voyage: 'V2509',
          plannedAmount: '18888',
          receivingPeriod: '04.05 - 04.08',
          route: '舟山 - 嘉兴',
          company: '中海油华东销售有限公司华东化工销售分公司'
        },
        {
          id: 'V2508',
          name: '兴通759',
          status: '航行中',
          cargo: {
            name: '对二甲苯',
            code: 'PX',
            amount: '19000'
          },
          voyage: 'V2509',
          plannedAmount: '18888',
          receivingPeriod: '04.05 - 04.08',
          route: '舟山 - 嘉兴',
          company: '中海油华东销售有限公司华东化工销售分公司'
        }
      ];
      
      // 复制足够的船舶数据以达到20条
      let allShips = [];
      // while (allShips.length < 20) {
      //   allShips = [...allShips, ...mockShips];
      // }
      // allShips = allShips.slice(0, 20);
      await API.queryVoyageConsolePage({pageSize: 100, pageIndex: 1}).then(res => {
        if (res.data.Code === 10000) {
          allShips = res.data.Result
          API.queryVoyagePlanConsolePage({pageSize: 100, pageIndex: 1}).then(res => {
            if (res.data.Code === 10000) {

            }
          })
        }
      })
      
      // 先设置ships属性
      this.ships = allShips;
      
      // 确保加载状态显示一小段时间
      if (this.isLoading) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
      
      // 清空当前显示的船舶
      this.displayShips = [];
      
      // 确保DOM更新
      await this.$nextTick();
      
      // 逐一加载船舶，创建进场动画效果
      for (let i = 0; i < allShips.length; i++) {
        await new Promise(resolve => setTimeout(resolve, 50));
        this.displayShips.push(allShips[i]);
      }
      
      this.isLoading = false;
      // 重置选中索引，延迟一下再选中第一个
      this.currentShipIndex = -1;
      setTimeout(() => {
        this.currentShipIndex = 0;
      }, 200);
    },
    async refreshShipData() {
      if (this.isExiting || this.isLoading) return;
      
      // 标记正在退场并清除当前选中
      this.isExiting = true;
      this.currentShipIndex = -1;
      
      // 等待退场动画完成
      const exitDuration = this.displayShips.length * 50 + 500;
      await new Promise(resolve => setTimeout(resolve, exitDuration));
      
      // 清空当前船舶列表，等待全部退出
      this.displayShips = [];
      
      // 确保DOM更新并等待一段时间，防止抖动
      await this.$nextTick();
      await new Promise(resolve => setTimeout(resolve, 300));
      
      // 设置加载状态
      this.isLoading = true;
      await new Promise(resolve => setTimeout(resolve, 300));
      
      // 重新获取船舶数据
      this.isExiting = false;
      await this.fetchShipData();
    },
    selectShip(index) {
      this.currentShipIndex = index;
      // 处理船舶选中逻辑
    },
    startAutoScroll() {
      this.autoScrollInterval = setInterval(() => {
        // 如果正在退场或加载，暂停自动滚动
        if (this.isExiting || this.isLoading) return;
        
        // 计算下一个船舶索引
        const nextIndex = (this.currentShipIndex + 1) % this.displayShips.length;
        
        // 立即将当前选中状态设为新索引，确保前一个船舶的效果立即消失
        this.currentShipIndex = nextIndex;
        
        // 如果已经滚动到最后一个船舶，触发刷新
        if (nextIndex === this.displayShips.length - 1) {
          setTimeout(() => {
            // 触发数据刷新
            this.triggerDataRefresh();
          }, 3000); // 最后一个船舶多停留3秒后刷新
        }
        
        // 保持当前选中的船舶在可视区域
        this.$nextTick(() => {
          const shipItems = this.$refs.shipListScroll.querySelectorAll('.ship-item');
          if (shipItems && shipItems[this.currentShipIndex]) {
            shipItems[this.currentShipIndex].scrollIntoView({ behavior: 'smooth', block: 'nearest' });
          }
        });
      }, 3000);
    },
    stopAutoScroll() {
      clearInterval(this.autoScrollInterval);
    },
    handleAutoScrollChange(value) {
      if (value) {
        this.startAutoScroll();
      } else {
        this.stopAutoScroll();
      }
    },
    checkNotificationTextOverflow() {
      if (this.$refs.notificationText && this.$refs.notificationText[this.notificationScrollIndex]) {
        const textElement = this.$refs.notificationText[this.notificationScrollIndex];
        const containerElement = this.$refs.notificationContent;
        
        if (textElement.scrollWidth > containerElement.clientWidth) {
          // 如果文本溢出，启动文本滚动
          this.startTextScroll(textElement, containerElement);
        } else {
          // 如果文本没有溢出，停止文本滚动
          this.stopTextScroll();
        }
      }
    },
    startTextScroll(textElement, containerElement) {
      // 清除之前的滚动定时器
      this.stopTextScroll();
      
      const textWidth = textElement.scrollWidth;
      const containerWidth = containerElement.clientWidth;
      let position = 0;
      
      this.textScrollInterval = setInterval(() => {
        position = (position + 1) % (textWidth + containerWidth);
        textElement.style.transform = `translateX(-${position}px)`;
        
        // 当文本滚动完一轮，暂停一段时间再继续
        if (position === 0) {
          clearInterval(this.textScrollInterval);
          setTimeout(() => {
            this.startTextScroll(textElement, containerElement);
          }, 2000);
        }
      }, 30);
    },
    stopTextScroll() {
      clearInterval(this.textScrollInterval);
      if (this.$refs.notificationText) {
        this.$refs.notificationText.forEach(el => {
          el.style.transform = 'translateX(0)';
        });
      }
    },
    initTideChart() {
      // 延迟初始化，确保DOM已经完全渲染
      this.$nextTick(() => {
        if (!this.$refs.tideChart) {
          console.error('无法找到潮汐图表容器');
          return;
        }
        
        // 如果已经初始化过，先销毁
        if (this.tideChart) {
          this.tideChart.dispose();
        }
        
        this.tideChart = echarts.init(this.$refs.tideChart);
        
        const option = {
          backgroundColor: 'transparent',
          grid: {
            top: 30,
            bottom: 20,
            left: 45,
            right: 20
          },
          xAxis: {
            type: 'category',
            data: [0, 5, 10, 15, 20],
            axisLine: {
              lineStyle: {
                color: '#456'
              }
            },
            axisLabel: {
              color: '#bbb',
              fontSize: 12
            }
          },
          yAxis: {
            type: 'value',
            min: 0,
            max: 300,
            interval: 50,
            axisLine: {
              lineStyle: {
                color: '#456'
              }
            },
            axisLabel: {
              color: '#bbb',
              fontSize: 12
            },
            splitLine: {
              lineStyle: {
                color: 'rgba(70, 100, 140, 0.3)'
              }
            }
          },
          series: [{
            data: [150, 250, 50, 150, 250],
            type: 'line',
            smooth: true,
            symbol: 'none',
            lineStyle: {
              color: '#39f',
              width: 3
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0,
                  color: 'rgba(51, 153, 255, 0.5)'
                }, {
                  offset: 1,
                  color: 'rgba(51, 153, 255, 0.1)'
                }]
              }
            },
            markPoint: {
              symbolSize: 40,
              data: [
                { coord: [1, 250], name: '高潮', itemStyle: { color: '#39f' } },
                { coord: [2, 50], name: '低潮', itemStyle: { color: '#f36' } }
              ],
              label: {
                formatter: '{b}',
                position: 'top',
                fontSize: 12,
                color: 'white'
              }
            },
            markLine: {
              symbol: 'none',
              lineStyle: {
                color: '#fff',
                type: 'dashed'
              },
              data: [
                { xAxis: 10, label: { formatter: '当前', position: 'middle' } }
              ]
            }
          }]
        };
        
        // 设置图表选项
        this.tideChart.setOption(option);
        
        // 添加窗口大小变化监听，实现自适应
        const resizeHandler = () => {
          if (this.tideChart) {
            this.tideChart.resize();
          }
        };
        
        window.addEventListener('resize', resizeHandler);
        
        // 组件销毁时移除监听器
        this.$once('hook:beforeDestroy', () => {
          window.removeEventListener('resize', resizeHandler);
        });
      });
    },
    getStatusClass(status) {
      switch(status) {
        case '航行中':
          return 'sailing';
        case '靠泊中':
          return 'docked';
        case '锚泊中':
          return 'anchored';
        case '抛锚中':
          return 'anchoring';
        default:
          return '';
      }
    },
    // 新增方法：触发数据刷新
    triggerDataRefresh() {
      if (this.isExiting || this.isLoading) return;
      
      // 显示全局刷新指示器
      this.refreshShipData();
      
      // 播放刷新音效（如果需要）
      this.playRefreshSound();
    },
    
    // 播放刷新音效
    playRefreshSound() {
      // 这里可以实现播放音效的逻辑
      // 例如：
      /*
      const audio = new Audio('/assets/refresh.mp3');
      audio.volume = 0.5;
      audio.play().catch(e => console.log('无法播放音效', e));
      */
    }
  },
  watch: {
    notificationScrollIndex() {
      // 当通知切换时，检查新的通知是否需要滚动
      this.$nextTick(() => {
        this.checkNotificationTextOverflow();
      });
    }
  }
};
</script>

<style scoped>
.ship-schedule-page {
  position: relative;
  /* background: #000; */
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  color: #fff;
}

/* 页面头部样式 */
.page-header {
  display: flex;
  background: rgba(0, 30, 60, 0.7);
  padding: 15px 20px;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.date {
  font-size: 16px;
  color: #ddd;
}

.time {
  font-size: 24px;
  font-weight: bold;
  margin-top: 5px;
}

.stats {
  display: flex;
  gap: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  background-color: rgba(0, 30, 60, 0.5);
  padding: 10px 15px;
  border-radius: 4px;
  min-width: 150px;
}

.stat-content {
  margin-left: 10px;
}

.stat-title {
  font-size: 14px;
  color: #bbb;
}

.stat-value {
  font-size: 16px;
  font-weight: bold;
  margin-top: 2px;
}

.notification {
  display: flex;
  align-items: center;
  background-color: rgba(255, 50, 0, 0.1);
  padding: 8px 15px;
  border-radius: 4px;
  border: 1px solid rgba(255, 50, 0, 0.3);
  min-width: 300px;
  overflow: hidden;
}

.notification-content {
  margin-left: 10px;
  overflow: hidden;
  height: 30px;
  position: relative;
}

.notification-scroller {
  transition: transform 0.5s ease;
}

.notification-item {
  height: 30px;
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.notification-text {
  color: #f50;
  font-weight: bold;
  transition: transform 0.5s linear;
}

/* 页面内容样式 - 修改左右比例 */
.page-content {
  display: flex;
  flex: 1;
  gap: 20px;
  overflow: hidden;
}

/* 船舶列表样式 */
.ship-list-container {
  width: 58%; /* 调整为与截图一致的比例 */
  background-color: rgba(0, 30, 60, 0.7);
  border-radius: 6px;
  display: flex;
  flex-direction: column;
}

.ship-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.ship-list-header .title {
  font-size: 16px;
  font-weight: bold;
}

.ship-list-scroll {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.ship-list-scroll::-webkit-scrollbar {
  width: 6px;
}

.ship-list-scroll::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.ship-item {
  position: relative;
  background-color: rgba(0, 20, 40, 0.5);
  border-radius: 4px;
  margin-bottom: 10px;
  padding: 15px;
  padding-left: 5px;
  display: flex;
  cursor: pointer;
  transition: all 0.3s;
  overflow: hidden;
  animation: slide-in-right 0.5s ease-out forwards;
  animation-fill-mode: both;
}

.ship-item:hover {
  background-color: rgba(0, 30, 60, 0.7);
}

/* 增强选中状态的科技感 */
.ship-item.active {
  background-color: rgba(0, 60, 120, 0.7);
  box-shadow: 0 0 15px rgba(0, 150, 255, 0.3);
  border-left: 2px solid #09f;
}

.ship-item.active::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    rgba(0, 120, 255, 0.05), 
    rgba(0, 120, 255, 0) 20%,
    rgba(0, 120, 255, 0) 80%,
    rgba(0, 120, 255, 0.05)
  );
  pointer-events: none;
}

/* 科技感指示器 */
.tech-indicator {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.tech-indicator-line {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, 
    rgba(0, 150, 255, 0),
    rgba(0, 150, 255, 1) 50%, 
    rgba(0, 150, 255, 0)
  );
  animation: tech-line 3s infinite;
}

.tech-indicator-dot {
  position: absolute;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #0af;
  box-shadow: 0 0 8px #0af;
  bottom: -3px;
  left: 0;
  animation: tech-dot 3s infinite;
}

@keyframes tech-line {
  0% { opacity: 0.3; }
  50% { opacity: 1; }
  100% { opacity: 0.3; }
}

@keyframes tech-dot {
  0% { left: -10px; }
  100% { left: calc(100% + 10px); }
}

.status-bar {
  width: 4px;
  height: auto;
  margin-right: 10px;
  border-radius: 2px;
}

.status-bar.sailing {
  background-color: #19be6b;
}

.status-bar.docked {
  background-color: #ff9900;
}

.status-bar.anchored {
  background-color: #2d8cf0;
}

.status-bar.anchoring {
  background-color: #ff9900;
}

.ship-info {
  flex: 1;
  padding-right: 70px; /* 为状态标签留出空间 */
}

.ship-name {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 5px;
}

.cargo-info, .route-info, .voyage-info {
  display: flex;
  flex-wrap: wrap;
  font-size: 14px;
  color: #bbb;
  margin-top: 5px;
  line-height: 1.5;
}

.cargo-label, .route-label, .voyage-label {
  color: #999;
  margin-right: 5px;
}

.cargo-amount, .receiving-period {
  margin-left: 10px;
}

.status-tag {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 4px 15px;
  border-radius: 2px;
  font-size: 14px;
  font-weight: bold;
}

.status-tag.sailing {
  background-color: rgba(25, 190, 107, 0.2);
  color: #19be6b;
}

.status-tag.docked {
  background-color: rgba(255, 153, 0, 0.7);
  color: #ff9900;
}

.status-tag.anchored {
  background-color: rgba(45, 140, 240, 0.2);
  color: #2d8cf0;
}

.status-tag.anchoring {
  background-color: rgba(255, 153, 0, 0.3);
  color: #ff9900;
}

/* 为抛锚中状态添加特殊样式 */
.ship-item .status-bar[class*='anchored'] {
  background-color: #ff9900;
}

.ship-item .status-tag:contains('抛锚中') {
  background-color: rgba(255, 153, 0, 0.3);
  color: #ff9900;
}

/* 右上角标签样式 */
.ship-item .tag-48h {
  position: absolute;
  top: 0;
  right: 0;
  background-color: #f30;
  color: white;
  font-size: 10px;
  padding: 2px 5px;
  border-top-right-radius: 4px;
}

/* 调整天气信息样式 */
.weather-day {
  text-align: center;
  flex: 1;
  background-color: rgba(0, 20, 40, 0.5);
  padding: 8px 5px;
  border-radius: 4px;
  margin: 0 5px;
}

.temp {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 5px;
  color: #fff;
}

.port-operations {
  margin-top: 15px;
}

/* 地图和信息样式 */
.map-info-container {
  width: 42%; /* 调整为与截图一致的比例 */
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.map-container {
  height: 280px; /* 调整高度与截图一致 */
  min-height: 280px;
  background-color: rgba(0, 30, 60, 0.7);
  border-radius: 6px;
  overflow: hidden;
  position: relative;
}

.map-placeholder {
  width: 100%;
  height: 100%;
  background-image: url('/src/assets/map-placeholder.jpg');
  background-size: cover;
  background-position: center;
}

.map-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ship-location {
  background-color: rgba(0, 0, 0, 0.5);
  padding: 5px 10px;
  border-radius: 4px;
}

/* 优化港口信息显示 */
.port-info {
  flex: 1; /* 改为弹性高度占用剩余空间 */
  min-height: 280px; /* 调整与截图一致 */
  background-color: rgba(0, 30, 60, 0.7);
  border-radius: 6px;
  padding: 10px;
  display: flex;
  flex-direction: column;
}

.port-name {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
}

.port-name i {
  margin-right: 5px;
}

/* 天气信息样式 */
.weather-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.weather-day {
  text-align: center;
  flex: 1;
  background-color: rgba(0, 20, 40, 0.5);
  padding: 8px 5px;
  border-radius: 4px;
  margin: 0 3px;
}

.date {
  margin-bottom: 3px;
  font-size: 12px;
}

.temp {
  font-size: 23px;
  font-weight: bold;
  margin-bottom: 5px;
  color: #fff;
}

.weather, .wind, .pressure {
  font-size: 11px;
  color: #bbb;
  margin-bottom: 2px;
}

/* 潮汐图表样式 */
.tide-chart {
  height: 220px;
  position: relative;
  margin-bottom: 15px;
  background-color: rgba(0, 20, 40, 0.5);
  border-radius: 4px;
  padding: 20px 10px 10px 10px;
}

.chart-title {
  font-size: 12px;
  font-weight: bold;
  color: #ddd;
  position: absolute;
  top: 8px;
  left: 10px;
  z-index: 1;
}

.tide-chart-container {
  width: 100%;
  height: 100%;
}

/* 作业状态样式 */
.port-operations-section {
  margin-bottom: 10px;
}

.operations-row {
  display: flex;
}

.operations-item {
  display: flex;
  align-items: center;
  margin-right: 15px;
  font-size: 12px;
}

.operations-label {
  color: #999;
}

.operations-value {
  font-weight: bold;
  margin-left: 5px;
}

/* 港口预警信息 */
.port-warning {
  display: flex;
  align-items: flex-start;
  font-size: 11px;
  color: #f50;
  background-color: rgba(255, 80, 0, 0.1);
  padding: 8px 10px;
  border-radius: 4px;
  line-height: 1.3;
}

.port-warning i {
  margin-right: 5px;
  flex-shrink: 0;
  margin-top: 2px;
}

.port-warning-text {
  flex: 1;
}

/* 底部状态信息 */
.bottom-status {
  display: flex;
  margin-top: 0;
  gap: 0;
  position: absolute;
  bottom: 40px;
  left: 20px;
  right: 20px;
  display: none; /* 根据截图隐藏底部状态栏，因为截图中没有显示这部分 */
}

.operation-status, .port-forecast {
  flex: 1;
  background-color: rgba(0, 30, 60, 0.3);
  border-radius: 6px;
  padding: 10px 15px;
}

.status-title, .forecast-title {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 5px;
  color: #ddd;
}

.status-items {
  display: flex;
}

.status-item {
  display: flex;
  align-items: center;
  margin-right: 20px;
}

.status-label {
  color: #aaa;
}

.status-value {
  color: #fff;
  font-weight: bold;
  margin-left: 5px;
}

.forecast-content {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: #f90;
}

.forecast-content i {
  margin-right: 10px;
}

/* 自动滚动开关 */
.auto-scroll-switch {
  margin-left: 5px;
  background-color: transparent; /* 移除背景，与截图一致 */
  padding: 5px 10px;
  border-radius: 4px;
  z-index: 10;
}

@media (max-width: 1366px) {
  /* 适配小屏幕 */
  .port-info-content {
    flex-direction: column;
  }
  
  .left-content, .right-content {
    width: 100%;
  }
  
  .right-content {
    min-height: 180px;
  }
  
  .tide-chart {
    min-height: 150px;
  }
}

/* 调整船舶信息中的公司名称样式 */
.company-info {
  display: flex;
  flex-wrap: wrap;
  font-size: 14px;
  color: #bbb;
  margin-top: 5px;
  line-height: 1.5;
}

.company-label {
  color: #999;
  margin-right: 5px;
}

.company-value {
  color: #bbb;
}

/* 状态样式 */
.status-bar.sailing {
  background-color: #19be6b;
}

.status-bar.docked {
  background-color: #ff9900;
}

.status-bar.anchored {
  background-color: #2d8cf0;
}

.status-bar.anchoring {
  background-color: #ff9900;
}

.status-tag.sailing {
  background-color: rgba(25, 190, 107, 0.2);
  color: #19be6b;
}

.status-tag.docked {
  background-color: rgba(255, 153, 0, 0.3);
  color: #ff9900;
}

.status-tag.anchored {
  background-color: rgba(45, 140, 240, 0.2);
  color: #2d8cf0;
}

.status-tag.anchoring {
  background-color: rgba(255, 153, 0, 0.3);
  color: #ff9900;
}

/* 48h标签样式 */
.tag-48h {
  position: absolute;
  top: 0;
  right: 0;
  background-color: #f30;
  color: white;
  font-size: 10px;
  padding: 2px 5px;
  border-top-right-radius: 4px;
}

/* 船舶列表内容容器 */
.ship-list-content {
  position: relative;
  min-height: 100%;
}

/* 船舶项进场动画 */
.ship-item-enter-active {
  animation: slide-in-right 0.5s ease-out forwards;
}

.ship-item-leave-active {
  animation: slide-out-left 0.3s ease-in forwards;
  position: absolute;
  width: calc(100% - 20px);
}

/* 退场动画类 */
.ship-item.exit-animation {
  animation: slide-out-left 0.5s ease-in forwards;
  opacity: 0;
  transform: translateX(-50px);
}

@keyframes slide-in-right {
  0% {
    transform: translateX(50px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-out-left {
  0% {
    transform: translateX(0);
    opacity: 1;
  }
  100% {
    transform: translateX(-50px);
    opacity: 0;
  }
}

/* 加载状态样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 20, 40, 0.7);
  z-index: 10;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(51, 153, 255, 0.3);
  border-radius: 50%;
  border-top-color: #39f;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 10px;
}

.loading-text {
  color: #39f;
  font-size: 14px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 科技感扫描效果 */
.ship-item.scan-effect::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to right,
    rgba(0, 150, 255, 0) 0%,
    rgba(0, 150, 255, 0.1) 45%,
    rgba(0, 150, 255, 0.3) 50%,
    rgba(0, 150, 255, 0.1) 55%,
    rgba(0, 150, 255, 0) 100%
  );
  pointer-events: none;
  animation: scan-effect 2s linear infinite;
  opacity: 0.7;
  z-index: 1;
}

.scan-line {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 1px;
  background-color: #39f;
  box-shadow: 0 0 8px 1px #39f;
  animation: scan-line 2s linear infinite;
  z-index: 2;
}

/* 数据进度指示器 */
.data-progress {
  position: absolute;
  bottom: 5px;
  right: 5px;
  width: 120px;
  height: 24px;
  background-color: rgba(0, 20, 40, 0.7);
  border-radius: 3px;
  border: 1px solid rgba(51, 153, 255, 0.5);
  overflow: hidden;
  z-index: 3;
}

.data-text {
  position: absolute;
  top: 2px;
  left: 8px;
  font-size: 10px;
  color: #39f;
  z-index: 2;
}

.progress-bar {
  position: absolute;
  bottom: 3px;
  left: 6px;
  right: 6px;
  height: 3px;
  background-color: rgba(51, 153, 255, 0.2);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  width: 0%;
  background-color: #39f;
  animation: progress-animation 3s infinite;
}

/* 动画定义 */
@keyframes scan-effect {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes scan-line {
  0% { left: -5px; }
  100% { left: 100%; }
}

@keyframes progress-animation {
  0% { width: 0%; }
  50% { width: 70%; }
  80% { width: 90%; }
  100% { width: 100%; }
}

/* 全局数据刷新指示器 */
.global-refresh-indicator {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: rgba(0, 30, 60, 0.8);
  border-radius: 4px;
  padding: 10px 15px;
  display: flex;
  align-items: center;
  box-shadow: 0 0 15px rgba(0, 150, 255, 0.5);
  border: 1px solid rgba(0, 150, 255, 0.3);
  z-index: 100;
  animation: pulse 2s infinite;
}

.refresh-circle {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(51, 153, 255, 0.5);
  border-top-color: #39f;
  border-radius: 50%;
  margin-right: 10px;
  animation: spin 1s linear infinite;
}

.refresh-text {
  color: #39f;
  font-size: 14px;
  font-weight: bold;
}

@keyframes pulse {
  0% { box-shadow: 0 0 10px rgba(0, 150, 255, 0.5); }
  50% { box-shadow: 0 0 20px rgba(0, 150, 255, 0.8); }
  100% { box-shadow: 0 0 10px rgba(0, 150, 255, 0.5); }
}
</style> 