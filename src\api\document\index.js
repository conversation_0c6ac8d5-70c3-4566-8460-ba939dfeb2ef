import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 查询航次信息
export function queryMainVoyagePage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/record/picture/queryMainVoyagePage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询航次图片
export function queryVoyagePicturePage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/record/picture/queryVoyagePicturePage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 修改图片名称
export function updateVoyagePicture (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/record/picture/updateVoyagePicture',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  queryMainVoyagePage,
  queryVoyagePicturePage,
  updateVoyagePicture
}
