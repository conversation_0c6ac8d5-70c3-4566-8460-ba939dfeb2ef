<template>
  <div class="node-list-con">
    <!-- *节点时间、*节点名称、*港口、*码头、预计时间、预计节点、备注 -->
    <div v-if="nodeObj.node_type === '1'">
      <span><span style="color: #007DFF;">{{ nodeObj.node_name }},</span>前往{{ nodeObj.port_name }}港{{ nodeObj.wharf_name }}
            码头,预计{{ nodeObj.expect_date }}{{ nodeObj.expect_node_name }}</span>
      <span>{{ nodeObj.remark === '' ? '' : ',' +  nodeObj.remark }}</span>
    </div>
    <!-- *节点时间、*节点名称、备注 -->
    <div v-if="nodeObj.node_type === '2'">
      <span style="color: #007DFF;">{{ nodeObj.node_name }},</span>
      <span>{{ nodeObj.remark === '' ? '' : ',' +  nodeObj.remark }}</span>
    </div>
    <!-- *节点时间、*节点名称、*港口、码头、泊位、备注 -->
    <div v-if="nodeObj.node_type === '3'">
      <span><span style="color: #007DFF;">{{ nodeObj.node_name }},</span>到达{{ nodeObj.port_name }}</span>
      <!-- 有靠泊计划 -->
      <span v-if="nodeObj.expect_date !== ''">计划{{ nodeObj.expect_date }}靠泊{{ nodeObj.wharf_name }}码头{{ nodeObj.berth_name }}泊位</span>
      <!-- 无靠泊计划 -->
      <span v-else>,等待靠泊计划</span>
      <span>{{ nodeObj.remark === '' ? '' : ',' +  nodeObj.remark }}</span>
    </div>
    <!-- *节点时间、*节点名称、锚地、预计时间、预计节点、备注 -->
    <div v-if="nodeObj.node_type === '4'">
      <span><span style="color: #007DFF;">{{ nodeObj.node_name }},</span>抛锚{{ nodeObj.port_name }}</span>
      <!-- 有靠泊计划 -->
      <span v-if="nodeObj.expect_date !== ''">计划{{ nodeObj.expect_date }}靠泊{{ nodeObj.wharf_name }}
        码头{{ nodeObj.berth_name }}泊位{{ nodeObj.anchorage === '' ? '' : nodeObj.anchorage + '锚地' }}</span>
      <!-- 无靠泊计划 -->
      <span v-else>,等待靠泊计划</span>
      <span>{{ nodeObj.remark === '' ? '' : ',' +  nodeObj.remark }}</span>
    </div>
    <!-- *节点名称、*节点时间、*港口、码头、泊位、备注 -->
    <div v-if="nodeObj.node_type === '5'">
      <span><span style="color: #007DFF;">{{ nodeObj.node_name }},</span>前往{{ nodeObj.port_name }}港口{{ nodeObj.wharf_name }}码头{{ nodeObj.berth_name }}泊位</span>
      <span>{{ nodeObj.remark === '' ? '' : ',' +  nodeObj.remark }}</span>
    </div>
    <!-- *节点时间、*节点名称、预计节点时间、预计节点名称、备注 -->
    <div v-if="nodeObj.node_type === '6'">
      <span><span style="color: #007DFF;">{{ nodeObj.node_name }},</span>抛锚{{ nodeObj.port_name }}</span>
      <!-- 有靠泊计划 -->
      <span v-if="nodeObj.expect_date !== ''">预计{{ nodeObj.expect_date }}{{ nodeObj.expect_node_name }}</span>
      <!-- 无靠泊计划 -->
      <span v-else>,等码头通知</span>
      <span>{{ nodeObj.remark === '' ? '' : ',' +  nodeObj.remark }}</span>
    </div>
    <!-- *节点时间、*节点名称、速率、预计节点时间、预计节点名称、备注 -->
    <div v-if="nodeObj.node_type === '7'">
      <span><span style="color: #007DFF;">{{ nodeObj.node_name }},</span>抛锚{{ nodeObj.port_name }}</span>
      <!-- 有速率 -->
      <span v-if="nodeObj.current_speed !== ''">速率{{ nodeObj.current_speed }}，预计{{ nodeObj.expect_date }}{{ nodeObj.expect_node_name }}</span>
      <!-- 无速率 -->
      <span v-else>,，速率待定</span>
      <span>{{ nodeObj.remark === '' ? '' : ',' +  nodeObj.remark }}</span>
    </div>
    <!-- *节点时间、*节点名称、备注 -->
    <div v-if="nodeObj.node_type === '8'">
      <span><span style="color: #007DFF;">{{ nodeObj.node_name }},</span>抛锚{{ nodeObj.port_name }}</span>
      <!-- 装港 -->
      <!-- <span v-if="nodeObj.port_mode === '1'">速率{{ nodeObj.current_speed }}，预计{{ nodeObj.expect_date }}{{ nodeObj.expect_node_name }}</span> -->
      <!-- 卸港 -->
      <!-- <span v-if="nodeObj.port_mode === '2'">,，速率待定</span> -->
      <span>{{ nodeObj.remark === '' ? '' : ',' +  nodeObj.remark }}</span>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    nodeObj: Object
  },
  data () {
    return {

    }
  }
}
</script>
<style lang="less">
  .node-list-con {
    font-size: 16px;
    color: #999;
  }
</style>
