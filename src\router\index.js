import Vue from 'vue'
import Router from 'vue-router'
import routes from './routers'
import store from '@/store'
import iView from 'iview'
import { canTurnTo, getToken, setTitle, setMenuShow } from '@/libs/util'
import { previewVoyageWord } from '@/api/voyageManage/curVoyage'
import config from '@/config'
const { homeName } = config

Vue.use(Router)
// 点击同一个路由报错问题  如果不采用此方法可以升级router至3.0 npm i vue-router@3.0 -S
const originalPush = Router.prototype.push
Router.prototype.push = function push (location) {
  return originalPush.call(this, location).catch(err => err)
}
const router = new Router({
  routes,
  mode: 'history',
  scrollBehavior (to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { x: 0, y: 0 }
    }
  }
})
const turnTo = (to, access, next) => {
  if (canTurnTo(to.name, access, routes)) next() // 有权限，可访问
  else next({ replace: true, name: 'error_401' }) // 无权限，重定向到401页面
}

const LOGIN_PAGE_NAME = 'login'
const MANAGELOGIN_PAGE_NAME = 'manageLogin'
// const SEARCH_DETAIL = 'searchDetail'
const UNLOGIN_PAGE_LIST = ['searchDetail', 'tce', 'sanction'] // 无需登录校验页面
const SHIP_MESS_DETAIL = 'shipMessDetail'

router.beforeEach((to, from, next) => {
  iView.LoadingBar.start()
  const token = getToken()
  if (to.name === SHIP_MESS_DETAIL) {
    let _that = this
    qing.call('getPersonInfo', { // 针对云之家小程序限定
      success: function (res) {
        localStorage.removeItem('outTime')
        if (res.data.openId && res.data.openId !== '') {
          localStorage.setItem('isYunZhiJia', true)
          store.commit('setMenuShow', false)
          setMenuShow(false)
          let seventyTwoList = ['63292e15e4b00a6b3607d63d'] // 志阳
          let fortyEightList = ['63292e15e4b00a6b3607d63e', '63565e26e4b0a9a155e6295f', '63565e26e4b0a9a155e62970'] // 陈总 柯总 史浩
          let thirdtySixList = [''] // 虹南 632cfe9be4b00130127ee36c
          let tweentyFourList = ['632cfe9be4b00130127ee36c', '63565e26e4b0a9a155e62977', '63565e26e4b0a9a155e6296e', '634cae03e4b0b6064a5d8e8c', '63565e26e4b0a9a155e6299d', '63565e26e4b0a9a155e629a0', '63565e26e4b0a9a155e62991', '63565e26e4b0a9a155e6299e', '63565e26e4b0a9a155e629a9', '63565e26e4b0a9a155e629a2', '63565e26e4b0a9a155e62964', '63565e26e4b0a9a155e6298d', '63511092e4b051cac3c10476', '63565e26e4b0a9a155e6296a', '63565e26e4b0a9a155e62960', '63565e26e4b0a9a155e6297d', '63565e26e4b0a9a155e62966', '649cf319e4b000e4d9e8e3b5']
          store.dispatch('phoneLogin', { 'userName': '15260223700',  'way': '1' }).then(result => {
            if (result.Code === 10000) {
              if (seventyTwoList.includes(res.data.openId)) {
                localStorage.setItem('outTime', 72)
              }
              if (fortyEightList.includes(res.data.openId)) {
                localStorage.setItem('outTime', 48)
              }
              if (thirdtySixList.includes(res.data.openId)) { // 虹南 36小时以上
                localStorage.setItem('outTime', 36)
              }
              if (tweentyFourList.includes(res.data.openId)) { // 其云 乐总 严船长 24小时以上
                localStorage.setItem('outTime', 24)
              }
              next({
                name: SHIP_MESS_DETAIL // 预警详情页
              })
            }
          }).catch(() => {
            next({
              name: LOGIN_PAGE_NAME // 跳转到登录页
            })
          })
          // }
        }
      },
      error: function (res) {
      }
    })
  }
  if (!token && to.name !== LOGIN_PAGE_NAME && to.name !== MANAGELOGIN_PAGE_NAME) {
    // 未登录且要跳转的页面不是登录页
    if (UNLOGIN_PAGE_LIST.includes(to.name)) { // 搜索详情页直接跳转
      turnTo(to, store.state.user.access, next)
    } else {
      next({
        name: LOGIN_PAGE_NAME // 跳转到登录页
      })
    }
  } else if (!token && (to.name === LOGIN_PAGE_NAME || to.name === MANAGELOGIN_PAGE_NAME)) {
    // 未登陆且要跳转的页面是登录页
    next() // 跳转
  } else if (token && (to.name === LOGIN_PAGE_NAME || to.name === MANAGELOGIN_PAGE_NAME)) {
    // 已登录且要跳转的页面是登录页
    if (to.query.router) {
      if (document.referrer.includes('ierptest.xtshipping.com') || document.referrer.includes('erp.xtshipping.com')) {
        store.commit('setMenuShow', false)
        setMenuShow(false)
      } else {
        store.commit('setMenuShow', true)
        setMenuShow(true)
      }
      qing.call('getPersonInfo', { // 针对云之家小程序限定
        success: function (res) {
          store.commit('setMenuShow', false)
          setMenuShow(false)
        },
        error: function (res) {

        }
      })
      setTimeout(() => {
        if (to.query.router.indexOf('/') > 0) {
          next({
            name: to.query.router.split('/')[0], //  如果有指向跳向指定页面
            params: {
              id: to.query.router.split('/')[1]
            }
          })
        } else {
          if (to.query.router === 'viewFile' && (document.referrer.includes('ierptest.xtshipping.com') || document.referrer.includes('erp.xtshipping.com'))) { // 航次命令特殊处理
            previewVoyageWord({ id: to.query.voyageId }).then(res => {
              if (res.data.Code === 10000) {
                sessionStorage.setItem('wpsUrl', res.data.wpsUrl)
                sessionStorage.setItem('token', res.data.token)
                next({
                  name: to.query.router // 如果有指向跳向指定页面
                })
              } else {
                this.$Message.error(res.data.Message)
                next({
                  name: homeName // 跳转到homeName页
                })
              }
            })
          } else {
            next({
              name: to.query.router // 如果有指向跳向指定页面
            })
          }
        }
      }, 100)
    } else {
      next({
        name: homeName // 跳转到homeName页
      })
    }
  } else {
    if (!document.referrer.includes('ierptest.xtshipping.com') && !document.referrer.includes('erp.xtshipping.com') && !localStorage.getItem('isYunZhiJia')) {
      store.commit('setMenuShow', true)
      setMenuShow(true)
    }
    if (store.state.user.hasGetInfo) {
      turnTo(to, store.state.user.access, next)
    } else {
      turnTo(to, store.state.user.access, next)
    }
  }
})

router.afterEach(to => {
  setTitle(to, router.app)
  iView.LoadingBar.finish()
  window.scrollTo(0, 0)
})

export default router
