import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 获取所有公司 下拉
export function dynamicList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/record/dynamic/queryDynamicPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 修改
export function dynamicUpdate (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/record/dynamic/updateVoyageDynamic',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 删除动态
export function dynamicDelete (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/voyage/record/dynamic/delVoyageDynamic',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询船东公司船舶商务配置 不分页
export function queryShipList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/auth/ship/config/querySpConfigList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}
