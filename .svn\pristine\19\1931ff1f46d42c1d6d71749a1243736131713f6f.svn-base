export default [
  {
    mainNode: [ // 关键节点
      {
        label: '到港',
        value: '3'
      },
      {
        label: '船舶就绪通知书递交',
        value: '4'
      },
      {
        label: '抛锚',
        value: '7'
      },
      {
        label: '起锚',
        value: '9'
      },
      {
        label: '第一条缆绳上岸',
        value: '10'
      },
      {
        label: '船舶系泊',
        value: '11'
      },
      {
        label: '商检上船',
        value: '13'
      },
      {
        label: '输油管接妥',
        value: '17'
      },
      {
        label: '开始装/卸货',
        value: '18'
      },
      {
        label: '结束装/卸货',
        value: '21'
      },
      {
        label: '输油管拆除',
        value: '24'
      },
      {
        label: '开始解缆',
        value: '27'
      },
      {
        label: '离清码头',
        value: '28'
      },
      {
        label: '离港',
        value: '30'
      }
      // {
      //   label: '预计到达目的港',
      //   value: '31'
      // }
    ],
    otherNode: [ // 非关键节点
      {
        label: '起航',
        value: '1'
      },
      {
        label: '航行中',
        value: '2'
      },
      {
        label: '船舶就绪通知书接受',
        value: '5'
      },
      {
        label: '引水员登轮',
        value: '6'
      },
      {
        label: '引水员离船',
        value: '8'
      },
      {
        label: '舷梯放置',
        value: '12'
      },
      {
        label: '安全联检结束',
        value: '14'
      },
      {
        label: '验舱/计量/取样开始',
        value: '15'
      },
      {
        label: '验舱/计量/取样结束',
        value: '16'
      },
      {
        label: '暂停装/卸货',
        value: '19'
      },
      {
        label: '恢复装/卸货',
        value: '20'
      },
      {
        label: '验舱/计量/取样开始',
        value: '22'
      },
      {
        label: '验舱/计量/取样结束',
        value: '23'
      },
      {
        label: '文件上船',
        value: '25'
      },
      {
        label: '引水员登轮',
        value: '26'
      },
      {
        label: '引水员离船',
        value: '29'
      }
    ]
  }
]
