<template>
  <div>
    <div class="time_end" v-if="isAis">
      <p><span style="font-weight: bold;">倒计时:</span><span style="color: red;"> {{ minutes }} 分钟 {{ seconds }} 秒</span> 后自动<a @click="refreshList">刷新</a></p>
    </div>
    <Row class="datalisthead">
      <Col span="3"  style="text-align: center;">船名</Col>
      <Col span="5" style="text-align: center;">目的港</Col>
      <Col span="6" style="text-align: center;">到港时间</Col>
      <Col span="4" style="text-align: center;">
        状态
        <Icon style="cursor: pointer;" :type="isUp ? 'md-arrow-dropup' : 'md-arrow-dropdown'" @click="statusChange"></Icon>
      </Col>
      <Col span="3" style="text-align: center;">预警(h)</Col>
      <Col span="3" style="text-align: center;">操作员</Col>
    </Row>
    <!-- 自营开始 -->
    <H3 style="margin-top: 15px;">自营船舶</H3>
    <div v-for="(item, index) in selfModelList" :key="'self' + index" class="voyage-list" @click="shipClick(item)">
      <Row class="voyage-area-info" justify="center" align="middle" :style="getDelayStyle(item)">
        <Col span="3" style="text-align: center;">
          <div>{{ item.ship_name }}</div>
        </Col>
        <Col span="5" style="text-align: center;">
          <div>{{ item.port_name }}</div>
        </Col>
        <Col span="6" style="text-align: center;">
          <div>{{ item.ata }}</div>
        </Col>
        <Col span="4" style="text-align: center;">
          <div>{{ item.navistatus }}</div>
        </Col>
        <Col span="3" style="text-align: center;">
          <div>{{ item.delayHour }}</div>
        </Col>
        <Col span="3" style="text-align: center;">
          <div>{{ item.business_name }}</div>
        </Col>
      </Row>
    </div>
    <!-- 自营结束 -->
    <!-- 期租开始 -->
    <H3 style="margin-top: 15px; ">期租船舶</H3>
    <div v-for="(item, index) in charterModelList" :key="'charter' + index" class="voyage-list" @click="shipClick(item)">
      <Row class="voyage-area-info" justify="center" align="middle" :style="getDelayStyle(item)">
        <Col span="3" style="text-align: center;">
          <div>{{ item.ship_name }}</div>
        </Col>
        <Col span="5" style="text-align: center;">
          <div>{{ item.port_name }}</div>
        </Col>
        <Col span="6" style="text-align: center;">
          <div>{{ item.ata }}</div>
        </Col>
        <Col span="4" style="text-align: center;">
          <div>{{ item.navistatus }}</div>
        </Col>
        <Col span="3" style="text-align: center;" :style="item.delayHour >= 72 ? 'color: red;' : ''">
          <div>{{ item.delayHour }}</div>
        </Col>
        <Col span="3" style="text-align: center;">
          <div>{{ item.business_name }}</div>
        </Col>
      </Row>
    </div>
    <!-- 期租结束 -->
    <!-- 期租开始 -->
    <H3 style="margin-top: 15px; ">国际船舶</H3>
    <div v-for="(item, index) in internalModelList" :key="'internal' + index" class="voyage-list" @click="shipClick(item)">
      <Row class="voyage-area-info" justify="center" align="middle" :style="getDelayStyle(item)">
        <Col span="3" style="text-align: center;">
          <div>{{ item.ship_name }}</div>
        </Col>
        <Col span="5" style="text-align: center;">
          <div>{{ item.port_name }}</div>
        </Col>
        <Col span="6" style="text-align: center;">
          <div>{{ item.ata }}</div>
        </Col>
        <Col span="4" style="text-align: center;">
          <div>{{ item.navistatus }}</div>
        </Col>
        <Col span="3" style="text-align: center;" :style="item.delayHour >= 72 ? 'color: red;' : ''">
          <div>{{ item.delayHour }}</div>
        </Col>
        <Col span="3" style="text-align: center;">
          <div>{{ item.business_name }}</div>
        </Col>
      </Row>
    </div>
    <!-- 期租结束 -->
    <!-- <div v-for="(item, index) in dataList" :key="index" class="voyage-list" @click="shipClick(item)">
      <Row class="voyage-area-info" justify="center" align="middle" :style="getDelayStyle(item)">
        <Col span="3" style="text-align: center;">
          <div>{{ item.ship_name }}</div>
        </Col>
        <Col span="4" style="text-align: center;">
          <div>{{ item.port_name }}</div>
        </Col>
        <Col span="7" style="text-align: center;">
          <div>{{ item.ata }}</div>
        </Col>
        <Col span="4" style="text-align: center;">
          <div>{{ item.navistatus }}</div>
        </Col>
        <Col span="6" style="text-align: center;">
          <div>{{ item.delayHour }}</div>
        </Col>
      </Row>
    </div> -->
    <div v-if="!isAis" style="position: absolute; right: 20px; top: 20px; cursor: pointer;z-index: 100;">
      <Icon type="md-refresh" size="26" @click="getList"/>
    </div>
    <div id="map" v-show="false"></div>
    <Spin fix v-if="loading"></Spin>
  </div>
</template>

<script>
import axios from 'axios'
import Qs from 'qs'
import API from '@/api/shipManagement'

export default {
  props: {
    isAis: {
      default: false
    }
  },
  data () {
    return {
      countdown: 30 * 60, // 初始倒计时时间，单位为秒
      timerId: null, // setTimeout 的计时器 ID
      loading: false,
      map: null,
      options: {},
      isUp: true,
      shipIgnoreList: ['413375790', '413375810', '413376840', '412379380', '412379370', '413376570', '413693020'],
      selfShipList: [], // 船舶列表
      dataList: [], // 船舶显示列表
      selfModelList: [], // 自营船舶列表
      charterModelList: [], // 期租船舶列表
      internalModelList: [], // 国际船舶列表
      otherModelList: [] // 其它船舶列表
    }
  },
  computed: {
    minutes () {
      return Math.floor(this.countdown / 60)
    },
    seconds () {
      return this.countdown % 60
    }
  },
  methods: {
    getDelayTimeList () {
      const apiUrl = 'https:www.hifleet.com/hifleetapi/getShipsTrajectory.do?dataType=json&&_v=5.3.77'
      let _param = {
        bbox: '1,1,1,1',
        zoom: 1,
        startdates: '2024-01-05 10:36:13',
        enddates: '2024-01-10 10:36:13',
        mmsis: 636020784
      }
      axios.post(apiUrl, Qs.stringify(_param)).then(res => {
        console.log(res)
      })
    },
    getList () {
      let that = this
      if (!localStorage.shipNameList) return
      this.loading = true
      this.selfShipList = JSON.parse(localStorage.shipNameList)
      this.dataList = []
      let totalShipList = []
      this.selfShipList.forEach((item, idx) => {
        if (!this.shipIgnoreList.includes(item.mmsi)) {
          totalShipList.push(item)
        }
      })
      if (!window.ShipService && !this.isAis) {
        this.map = new ShipxyAPI.Map('map', { ak: 'a5bb8f37140d428391e1546d7b704413' })
      }
      totalShipList.forEach((item, idx) => {
        let _shipDetail = window.ShipService.getShipByMmsi(item.mmsi, false)
        this.dataList.push({
          business_model: item.business_model,
          business_name: item.business_name,
          mmsi: item.mmsi,
          ship_name: item.ship_name
        })
        Object.assign(this.dataList[idx], {
          status_code: _shipDetail.navistatus,
          port_name: _shipDetail.dest,
          ata: _shipDetail.eta_std,
          navistatus: CanvasShipUtils.getDisValue(_shipDetail.navistatus, 'naviStatus', 'zh_CN')
        })
        if (this.dataList[idx].status_code !== 1) { // 非锚泊状态
          this.getCurVoyagePort(item.mmsi, idx, totalShipList.length)
        } else {
          this.getLastVoyageListShipXy(item.mmsi, idx, totalShipList.length)
          // this.getLastVoyageList(item.mmsi, idx, totalShipList.length)
        }
        // this.getCurVoyagePort(item.mmsi, idx, totalShipList.length)
        // this.getHistoryVoyagePort(item.mmsi, idx)
        if (idx === totalShipList.length - 1) {
          setTimeout(() => {
            that.dataList.sort((a, b) => {
              if (parseFloat(a.delayTime) > parseFloat(b.delayTime)) {
                return -1
              } else if (parseFloat(a.delayTime) < parseFloat(b.delayTime)) {
                return 1
              } else {
                if (a.status_code > b.status_code) {
                  return -1
                } else if (a.status_code < b.status_code) {
                  return 1
                } else {
                  return 0
                }
              }
            })
            that.$nextTick(() => {
              that.modelSort()
            })
            that.loading = false
          }, 2500)
        }
        if (this.isAis && idx === totalShipList.length - 1) {
          setTimeout(() => { // 5分钟后再提交数据  防止有人开起来就提交导致数据重复
            let paramJson = this.dataList.map(item => {
              return {
                mmsi: item.mmsi,
                ship_name: item.ship_name,
                destination_port: item.port_name,
                arrival_time: item.ata,
                node_type: item.navistatus,
                warning_time: item.delayTime
              }
            })
            let _param = { detailJson: JSON.stringify(paramJson) }
            API.addBatchAisEarlyWarning(_param).then(res => {
              if (res.data.Code === 10000) {
                console.log('数据提交成功!')
              } else {
                console.log('数据提交失败!')
              }
            })
          }, 1000 * 5 * 60)
        }
      })
    },
    startCountdown () {
      this.timerId = setInterval(() => {
        if (this.countdown > 0) {
          this.countdown--
        } else {
          this.refreshList()
        }
      }, 1000)
    },
    refreshList () {
      this.$nextTick(() => {
        this.$emit('shipDetailReload')
        this.stopCountdown()
        // this.getList()
        this.restartCountdown()
      })
    },
    restartCountdown () {
      this.countdown = 30 * 60 // 重新设置倒计时时间
      this.startCountdown() // 开始新的倒计时
    },
    stopCountdown () {
      clearInterval(this.timerId) // 清除计时器
    },
    // 船舶点击事件
    shipClick (item) {
      if (!this.isAis) return
      this.$emit('shipDetailSelect', item.mmsi + '__' + item.shipName)
    },
    // 获取预警时间警戒色
    getDelayStyle (item) {
      if (item.business_model !== '1') return ''
      if (parseFloat(item.delayHour) >= 24 && parseFloat(item.delayHour) < 36) {
        return 'background: #ADECAD;'
      }
      if (parseFloat(item.delayHour) >= 36 && parseFloat(item.delayHour) < 48) {
        return 'background: yellow;'
      }
      if (parseFloat(item.delayHour) >= 48 && parseFloat(item.delayHour) < 72) {
        return 'background: #ff6c00; color: #fff;'
      }
      if (parseFloat(item.delayHour) >= 72) {
        return 'background: red; color: #fff;'
      }
    },
    // 排序
    statusChange () {
      this.isUp = !this.isUp
      if (this.isUp) {
        this.dataList.sort((a, b) => {
          if (a.delayTime > b.delayTime) {
            return -1
          } else if (a.delayTime < b.delayTime) {
            return 1
          } else {
            if (a.status_code > b.status_code) {
              return -1
            } else if (a.status_code < b.status_code) {
              return 1
            } else {
              return 0
            }
          }
        })
      } else {
        this.dataList.sort((a, b) => {
          if (a.delayTime > b.delayTime) {
            return -1
          } else if (a.delayTime < b.delayTime) {
            return 1
          } else {
            if (b.status_code > a.status_code) {
              return -1
            } else if (b.status_code < a.status_code) {
              return 1
            } else {
              return 0
            }
          }
        })
      }
      this.modelSort()
    },
    // 按运营类型分类
    modelSort () {
      this.selfModelList = [] // 自营船舶列表
      this.charterModelList = [] // 期租船舶列表
      this.internalModelList = [] // 国际船舶列表
      this.otherModelList = [] // 其它船舶列表
      this.dataList.forEach(item => {
        if (item.business_model === '1') {
          this.selfModelList.push(item)
        } else if (item.business_model === '2') {
          this.charterModelList.push(item)
        } else if (item.business_model === '3') {
          this.internalModelList.push(item)
        } else {
          this.otherModelList.push(item)
        }
      })
      // this.dataList = [...[], ...selfModelList, ...charterModelList, ...internalModelList, ...otherModelList]
    },
    // 获取历史航次列表 hifleet
    async getLastVoyageList (mmsi, idx, len) {
      const that = this
      // https://www.hifleet.com/portofcall/getrecentvoyage?mmsi=413219350&limit1=0&limit2=1&array=0&_v=5.3.77&
      const url = 'https://www.hifleet.com/portofcall/getrecentvoyage?mmsi=' + mmsi + '&limit1=0&limit2=1&array=0&_v=5.3.77&'
      API.getRecentVoyage({ url: url }).then(res => {
        if (res.data.list.length > 0) {
          let startDate = res.data.list[0].starttime
          that.getCurWarnList(mmsi, idx, len, startDate)
        }
      })
    },
    // 获取历史航次列表 shipXy
    async getLastVoyageListShipXy (mmsi, idx, len) {
      const that = this
      let dateTime = new Date()
      let currentYear = dateTime.getFullYear()
      let currentMonth = dateTime.getMonth()
      let currentDate = dateTime.getDate()
      let startDay = new Date(currentYear, currentMonth - 1, currentDate).getTime().toString().substring(0, 10)
      let endDay = dateTime.getTime().toString().substring(0, 10)
      // 获取时间段内航次 船舶靠港记录
      let url1 = 'http://api.shipxy.com/apicall/GetPortOfCallByShip?k=a5bb8f37140d428391e1546d7b704413' + '&v=2&mmsi=' + mmsi + '&timetype=2&begin=' + startDay + '&end=' + endDay
      // 获取港口及到港时间
      await jQuery.getJSON(url1 + '&jsf=?', function (result) {
        if (result.records.length > 0) {
          let startDate = result.records[result.records.length - 1].atd || result.records[result.records.length - 1].ata // 先拿离港，如果没有离港就拿到港时间
          that.getCurWarnList(mmsi, idx, len, startDate)
        }
      })
      // const that = this
      // const url = 'https://www.hifleet.com/portofcall/getrecentvoyage?mmsi=' + mmsi + '&limit1=0&limit2=1&array=0&_v=5.3.77&'
      // API.getRecentVoyage({ url: url }).then(res => {
      //   if (res.data.list.length > 0) {
      //     let startDate = res.data.list[0].starttime
      //     that.getCurWarnList(mmsi, idx, len, startDate)
      //   }
      // })
    },
    // 获取预警时间 hifleet
    async getCurWarnList (mmsi, idx, len, startDate) {
      const that = this
      const date = new Date()
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')

      let currentTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
      let _param = {
        mmsis: mmsi,
        zoom: 1,
        startdates: startDate,
        endates: currentTime
      }
      await API.getShipsTrajectory(_param).then(res => {
        if (res.data.data.length > 0) {
          let delayHour = that.getDelayHours(mmsi, res.data.data[0].offers)
          Object.assign(that.dataList[idx], {
            port_name: that.dataList[idx].port_name, // result.data.list.startport.indexOf('[') > -1 ? result.data.list.startport.split('[')[0] : result.data.list.startport,
            ata: that.dataList[idx].ata,
            delayTime: delayHour, // that.hoursChange(result.data.list.stoppedHourFormat),
            delayHour: delayHour, // that.hoursChange(result.data.list.stoppedHourFormat),
            delayTimeStr: delayHour
          })
        } else {
          Object.assign(that.dataList[idx], {
            port_name: '--',
            ata: '--'
          })
        }
        // if (idx === len - 1) {
        //   setTimeout(() => {
        //     that.dataList.sort((a, b) => {
        //       if (parseFloat(a.delayTime) > parseFloat(b.delayTime)) {
        //         return -1
        //       } else if (parseFloat(a.delayTime) < parseFloat(b.delayTime)) {
        //         return 1
        //       } else {
        //         if (a.status_code > b.status_code) {
        //           return -1
        //         } else if (a.status_code < b.status_code) {
        //           return 1
        //         } else {
        //           return 0
        //         }
        //       }
        //     })
        //     that.$nextTick(() => {
        //       that.modelSort()
        //     })
        //     that.loading = false
        //   }, 500)
        // }
      })
    },
    // 获取船舶当前航行状态数据 -- 船队在线
    async getCurVoyagePort (mmsi, idx, len) {
      const that = this
      // 获取时间段内航次 船舶靠港记录
      let url = 'https://www.hifleet.com/portofcall/getshipcurrentvoyagestatus?mmsi=' + mmsi + '&_v=5.3.77&'
      // 获取港口及到港时间
      await axios.get(url).then(result => {
        if (result.data.list) {
          // if (that.dataList[idx].status_code !== 1) { // 非锚泊状态
          Object.assign(that.dataList[idx], {
            port_name: that.dataList[idx].port_name, // result.data.list.startport.indexOf('[') > -1 ? result.data.list.startport.split('[')[0] : result.data.list.startport,
            ata: that.dataList[idx].status_code === 0 ? that.dataList[idx].ata : that.getBerthAta(result.data.list.stoppedHourFormat, that.dataList[idx].ata),
            delayTime: 0,
            delayHour: '--',
            delayTimeStr: '--'
          })
          // } else { // 锚泊状态
          //   Object.assign(that.dataList[idx], {
          //     port_name: that.dataList[idx].port_name, // result.data.list.startport.indexOf('[') > -1 ? result.data.list.startport.split('[')[0] : result.data.list.startport,
          //     ata: that.dataList[idx].ata,
          //     delayTime: that.hoursChange(result.data.list.stoppedHourFormat),
          //     delayHour: that.hoursChange(result.data.list.stoppedHourFormat),
          //     delayTimeStr: result.data.list.stoppedHourFormat
          //   })
          // }
        }
        // else {
        //   Object.assign(that.dataList[idx], {
        //     port_name: '--',
        //     ata: '--'
        //   })
        // }
        // if (idx === len - 1) {
        //   setTimeout(() => {
        //     that.dataList.sort((a, b) => {
        //       if (a.delayTime > b.delayTime) {
        //         return -1
        //       } else if (a.delayTime < b.delayTime) {
        //         return 1
        //       } else {
        //         if (a.status_code > b.status_code) {
        //           return -1
        //         } else if (a.status_code < b.status_code) {
        //           return 1
        //         } else {
        //           return 0
        //         }
        //       }
        //     })
        //     that.$nextTick(() => {
        //       that.modelSort()
        //     })
        //     that.loading = false
        //   }, 500)
        // }
      })
    },
    getDelayHours (mmsi, list) {
      let totalMinutes = 0
      let backHour = 0
      if (list.length > 0) {
        list.forEach(item => {
          if (item.accumulatetime && item.accumulatetime !== '-') {
            const timeStr = item.accumulatetime.replace('h', ' ').replace('m', '')
            const [hours, minutes] = timeStr.split(' ')
            totalMinutes += parseInt(hours) * 60 + parseInt(minutes)
          }
        })
        backHour = (parseFloat(totalMinutes / 60)).toFixed(1)
      } else {
        backHour = '-'
      }
      return backHour
    },
    hoursChange (time) { // 时间转换
      let backHours = '--'
      if (time && time !== '') {
        if (time.indexOf('d') > -1) { // 带有天数的时间
          let regex = /^(\d+)d([\d.]+)h$/
          let match = regex.exec(time)
          let days = parseInt(match[1], 10)
          let hours = parseFloat(match[2])
          backHours = days * 24 + hours
        } else { // 只有小时的时间
          backHours = parseFloat(time.split('h')[0])
        }
      }
      return backHours
    },
    getBerthAta (time, ata) { // 重新计算靠泊状态下的到港时间
      let backAta = '--'
      let curTime = new Date()
      if (time && time !== '') {
        if (time.indexOf('d') > -1) { // 带有天数的时间
          let regex = /^(\d+)d([\d.]+)h$/
          let match = regex.exec(time)
          let days = parseInt(match[1], 10)
          let hours = parseFloat(match[2])
          let totalHours = days * 24 + hours
          let disTime = new Date(curTime.getTime() - totalHours * 60 * 60 * 1000)
          let dayStr = disTime.getDay()
          if (disTime.getDay() < 10) {
            dayStr = '0' + dayStr
          }
          backAta = disTime.getFullYear() + '-' + disTime.getMonth() + 1 + '-' + dayStr + ' ' + disTime.toLocaleTimeString()
        } else { // 只有小时的时间
          let hours = parseFloat(time.split('h')[0])
          let disTime = new Date(curTime.getTime() - hours * 60 * 60 * 1000)
          let dayStr = disTime.getDay()
          if (disTime.getDay() < 10) {
            dayStr = '0' + dayStr
          }
          backAta = disTime.getFullYear() + '-' + disTime.getMonth() + 1 + '-' + dayStr + ' ' + disTime.toLocaleTimeString()
        }
      } else {
        backAta = ata
      }
      return backAta
    },
    // 获取历史靠泊港口数据 -- 船讯网
    async getHistoryVoyagePort (mmsi, idx) {
      const that = this
      let dateTime = new Date()
      let currentYear = dateTime.getFullYear()
      let currentMonth = dateTime.getMonth()
      let currentDate = dateTime.getDate()
      let startDay = new Date(currentYear, currentMonth - 1, currentDate).getTime().toString().substring(0, 10)
      let endDay = dateTime.getTime().toString().substring(0, 10)
      // 获取时间段内航次 船舶靠港记录
      let url1 = 'http://api.shipxy.com/apicall/GetPortOfCallByShip?k=a5bb8f37140d428391e1546d7b704413' + '&v=2&mmsi=' + mmsi + '&timetype=2&begin=' + startDay + '&end=' + endDay
      // 获取港口及到港时间
      await jQuery.getJSON(url1 + '&jsf=?', function (result) {
        if (result.records.length > 0) {
          if (that.dataList[idx].status_code !== 1) { // 非锚泊状态
            Object.assign(that.dataList[idx], {
              port_name: that.dataList[idx].status_code === 0 ? that.dataList[idx].port_name : result.records[result.records.length - 1].portname_cn,
              ata: that.dataList[idx].status_code === 0 ? that.dataList[idx].ata : result.records[result.records.length - 1].ata,
              delayTime: 0,
              delayHour: '--',
              delayTimeStr: '--'
            })
          } else { // 锚泊状态
            // 计算锚泊预期时间
            let voyageAta = new Date(result.records[result.records.length - 1].ata).getTime() // 最后一个航次到港时间
            let curAta = new Date(that.dataList[idx].ata).getTime() // 预抵时间
            if (curAta - voyageAta > 0) { // 判断最后一个航次与预抵时间哪个时间最大，按大的进行换算锚泊时长
              Object.assign(that.dataList[idx], {
                port_name: that.dataList[idx].port_name,
                ata: that.dataList[idx].ata
              })
              that.diffTime(new Date(that.dataList[idx].ata), dateTime, idx)
            } else {
              Object.assign(that.dataList[idx], {
                port_name: result.records[result.records.length - 1].portname_cn,
                ata: result.records[result.records.length - 1].ata
              })
              that.diffTime(new Date(result.records[result.records.length - 1].ata), dateTime, idx)
            }
          }
          setTimeout(() => {
            that.dataList.sort((a, b) => {
              if (a.delayTime > b.delayTime) {
                return -1
              } else if (a.delayTime < b.delayTime) {
                return 1
              } else {
                if (a.status_code > b.status_code) {
                  return -1
                } else if (a.status_code < b.status_code) {
                  return 1
                } else {
                  return 0
                }
              }
            })
            that.$nextTick(() => {
              that.modelSort()
            })
            that.loading = false
          }, 500)
        } else {
          Object.assign(that.dataList[idx], {
            port_name: '--',
            ata: '--'
          })
        }
      })
    },
    diffTime (startDate, endDate, idx) {
      let diffvalue = endDate.getTime() - startDate.getTime()
      if (diffvalue > 0) {
        let backStr = ''
        let minute = 1000 * 60
        let hour = minute * 60
        let day = hour * 24
        let halfamonth = day * 15
        let month = day * 30

        var monthC = diffvalue / month
        var weekC = diffvalue / (7 * day)
        var dayC = diffvalue / day
        var hourC = diffvalue / hour
        var minC = diffvalue / minute
        if (parseInt(monthC) >= 1) {
          backStr = parseInt(monthC) + '个月前'
        } else if (parseInt(dayC) > 1) {
          backStr = parseInt(dayC) + '天前'
        } else if (parseInt(dayC) === 1) {
          backStr = '昨天'
        } else if (parseInt(hourC) >= 1) {
          backStr = parseInt(hourC) + '小时前'
        } else if (parseInt(minC) >= 1) {
          backStr = parseInt(minC) + '分钟前'
        } else {
          backStr = '刚刚'
        }
        Object.assign(this.dataList[idx], {
          delayTime: parseInt(diffvalue), // 时间戳差
          delayHour: parseFloat(hourC).toFixed(2),
          delayTimeStr: backStr // 相差时间字符
        })
      } else {
        Object.assign(this.dataList[idx], {
          delayTime: 1,
          delayHour: '--',
          delayTimeStr: '刚刚' // 相差时间字符
        })
      }
    },
    // 开启船视图
    openShip (map) {
      // 默认 MT_SATELLITE 卫星图 MT_GOOGLE 谷歌地图 MT_SEA 海图
      map.basemapsControl.changeMap('MT_SEA')
      // 开启区域船服务
      const canvasShips = ShipxyAPI.ShipService(map, {
        enableAreaShip: true, // 区域船
        enableFleetShip: true, // 船队船
        // enableDShip: true, // D+船
        lableFont: ['600 12px Arial', '600 12px 宋体'], // 船舶名称，文字字体，默认值：["600 12px Arial", "500 12px Arial"]
        lableTxtColor: ['#000', '#eee'], // 船舶名称，文字颜色，默认值：["#000","#fff"]
        lableLineColor: ['rgba(1, 30, 62, 1)', 'rgba(1, 30, 62, 1)'], //  边框颜色，默认值：["#000","#000"]
        lableLinefillColor: ['rgba(255, 255, 255, 0.7)', 'rgba(1, 30, 62, 0.3)'], // 框内填充颜色，默认值：[null,null]
        obliqueLineColor: ['#000', '#000'], // 船舶名称，斜线颜色，默认值：[null,null]
        dShipColor: '#FF6437' // D+船颜色，默认：#ff6347
      })
      window.ShipService = canvasShips
      window.ShipService.setPointerEvents(!0)
    }
  },
  beforeDestroy () {
    this.stopCountdown()
  },
  mounted () {
    if (!window.ShipService && !this.isAis) {
      this.map = new ShipxyAPI.Map('map', { ak: 'a5bb8f37140d428391e1546d7b704413' })
      this.openShip(this.map)
    }
    this.getList()
    this.startCountdown()
  }
}
</script>
<style lang="less">
.voyage-area-info {
    cursor: pointer;
    &:hover {
      background: #ebf7ff;
    }
    display: flex;
    min-height: 32px;
    justify-content: center;
    justify-items: center;
    align-items: center;
    padding: 0 20px;
    background:#fff;
    border-radius:4px;
    border:1px solid #D9D9D9;
    margin-top: 10px;
    color: #333;
    text-align: left;
    .btn-area {
      border: none;
      color: #333;
      &:hover {
        color: #007DFF;
      }
    }
    .copy_btn {
      padding: 0 8px;
      margin-left: 12px;
      font-size: 12px;
      transform: scale(0.833,0.833);
      *font-size: 10px;
      color: #007DFF;
      background:#fff;
      border-radius:12px;
      border:1px solid #007DFF;
      cursor: pointer;
    }
    .cargo_result {
      font-weight: normal;
      margin-top: 10px;
    }
  }
  .datalisthead {
    position: sticky;
    background: #fff;
    top: 0;
    padding: 10px !important;
    z-index: 99 !important;
    margin: 5px 0 -10px 0;
    padding: 0 15px;
    font-size: 14px;
    font-weight: bold;
    text-align: center;
    .ivu-col {
      padding: 0 5px;
      color: #918c8c;
    }
  }
  .time_end {
    position: absolute;
    bottom: 10px;
    right: 10px;
  }
</style>
