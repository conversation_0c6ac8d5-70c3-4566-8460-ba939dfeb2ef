<template>
  <Modal :width="768" :mask-closable="false" v-model="modalData.modal" ok-text="保存" @on-ok="handleModify" :title="modalData.title" @on-visible-change="modalShow">
    <div class="nodeTitle">
      <h3>适用节点: {{ curNodeName }}</h3>
    </div>
    <CheckboxGroup v-model="selectGroup" @on-change="checkGroupChange">
      <Checkbox v-for="(item, index) in checkList" :disabled="disabledGroup.includes(item.template_content)" :label="item.template_detail_id" :key="index">
        {{ item.template_content }}
      </Checkbox>
    </CheckboxGroup>
    <textarea :value="nodeString" class="nodeLabel" readonly></textarea>
  </Modal>
</template>

<script>
import API from '@/api/setting/SMSSetting'

export default {
  props: {
    modalData: Object
  },
  data () {
    return {
      listQuery: {
        node_info_id: ''
      },
      curNodeName: '', // 适用节点名称
      nodeString: '', // 所有节点名称
      checkList: [], // 节点模板列表
      selectGroup: [], // 选中节点列表
      disabledGroup: ['船名', '节点时间', '节点名称'], // 不可编辑节点
      saveParam: {
        msg_shipowner_config_id: '',
        node_info_id: '',
        template_detail_ids: '',
        content_codes: ''
      }
    }
  },
  methods: {
    modalShow (val) {
      if (val) {
        this.saveParam.node_info_id = this.listQuery.node_info_id = this.modalData.data.node_info_id
        this.curNodeName = this.modalData.data.template_node_name
        this.saveParam.msg_shipowner_config_id = this.modalData.data.msg_shipowner_config_id
        API.querySingleNode(this.listQuery).then(res => {
          if (res.data.Code === 10000) {
            this.checkList = res.data.Result
            setTimeout(() => {
              this.selectGroup = this.modalData.data.template_detail_ids.split(',')
              this.getParam()
            }, 100)
          } else {
            this.$Message.error(res.data.Message)
          }
        })
      }
    },
    // 入参遍历获取
    getParam () {
      let template_detail_ids_arr = []
      let content_codes_arr = []
      let template_content_arr = []
      this.sortSelectGroup()
      this.checkList.map((item, index) => {
        if (this.selectGroup.includes(item.template_detail_id)) {
          template_detail_ids_arr.push(item.template_detail_id)
          content_codes_arr.push(item.template_content_code)
          template_content_arr.push(item.template_content)
        }
      })
      this.getNodeString(template_content_arr)
      this.saveParam.template_detail_ids = template_detail_ids_arr.join()
      this.saveParam.content_codes = content_codes_arr.join()
    },
    // 排序
    sortSelectGroup () {
      this.selectGroup = this.selectGroup.sort((a, b) => {
        return a.localeCompare(b)
      })
    },
    // 获取节点名称文案 自定义样式
    getNodeString (nodeArr) {
      this.nodeString = ''
      nodeArr.map(item => {
        this.nodeString += '<#' + item + '#> '
      })
    },
    // 选项改变触发
    checkGroupChange (arr) {
      this.getParam()
    },
    // 保存修改
    handleModify () {
      API.saveSingleNode(this.saveParam).then(res => {
        if (res.data.Code === 10000) {
          this.$Message.success(res.data.Message)
          this.$emit('callback')
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    }
  }
}
</script>

<style lang="less">
  .nodeTitle {
    margin: 0 0 10px 0;
  }
  .nodeLabel {
    width: 100%;
    height: 100px;
    margin-top: 20px;
    padding: 10px;
  }
</style>
